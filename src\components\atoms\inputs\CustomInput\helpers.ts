import {
    AutocompleteArrayInput,
    AutocompleteInput,
    BooleanInput,
    CheckboxGroupInput,
    DateInput,
    DateTimeInput,
    FileInput,
    ImageInput,
    NullableBooleanInput,
    NumberInput,
    PasswordInput,
    RadioButtonGroupInput,
    SelectArrayInput,
    SelectInput,
    TextArrayInput,
    TextInput,
    TimeInput,
} from 'react-admin';

import {
    IDENTIFIER_PRESET,
    MULTI_LINE_PRESET,
    SINGLE_LINE_PRESET,
} from '~/utils/sanitizeTextInput';
import CodeInput from '../CodeInput';
import PhoneNumberInput from '../PhoneNumberInput';
import RaFileUploadComponent from '../../../organisms/FileUpload/RaFileUploadComponent';

import type { CustomInputProps, SanitizePreset } from './types';
import type { SanitizationOptions } from '~/utils/sanitizeTextInput';
import type { ComponentType } from 'react';
import type { Validator } from 'react-admin';

/**
 * Detects if a field is required based on its validate prop
 * Uses the same logic as React-Admin's isRequired function
 */
export const detectRequired = (validate?: Validator | Validator[]): boolean => {
    if (!validate) return false;

    // Check if validator has isRequired property (set by required() validator)
    if (validate && (validate as any).isRequired) {
        return true;
    }

    // Check array of validators
    if (Array.isArray(validate)) {
        return !!validate.find(v => (v as any).isRequired);
    }

    return false;
};

/**
 * Maps sanitize preset string to SanitizationOptions
 */
export const getSanitizationPreset = (
    sanitize: SanitizePreset
): SanitizationOptions | null => {
    if (!sanitize) {
        return null;
    }

    switch (sanitize) {
        case 'singleLine':
            return SINGLE_LINE_PRESET;
        case 'multiLine':
            return MULTI_LINE_PRESET;
        case 'identifier':
            return IDENTIFIER_PRESET;
        default:
            return null;
    }
};

/**
 * Maps input type string to corresponding React-Admin component
 */
export const getInputComponent = (
    type: CustomInputProps['type']
): ComponentType<any> => {
    switch (type) {
        case 'text':
            return TextInput;
        case 'number':
            return NumberInput;
        case 'date':
            return DateInput;
        case 'datetime':
            return DateTimeInput;
        case 'time':
            return TimeInput;
        case 'boolean':
            return BooleanInput;
        case 'nullableBoolean':
            return NullableBooleanInput;
        case 'select':
            return SelectInput;
        case 'autocomplete':
            return AutocompleteInput;
        case 'radio':
            return RadioButtonGroupInput;
        case 'checkboxGroup':
            return CheckboxGroupInput;
        case 'file':
            return FileInput;
        case 'image':
            return ImageInput;
        case 'password':
            return PasswordInput;
        case 'email':
            return TextInput;
        case 'url':
            return TextInput;
        case 'textArray':
            return TextArrayInput;
        case 'selectArray':
            return SelectArrayInput;
        case 'autocompleteArray':
            return AutocompleteArrayInput;
        case 'phone':
            return PhoneNumberInput;
        case 'code':
            return CodeInput;
        case 'fileUpload':
            return RaFileUploadComponent;
        default:
            return TextInput;
    }
};

/**
 * Gets additional props for specific input types
 */
export const getInputTypeSpecificProps = (
    type: CustomInputProps['type']
): Record<string, any> => {
    switch (type) {
        case 'email':
            return { type: 'email' };
        case 'url':
            return { type: 'url' };
        case 'password':
            return { type: 'password' };
        default:
            return {};
    }
};
