import React from 'react';
import {
  Box,
  // Button, // Commented: Not used in gateway pattern version
  // Collapse, // Commented: Not used in gateway pattern version
  // InputAdornment, // Commented: Not used in gateway pattern version
  // Link, // Commented: Not used in gateway pattern version
  // TextField, // Commented: Not used in gateway pattern version
  Typography,
} from '@mui/material';

// import { useLogin, useNotify } from 'react-admin'; // Commented: Not used in gateway pattern version

// Selio logo component
const SelioLogo = () => (
  <Typography
    variant="h6"
    component="div"
    sx={{
      fontWeight: 'bold',
      letterSpacing: 2,
      fontSize: '1.25rem', // Scaled up
      color: '#212121', // Explicit dark color for logo
    }}
  >
    S Ξ L I O
  </Typography>
);

// ============================================================================
// AUTH PATTERN: This component should rarely be reached
// ============================================================================
//
// With the current auth implementation:
// - index.html initializes Firebase and checks auth state BEFORE React loads
// - Unauthenticated users are redirected to /auth page (auth.html)
// - auth.html handles all authentication (Google OAuth, email/password)
// - This component exists only as a fallback for react-admin's loginPage prop
//
// If this component is reached, it redirects to /auth page immediately
// ============================================================================

export const LoginPage: React.FC = () => {
  // Redirect to /auth page where all authentication is properly handled
  React.useEffect(() => {
    const currentPath =
      window.location.pathname + window.location.search + window.location.hash;
    const returnUrl =
      currentPath !== '/' && currentPath !== '/login'
        ? '?returnUrl=' + encodeURIComponent(currentPath)
        : '';
    window.location.href = '/auth' + returnUrl;
  }, []);

  // Show simple loading state while redirecting
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="#fff"
      px={2}
    >
      <SelioLogo />
      <Typography
        variant="body1"
        align="center"
        color="#616161"
        mt={2}
        sx={{ fontSize: '1rem' }}
      >
        Redirecting to login...
      </Typography>
    </Box>
  );
};

/* COMMENTED OUT: Old LoginPage implementation
 * This component handled login within the React app, but had issues:
 * - Called login() from authProvider which used signInWithRedirect
 * - signInWithRedirect wasn't properly handling getRedirectResult
 * - Users had to load 3.5MB React bundle before seeing login screen
 *
 * The old implementation is preserved below for reference:

export const LoginPage_OLD: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loginStep, setLoginStep] = useState<'email' | 'password'>('email');
  const [loadingPassword, setLoadingPassword] = useState(false);
  const [loadingGoogle, setLoadingGoogle] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const login = useLogin();
  const notify = useNotify();

  const handleEmailContinue = () => {
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      notify('Please enter a valid email address.', { type: 'warning' });
      return;
    }
    setLoginStep('password');
  };

  const handlePasswordLogin = () => {
    if (!password) {
      notify('Please enter your password.', { type: 'warning' });
      return;
    }
    setLoadingPassword(true);
    login({ email, password }, '/')
      .catch(error => {
        console.error('Password Login Error:', error);
        notify(
          error.message ||
            'Login failed. Please check your email and password.',
          { type: 'error' }
        );
        setLoginStep('email');
        setPassword('');
        setShowForgotPassword(true);
      })
      .finally(() => {
        setLoadingPassword(false);
      });
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (loginStep === 'email') {
      handleEmailContinue();
    } else {
      handlePasswordLogin();
    }
  };

  const handleGoogleLogin = () => {
    setLoadingGoogle(true);
    // ISSUE: This called authProvider.login({ method: 'google' })
    // which used signInWithRedirect without proper getRedirectResult handling
    login({ method: 'google' }, '/')
      .catch(error => {
        console.error('Google Login Error:', error);
        notify(error.message || 'Google Sign-In failed.', { type: 'error' });
      })
      .finally(() => {
        setLoadingGoogle(false);
      });
  };

  // ... [Full JSX implementation with forms, buttons, Google sign-in, etc.]
  // ... [See git history or older commits for complete implementation]
};

*/
