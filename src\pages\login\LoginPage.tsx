import React, { useState } from 'react';
import {
  Box,
  Button,
  Collapse,
  InputAdornment,
  Link,
  TextField,
  Typography,
} from '@mui/material';
// Re-import Collapse, Import Link and InputAdornment
import { useLogin, useNotify } from 'react-admin';

// Selio logo component
const SelioLogo = () => (
  <Typography
    variant="h6"
    component="div"
    sx={{
      fontWeight: 'bold',
      letterSpacing: 2,
      fontSize: '1.25rem', // Scaled up
      color: '#212121', // Explicit dark color for logo
    }}
  >
    S Ξ L I O
  </Typography>
);

// This component will now be rendered via a separate route, not the loginPage prop
export const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState(''); // Add password state
  const [loginStep, setLoginStep] = useState<'email' | 'password'>('email'); // Add login step state
  const [loadingPassword, setLoadingPassword] = useState(false); // Add password loading state
  const [loadingGoogle, setLoadingGoogle] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false); // State for forgot password link
  const login = useLogin(); // Call hooks at top level - should be safe now
  const notify = useNotify();

  const handleEmailContinue = () => {
    // Basic email validation (can be enhanced)
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      notify('Please enter a valid email address.', { type: 'warning' });
      return;
    }
    // Proceed to password step
    setLoginStep('password');
  };

  const handlePasswordLogin = () => {
    if (!password) {
      notify('Please enter your password.', { type: 'warning' });
      return;
    }
    setLoadingPassword(true);
    login({ email, password }, '/') // Use email/password for login
      .catch(error => {
        console.error('Password Login Error:', error);
        notify(
          error.message ||
            'Login failed. Please check your email and password.',
          {
            type: 'error',
          }
        );
        // Reset to email step on failure and show forgot password link
        setLoginStep('email');
        setPassword(''); // Clear password field
        setShowForgotPassword(true);
      })
      .finally(() => {
        setLoadingPassword(false);
      });
  };

  // Combined form submission handler
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (loginStep === 'email') {
      handleEmailContinue();
    } else {
      handlePasswordLogin();
    }
  };

  // handlePasswordLogin added above
  const handleGoogleLogin = () => {
    setLoadingGoogle(true);
    login({ method: 'google' }, '/') // Let authProvider handle login
      .catch(error => {
        console.error('Google Login Error:', error);
        notify(error.message || 'Google Sign-In failed.', { type: 'error' });
      })
      .finally(() => {
        setLoadingGoogle(false);
      });
  };

  return (
    <Box // Use MUI components for styling
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="#fff"
      px={2}
    >
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        maxWidth={500} // Scaled up 25%
        width="100%"
      >
        {/* Header */}
        <Typography
          variant="h4"
          component="h1"
          fontWeight="bold"
          align="center"
          gutterBottom
          fontSize="1.25rem !important"
          sx={{color: '#212121', textShadow: '2px 2px 4px #b1b1b1' }} // Scaled up
        >
          Welcome
        </Typography>
        <Typography
          variant="body1"
          align="center"
          color="#616161" // Explicit secondary text color
          mb={5} // Scaled up margin
          sx={{ fontSize: '1.25rem' }} // Scaled up font size
        >
          Sign in to your account
        </Typography>

        {/* Wrap Email/Password Form in Collapse controlled by !loadingGoogle */}
        {/* <Collapse
          in={!loadingGoogle}
          timeout="auto"
          unmountOnExit
          sx={{ width: '100%' }}
        >
          <Box component="form" onSubmit={handleSubmit} width="100%">
            {loginStep === 'email' && (
              <TextField
                placeholder="Enter your email address"
                type="email"
                value={email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  // Add type to event
                  setEmail(e.target.value);
                  // Hide forgot password link when user starts typing again
                  if (showForgotPassword) {
                    setShowForgotPassword(false);
                  }
                }}
                required
                fullWidth
                variant="outlined"
                disabled={loadingGoogle || loadingPassword}
                sx={{
                  mb: 3, // Add margin bottom back to TextField
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    height: '62px', // Scaled up height
                    pr: showForgotPassword ? 0 : undefined, // Remove padding-right if adornment is shown
                    '& fieldset': { borderColor: '#dadce0' },
                    '&:hover fieldset': { borderColor: '#c0c0c0' },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1a73e8',
                      borderWidth: '1px',
                    },
                    '&.Mui-disabled': {
                      // Styles for disabled root
                      backgroundColor: '#eeeeee', // Light grey background
                      '& fieldset': { borderColor: '#bdbdbd' }, // Grey border
                    },
                  },
                  '& .MuiOutlinedInput-input': {
                    color: '#212121',
                    fontSize: '1rem', // Scaled up font size
                    padding: '18px 16px', // Adjust padding for 62px height
                    '::placeholder': { color: '#6c757d', opacity: 1 },
                    '&.Mui-disabled': {
                      // Style for disabled input text
                      color: '#757575', // Grey text
                      WebkitTextFillColor: '#757575', // Ensure color override in WebKit
                    },
                  },
                }}
                InputLabelProps={{ shrink: false }}
                label=""
                InputProps={{
                  // Add InputProps for endAdornment
                  endAdornment: showForgotPassword ? ( // Render only if showForgotPassword is true
                    <InputAdornment position="end" sx={{ height: '100%' }}>
                      <Link
                        component="button"
                        variant="body2"
                        onClick={() => {
                          // TODO: Implement forgot password logic later
                          notify(
                            'Forgot password clicked - functionality to be implemented.',
                            {
                              type: 'info',
                            }
                          );
                        }}
                        sx={{
                          whiteSpace: 'nowrap',
                          cursor: 'pointer',
                          fontSize: '0.9rem',
                          color: '#007bff',
                          textDecoration: 'none',
                          mr: 2, // Adjust margin for better spacing inside adornment
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Forgot password?
                      </Link>
                    </InputAdornment>
                  ) : null, // Render null otherwise
                }}
              />
            )}
            {loginStep === 'password' && (
              <TextField
                placeholder="Enter your password"
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
                fullWidth
                variant="outlined"
                disabled={loadingGoogle || loadingPassword} // Disable during either loading state
                sx={{
                  mb: 3, // Scaled up margin
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    height: '62px', // Scaled up height
                    '& fieldset': { borderColor: '#dadce0' },
                    '&:hover fieldset': { borderColor: '#c0c0c0' },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1a73e8',
                      borderWidth: '1px',
                    },
                    '&.Mui-disabled': {
                      // Styles for disabled root
                      backgroundColor: '#eeeeee', // Light grey background
                      '& fieldset': { borderColor: '#bdbdbd' }, // Grey border
                    },
                  },
                  '& .MuiOutlinedInput-input': {
                    color: '#212121',
                    fontSize: '1.1rem', // Scaled up font size
                    padding: '18px 16px', // Adjust padding for 62px height
                    '::placeholder': { color: '#6c757d', opacity: 1 },
                    '&.Mui-disabled': {
                      // Style for disabled input text
                      color: '#757575', // Grey text
                      WebkitTextFillColor: '#757575', // Ensure color override in WebKit
                    },
                  },
                }}
                InputLabelProps={{ shrink: false }}
                label=""
              />
            )}
            <Button
              type="submit"
              variant="contained"
              fullWidth
              disabled={loadingGoogle || loadingPassword} // Disable during either loading state
              sx={{
                py: 0, // Control height directly
                borderRadius: '8px', // Match other elements
                textTransform: 'none',
                backgroundColor: '#007bff',
                color: '#ffffff', // Set text color to white
                fontSize: '1rem !important', // Scaled up font size
                fontWeight: 500,
                height: '62px', // Scaled up height
                boxShadow: 'none', // No shadow like image
                '&:hover': {
                  backgroundColor: '#0069d9', // Darker blue on hover
                  boxShadow: 'none', // Ensure no shadow on hover either
                },
                '&.Mui-disabled': {
                  // Styles for disabled button
                  backgroundColor: '#e0e0e0', // Light grey background
                  color: '#9e9e9e', // Grey text
                  boxShadow: 'none',
                },
              }}
            >
              {loginStep === 'email' ? 'Continue' : 'Login'}{' '}
            </Button>
          </Box>
        </Collapse> */}

        {/* Outer Collapse for Divider (Hides on Google load OR password step) */}
        {/* <Collapse
          in={loginStep === 'email' && !loadingGoogle}
          timeout="auto"
          unmountOnExit
          sx={{ width: '100%' }}
        >
          <Collapse
            in={!email}
            timeout="auto"
            unmountOnExit
            sx={{ width: '100%' }}
          >
            <Box width="100%" display="flex" alignItems="center" my={3}>
              {' '}
              <Box flex={1} borderBottom={1} borderColor="#e0e0e0" />{' '}
              <Typography
                variant="body2"
                color="#616161"
                px={2}
                sx={{ fontSize: '1rem' }}
              >
                {' '}
                or
              </Typography>
              <Box flex={1} borderBottom={1} borderColor="#e0e0e0" />{' '}
            </Box>
          </Collapse>
        </Collapse> */}

        {/* Outer Collapse for Google Button (Hides on password step ONLY) */}
        <Collapse
          in={loginStep === 'email'}
          timeout="auto"
          unmountOnExit
          sx={{ width: '100%' }}
        >
          {/* Inner Collapse for Google Button (Hides when typing email) */}
          <Collapse
            in={!email}
            timeout="auto"
            unmountOnExit
            sx={{ width: '100%' }}
          >
            <Button
              variant="outlined"
              fullWidth
              onClick={handleGoogleLogin}
              disabled={loadingGoogle || loadingPassword} // Also disable if password login is loading
              sx={{
                py: 0, // Control height directly
                px: 2,
                // mb: 3, // No margin needed at the bottom now
                borderRadius: '8px', // More rounded corners like image
                borderColor: '#dadce0',
                color: '#3c4043',
                textTransform: 'none',
                backgroundColor: '#ffffff',
                // Standard Google button shadow
                boxShadow:
                  '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)',
                height: '62px', // Scaled up height
                '&:hover': {
                  backgroundColor: '#f8f9fa',
                  borderColor: '#dadce0',
                  // Slightly enhanced standard Google shadow on hover
                  boxShadow:
                    '0 1px 3px 0 rgba(60,64,67,.3), 0 2px 6px 2px rgba(60,64,67,.15)',
                },
              }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
              >
                {' '}
                {/* Add justifyContent */}
                <Box
                  mr={2}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  width="24px"
                  sx={{
                    // Define keyframes here for scope
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                  }}
                >
                  <svg
                    width="20"
                    height="20"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 48 48"
                    style={{
                      // Apply animation conditionally via style prop
                      animation: loadingGoogle
                        ? 'spin 1s linear infinite'
                        : 'none',
                    }}
                  >
                    <path
                      fill="#EA4335"
                      d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                    />
                    <path
                      fill="#4285F4"
                      d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                    />
                    <path
                      fill="#34A853"
                      d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                    />
                    <path fill="none" d="M0 0h48v48H0z" />
                  </svg>
                </Box>
                <Typography
                  variant="body1"
                  component="span"
                  sx={{
                    fontSize: '1.25rem',
                    fontWeight: 500,
                    color: '#3c4043',
                  }}
                >
                  {' '}
                  {/* Scaled up font size */}
                  Sign in with Google
                </Typography>
              </Box>
            </Button>
          </Collapse>
        </Collapse>

        {/* Footer moved inside the main content Box */}
        <Box
          component="footer"
          sx={{
            textAlign: 'center',
            color: '#616161', // Explicit secondary text color for footer
            py: 4, // Scaled up padding
          }}
        >
          <Typography
            variant="caption"
            display="block"
            sx={{ fontSize: '0.9rem' }}
          >
            {' '}
            © {new Date().getFullYear()} Selio Software
          </Typography>
          <Box>
            {' '}
            {/* Scaled up margin */}
            <SelioLogo />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
