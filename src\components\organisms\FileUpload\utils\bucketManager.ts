import { getBlob, getDownloadURL, getMetadata, ref } from 'firebase/storage';

import { getStorageInstance } from '~/configs/firebaseConfig';
import { inMemoryFileManager } from '../core/InMemoryFileManager';
import { UploadedFile, UrlGenerationOptions } from '../types/fileUpload';
// Import generateFileUrl statically to avoid circular dependency warning
import { generateFileUrl } from './urlGeneration';

/**
 * Environment variable names for different buckets
 * Note: Temp bucket removed - all temporary files now use in-memory storage
 */
const BUCKET_ENV_VARS = {
  public: 'VITE_FIREBASE_STORAGE_BUCKET_CDN',
  private: 'VITE_FIREBASE_STORAGE_BUCKET_PRIVATE',
} as const;

/**
 * Gets the appropriate bucket name based on file type
 * Note: Temporary files are now handled in-memory only, no Firebase bucket needed
 */
export const getBucketForFile = (
  fileType: 'i' | 'v' | 's' | 'p',
  isTemporary: boolean
): string => {
  if (isTemporary) {
    throw new Error(
      'Temporary files are now handled in-memory only. Use createInMemoryFile() instead of uploading to temp bucket.'
    );
  }

  const bucketMap = {
    i: BUCKET_ENV_VARS.public,
    v: BUCKET_ENV_VARS.public,
    s: BUCKET_ENV_VARS.public,
    p: BUCKET_ENV_VARS.private,
  };

  const envVar = bucketMap[fileType];
  const bucket = import.meta.env[envVar];

  if (!bucket) {
    throw new Error(
      `Environment variable ${envVar} is not defined for file type '${fileType}'`
    );
  }

  return bucket;
};

/**
 * Gets Firebase Storage instance for the appropriate bucket
 * Note: Temporary files should use in-memory storage, not Firebase
 */
export const getStorageInstanceForFile = (
  fileType: 'i' | 'v' | 's' | 'p',
  isTemporary: boolean
) => {
  if (isTemporary) {
    throw new Error(
      'Temporary files should use in-memory storage. Use InMemoryFileManager instead.'
    );
  }

  const bucketName = getBucketForFile(fileType, isTemporary);
  return getStorageInstance(bucketName);
};

/**
 * Constructs the full filename with extension
 */
export const constructFullFilename = (file: UploadedFile): string => {
  return `${file.f}.${file.e}`;
};

/**
 * Synchronous URL generator for immediate use (fallback for emergency cases)
 * Note: This may not work due to CORS/ORB restrictions
 */
export const generateFileUrlSync = (file: UploadedFile): string => {
  const bucket = getBucketForFile(file.t, file.x || false);
  const fullFilename = constructFullFilename(file);

  return `https://firebasestorage.googleapis.com/v0/b/${bucket}/o/${encodeURIComponent(fullFilename)}?alt=media`;
};

/**
 * Validates that all required bucket environment variables are set
 * Note: Temp bucket no longer required as files use in-memory storage
 */
export const validateBucketConfiguration = (): void => {
  const missingVars: string[] = [];

  Object.values(BUCKET_ENV_VARS).forEach(envVar => {
    if (!import.meta.env[envVar]) {
      missingVars.push(envVar);
    }
  });

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}`
    );
  }
};

/**
 * Cache entry for private files
 */
interface CacheEntry {
  blob: Blob;
  blobUrl: string;
  generation: string;
  metageneration: string;
  cached: number;
  fileSize: number;
}

/**
 * Configuration for cache behavior by bucket type
 */
const CACHE_CONFIG = {
  privateFiles: {
    memoryLimit: 100 * 1024 * 1024, // 100MB in memory
    memoryTTL: 2 * 60 * 60 * 1000, // 2 hours
  },
  // Temp files removed - now handled entirely by InMemoryFileManager
};

/**
 * Temp File Manager
 * Delegates to InMemoryFileManager for all temporary file operations
 * Maintains backward compatibility while leveraging the comprehensive
 * blob URL limits, validation, and cleanup features of InMemoryFileManager
 */
class TempFileManager {
  async getUrl(file: UploadedFile, variant?: string): Promise<string> {
    // All temp files are now in-memory - delegate to InMemoryFileManager
    if (file.inMemoryData) {
      return await inMemoryFileManager.getUrl(file, variant);
    }

    // This should never happen in the new system - all temp files should have inMemoryData
    throw new Error(
      `Temporary file ${file.f}.${file.e} missing in-memory data. This indicates a system error.`
    );
  }

  cleanup(): void {
    // Delegate cleanup to InMemoryFileManager which has comprehensive cleanup logic
    inMemoryFileManager.forceCleanup();
  }

  revokeUrl(url: string): void {
    // For blob URLs, revoke directly
    // Note: InMemoryFileManager handles URL lifecycle internally,
    // but this method is kept for backward compatibility
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url);
    }
  }

  /**
   * Get memory usage statistics
   * Delegates to InMemoryFileManager for comprehensive memory tracking
   */
  getMemoryUsage() {
    return inMemoryFileManager.getMemoryUsage();
  }

  /**
   * Mark files as active to protect from cleanup
   * Delegates to InMemoryFileManager's active file protection
   */
  markFilesAsActive(fileIds: string[]): void {
    inMemoryFileManager.markFilesAsActive(fileIds);
  }

  /**
   * Clear active file markings
   */
  clearActiveFiles(): void {
    inMemoryFileManager.clearActiveFiles();
  }
}

/**
 * Private file cache with aggressive memory caching for immutable files
 * Handles default bucket (p) and cloud bucket (c) - files that never change
 * Uses in-memory caching only with 2-hour TTL and 100MB limit
 */
class PrivateFileCache {
  private memoryCache = new Map<string, CacheEntry>();
  private pendingFetches = new Map<string, Promise<string>>();
  private readonly MAX_MEMORY_SIZE = CACHE_CONFIG.privateFiles.memoryLimit;
  private readonly MEMORY_TTL = CACHE_CONFIG.privateFiles.memoryTTL;
  private currentMemoryUsage = 0;

  private getCacheKey(file: UploadedFile): string {
    return `${file.t}:${file.f}.${file.e}`;
  }

  private getFileRef(file: UploadedFile, context?: any) {
    const storage = getStorageInstanceForFile(file.t, false);

    if (file.t === 'p') {
      // Private files need the full path with account context
      // For now, we'll use a simple path structure since context isn't always available
      // In a full implementation, this would need proper context handling
      const filename = constructFullFilename(file);
      return ref(storage, filename);
    }

    const filename = constructFullFilename(file);
    return ref(storage, filename);
  }

  private evictLRU(): void {
    if (this.memoryCache.size === 0) return;

    // Find oldest entry
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.cached < oldestTime) {
        oldestTime = entry.cached;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.memoryCache.get(oldestKey);
      if (entry) {
        URL.revokeObjectURL(entry.blobUrl);
        this.currentMemoryUsage -= entry.fileSize;
        // Explicitly clear blob reference to help garbage collection
        (entry as any).blob = undefined;
        this.memoryCache.delete(oldestKey);
      }
    }
  }

  private async validateCache(
    file: UploadedFile,
    cached: CacheEntry
  ): Promise<boolean> {
    try {
      // Check TTL first (fast check for immutable files)
      const age = Date.now() - cached.cached;
      if (age > this.MEMORY_TTL) {
        return false;
      }

      // For immutable files in default/cloud buckets, generation-based validation is sufficient
      // Since these files never change once created, we can rely on generation matching
      const metadata = await getMetadata(this.getFileRef(file));

      // Check generation (primary validation for immutable files)
      if (metadata.generation !== cached.generation) {
        return false;
      }

      return true;
    } catch (error) {
      // If metadata check fails, invalidate cache
      return false;
    }
  }

  async getUrl(file: UploadedFile): Promise<string> {
    const cacheKey = this.getCacheKey(file);
    const cached = this.memoryCache.get(cacheKey);

    if (cached) {
      const isValid = await this.validateCache(file, cached);
      if (isValid) {
        return cached.blobUrl;
      }

      // Cache is stale, remove it
      URL.revokeObjectURL(cached.blobUrl);
      this.currentMemoryUsage -= cached.fileSize;
      this.memoryCache.delete(cacheKey);
    }

    // Check if fetch is already in progress for this file
    const pending = this.pendingFetches.get(cacheKey);
    if (pending) {
      return pending;
    }

    // Fetch fresh file and cache
    const promise = this.fetchAndCache(file, cacheKey);
    this.pendingFetches.set(cacheKey, promise);

    try {
      const url = await promise;
      return url;
    } finally {
      // Clean up pending fetch tracker
      this.pendingFetches.delete(cacheKey);
    }
  }

  private async fetchAndCache(
    file: UploadedFile,
    cacheKey: string
  ): Promise<string> {
    const fileRef = this.getFileRef(file);

    // Get both blob and metadata in parallel
    const [blob, metadata] = await Promise.all([
      getBlob(fileRef), // Respects security rules
      getMetadata(fileRef),
    ]);

    const blobUrl = URL.createObjectURL(blob);

    // Check if we need to evict entries to make room
    while (
      this.currentMemoryUsage + blob.size > this.MAX_MEMORY_SIZE &&
      this.memoryCache.size > 0
    ) {
      this.evictLRU();
    }

    // Cache the entry if it fits
    if (blob.size <= this.MAX_MEMORY_SIZE) {
      const entry: CacheEntry = {
        blob,
        blobUrl,
        generation: metadata.generation,
        metageneration: metadata.metageneration,
        cached: Date.now(),
        fileSize: blob.size,
      };

      this.memoryCache.set(cacheKey, entry);
      this.currentMemoryUsage += blob.size;
    }

    return blobUrl;
  }

  clear(): void {
    for (const entry of this.memoryCache.values()) {
      URL.revokeObjectURL(entry.blobUrl);
      // Explicitly clear blob reference
      (entry as any).blob = undefined;
    }
    this.memoryCache.clear();
    this.pendingFetches.clear();
    this.currentMemoryUsage = 0;
  }
}

/**
 * Smart secure file manager implementing bucket-specific strategies
 * Follows the revised bucket-specific approach:
 * - Default & Cloud buckets (p): Aggressive caching (immutable files)
 * - Temp bucket (x): Direct access, no caching (large, short-lived)
 * - Public buckets (i,v,s): CDN/Firebase direct (existing logic)
 */
class SecureFileManager {
  private privateCache = new PrivateFileCache();
  private tempManager = new TempFileManager();

  async getFileUrl(
    file: UploadedFile,
    options?: UrlGenerationOptions
  ): Promise<string> {
    // PUBLIC FILES: Use existing CDN/Firebase URL generation for caching
    if ((file.t === 'i' || file.t === 'v' || file.t === 's') && !file.x) {
      return generateFileUrl(file, options);
    }

    // TEMP FILES: Use blob URLs, no caching, with variant support for images
    if (file.x) {
      return this.tempManager.getUrl(file, options?.imageVariant);
    }

    // PRIVATE FILES: Use blob URLs with aggressive caching
    // These are immutable files like reports, bills, invoices - perfect for caching
    if (file.t === 'p') {
      return this.privateCache.getUrl(file);
    }

    throw new Error(
      `Unknown file type: ${file.t} with temporary flag: ${file.x}`
    );
  }

  /**
   * Clear all private file caches (call on user logout)
   */
  clearPrivateCache(): void {
    this.privateCache.clear();
  }

  /**
   * Clean up temp file blob URLs (call on component unmount)
   */
  cleanupTempFiles(): void {
    this.tempManager.cleanup();
  }

  /**
   * Revoke specific temp file URL (call when file moved from temp to permanent)
   */
  revokeTempUrl(url: string): void {
    this.tempManager.revokeUrl(url);
  }

  /**
   * Clean up specific in-memory file (call when file moved from temp to permanent)
   */
  cleanupInMemoryFile(file: UploadedFile): void {
    if (file.inMemoryData) {
      inMemoryFileManager.cleanupFile(file);
    }
  }
}

// Singleton instance
const secureFileManager = new SecureFileManager();

/**
 * Get secure file URL with proper caching and security
 */
export const getSecureFileUrl = async (
  file: UploadedFile,
  options?: UrlGenerationOptions
): Promise<string> => {
  return secureFileManager.getFileUrl(file, options);
};

/**
 * Clear private file cache (call on user logout)
 */
export const clearPrivateFileCache = (): void => {
  secureFileManager.clearPrivateCache();
};

/**
 * Clean up temp file blob URLs (call on component unmount)
 */
export const cleanupTempFiles = (): void => {
  secureFileManager.cleanupTempFiles();
};

/**
 * Revoke specific temp file URL (call when file moved from temp to permanent)
 */
export const revokeTempFileUrl = (url: string): void => {
  secureFileManager.revokeTempUrl(url);
};

/**
 * Clean up specific in-memory file (call when file moved from temp to permanent)
 */
export const cleanupInMemoryFile = (file: UploadedFile): void => {
  secureFileManager.cleanupInMemoryFile(file);
};

/**
 * Check if a file should use secure access (private buckets or temp files)
 */
export const shouldUseSecureAccess = (file: UploadedFile): boolean => {
  // Private files (p maps to both default and cloud buckets) and temp files (x) use secure access
  return file.t === 'p' || file.x === true;
};

/**
 * Check if a file is public and not temporary (should show Copy URL button)
 */
export const isPublicFile = (file: UploadedFile): boolean => {
  // Public files have type 'i', 'v', or 's' and are not temporary
  return (file.t === 'i' || file.t === 'v' || file.t === 's') && !file.x;
};
