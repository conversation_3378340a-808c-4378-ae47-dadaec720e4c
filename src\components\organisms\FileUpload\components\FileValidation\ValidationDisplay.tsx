import React, { useCallback } from 'react';
import {
  Close as CloseIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import {
  Alert,
  AlertTitle,
  Box,
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { ValidationError } from '../../types';

/**
 * Validation display component for showing validation errors and warnings
 */
interface ValidationDisplayProps {
  errors: ValidationError[];
  warnings: ValidationError[];
  display?: 'inline' | 'toast' | 'both';
  onDismissError?: (errorId: string) => void;
  collapsible?: boolean;
  maxHeight?: number;
}

export const ValidationDisplay: React.FC<ValidationDisplayProps> = ({
  errors,
  warnings,
  display = 'both',
  onDismissError,
  collapsible = true,
  maxHeight = 200,
}) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = React.useState(false);

  const allValidationIssues = [...errors, ...warnings];
  const hasErrors = errors.length > 0;
  const hasWarnings = warnings.length > 0;

  // Toggle expanded state
  const handleToggleExpanded = useCallback(() => {
    setExpanded(prev => !prev);
  }, []);

  // Handle dismiss error
  const handleDismiss = useCallback(
    (errorId: string) => {
      if (onDismissError) {
        onDismissError(errorId);
      }
    },
    [onDismissError]
  );

  // Get alert severity
  const getAlertSeverity = useCallback(() => {
    if (hasErrors) return 'error';
    if (hasWarnings) return 'warning';
    return 'info';
  }, [hasErrors, hasWarnings]);

  // Get alert title
  const getAlertTitle = useCallback(() => {
    if (hasErrors && hasWarnings) {
      return t('fileUpload.validation.errorsAndWarnings', 'Validation Issues');
    }
    if (hasErrors) {
      return t('fileUpload.validation.errors', 'Validation Errors');
    }
    if (hasWarnings) {
      return t('fileUpload.validation.warnings', 'Validation Warnings');
    }
    return '';
  }, [hasErrors, hasWarnings, t]);

  // Get summary message
  const getSummaryMessage = useCallback(() => {
    const parts: string[] = [];

    if (hasErrors) {
      parts.push(
        t('fileUpload.validation.errorCount', '{{count}} error(s)', {
          count: errors.length,
        })
      );
    }

    if (hasWarnings) {
      parts.push(
        t('fileUpload.validation.warningCount', '{{count}} warning(s)', {
          count: warnings.length,
        })
      );
    }

    return parts.join(' and ');
  }, [hasErrors, hasWarnings, errors.length, warnings.length, t]);

  if (allValidationIssues.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: 1 }}>
      <Alert
        severity={getAlertSeverity()}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {collapsible && allValidationIssues.length > 1 && (
              <IconButton
                size="small"
                onClick={handleToggleExpanded}
                sx={{ mr: 1 }}
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            )}
            {onDismissError && (
              <IconButton
                size="small"
                onClick={() => {
                  // Dismiss all errors
                  allValidationIssues.forEach(issue => handleDismiss(issue.id));
                }}
              >
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        }
      >
        <AlertTitle>{getAlertTitle()}</AlertTitle>

        {allValidationIssues.length === 1 ? (
          // Single issue - show directly
          <Typography variant="body2">
            {allValidationIssues[0].message}
          </Typography>
        ) : (
          // Multiple issues - show summary and expandable list
          <>
            <Typography variant="body2" sx={{ mb: 1 }}>
              {getSummaryMessage()}
            </Typography>

            <Collapse in={expanded || !collapsible}>
              <List
                dense
                sx={{
                  maxHeight: maxHeight,
                  overflow: 'auto',
                  '& .MuiListItem-root': {
                    py: 0.5,
                  },
                }}
              >
                {allValidationIssues.map(issue => (
                  <ListItem
                    key={issue.id}
                    secondaryAction={
                      onDismissError && (
                        <IconButton
                          size="small"
                          onClick={() => handleDismiss(issue.id)}
                        >
                          <CloseIcon fontSize="small" />
                        </IconButton>
                      )
                    }
                  >
                    <ListItemText
                      primary={issue.message}
                      secondary={issue.fileName}
                      primaryTypographyProps={{
                        variant: 'body2',
                        color: issue.severity === 'error' ? 'error' : 'warning',
                      }}
                      secondaryTypographyProps={{
                        variant: 'caption',
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Collapse>
          </>
        )}
      </Alert>
    </Box>
  );
};
