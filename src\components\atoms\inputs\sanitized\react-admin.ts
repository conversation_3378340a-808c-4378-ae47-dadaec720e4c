/**
 * Sanitized React-Admin Components
 *
 * Pre-wrapped React-Admin input components with automatic text sanitization.
 * These components work seamlessly with React-Admin forms while preventing
 * invisible character issues from copy-pasted content.
 *
 * @module sanitized/react-admin
 */

import {
    TextInput,
    AutocompleteInput,
    AutocompleteArrayInput,
    SelectInput,
    SelectArrayInput,
    PasswordInput,
} from 'react-admin';
import {
    withSanitization,
    createSanitizedComponent,
} from '~/utils/withSanitization';
import { MULTI_LINE_PRESET, IDENTIFIER_PRESET } from '~/utils/sanitizeTextInput';

/**
 * Sanitized TextInput component for React-Admin forms
 *
 * @example
 * ```tsx
 * import { SanitizedTextInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedTextInput source="name" label="Name" />
 *   <SanitizedTextInput source="email" label="Email" />
 * </SimpleForm>
 * ```
 */
export const SanitizedTextInput = withSanitization(TextInput, {
    handlers: ['onChange'],
});

/**
 * Sanitized TextInput for multiline text in React-Admin forms
 *
 * @example
 * ```tsx
 * import { SanitizedTextInputMultiline } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedTextInputMultiline
 *     source="description"
 *     label="Description"
 *     multiline
 *     rows={4}
 *   />
 * </SimpleForm>
 * ```
 */
export const SanitizedTextInputMultiline = withSanitization(TextInput, {
    ...MULTI_LINE_PRESET,
    handlers: ['onChange'],
});

/**
 * Sanitized TextInput for identifiers (uppercase) in React-Admin forms
 *
 * @example
 * ```tsx
 * import { SanitizedTextInputIdentifier } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedTextInputIdentifier source="sku" label="SKU" />
 *   <SanitizedTextInputIdentifier source="code" label="Code" />
 * </SimpleForm>
 * ```
 */
export const SanitizedTextInputIdentifier = withSanitization(TextInput, {
    ...IDENTIFIER_PRESET,
    handlers: ['onChange'],
});

/**
 * Sanitized AutocompleteInput component for React-Admin forms
 *
 * @example
 * ```tsx
 * import { SanitizedAutocompleteInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedAutocompleteInput
 *     source="categoryId"
 *     choices={categories}
 *     label="Category"
 *   />
 * </SimpleForm>
 * ```
 */
export const SanitizedAutocompleteInput = withSanitization(AutocompleteInput, {
    handlers: ['onInputChange'],
});

/**
 * Sanitized AutocompleteArrayInput component for React-Admin forms
 *
 * @example
 * ```tsx
 * import { SanitizedAutocompleteArrayInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedAutocompleteArrayInput
 *     source="tags"
 *     choices={availableTags}
 *     label="Tags"
 *   />
 * </SimpleForm>
 * ```
 */
export const SanitizedAutocompleteArrayInput = withSanitization(
    AutocompleteArrayInput,
    {
        handlers: ['onInputChange'],
    }
);

/**
 * Sanitized PasswordInput component for React-Admin forms
 * Note: Password fields are sanitized but typically don't need
 * aggressive sanitization. This is mainly for consistency.
 *
 * @example
 * ```tsx
 * import { SanitizedPasswordInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedPasswordInput source="password" label="Password" />
 * </SimpleForm>
 * ```
 */
export const SanitizedPasswordInput = withSanitization(PasswordInput, {
    handlers: ['onChange'],
});

/**
 * Re-export non-text inputs that don't need sanitization
 * These are included for convenience and consistency
 */
export { SelectInput, SelectArrayInput } from 'react-admin';
