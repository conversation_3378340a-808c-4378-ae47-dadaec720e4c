import { Fragment, useMemo } from 'react';
import { Box, Chip, Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  BulkDeleteWithConfirmButton,
  CreateButton,
  DataTable,
  FunctionField,
  InfiniteList,
  ReferenceArrayField,
  ReferenceField,
  TextField,
  TopToolbar,
  useRecordContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { ValueFieldWithError } from '~/components/atoms/ValueFieldWithError';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import { CustomEmpty } from '~/components/organisms/CustomEmpty';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import {
  RESOURCES,
  resourcesInfo,
  useGetListPermissionsLive,
} from '~/providers/resources';
import { getValueFromRecord } from '~/utils/getValueFromRecord';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { useTheme } from '../../contexts';

const PostListActions = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { t } = useTranslation('');

  return (
    <TopToolbar>
      <CreateButton
        variant="contained"
        label={t('members.addTeamMember')}
        {...(isXSmall ? {} : { icon: <></> })}
      />
    </TopToolbar>
  );
};

const AssetBulkActionButtons = (props: any) => (
  <Fragment>
    <BulkDeleteWithConfirmButton {...props} />
  </Fragment>
);

export const MemberList = () => {
  const { theme } = useTheme();
  const { t } = useTranslation('');
  const record = useRecordContext();

  const { data: permissions } = useGetListPermissionsLive();
  const permissionIdToName = useMemo(() => {
    if (!permissions) return {};
    return permissions.reduce((acc: any, permission: any) => {
      acc[permission.id] = permission.name;
      return acc;
    }, {});
  }, [permissions]);

  const filters = useMemo(
    () => [
      <CustomSearchInput
        placeholder={t('members.filterTeamMembers')}
        key="search-input"
        source="q"
        alwaysOn
      />,
    ],
    [t]
  );
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  return (
    <InfiniteList
      resource={RESOURCES.TEAM_MEMBERS}
      component="div"
      sort={resourcesInfo[RESOURCES.TEAM_MEMBERS].defaultSort}
      filters={filters}
      actions={<PostListActions />}
      empty={<CustomEmpty />}
      perPage={25}
      sx={{
        '& .RaFilterFormInput-spacer': {
          display: { xs: 'none', md: 'block' },
        },
        '& .RaDataTable-headerCell': {
          backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
          borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
        },
      }}
    >
      {isXSmall ? (
        <MobileGrid>
          <MobileCard titleSource="displayName" cardClick="show" actions={true}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('shared.location_few')}
              </Typography>
              <FunctionField
                render={record => {
                  if (record?.sellPointIds?.length > 0) {
                    return (
                      <ReferenceArrayField
                        reference={RESOURCES.LOCATIONS}
                        filter={{ _d: false }}
                        source="sellPointIds"
                      />
                    );
                  }
                  return <Chip label="All" />;
                }}
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('members.role')}
              </Typography>
              <ReferenceField
                label={t('members.role')}
                source="roleId"
                reference={RESOURCES.PERMISSIONS}
                textAlign="right"
              >
                <TextField source="name" />
              </ReferenceField>
            </Box>

            <TextField
              source="status"
              textAlign="right"
              label="Live Status"
            ></TextField>
          </MobileCard>
        </MobileGrid>
      ) : (
        <DataTable
          rowClick="show"
          sx={{
            '& .RaBulkActionsToolbar-topToolbar': {
              backgroundColor: 'transparent',
            },
            '& .RaDataTable-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
            marginTop: '10px',
            '& .RaDatagrid-cell': {
              textAlign: 'right',
            },
          }}
          bulkActionButtons={<AssetBulkActionButtons />}
        >
          <DataTable.Col
            source="displayName"
            label={t('members.displayName')}
          />
          <DataTable.Col label={t('shared.location_few')}>
            <FunctionField
              render={record => {
                if (record?.sellPointIds?.length > 0) {
                  return (
                    <ReferenceArrayField
                      reference={RESOURCES.LOCATIONS}
                      filter={{ _d: false }}
                      source="sellPointIds"
                    />
                  );
                }
                return <Chip label="All" />;
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('members.role')} align="right">
            <FunctionField
              textAlign="right"
              render={record => {
                const permission = getValueFromRecord(
                  permissionIdToName,
                  record.roleId
                );
                return (
                  <ValueFieldWithError
                    value={permission}
                    errorMessage={t('members.missingRole')}
                  />
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('prepStations.actions')} align="right">
            <ActionsField
              hasEdit={true}
              hasDelete={true}
              deleteMutationMode="pessimistic"
              recordNameField="displayName"
            />
          </DataTable.Col>
        </DataTable>
      )}
      <ListLiveUpdate />
    </InfiniteList>
  );
};
