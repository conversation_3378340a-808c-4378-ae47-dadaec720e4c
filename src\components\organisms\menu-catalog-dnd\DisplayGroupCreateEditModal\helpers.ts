import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';

import { generateFirestoreId } from '~/utils/generateFirestoreId';
import { Coordinates } from '../types';

// Type for the optional conflict context
interface ConflictContext {
  trackChange: (path: string, value: any) => void;
}

/**
 * Initialize temporary form fields for create mode
 */
export const initializeTempFields = (
  setValue: UseFormSetValue<any>,
  clearErrors: (name?: string | string[]) => void,
  tempPrefix: string,
  initialData: Record<string, any>
) => {
  setValue(tempPrefix, initialData, { shouldDirty: false });
  clearErrors(tempPrefix);
};

/**
 * Cleanup temporary form fields
 */
export const cleanupTempFields = (
  resetField: (name: string) => void,
  tempPrefix: string
) => {
  resetField(tempPrefix);
};

/**
 * Sort items: display groups first, then by display name
 */
export const sortItemsByTypeAndName = (items: any[]) => {
  return items.sort((a, b) => {
    const typeA = a.type ?? 'product';
    const typeB = b.type ?? 'product';

    // Display groups should come first
    if (typeA === 'displayGroup' && typeB !== 'displayGroup') return -1;
    if (typeA !== 'displayGroup' && typeB === 'displayGroup') return 1;

    // Then sort by display name
    return a.displayName.localeCompare(b.displayName);
  });
};

/**
 * Remove position property from items (used when inside display groups)
 */
export const removePositionFromItems = (items: any[]) => {
  return items.map(item => {
    const { position, ...rest } = item;
    return rest;
  });
};

/**
 * Create a new display group item
 */
export const createDisplayGroupItem = (
  tempFieldData: any,
  path: string,
  position?: Coordinates
) => {
  const id = generateFirestoreId();
  const type = 'displayGroup';

  return {
    ...tempFieldData,
    type,
    items: [],
    position: path.includes('items')
      ? { startX: 0, startY: 0, endX: 1, endY: 1 }
      : position,
    id,
  };
};

/**
 * Handle adding a new item to the data array
 * Includes sorting logic if inside a display group
 */
export const addItemToData = (
  data: any[],
  newItem: any,
  path: string
): any[] => {
  let tmp = [...data];
  tmp.push(newItem);

  // If we are inside display group, we need to arrange items
  // first by type (displayGroup first) and then by display name
  if (path.includes('items')) {
    tmp = sortItemsByTypeAndName(tmp);
    // Remove position if we're inside display group
    tmp = removePositionFromItems(tmp);
  }

  return tmp;
};

/**
 * Complete the create operation: validate, create item, add to data, cleanup
 */
export const handleCreateDisplayGroup = async (
  tempPrefix: string,
  path: string,
  data: any[],
  position: Coordinates | undefined,
  trigger: (name: string) => Promise<boolean>,
  getValues: UseFormGetValues<any>,
  setValue: UseFormSetValue<any>,
  conflictCtx?: ConflictContext | null
): Promise<boolean> => {
  // Trigger validation for the displayName field
  const isValid = await trigger(`${tempPrefix}.displayName`);

  if (!isValid) {
    return false;
  }

  // Get data from temp fields
  const tempFieldData = getValues(tempPrefix);

  // Create new item
  const newItem = createDisplayGroupItem(tempFieldData, path, position);

  // Add to data with sorting
  const updatedData = addItemToData(data, newItem, path);

  // Update form
  setValue(path, updatedData, { shouldDirty: true });
  // Track change for conflict detection
  conflictCtx?.trackChange(path, updatedData);

  // Cleanup temp fields
  setValue(tempPrefix, undefined, { shouldDirty: false });

  return true;
};
