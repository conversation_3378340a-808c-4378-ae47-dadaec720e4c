import { useState } from 'react';
import { Box, Button, Grid, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';

export default function WhatsNewBanner() {
  const [show, setShow] = useState(true);
  const { theme } = useTheme();

  return (
    <>
      {show && (
        <Grid
          container
          mt={3}
          sx={{
            width: '100$',
            borderRadius: '6px',
            padding: 2,
            bgcolor:
              theme.palette.mode == 'light'
                ? 'transparent'
                : 'background.tinted',
            boxShadow: '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)',
          }}
        >
          <Grid
            size={{
              xs: 12,
              md: 8
            }}>
            <Typography variant="body1" fontWeight={600}>
              What’s new in our shop?
            </Typography>
            <Typography variant="body2" color="custom.gray600">
              Manage contactless payments, online sales, pickup and delivery—all
              with one POS.
            </Typography>
            <Box mt={2} sx={{ display: { xs: 'none', md: 'flex' }, gap: 2 }}>
              <Button variant="contained">Buy now</Button>
              <Button onClick={() => setShow(false)}>Dismiss</Button>
            </Box>
          </Grid>
          <Grid
            sx={{ display: 'flex', justifyContent: 'center' }}
            size={{
              xs: 12,
              md: 4
            }}>
            <img src="/assets/pos.png" alt="app-img" width={'100px'} />
          </Grid>
          <Box
            mt={2}
            sx={{ display: { xs: 'flex', md: 'none' }, gap: 2, width: '100%' }}
          >
            <Button variant="contained" sx={{ flex: 1 }}>
              Buy now
            </Button>
            <Button sx={{ flex: 1 }} onClick={() => setShow(false)}>
              Dismiss
            </Button>
          </Box>
        </Grid>
      )}
    </>
  );
}
