import { useCallback, useEffect, useState } from 'react';
import { Box, Button } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useGetList,
  useRecordContext,
  useRedirect,
  useSaveContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import CloseWithConfirmationModal from '~/components/organisms/CloseWithConfirmationModal';
import FloorPlanTabSelector from '~/components/organisms/table-selection/FloorPlanTabSelector';
import { RESOURCES, useGetListTeamMembers } from '~/providers/resources';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';

function TeamMemberWorkingAreaEditInner({
  sellPointId,
}: {
  sellPointId: string;
}) {
  const { t } = useTranslation('');
  const redirect = useRedirect();
  const record = useRecordContext();
  const { setValue, watch, reset, clearErrors, trigger, formState } =
    useFormContext();

  // Fetch team members and working areas for selection mode
  const { data: teamMembers } = useGetListTeamMembers();
  const { data: existingWorkingAreas } = useGetList(
    RESOURCES.TEAM_MEMBERS_WORKING_AREA,
    {
      filter: { sellPointId },
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
      meta: { sellPointId },
    }
  );

  // Track whether we're in a "clean" state (values match original)
  const [isCleanState, setIsCleanState] = useState(false);

  // State for discard changes modal
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Effect to monitor form dirty state and override when needed
  useEffect(() => {
    if (isCleanState && formState.isDirty) {
      // Force the form to be clean when we know values match original
      const currentValues = watch();
      reset(currentValues, {
        keepValues: true,
        keepDefaultValues: false,
        keepDirtyValues: false,
      });
    }
  }, [isCleanState, formState.isDirty, reset, watch]);

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.TEAM_MEMBERS_WORKING_AREA);
  }, [redirect]);

  const handleCloseWithConfirmation = useCallback(() => {
    // Check if form has changes that haven't been saved
    const hasUnsavedChanges = formState.isDirty && !isCleanState;

    if (hasUnsavedChanges) {
      setShowConfirmModal(true);
    } else {
      handleClose();
    }
  }, [formState.isDirty, isCleanState, handleClose]);

  const handleDiscard = useCallback(() => {
    setShowConfirmModal(false);
    handleClose();
  }, [handleClose]);

  const handleAreaItemsChange = useCallback(
    (areaItems: any[]) => {
      const originalAreaItems = record?.areaItems || [];

      // Helper function to compare two arrays of area items
      const areEqual = (items1: any[], items2: any[]) => {
        if (items1.length !== items2.length) return false;

        const sorted1 = [...items1].sort((a, b) =>
          `${a.floorPlanId}-${a.itemNumber}-${a.itemTag || ''}`.localeCompare(
            `${b.floorPlanId}-${b.itemNumber}-${b.itemTag || ''}`
          )
        );
        const sorted2 = [...items2].sort((a, b) =>
          `${a.floorPlanId}-${a.itemNumber}-${a.itemTag || ''}`.localeCompare(
            `${b.floorPlanId}-${b.itemNumber}-${b.itemTag || ''}`
          )
        );

        return sorted1.every((item1, index) => {
          const item2 = sorted2[index];
          return (
            item1.floorPlanId === item2.floorPlanId &&
            item1.itemNumber === item2.itemNumber &&
            (item1.itemTag || '') === (item2.itemTag || '')
          );
        });
      };

      const hasChanged = !areEqual(areaItems, originalAreaItems);

      if (hasChanged) {
        setIsCleanState(false);
        setValue('areaItems', areaItems, {
          shouldDirty: true,
          shouldValidate: true,
        });
      } else {
        setIsCleanState(true);
        setValue('areaItems', areaItems, {
          shouldDirty: false,
          shouldValidate: true,
        });
      }
    },
    [setValue, reset, record, setIsCleanState]
  );

  const currentAreaItems = watch('areaItems') || [];

  return (
    <>
      <ModalHeader
        handleClose={handleCloseWithConfirmation}
        title={t('workingArea.editWorkingArea')}
      >
        <SaveButton type="button" icon={<></>} label={t('shared.save')} />
      </ModalHeader>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '1200px',
          width: '95%',
          mx: 'auto',
          my: 4,
          gap: 4,
        }}
      >
        <ReferenceInput source="id" reference={RESOURCES.TEAM_MEMBERS}>
          <CustomInput
            type="select"
            label={t('workingArea.member')}
            optionText="displayName"
            optionValue="id"
            validate={[required()]}
            readOnly
          />
        </ReferenceInput>
        <FloorPlanTabSelector
          sellPointId={sellPointId}
          value={currentAreaItems}
          onChange={handleAreaItemsChange}
          excludeAssignedTables={true}
          excludeMemberId={record?.id as string} // Exclude current member's assignments from filtering
          title={t('workingArea.selectTablesFromFloorPlans')}
          description={t('workingArea.selectTablesFromFloorPlansDescription')}
          teamMembers={teamMembers}
          existingWorkingAreas={existingWorkingAreas}
        />
      </Box>

      {/* Confirmation Modal */}
      <CloseWithConfirmationModal
        open={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onDiscard={handleDiscard}
        saveButton={
          <SaveButton
            type="button"
            icon={<></>}
            label={t('shared.save')}
            onClick={() => setShowConfirmModal(false)}
          />
        }
        title={t('shared.unsavedChanges', 'Unsaved Changes')}
        message={t(
          'shared.unsavedChangesMessage',
          'You have unsaved changes. Would you like to save them?'
        )}
        btnDiscardText={t('shared.discardChanges', 'Discard Changes')}
      />
    </>
  );
}

export const TeamMemberWorkingAreaEdit = (props: { sellPointId: string }) => {
  const { sellPointId } = props;

  return (
    <EditDialog
      {...getFullscreenModalProps()}
      mutationMode="pessimistic"
      mutationOptions={{
        meta: {
          sellPointId: sellPointId,
        },
      }}
      queryOptions={{ meta: { sellPointId: sellPointId } }}
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <TeamMemberWorkingAreaEditInner sellPointId={sellPointId} />
      </SimpleForm>
    </EditDialog>
  );
};
