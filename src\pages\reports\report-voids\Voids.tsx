import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box, capitalize, useMediaQuery } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV, useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { FieldOption } from '~/components/organisms/CustomTable/types/globals';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import {
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import cleanStringArond from '~/utils/cleanStringArond';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import VoidsGraph from './components/VoidsGraph';
import VoidsTable from './components/VoidsTable';

const REPORT_TYPE = 'voids';
const DEFAULT_FIELDS = [
  { isChecked: true, value: 'reason' },
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'groupId' },
  { isChecked: true, value: 'measureUnit' },
  { isChecked: true, value: 'type' },
  { isChecked: false, value: 'prepStation' },
  { isChecked: true, value: 'quantity' },
  { isChecked: true, value: 'price' },
  { isChecked: true, value: 'variant' },
  { isChecked: true, value: 'id' },
  { isChecked: true, value: 'value' },
];

export default function Voids() {
  const { details: fbDetails } = useFirebase();
  const { t } = useTranslation();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [rawData, setRawData] = useState<any>();
  const [currency, setCurrency] = useState<string>('RON');
  const [tableFields, setTableFields] = useState<FieldOption[]>(DEFAULT_FIELDS);
  const [groupingItems, setGroupingItems] = useState<string[]>([
    'reason',
    'type',
  ]);
  const [enrichedVoidsData, setEnrichedVoidsData] = useState<any[]>([]);
  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();
  // TODO! change to useGetListLive after implementation
  const { data: modifiersLibrary } = useGetList('modifiers');
  const { data: itemsLibrary } = useGetListHospitalityItemsLive();
  const { data: categories } = useGetListHospitalityCategoriesLive();

  const updateCommonField = (key: string, value: OptionType[]) =>
    setCommonFields(prevState => ({ ...prevState, [key]: value }));

  const contentRef = useRef<HTMLDivElement>(null);

  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;
      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });
        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });
        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        });
        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });
        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }
    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const { hierarchicalVoidsData, graphData } = useMemo(() => {
    if (!rawData || !filters)
      return { hierarchicalVoidsData: [], graphData: {} };

    if (rawData.length) {
      setCurrency(rawData[0].currency);
    }

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const highestValueProductIds = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      ['id']
    )[0]
      ?.report.sort((a, b) => (b.value ?? 0) - (a.value ?? 0))
      .slice(0, 5)
      ?.map(el => el.id);

    const filteredByIds = filterReport(REPORT_TYPE, rawData, composedFilters, [
      {
        field: 'id',
        operator: 'in',
        value: highestValueProductIds,
      },
    ]);

    const groupedByHour = groupReport(
      REPORT_TYPE,
      filteredByIds,
      ['hourOfDay'],
      ['id', 'type']
    );

    const labels = groupedByHour?.map(el => el.hourOfDay.toString());

    const datasets: { label: string; data: number[]; type?: string }[] =
      highestValueProductIds?.map(id => {
        const record = groupedByHour[0].report.find(r => r.id === id);

        return {
          label: id,
          data: [],
          type: record?.type || 'item',
        };
      });

    groupedByHour.forEach(({ report: items }) => {
      datasets.forEach(el => {
        const item = items.find(i => i.id === el.label);
        const itemsValue = item?.value || 0;
        const formattedValue = Math.round((itemsValue / 10000) * 10) / 10;
        el.data.push(formattedValue);
      });
    });

    const graphData = {
      datasets: datasets?.map(el => ({
        ...el,
        label: el.label,
        type: el.type,
      })),
      labels,
    };

    const filteredFields = tableFields.filter((field: any) => {
      return (
        field.isChecked &&
        reportSpecificFields.voids.some(
          discountField =>
            cleanStringArond(field.value) === cleanStringArond(discountField)
        )
      );
    });

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      filteredFields.map(item => item.value)
    );

    const hierarchicalVoidsData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        groupedTableData,
        groupingItems as []
      )[0]?.report || [];

    return { hierarchicalVoidsData, graphData: graphData };
  }, [filters, rawData, groupingItems, tableFields]);

  const fetchEnrichedVoidsData = useCallback(() => {
    if (!hierarchicalVoidsData) return;

    const abortController = new AbortController();

    const collectGroupIds = (items: any[]): Set<string> => {
      const groupIds = new Set<string>();
      items.forEach(item => {
        if (item.groupId) {
          groupIds.add(item.groupId);
        }
        if (item.subReport && item.subReport.length > 0) {
          const childIds = collectGroupIds(item.subReport);
          childIds.forEach(id => groupIds.add(id));
        }
      });
      return groupIds;
    };

    const groupIds = Array.from(collectGroupIds(hierarchicalVoidsData));

    const groupNameMap: Record<string, string> = {};
    groupIds.map(groupId => {
      try {
        const categoryFound = categories?.find(
          category => category.id === groupId
        );
        groupNameMap[groupId] = categoryFound?.name;
      } catch (error) {
        console.error(`Failed to fetch category with id: ${groupId}`, error);
        groupNameMap[groupId] = groupId;
      }
    });

    const enrichItems = (items: any[]): any[] => {
      return items.map(item => {
        let enrichedSubReport;
        if (item.subReport && item.subReport.length > 0) {
          enrichedSubReport = enrichItems(item.subReport);
        }
        let enrichedItem;

        if (!item.subReport || item.subReport.length === 0) {
          try {
            let response;
            if (item.type === 'item') {
              response = itemsLibrary?.find(
                itemLibrary => itemLibrary.id === item.id
              );
            } else {
              response = modifiersLibrary?.find(
                modifierLibrary => modifierLibrary.id === item.id
              );
            }
            enrichedItem = { ...item, name: response?.name };
          } catch (error) {
            console.error(`Failed to fetch record with id: ${item.id}`, error);
            enrichedItem = { ...item, name: item.id };
          }
        } else {
          enrichedItem = { ...item };
        }

        return {
          ...enrichedItem,
          groupName: groupNameMap[item.groupId] || item.groupId,
          subReport: enrichedSubReport,
        };
      });
    };

    try {
      const enrichedData = enrichItems(hierarchicalVoidsData);
      setEnrichedVoidsData(enrichedData);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching enriched voids data:', error);
      }
    }
    return () => abortController.abort();
  }, [hierarchicalVoidsData, itemsLibrary, modifiersLibrary, categories]);

  useEffect(() => {
    fetchEnrichedVoidsData();
  }, [fetchEnrichedVoidsData]);

  const sortDataRecursively = (items: any[]): any[] => {
    if (!items || items.length === 0) return [];

    return items
      .map(item => ({
        ...item,
        subReport: item.subReport ? sortDataRecursively(item.subReport) : [],
      }))
      .sort((a, b) => {
        if (groupingItems.length > 0) {
          const quantityA = a.quantity ?? 0;
          const quantityB = b.quantity ?? 0;
          if (quantityA !== quantityB) return quantityB - quantityA;
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const finalTableData = useMemo(() => {
    const sortedData = sortData(enrichedVoidsData);
    return remapReports(sortedData, 'id');
  }, [enrichedVoidsData]);

  const onChangeGrouping = (items: string[]) => {
    setGroupingItems(items);
  };

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('md'));

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('voids.title')}
        description={
          <>
            {t('voids.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={<ExportMenuButton contentRef={contentRef} printOnly={true} />}
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />
      <ReportDateTitle />
      {isSmall ? null : (
        <VoidsGraph data={graphData} currency={currency as 'RON' | 'USD'} />
      )}
      <Box sx={{ pb: 3 }}>
        <VoidsTable
          filters={filters}
          fields={tableFields}
          setFields={setTableFields}
          tableData={finalTableData || []}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
        />
      </Box>
    </Box>
  );
}
