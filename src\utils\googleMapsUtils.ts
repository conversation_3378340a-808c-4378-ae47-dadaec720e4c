import { setOptions, importLibrary } from '@googlemaps/js-api-loader';

// Google Maps API configuration
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_FIREBASE_API_KEY || '';

// Set Google Maps options once
setOptions({
    key: GOOGLE_MAPS_API_KEY,
    v: 'weekly',
});

/**
 * Initialize Google Maps API
 * @returns Promise that resolves when Google Maps is loaded
 */
export const initializeGoogleMaps = async (): Promise<any> => {
    try {
        // Load the core maps library
        await importLibrary('maps');
        // Load additional libraries
        await Promise.all([
            importLibrary('places'),
            importLibrary('geometry'),
            importLibrary('marker')
        ]);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return (window as any).google.maps;
    } catch (error) {
        console.error('Error loading Google Maps:', error);
        throw new Error('Failed to load Google Maps');
    }
};

/**
 * Validate coordinates
 * @param lat - Latitude
 * @param lng - Longitude
 * @returns true if coordinates are valid
 */
export const isValidCoordinates = (lat: number, lng: number): boolean => {
    return (
        typeof lat === 'number' &&
        typeof lng === 'number' &&
        lat >= -90 &&
        lat <= 90 &&
        lng >= -180 &&
        lng <= 180 &&
        !isNaN(lat) &&
        !isNaN(lng)
    );
};

/**
 * Calculate distance between two points using Haversine formula
 * @param lat1 - First point latitude
 * @param lng1 - First point longitude
 * @param lat2 - Second point latitude
 * @param lng2 - Second point longitude
 * @returns Distance in meters
 */
export const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const a =
        Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
};

/**
 * Format coordinates for display
 * @param lat - Latitude
 * @param lng - Longitude
 * @param precision - Number of decimal places (default: 6)
 * @returns Formatted coordinate string
 */
export const formatCoordinates = (lat: number, lng: number, precision: number = 6): string => {
    return `${lat.toFixed(precision)}, ${lng.toFixed(precision)}`;
};

/**
 * Parse coordinate string to lat/lng object
 * @param coordString - Coordinate string in format "lat, lng"
 * @returns Object with lat and lng properties or null if invalid
 */
export const parseCoordinates = (coordString: string): { lat: number; lng: number } | null => {
    const parts = coordString.split(',').map(part => part.trim());
    if (parts.length !== 2) return null;

    const lat = parseFloat(parts[0]);
    const lng = parseFloat(parts[1]);

    if (isValidCoordinates(lat, lng)) {
        return { lat, lng };
    }

    return null;
};

/**
 * Get user's current location
 * @returns Promise that resolves with user's coordinates
 */
export const getCurrentLocation = (): Promise<{ lat: number; lng: number }> => {
    return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
            reject(new Error('Geolocation is not supported by this browser'));
            return;
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                resolve({
                    lat: position.coords.latitude,
                    lng: position.coords.longitude,
                });
            },
            (error) => {
                reject(new Error(`Geolocation error: ${error.message}`));
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000, // 5 minutes
            }
        );
    });
};

/**
 * Create a bounds object from center and radius
 * @param center - Center coordinates
 * @param radiusInMeters - Radius in meters
 * @param maps - Google Maps API object
 * @returns Google Maps LatLngBounds object
 */
export const createBoundsFromRadius = (
    center: { lat: number; lng: number },
    radiusInMeters: number,
    maps: any
): any => {
    const bounds = new maps.LatLngBounds();

    // Calculate approximate offset in degrees
    const lat = center.lat;
    const lng = center.lng;

    // 1 degree latitude ≈ 111,320 meters
    const latOffset = radiusInMeters / 111320;

    // 1 degree longitude varies by latitude
    const lngOffset = radiusInMeters / (111320 * Math.cos((lat * Math.PI) / 180));

    // Extend bounds to include all points within the radius
    bounds.extend(new maps.LatLng(lat + latOffset, lng + lngOffset));
    bounds.extend(new maps.LatLng(lat + latOffset, lng - lngOffset));
    bounds.extend(new maps.LatLng(lat - latOffset, lng + lngOffset));
    bounds.extend(new maps.LatLng(lat - latOffset, lng - lngOffset));

    return bounds;
};

/**
 * Validate ordering range
 * @param range - Range in meters
 * @returns true if range is valid
 */
export const isValidOrderingRange = (range: number): boolean => {
    return typeof range === 'number' && range > 0 && range <= 1000 && !isNaN(range); // Max 1km
};
