import { useState } from 'react';
import { CssBaseline } from '@mui/material';
import { AppLocationContext } from '@react-admin/ra-navigation';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { CheckForApplicationUpdate, Layout, LayoutProps } from 'react-admin';

import useEnv from '~/hooks/useEnv';
import ContainerWithCustomMenu from '../../molecules/ContainerWithCustomMenu';
import CustomAppBar from './CustomAppBar';
import { CustomNotificationDialog } from './CustomNotificationDialog';
import CustomSidebar from './CustomSidebar';

const CHECK_INTERVAL: number = 10 * 60 * 1000;

export default function CustomLayout(props: LayoutProps) {
  const { NODE_ENV } = useEnv();
  const { children } = props;

  const [isOpen, setIsOpen] = useState(false);

  return (
    <AppLocationContext>
      <Layout
        {...props}
        appBar={CustomAppBar}
        sidebar={CustomSidebar}
        sx={{
          // Override react-admin's layout to allow natural document scrolling
          // This enables mobile browser chrome (address bar) to hide/show on scroll

          // Root Layout container - remove height constraint and overflow
          height: 'auto',
          minHeight: '100vh',
          overflow: 'visible',

          '& .RaLayout-content': {
            width: '100%',
            maxWidth: '100%',
            overflow: 'visible',
            height: 'auto',
          },
          '& .RaLayout-contentWithSidebar': {
            overflow: 'visible',
            height: 'auto',
          },
          '& .RaLayout-appFrame': {
            overflow: 'visible',
            height: 'auto',
            minHeight: 'calc(100vh - 60px)',
          },
          '& #main-content': {
            overflow: 'visible',
            height: 'auto',
          },
        }}
      >
        <CssBaseline />
        <ContainerWithCustomMenu>{children}</ContainerWithCustomMenu>
        <CheckForApplicationUpdate
          interval={CHECK_INTERVAL}
          disabled={NODE_ENV === 'dev'}
          fetchOptions={{ cache: 'no-cache' }}
          onNewVersionAvailable={() => setIsOpen(true)}
        />
        <CustomNotificationDialog isOpen={isOpen} />
        {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
      </Layout>
    </AppLocationContext>
  );
}
