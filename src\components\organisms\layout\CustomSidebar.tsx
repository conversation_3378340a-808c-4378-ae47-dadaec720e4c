import { useEffect, useRef } from 'react';
import { Box, Drawer, Theme, useMediaQuery } from '@mui/material';
import {
  SidebarClasses,
  SidebarProps,
  useLocale,
  useSidebarState,
} from 'react-admin';
import { useLocation } from 'react-router-dom';

import { RESOURCES } from '~/providers/resources';
import MainMenu from '../menus/MainMenu';

export default function CustomSidebar(_props: SidebarProps) {
  // Don't pass react-admin props to DOM elements - we handle everything ourselves
  const location = useLocation();
  const [open, setOpen] = useSidebarState();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  useLocale(); // force redraw on locale change

  const hasSecondaryMenu = !['/', '/shifts', '/payroll'].includes(
    location.pathname
  );

  const prevHasSecondaryMenu = useRef(hasSecondaryMenu);
  const prevPathname = useRef(location.pathname);

  // Only show backdrop if we didn't just navigate from a page without secondary menu
  const showBackdrop =
    hasSecondaryMenu &&
    !(prevHasSecondaryMenu.current === false && hasSecondaryMenu === true);

  useEffect(() => {
    if (hasSecondaryMenu) {
      setOpen(false);
    } else if (!isXSmall) {
      setOpen(true);
    }

    // Update refs after a small delay to ensure the closing animation doesn't show backdrop
    const timer = setTimeout(() => {
      prevHasSecondaryMenu.current = hasSecondaryMenu;
      prevPathname.current = location.pathname;
    }, 500);

    return () => clearTimeout(timer);
  }, [hasSecondaryMenu, location.pathname]);

  const toggleSidebar = () => setOpen(!open);

  return (
    <Drawer
      variant={isXSmall ? 'temporary' : 'permanent'}
      open={open}
      onClose={toggleSidebar}
      classes={SidebarClasses}
      sx={{
        zIndex: (showBackdrop && open) || (open && isXSmall) ? 3 : 0,
        transition:
          showBackdrop && open ? 'z-index 0s ease' : 'z-index 0s 0.4s ease',
        '& .RaSidebar-fixed': {},
        '& .MuiDrawer-paper': {
          // Mobile (xs/sm): temporary drawer handles its own positioning
          // Desktop WITHOUT secondary menu (dashboard): sticky = pushes content but stays in place when scrolling
          // Desktop WITH secondary menu: fixed = overlays content
          ...(!isXSmall && {
            position: hasSecondaryMenu ? 'fixed' : 'sticky',
            top: 60,
            left: hasSecondaryMenu ? 0 : 'auto',
            height: 'calc(100dvh - 60px)',
            alignSelf: 'flex-start', // Required for sticky to work properly in flex container
          }),
          width: isXSmall ? '100vw' : open ? 300 : 0,
          maxWidth: isXSmall ? '100vw' : 'none',
          // Smooth transition for width changes during navigation
          transition: theme =>
            theme.transitions.create(['width', 'opacity'], {
              easing: theme.transitions.easing.easeOut,
              duration: theme.transitions.duration.enteringScreen,
            }),
          backgroundColor: isXSmall
            ? theme => theme.palette.background.default
            : 'transparent',
          borderRight: 'none',
          overflowX: 'hidden',
          // Mobile specific styles
          ...(isXSmall && {
            marginTop: 0,
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 1200,
          }),
        },
      }}
    >
      <>
        <Box
          sx={{
            display: 'flex',
            height: '100%',
            width: '100%',
          }}
        >
          {/* backdrop for main menu */}
          <Box
            onClick={() => setOpen(false)}
            sx={{
              position: 'fixed',
              width: '100vw',
              height: '100%',
              background: showBackdrop && open ? '#00000066' : '#00000000',
              zIndex: showBackdrop && open ? 5 : -1,
              transition:
                showBackdrop && open
                  ? 'background 0.4s ease'
                  : 'background 0.4s ease, z-index 0s 0.4s',
            }}
          />
          <Box
            sx={{
              bgcolor: 'background.default',
              zIndex: 6,
              height: '100%',
              width: '100%',
            }}
          >
            {/* main menu */}
            <Box
              sx={{
                position:
                  hasSecondaryMenu && isXSmall ? 'absolute' : 'relative',
                zIndex: 6,
                bgcolor: 'background.default',
                height: '100%',
                width: '100%',
              }}
            >
              <MainMenu />
            </Box>
          </Box>
        </Box>
      </>
    </Drawer>
  );
}
