import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box, LinearProgress, useMediaQuery } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { FieldOption } from '~/components/organisms/CustomTable/types/globals';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useReportRawData } from '~/hooks/useReportRawData'; // Import the hook
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import {
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import cleanStringArond from '~/utils/cleanStringArond';
import { CurrencyType } from '~/utils/formatNumber';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import ItemSalesGraph from './components/ItemSalesGraph';
import ItemSalesTable from './components/ItemSalesTable';

const REPORT_TYPE = 'items';
const DEFAULT_FIELDS = [
  { isChecked: true, value: 'groupId' },
  { isChecked: true, value: 'id' },
  { isChecked: true, value: 'measureUnit' },
  { isChecked: true, value: 'quantity' },
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'couponsValue' },
  { isChecked: true, value: 'discountsValue' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'prepStation' },
  { isChecked: true, value: 'netValue' },
  { isChecked: true, value: 'value' },
];

export default function ItemSales() {
  const { t } = useTranslation();
  // const { details: fbDetails } = useFirebase(); // No longer needed
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  // Use the useReportRawData hook
  const {
    data: rawData,
    isLoading,
    error,
    isInitialStorageFetchDone,
  } = useReportRawData(REPORT_TYPE);

  // Add isProcessingData state
  const [isProcessingData, setIsProcessingData] = useState(false);

  const [filters, setFilters] = useState<ReportFiltersState>();
  const [currency, setCurrency] = useState<CurrencyType>();
  const [selectedFields, setSelectedFields] =
    useState<FieldOption[]>(DEFAULT_FIELDS);
  const [selectedGrouping, setSelectedGrouping] = useState<string[]>([]);
  const [enrichedItemSalesData, setEnrichedItemSalesData] = useState<any[]>([]);

  const { data: itemsLibrary } = useGetListHospitalityItemsLive();
  const itemsIdToName = useMemo(() => {
    if (!itemsLibrary) return {};
    return itemsLibrary.reduce((acc: any, item: any) => {
      acc[item.id] = item.name;
      return acc;
    }, {});
  }, [itemsLibrary]);

  const { data: categories } = useGetListHospitalityCategoriesLive();
  const categoriesIdToName = useMemo(() => {
    if (!categories) return {};
    return categories.reduce((acc: any, category: any) => {
      acc[category.id] = category.name;
      return acc;
    }, {});
  }, [categories]);

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = useCallback((key: string, value: OptionType[]) => {
    setCommonFields(prev => ({ ...prev, [key]: value }));
  }, []);

  const contentRef = useRef<HTMLDivElement>(null);

  const defaultFilterValues = useMemo(
    () => ({
      dateRange,
      sellpointId: sellPointId,
      timeRange: {
        allDay: !timeRange,
        start: timeRange?.[0],
        end: timeRange?.[1],
      },
      diningOption: DiningOption.ALL,
      source: SourceOption.ALL,
    }),
    [dateRange, sellPointId, timeRange]
  );

  // Set filters with default values
  useEffect(() => {
    setFilters(defaultFilterValues);
  }, [defaultFilterValues]);

  // Process common fields from rawData
  useEffect(() => {
    if (!rawData || !Array.isArray(rawData) || rawData.length === 0) {
      return;
    }

    setIsProcessingData(true);
    try {
      if (rawData.length && rawData[0].currency) {
        setCurrency(rawData[0].currency);
      }

      const commonValues = getReportCommonFieldsValues(REPORT_TYPE, rawData);
      const memberOptions: OptionType[] = commonValues.member.map(
        (member, idx) => ({
          label: member,
          value: commonValues.memberId[idx],
        })
      );
      updateCommonField('member', memberOptions);

      const floorOptions: OptionType[] = commonValues.section.map(section => ({
        label: section,
        value: section,
      }));
      updateCommonField('floor', floorOptions);

      console.log(
        '[ItemSales] Raw serviceType values from API:',
        commonValues.serviceType
      );
      const serviceTypeOptions: OptionType[] = commonValues.serviceType.map(
        service => ({
          label: capitalize(service),
          value: service,
        })
      );
      console.log(
        '[ItemSales] Processed serviceType options:',
        serviceTypeOptions
      );
      updateCommonField('serviceType', serviceTypeOptions);

      const sourceOptions: OptionType[] = commonValues.source.map(source => ({
        label: SourceTypes[source as keyof typeof SourceTypes] || source,
        value: source,
      }));
      updateCommonField('sources', sourceOptions);
    } catch (err) {
      console.error('Error processing common fields for ItemSales:', err);
    } finally {
      setIsProcessingData(false);
    }
  }, [rawData, updateCommonField]);

  // Process graph and table data
  const { graphData, composedFilters, groupedByItemsData } = useMemo(() => {
    if (
      !rawData ||
      !filters ||
      !Array.isArray(rawData) ||
      rawData.length === 0
    ) {
      return {
        graphData: { datasets: [], labels: [] },
        composedFilters: [],
        groupedByItemsData: [],
      };
    }

    setIsProcessingData(true);
    try {
      const appliedFilters = composeFilters(filters, REPORT_TYPE);
      const filteredData = filterReport(
        REPORT_TYPE,
        rawData,
        appliedFilters,
        []
      );

      const topProductIds = groupReport(
        REPORT_TYPE,
        filteredData,
        [],
        ['id']
      )[0]
        ?.report.sort((a, b) => b.value - a.value)
        ?.slice(0, 5)
        ?.map(el => el.id);

      const filteredByTopIds = filterReport(
        REPORT_TYPE,
        rawData,
        appliedFilters,
        [{ field: 'id', operator: 'in', value: topProductIds }]
      );

      const groupedByHour = groupReport(
        REPORT_TYPE,
        filteredByTopIds,
        ['hourOfDay'],
        ['id']
      );

      const labels = groupedByHour?.map(group => group.hourOfDay.toString());
      const datasets: { label: string; data: number[] }[] =
        topProductIds?.map(id => ({
          label: id,
          data: [],
        })) || [];

      groupedByHour.forEach(({ report: items }) => {
        datasets.forEach(ds => {
          const item = items.find(i => i.id === ds.label);
          const value = item?.value || 0;
          const formattedValue = value / 10000;
          ds.data.push(formattedValue);
        });
      });

      // Enrich dataset labels with item names
      const enrichedDatasets = datasets.map(ds => {
        return {
          ...ds,
          label: capitalize(itemsIdToName[ds.label]) || ds.label,
        };
      });

      const computedGraphData = {
        datasets: enrichedDatasets,
        labels,
      };

      const availableFields = selectedFields.filter((field: any) => {
        return (
          field.isChecked &&
          reportSpecificFields.items.some(
            discountField =>
              cleanStringArond(field.value) === cleanStringArond(discountField)
          )
        );
      });

      const rawGroupedData = groupReport(
        REPORT_TYPE,
        filteredData,
        [],
        availableFields.map((item: FieldOption) => item.value)
      );

      const hierarchicalGroupedData =
        groupGroupedReportBySpecificFieldsHierarchical(
          REPORT_TYPE,
          rawGroupedData,
          selectedGrouping as []
        )[0]?.report || [];

      return {
        graphData: computedGraphData,
        composedFilters: appliedFilters,
        groupedByItemsData: hierarchicalGroupedData,
      };
    } catch (err) {
      console.error('Error in ItemSales data processing:', err);
      return {
        graphData: { datasets: [], labels: [] },
        composedFilters: [],
        groupedByItemsData: [],
      };
    } finally {
      setIsProcessingData(false);
    }
  }, [
    filters,
    rawData,
    selectedGrouping,
    selectedFields,
    itemsLibrary,
    sellPointId,
    itemsIdToName,
  ]);

  const fetchEnrichedItemSalesData = useCallback(() => {
    if (
      !groupedByItemsData ||
      !itemsLibrary ||
      !categories ||
      groupedByItemsData.length === 0
    ) {
      // Clear data when groupedByItemsData is empty or missing dependencies
      setEnrichedItemSalesData([]);
      setIsProcessingData(false);
      return;
    }

    setIsProcessingData(true);
    try {
      const collectLeafAndGroupIds = (
        items: any[]
      ): { leafIds: string[]; groupIds: Set<string> } => {
        let leafIds: string[] = [];
        let groupIds: Set<string> = new Set();

        for (const item of items) {
          if (!item.subReport || item.subReport.length === 0) {
            leafIds.push(item.id);
            groupIds.add(item.groupId);
          } else {
            groupIds.add(item.groupId);
            const { leafIds: childLeafIds, groupIds: childGroupIds } =
              collectLeafAndGroupIds(item.subReport);
            leafIds = [...leafIds, ...childLeafIds];
            childGroupIds.forEach(id => groupIds.add(id));
          }
        }

        return { leafIds, groupIds };
      };

      const { leafIds, groupIds } = collectLeafAndGroupIds(groupedByItemsData);

      const enrichItems = (items: any[]): any[] => {
        return items.map(item => {
          if (item.groupedBy) {
            const groupField = item.groupedBy.field;
            const groupValue = item.groupedBy.value;

            return {
              ...item,
              name:
                groupField === 'groupId'
                  ? categoriesIdToName[groupValue] || groupValue
                  : `${capitalize(groupField)}: ${groupValue}`,
              groupName:
                groupField === 'groupId' && item.groupedBy
                  ? categoriesIdToName[groupValue] || groupValue
                  : '',
              subReport: item.subReport
                ? enrichItems(item.subReport)
                : undefined,
            };
          } else {
            const enrichedItem = {
              ...item,
              name: itemsIdToName[item.id] || item.id,
              groupName: categoriesIdToName[item.groupId] || item.groupId,
            };

            if (item.subReport && item.subReport.length > 0) {
              enrichedItem.subReport = enrichItems(item.subReport);
            }

            return enrichedItem;
          }
        });
      };

      const enrichedData = enrichItems(groupedByItemsData);
      setEnrichedItemSalesData(enrichedData);
    } catch (error: any) {
      console.error('Error fetching enriched item sales data:', error);
    } finally {
      setIsProcessingData(false);
    }
  }, [groupedByItemsData, itemsIdToName, categoriesIdToName, sellPointId]);

  useEffect(() => {
    fetchEnrichedItemSalesData();
  }, [fetchEnrichedItemSalesData]);

  // Clear data when sellpoint changes or rawData becomes empty
  useEffect(() => {
    if (!rawData || !Array.isArray(rawData) || rawData.length === 0) {
      setEnrichedItemSalesData([]);
      setCommonFields({});
      setCurrency(undefined);
    }
  }, [sellPointId, rawData]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const finalTableData = useMemo(() => {
    const sortedData = sortData(enrichedItemSalesData);
    return remapReports(sortedData, 'id');
  }, [enrichedItemSalesData]);

  const handleGroupingChange = useCallback((groupFields: string[]) => {
    setSelectedGrouping(groupFields);
  }, []);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const filterValues = useMemo(() => {
    return { defaultFilterValues, setFilters, commonFields };
  }, [defaultFilterValues, setFilters, commonFields]);

  const formattedFilters = useMemo(() => {
    if (!filters) return {};

    const getLabel = (field: string, value: string) => {
      const options = commonFields[field];
      const found = options?.find(opt => opt.value === value);
      return found?.label || value;
    };

    return {
      sellpoint: filters.sellpointId,
      dateRange: filters.dateRange.map(d => d?.format('YYYY-MM-DD')),
      timeRange: filters.timeRange,
      member: getLabel('member', filters.member ?? ''),
      floor: getLabel('floor', filters.floor ?? ''),
      serviceType: getLabel('serviceType', filters.diningOption),
      source: getLabel('sources', filters.source),
    };
  }, [filters, commonFields]);

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report item sales';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Item',
        'VAT',
        'Prep Station',
        'Category',
        'Unit',
        'Items Sold',
        'Gross Sales',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Sales',
      ].join(','),
      ...finalTableData.map(el =>
        [
          el.name,
          el.vat,
          el.prepStation,
          categories?.find(cat => cat.id === el.groupId)?.name || el.groupId,
          el.measureUnit,
          el.quantity / 1000 || 0,
          el.value / 10000 || 0,
          -el.discountsValue / 10000 || 0,
          -el.couponsValue / 10000 || 0,
          el.promotionsValue / 10000 || 0,
          el.netValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        '',
        '',
        '',
        '',
        finalTableData.reduce((acc, el) => acc + el.quantity, 0) / 1000,
        finalTableData.reduce((acc, el) => acc + el.value, 0) / 10000,
        -finalTableData.reduce(
          (acc, el) => acc + (el.discountsValue || 0) / 10000,
          0
        ),
        -finalTableData.reduce(
          (acc, el) => acc + (el.couponsValue || 0) / 10000,
          0
        ),
        finalTableData.reduce(
          (acc, el) => acc + (el.promotionsValue || 0) / 10000,
          0
        ),
        finalTableData.reduce((acc, el) => acc + (el.netValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'itemSales');
  };

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('md'));

  return (
    <Box sx={{ width: '100%' }} p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('menu.itemSales')}
        description={
          <>
            {t('reportsPage.itemSalesDescription')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={
          <ExportMenuButton
            contentRef={contentRef}
            handleExport={handleExport}
          />
        }
      />
      <ReportFilters
        defaultValues={defaultFilterValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />

      {/* Show linear progress when loading or processing */}
      <Box
        sx={{
          width: isSmall ? 'calc(100% + 50px)' : '100%',
          ml: isSmall ? '-25px' : 0,
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          height: 4,
        }}
      >
        {(isLoading || isProcessingData) && <LinearProgress />}
      </Box>

      <ReportDateTitle />

      {/* Show error when applicable */}
      {error && !isLoading && !isProcessingData ? (
        <Box sx={{ color: 'error.main', textAlign: 'center', my: 2 }}>
          {t('errors.data-loading-error', {
            error:
              typeof error === 'object' && error && 'message' in error
                ? (error as any).message
                : String(error),
          })}
        </Box>
      ) : null}

      {/* Always render content when we have initial data, regardless of loading state */}
      {isInitialStorageFetchDone && (
        <>
          {isSmall ? null : (
            <ItemSalesGraph data={graphData || {}} currency={currency} />
          )}
          <ItemSalesTable
            formattedFilters={formattedFilters}
            rawData={rawData}
            filterValues={filterValues}
            reportType={REPORT_TYPE}
            fields={selectedFields}
            setFields={setSelectedFields}
            composedFilters={composedFilters}
            filters={filters}
            tableData={finalTableData || []}
            groupingItems={selectedGrouping}
            onChangeGrouping={handleGroupingChange}
          />
        </>
      )}
    </Box>
  );
}
