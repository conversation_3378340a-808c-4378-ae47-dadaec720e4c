import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, capitalize } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import { useGetListLocationsLive } from '~/providers/resources';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import VatCards from './components/VatCards';
import VatTable from './components/VatTable';

const REPORT_TYPE = 'vat';

export default function Vat() {
  const { details: fbDetails } = useFirebase();
  const { t } = useTranslation();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [rawData, setRawData] = useState<any>();
  const [currency, setCurrency] = useState<string>('RON');

  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = (key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  const contentRef = useRef<HTMLDivElement>(null);

  //TODO: asta nu mai e ok la dining si la source
  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });

        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });

        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        });

        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });

        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const { tableData } = useMemo(() => {
    if (!rawData || !filters) return { tableData: [], cardsData: {} };

    if (rawData.length) {
      setCurrency(rawData[0].currency);
    }

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      ['vat']
    );

    const tableData =
      groupedTableData[0]?.report?.sort((a, b) => {
        return (a.vat ?? 0) - (b.vat ?? 0);
      }) || [];

    return { tableData };
  }, [filters, rawData]);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report VAT';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'VAT',
        'Items Sales',
        'Modifiers Sales',
        'Gift Cards Sales',
        'Extra Charges Sales',
        'Gross Sales',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Sales',
        'Tips Amount',
        'Total Collected',
        'VAT Amount',
      ].join(','),
      ...tableData.map(el =>
        [
          el.vat,
          el.itemsValue / 10000 || 0,
          el.modifiersValue / 10000 || 0,
          el.giftCardsValue / 10000 || 0,
          el.extraChargesValue / 10000 || 0,
          el.value / 10000 || 0,
          -el.discountsValue / 10000 || 0,
          -el.couponsValue / 10000 || 0,
          el.promotionsValue / 10000 || 0,
          el.netValue / 10000 || 0,
          el.tipsValue / 10000 || 0,
          el.totalValue / 10000 || 0,
          el.vatValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        tableData.reduce((acc, el) => acc + (el.itemsValue || 0) / 10000, 0),
        tableData.reduce(
          (acc, el) => acc + (el.modifiersValue || 0) / 10000,
          0
        ),
        tableData.reduce(
          (acc, el) => acc + (el.giftCardsValue || 0) / 10000,
          0
        ),
        tableData.reduce(
          (acc, el) => acc + (el.extraChargesValue || 0) / 10000,
          0
        ),
        tableData.reduce((acc, el) => acc + (el.value || 0) / 10000, 0),
        -tableData.reduce(
          (acc, el) => acc + (el.discountsValue || 0) / 10000,
          0
        ),
        -tableData.reduce((acc, el) => acc + (el.couponsValue || 0) / 10000, 0),
        -tableData.reduce(
          (acc, el) => acc + (el.promotionsValue || 0) / 10000,
          0
        ),
        tableData.reduce((acc, el) => acc + (el.netValue || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.tipsValue || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.totalValue || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.vatValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'reportVAT');
  };

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('shared.tva')}
        description={
          <>
            {t('vat.description')}
            <a href="https://selio.io/support-center" target="_blank">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={
          <ExportMenuButton
            contentRef={contentRef}
            handleExport={handleExport}
          />
        }
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />
      <ReportDateTitle />
      <VatCards tableData={tableData} />
      <Box sx={{ pb: 3 }}>
        <VatTable tableData={tableData || []} />
      </Box>
    </Box>
  );
}
