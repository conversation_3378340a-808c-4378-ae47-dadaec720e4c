import { getDownloadURL, ref } from 'firebase/storage';

import {
  ImageVariant,
  PrivateFileContext,
  UploadedFile,
  UrlGenerationOptions,
} from '../types/fileUpload';
import { getStorageInstanceForFile } from './bucketManager';

/**
 * CDN base URL
 */
const CDN_BASE_URL = 'https://cdn.selio.cloud';

/**
 * Check if we're in production environment
 */
const isProduction = (): boolean => {
  return import.meta.env.VITE_NODE_ENV === 'prod';
};

/**
 * Generate URL for public images (permanent)
 * In production: Uses CDN URLs
 * In development: Uses Firebase Storage URLs with getDownloadURL for caching
 */
export const generatePublicImageUrl = async (
  file: UploadedFile,
  variant?: ImageVariant,
  format?: string
): Promise<string> => {
  // Determine the correct format based on variant
  let actualFormat = format;
  if (!actualFormat) {
    // Original keeps the original extension, others default to webp
    actualFormat = variant === 'original' ? file.e : 'webp';
  }

  // In production, use CDN URLs
  if (isProduction()) {
    if (variant) {
      // Image variants are stored in folder structure: /i/uniqueId/variant.{format}
      return `${CDN_BASE_URL}/i/${file.f}/${variant}.${actualFormat}`;
    }

    // All public images have variants - default to thumbnail
    return `${CDN_BASE_URL}/i/${file.f}/thumbnail.webp`;
  }

  // In development, use Firebase Storage URLs for caching
  const storage = getStorageInstanceForFile('i', false);
  let filePath: string;

  if (variant) {
    // Image variants are stored in folder structure: i/uniqueId/variant.{format}
    filePath = `i/${file.f}/${variant}.${actualFormat}`;
  } else {
    // For images without specified variant, default to thumbnail
    filePath = `i/${file.f}/thumbnail.webp`;
  }

  const fileRef = ref(storage, filePath);

  try {
    return await getDownloadURL(fileRef);
  } catch (error: any) {
    // If specific variant or thumbnail doesn't exist, try to fallback to original
    if (error?.code === 'storage/object-not-found') {
      const originalPath = `i/${file.f}/original.${file.e}`;
      const originalRef = ref(storage, originalPath);
      return await getDownloadURL(originalRef);
    }

    throw error;
  }
};

/**
 * Generate URL for videos (permanent)
 * In production: Uses CDN URLs
 * In development: Uses Firebase Storage URLs with getDownloadURL for caching
 */
export const generateVideoUrl = async (file: UploadedFile): Promise<string> => {
  // In production, use CDN URLs
  if (isProduction()) {
    return `${CDN_BASE_URL}/v/${file.f}.${file.e}`;
  }

  // In development, use Firebase Storage URLs for caching
  const storage = getStorageInstanceForFile('v', false);
  const filePath = `v/${file.f}.${file.e}`;
  const fileRef = ref(storage, filePath);
  return await getDownloadURL(fileRef);
};

/**
 * Generate URL for public files (permanent)
 * In production: Uses CDN URLs
 * In development: Uses Firebase Storage URLs with getDownloadURL for caching
 */
export const generatePublicFileUrl = async (
  file: UploadedFile
): Promise<string> => {
  // In production, use CDN URLs
  if (isProduction()) {
    return `${CDN_BASE_URL}/s/${file.f}.${file.e}`;
  }

  // In development, use Firebase Storage URLs for caching
  const storage = getStorageInstanceForFile('s', false);
  const filePath = `s/${file.f}.${file.e}`;
  const fileRef = ref(storage, filePath);
  return await getDownloadURL(fileRef);
};

/**
 * Generate Firebase Storage URL for private files
 * Private files use organized paths and require authentication
 */
export const generatePrivateFileUrl = async (
  file: UploadedFile,
  context: PrivateFileContext
): Promise<string> => {
  const pathParts = ['a', context.accountId];

  if (context.sellpointId) {
    pathParts.push(context.sellpointId);
  }

  if (context.customPath) {
    pathParts.push(context.customPath);
  }

  pathParts.push(`${file.f}.${file.e}`);

  const filePath = pathParts.join('/');
  const storage = getStorageInstanceForFile('p', false);
  const fileRef = ref(storage, filePath);

  return await getDownloadURL(fileRef);
};

/**
 * Main URL generation function that routes to appropriate method
 */
export const generateFileUrl = async (
  file: UploadedFile,
  options?: UrlGenerationOptions
): Promise<string> => {
  // If file has cached URL and we're not forcing refresh, use it
  if (file.url && !options?.forceRefresh) {
    return file.url;
  }

  // Handle temporary files - these need special handling via getSecureFileUrl
  if (file.x) {
    throw new Error('Use getSecureFileUrl for temporary files');
  }

  // Handle permanent files based on type
  switch (file.t) {
    case 'i':
      return await generatePublicImageUrl(
        file,
        options?.imageVariant as ImageVariant
      );

    case 'v':
      return await generateVideoUrl(file);

    case 's':
      return await generatePublicFileUrl(file);

    case 'p':
      // For private files, let the caller handle it via getSecureFileUrl
      // This avoids circular dependency
      throw new Error('Use getSecureFileUrl for private files');

    default:
      throw new Error(`Unknown file type: ${file.t}`);
  }
};

/**
 * Get the destination path for file movement based on file type
 */
export const getDestinationPath = (
  file: UploadedFile,
  context?: PrivateFileContext
): string => {
  const filename = `${file.f}.${file.e}`;

  switch (file.t) {
    case 'i':
      // For images, check if it uses the new folder structure (all images now use this)
      // Images with variants are stored in folder structure: i/{uniqueId}/original.ext
      // For deletion, we need to target the specific file within the folder
      return `i/${file.f}/original.${file.e}`;

    case 'v':
      // Videos go to /v/ folder
      return `v/${filename}`;

    case 's':
      // Public files go to /s/ folder
      return `s/${filename}`;

    case 'p':
      // Private files use organized paths
      if (!context) {
        throw new Error('Private file context required for destination path');
      }

      const pathParts = ['a', context.accountId];
      if (context.sellpointId) pathParts.push(context.sellpointId);
      if (context.customPath) pathParts.push(context.customPath);
      pathParts.push(filename);

      return pathParts.join('/');

    default:
      throw new Error(`Unknown file type: ${file.t}`);
  }
};
