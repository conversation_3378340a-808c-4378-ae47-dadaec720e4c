import {
    BROADCAST_CHANNEL_NAME,
    BROWSER_TAB_ID,
} from '~/providers/utils/constants';

interface TabMessage {
    type: 'TAB_CHECK' | 'TAB_RESPONSE';
    tabId: string;
    timestamp: number;
}

const STORAGE_KEY = 'selio_active_tab';
const HEARTBEAT_INTERVAL = 5000;
const HEARTBEAT_TIMEOUT = 12000;
const STORAGE_EVENTS_KEY = 'selio_tab_events';

const createTabManager = () => {
    let isActive = false;
    let channel: BroadcastChannel | null = null;
    let heartbeatInterval: number | null = null;
    let isMobile = false;
    let storageEventListener: ((event: StorageEvent) => void) | null = null;
    let beforeUnloadListener: (() => void) | null = null;

    const detectMobile = () => {
        const userAgent = navigator.userAgent || navigator.vendor;
        return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
            userAgent.toLowerCase()
        );
    };

    // localStorage operations for mobile
    const setStorageData = (data: { tabId: string; timestamp: number }) => {
        try {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
            console.error('Failed to write to localStorage:', error);
        }
    };

    const getStorageData = () => {
        try {
            const data = localStorage.getItem(STORAGE_KEY);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Failed to read from localStorage:', error);
            return null;
        }
    };

    const setupStorageEvents = () => {
        storageEventListener = (event: StorageEvent) => {
            if (event.key === STORAGE_EVENTS_KEY && event.newValue) {
                try {
                    const eventData = JSON.parse(event.newValue);
                    if (
                        eventData.type === 'TAB_CHECK' &&
                        eventData.tabId !== BROWSER_TAB_ID &&
                        isActive
                    ) {
                        const responseData = {
                            type: 'TAB_RESPONSE',
                            tabId: BROWSER_TAB_ID,
                            timestamp: Date.now(),
                            responseTo: eventData.tabId,
                        };
                        localStorage.setItem(
                            STORAGE_EVENTS_KEY,
                            JSON.stringify(responseData)
                        );
                    } else if (
                        eventData.type === 'TAB_RESPONSE' &&
                        eventData.responseTo === BROWSER_TAB_ID
                    ) {
                        console.debug('Another tab is active via storage events');
                        isActive = false;
                    }
                } catch (error) {
                    console.error('Error processing storage event:', error);
                }
            }
        };

        window.addEventListener('storage', storageEventListener);
    };

    const cleanupStorage = () => {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
        }

        if (storageEventListener) {
            window.removeEventListener('storage', storageEventListener);
            storageEventListener = null;
        }

        if (beforeUnloadListener) {
            window.removeEventListener('beforeunload', beforeUnloadListener);
            window.removeEventListener('unload', beforeUnloadListener);
            beforeUnloadListener = null;
        }

        if (isActive) {
            const activeTab = getStorageData();
            if (activeTab && activeTab.tabId === BROWSER_TAB_ID) {
                try {
                    localStorage.removeItem(STORAGE_KEY);
                    localStorage.removeItem(STORAGE_EVENTS_KEY);
                } catch (error) {
                    console.error('Error cleaning up storage:', error);
                }
            }
        }
    };

    const startStorageHeartbeat = () => {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
        }

        const updateHeartbeat = () => {
            if (!isActive) return;

            const now = Date.now();
            const existingData = getStorageData();

            if (
                !existingData ||
                existingData.tabId !== BROWSER_TAB_ID ||
                now - existingData.timestamp > HEARTBEAT_INTERVAL - 1000
            ) {
                setStorageData({
                    tabId: BROWSER_TAB_ID,
                    timestamp: now,
                });
            }
        };

        updateHeartbeat();
        heartbeatInterval = window.setInterval(updateHeartbeat, HEARTBEAT_INTERVAL);
    };

    const initializeWithStorage = (): Promise<boolean> => {
        return new Promise(resolve => {
            const now = Date.now();
            const activeTab = getStorageData();

            setupStorageEvents();

            if (activeTab && now - activeTab.timestamp < HEARTBEAT_TIMEOUT) {
                const checkData = {
                    type: 'TAB_CHECK',
                    tabId: BROWSER_TAB_ID,
                    timestamp: now,
                };

                localStorage.setItem(STORAGE_EVENTS_KEY, JSON.stringify(checkData));

                setTimeout(() => {
                    if (!isActive) {
                        console.debug('Found active tab via storage, becoming inactive');
                        resolve(false);
                    } else {
                        console.debug('No response from existing tab, becoming active');
                        isActive = true;
                        setStorageData({ tabId: BROWSER_TAB_ID, timestamp: now });
                        startStorageHeartbeat();
                        resolve(true);
                    }
                }, 800);
            } else {
                console.debug('No active tab found, becoming active');
                isActive = true;
                setStorageData({ tabId: BROWSER_TAB_ID, timestamp: now });
                startStorageHeartbeat();
                resolve(true);
            }
        });
    };

    const canUseBroadcastChannel = () => {
        isMobile = detectMobile();
        return !isMobile && 'BroadcastChannel' in window;
    };

    const initializeChannel = () => {
        try {
            if (canUseBroadcastChannel()) {
                console.debug('Using BroadcastChannel for desktop');
                channel = new BroadcastChannel(BROADCAST_CHANNEL_NAME);
                return true;
            } else {
                console.debug(
                    'Using localStorage fallback for mobile/unsupported browsers'
                );
                return false;
            }
        } catch (error) {
            console.error('Failed to create BroadcastChannel:', error);
            return false;
        }
    };

    // Don't create channel at module load time - wait for initialize() call
    const hasBroadcastChannel = canUseBroadcastChannel();

    if (!hasBroadcastChannel) {
        return {
            initialize: async () => {
                const result = await initializeWithStorage();

                beforeUnloadListener = cleanupStorage;
                window.addEventListener('beforeunload', beforeUnloadListener);
                window.addEventListener('unload', beforeUnloadListener);

                return result;
            },
            cleanup: cleanupStorage,
            isActiveTab: () => isActive,
        };
    }

    // BroadcastChannel implementation for desktop
    const setupListeners = () => {
        if (!channel) return;

        try {
            channel.onmessage = (event: MessageEvent<TabMessage>) => {
                console.debug('Tab message received:', event.data);

                // Ignore messages from this tab
                if (event.data.tabId === BROWSER_TAB_ID) {
                    return;
                }

                switch (event.data.type) {
                    case 'TAB_CHECK':
                        if (isActive && channel) {
                            console.debug('Responding to TAB_CHECK as active tab');
                            channel.postMessage({
                                type: 'TAB_RESPONSE',
                                tabId: BROWSER_TAB_ID,
                                timestamp: Date.now(),
                            });
                        }
                        break;
                    case 'TAB_RESPONSE':
                        // Only set inactive if this is a response to a check we sent
                        // Active tabs shouldn't become inactive when they send responses
                        if (!isActive) {
                            console.debug('Another tab is already active:', event.data.tabId);
                        }
                        break;
                }
            };

            channel.onmessageerror = error => {
                console.error('BroadcastChannel message error:', error);
            };
        } catch (error) {
            console.error('Error in setupListeners:', error);
            isActive = true;
        }
    };

    const checkForExistingTab = (): Promise<boolean> => {
        return new Promise(resolve => {
            if (!channel) {
                resolve(true);
                return;
            }

            console.debug('Checking for existing active tab');

            // First check localStorage for active tab
            const now = Date.now();
            const activeTab = getStorageData();

            if (activeTab && activeTab.tabId !== BROWSER_TAB_ID && now - activeTab.timestamp < HEARTBEAT_TIMEOUT) {
                console.debug('Found active tab in localStorage:', activeTab.tabId, 'age:', now - activeTab.timestamp, 'ms');

                // There's a recent active tab in localStorage, verify via BroadcastChannel
                let hasResponse = false;
                let timeoutId: number;

                const responseHandler = (event: MessageEvent<TabMessage>) => {
                    if (
                        event.data.type === 'TAB_RESPONSE' &&
                        event.data.tabId !== BROWSER_TAB_ID &&
                        !hasResponse
                    ) {
                        hasResponse = true;
                        console.debug('Active tab confirmed via BroadcastChannel:', event.data.tabId);
                        isActive = false;
                        clearTimeout(timeoutId);
                        channel?.removeEventListener('message', responseHandler);
                        resolve(false);
                    }
                };

                channel.addEventListener('message', responseHandler);

                channel.postMessage({
                    type: 'TAB_CHECK',
                    tabId: BROWSER_TAB_ID,
                    timestamp: Date.now(),
                } as TabMessage);

                timeoutId = window.setTimeout(() => {
                    channel?.removeEventListener('message', responseHandler);
                    if (!hasResponse) {
                        // No response via BroadcastChannel, but localStorage says there's an active tab
                        // This could mean the active tab is on a different page (like /auth)
                        console.debug('Active tab exists in localStorage but no BroadcastChannel response - another tab is active');
                        isActive = false;
                        resolve(false);
                    }
                }, 300);
            } else {
                // No recent active tab in localStorage, check via BroadcastChannel
                console.debug('No active tab in localStorage, checking via BroadcastChannel');

                let hasResponse = false;
                let timeoutId: number;

                const responseHandler = (event: MessageEvent<TabMessage>) => {
                    if (
                        event.data.type === 'TAB_RESPONSE' &&
                        event.data.tabId !== BROWSER_TAB_ID &&
                        !hasResponse
                    ) {
                        hasResponse = true;
                        console.debug('Found existing active tab via BroadcastChannel:', event.data.tabId);
                        isActive = false;
                        clearTimeout(timeoutId);
                        channel?.removeEventListener('message', responseHandler);
                        resolve(false);
                    }
                };

                channel.addEventListener('message', responseHandler);

                channel.postMessage({
                    type: 'TAB_CHECK',
                    tabId: BROWSER_TAB_ID,
                    timestamp: Date.now(),
                } as TabMessage);

                timeoutId = window.setTimeout(() => {
                    if (!hasResponse) {
                        console.debug('No active tab found, this tab becomes active');
                        isActive = true;
                        setStorageData({ tabId: BROWSER_TAB_ID, timestamp: now });
                        channel?.removeEventListener('message', responseHandler);
                        resolve(true);
                    }
                }, 300);
            }
        });
    };

    const initialize = async (): Promise<boolean> => {
        console.debug('[TabManager] Initialize called, hasBroadcastChannel:', hasBroadcastChannel, 'channel exists:', !!channel);

        if (hasBroadcastChannel) {
            // Create channel on first initialization
            if (!channel) {
                initializeChannel();
            }

            if (channel) {
                setupListeners();
                const result = await checkForExistingTab();

                console.debug('[TabManager] Initialization complete, isActive:', isActive);

                // Start heartbeat to maintain localStorage presence even on desktop
                if (isActive) {
                    startStorageHeartbeat();
                }

                beforeUnloadListener = () => {
                    if (channel) {
                        channel.close();
                    }
                    // Don't clean up localStorage on navigation - only on actual tab close
                    // The heartbeat will eventually expire (12 seconds) if tab is truly closed
                    // This allows the active tab state to persist across page navigations
                };
                window.addEventListener('beforeunload', beforeUnloadListener);
                window.addEventListener('unload', beforeUnloadListener);

                return result;
            }
        }

        return await initializeWithStorage();
    };

    const cleanup = () => {
        console.debug('[TabManager] Cleanup called, isActive:', isActive);

        if (hasBroadcastChannel && channel) {
            channel.close();
            channel = null;
        } else {
            cleanupStorage();
        }

        if (beforeUnloadListener) {
            window.removeEventListener('beforeunload', beforeUnloadListener);
            window.removeEventListener('unload', beforeUnloadListener);
            beforeUnloadListener = null;
        }
    };

    const isActiveTab = (): boolean => {
        return isActive;
    };

    return {
        initialize,
        cleanup,
        isActiveTab,
    };
};

export const tabManager = createTabManager();
