import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '../atoms/inputs/CustomInput/CustomInput';
import { CustomTranslatableInputs } from '../atoms/inputs/CustomTranslatableInputs';
import { ConflictAwareInput } from '../conflict-detection';

export const PublicNameAndDescriptionSection = (props: any) => {
  const { t } = useTranslation('');
  const { catalogSpecific, getCatalogName, vatsChoices } = props;

  // Sort entries by catalog name
  const sortedEntries = Object.entries(catalogSpecific).sort(([idA], [idB]) => {
    const nameA = getCatalogName(idA) || '';
    const nameB = getCatalogName(idB) || '';
    return nameA.localeCompare(nameB);
  });

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="h5">
        {t('itemsLibrary.nameAndDescriptionVariations')}
      </Typography>
      {sortedEntries.map(
        ([catalogId, catalogSpecificValues]: [string, any]) => {
          console.log(catalogSpecificValues);
          const catalogName = getCatalogName(catalogId);
          if (!catalogName) return null;
          return (
            <CustomTranslatableInputs
              key={catalogId}
              locales={['en', 'ro']}
              roundedCorners="top"
              title={catalogName}
            >
              <ConflictAwareInput>
                <CustomInput
                  type="text"
                  ui="custom"
                  sanitize="singleLine"
                  source={`catalogSpecific.${catalogId}.publicName`}
                  label={t('itemsLibrary.publicName')}
                  roundedCorners="none"
                />
              </ConflictAwareInput>
              <ConflictAwareInput>
                <CustomInput
                  type="text"
                  ui="custom"
                  sanitize="singleLine"
                  source={`catalogSpecific.${catalogId}.description`}
                  label={t('itemsLibrary.publicDescription')}
                  roundedCorners="none"
                />
              </ConflictAwareInput>
              <ConflictAwareInput>
                <CustomInput
                  type="text"
                  ui="custom"
                  sanitize="singleLine"
                  source={`catalogSpecific.${catalogId}.ingredients`}
                  label={t('itemsLibrary.ingredients')}
                  roundedCorners="bottom"
                />
              </ConflictAwareInput>
            </CustomTranslatableInputs>
          );
        }
      )}
    </Box>
  );
};
