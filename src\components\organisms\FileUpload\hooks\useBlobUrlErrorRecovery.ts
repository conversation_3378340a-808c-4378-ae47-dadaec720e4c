import { useCallback } from 'react';

import { inMemoryFileManager } from '../core/InMemoryFileManager';
import { UploadedFile } from '../types/fileUpload';

/**
 * Hook for handling blob URL errors and recovering with fresh URLs
 *
 * This hook provides automatic recovery when blob URLs become invalid due to:
 * - Browser tab suspend/resume
 * - Memory pressure
 * - Race conditions during URL generation
 *
 * Works only for in-memory files (files with inMemoryData)
 */
export const useBlobUrlErrorRecovery = () => {
  /**
   * Handle blob URL error by regenerating the URL
   */
  const handleBlobError = useCallback(
    async (file: UploadedFile, variant?: string): Promise<string> => {
      if (!file.inMemoryData) {
        return '';
      }

      try {
        const newUrl = await inMemoryFileManager.handleBlobError(file, variant);
        return newUrl;
      } catch (error) {
        return '';
      }
    },
    []
  );

  /**
   * Create an error handler for image elements
   * Automatically attempts to recover from blob URL failures
   */
  const createImageErrorHandler = useCallback(
    (
      file: UploadedFile,
      variant?: string,
      onUrlRecovered?: (newUrl: string) => void
    ) => {
      return async (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        const currentSrc = img.src;

        // Only handle blob URL errors
        if (!currentSrc.startsWith('blob:')) {
          return;
        }

        try {
          const newUrl = await handleBlobError(file, variant);
          if (newUrl && newUrl !== currentSrc) {
            // Update the image source with fresh blob URL
            img.src = newUrl;
            onUrlRecovered?.(newUrl);
          }
        } catch (error) {
          // Silent fail - image will remain broken but won't crash
        }
      };
    },
    [handleBlobError]
  );

  return {
    handleBlobError,
    createImageErrorHandler,
  };
};
