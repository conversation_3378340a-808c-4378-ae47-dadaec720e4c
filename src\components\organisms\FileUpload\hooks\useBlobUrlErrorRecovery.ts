import { useCallback } from 'react';

import { inMemoryFileManager } from '../core/InMemoryFileManager';
import { UploadedFile } from '../types/fileUpload';

/**
 * Hook for handling blob URL errors and recovering with fresh URLs
 */
export const useBlobUrlErrorRecovery = () => {
  /**
   * Handle blob URL error by regenerating the URL
   */
  const handleBlobError = useCallback(
    async (file: UploadedFile, variant?: string): Promise<string> => {
      if (!file.inMemoryData) {
        console.warn(
          '🚨 [useBlobUrlErrorRecovery] Cannot recover URL for file without in-memory data:',
          file.f
        );
        return '';
      }

      console.log('🔄 [useBlobUrlErrorRecovery] Recovering blob URL for:', {
        fileName: file.f,
        variant: variant || 'original',
      });

      try {
        const newUrl = await inMemoryFileManager.handleBlobError(file, variant);
        console.log(
          '✅ [useBlobUrlErrorRecovery] Successfully recovered URL:',
          newUrl
        );
        return newUrl;
      } catch (error) {
        console.error(
          '❌ [useBlobUrlErrorRecovery] Failed to recover URL:',
          error
        );
        return '';
      }
    },
    []
  );

  /**
   * Create an error handler for image elements
   */
  const createImageErrorHandler = useCallback(
    (
      file: UploadedFile,
      variant?: string,
      onUrlRecovered?: (newUrl: string) => void
    ) => {
      return async (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        const currentSrc = img.src;

        // Only handle blob URL errors
        if (!currentSrc.startsWith('blob:')) {
          return;
        }

        console.log(
          '🚨 [useBlobUrlErrorRecovery] Image failed to load, attempting recovery:',
          {
            fileName: file.f,
            variant: variant || 'original',
            failedUrl: currentSrc,
          }
        );

        try {
          const newUrl = await handleBlobError(file, variant);
          if (newUrl && newUrl !== currentSrc) {
            // Update the image source
            img.src = newUrl;
            onUrlRecovered?.(newUrl);
            console.log(
              '✅ [useBlobUrlErrorRecovery] Image URL recovered successfully'
            );
          }
        } catch (error) {
          console.error(
            '❌ [useBlobUrlErrorRecovery] Image URL recovery failed:',
            error
          );
        }
      };
    },
    [handleBlobError]
  );

  return {
    handleBlobError,
    createImageErrorHandler,
  };
};
