/**
 * Gateway - Authentication Check
 *
 * Checks if user is authenticated:
 * - Authenticated: Loads main React app via script tag
 * - Not authenticated: Redirects to /auth
 */

import { onAuthStateChanged } from 'firebase/auth';

import { auth } from '../firebase-core';

(async () => {
  console.log('[Gateway] Checking authentication status...');

  const user = await new Promise<any>(resolve => {
    const unsubscribe = onAuthStateChanged(auth, user => {
      console.log(
        '[Gateway] Auth state determined:',
        user ? user.email : 'not authenticated'
      );
      unsubscribe();
      resolve(user);
    });
  });

  if (user) {
    console.log('[Gateway] User authenticated, loading React application...');

    // Load main app via script tag (prevents prefetching before auth)
    const script = document.createElement('script');
    script.type = 'module';
    script.src = '/assets/main-PLACEHOLDER.js'; // Replaced during build
    script.onload = () => {
      console.log('[Gateway] React app loaded successfully');
    };
    script.onerror = err => {
      console.error('[Gateway] Failed to load React app:', err);
      document.body.innerHTML = `
        <div style="padding:40px;text-align:center;font-family:sans-serif;">
          <h1 style="color:#d32f2f;">Failed to Load Application</h1>
          <p style="color:#666;margin:20px 0;">Please refresh the page or contact support if the problem persists.</p>
          <button onclick="window.location.reload()" style="padding:10px 20px;font-size:16px;cursor:pointer;">
            Refresh Page
          </button>
        </div>
      `;
    };
    document.head.appendChild(script);
  } else {
    console.log('[Gateway] Not authenticated, redirecting to /auth');

    const currentPath =
      window.location.pathname + window.location.search + window.location.hash;
    const returnUrl =
      currentPath !== '/'
        ? '?returnUrl=' + encodeURIComponent(currentPath)
        : '';

    window.location.href = '/auth' + returnUrl;
  }
})();
