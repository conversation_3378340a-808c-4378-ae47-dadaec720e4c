/**
 * Wrapper component for DraggableTable with conflict detection.
 * This is needed because we can't use hooks inside .map()
 */
import { useState } from 'react';
import { useDndMonitor } from '@dnd-kit/core';

import DraggableTable from '../organisms/table-dnd/DraggableTable';
import { Coordinates, Table } from '../organisms/table-dnd/types';
import {
  EntityConflictIndicator,
  useEntityConflictStatus,
} from './ConflictAwareEntity';

interface ConflictAwareDraggableTableProps {
  table: Table;
  index: number;
  record: any;
  selectedTable: number | null;
  tables: Table[];
  setSelectedTable: (index: number | null) => void;
  updateTable: (index: number, table: Partial<Table>) => void;
  elementsCollideHelper: (position: Coordinates, index: number) => void;
  /** Translation function */
  t: (key: string) => string;
}

export function ConflictAwareDraggableTable({
  table,
  index,
  record,
  selectedTable,
  tables,
  setSelectedTable,
  updateTable,
  elementsCollideHelper,
  t,
}: ConflictAwareDraggableTableProps) {
  const entityId = `number:${table.number}`;
  const status = useEntityConflictStatus(entityId);

  // Track if THIS table is being dragged (dnd-kit uses id = index + 1)
  const [isDragging, setIsDragging] = useState(false);

  useDndMonitor({
    onDragStart(event) {
      // dnd-kit uses id = index + 1 for draggable items
      if (event.active.id === index + 1) {
        setIsDragging(true);
      }
    },
    onDragEnd() {
      setIsDragging(false);
    },
    onDragCancel() {
      setIsDragging(false);
    },
  });

  return (
    <>
      <DraggableTable
        id={index}
        position={table.position}
        shape={table.shape}
        number={table.number}
        tag={table.tag}
        label={record.label ?? ''}
        selected={selectedTable === index}
        error={!!tables[index].error}
        boxShadow={status.boxShadowStyle}
        updateTable={(newTable: any) => {
          setSelectedTable(index);
          updateTable(index, newTable);
          elementsCollideHelper(tables[index].position, index);
        }}
      />
      {status.hasIndicator && !isDragging && (
        <EntityConflictIndicator
          entityId={entityId}
          entityLabel={`${t('floorPlansPage.table')} ${table.number}`}
          style={{
            position: 'absolute',
            top: table.position.startY - 10,
            left: table.position.endX - 10,
            zIndex: 200,
          }}
        />
      )}
    </>
  );
}

export default ConflictAwareDraggableTable;
