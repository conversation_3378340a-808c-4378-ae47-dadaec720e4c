/**
 * Standard image size definitions used across the application
 * These sizes are generated for every uploaded image and known by the backend
 */

export interface StandardImageSize {
    key: string; // Unique identifier (e.g., 'thumbnail', 'card')
    name: string; // Human-readable name
    width: number; // Target width in pixels
    height: number; // Target height in pixels
    aspectRatio: number; // Calculated aspect ratio (width/height)
    description?: string; // Optional description of usage
    isDefault?: boolean; // Whether this size is always generated
}

/**
 * Standard image sizes used throughout the application
 * Backend will know to serve these specific sizes
 */
export const STANDARD_IMAGE_SIZES: Record<string, StandardImageSize> = {
    // Automatic thumbnail (always required for public images)
    thumbnail: {
        key: 'thumbnail',
        name: 'Thumbnail',
        width: 50,
        height: 50,
        aspectRatio: 1,
        description: 'Automatic thumbnail for all public images',
        isDefault: true, // Always generated
    },

    // Square formats (1:1 aspect ratio)
    square_xs: {
        key: 'square_xs',
        name: 'Square XSmall',
        width: 100,
        height: 100,
        aspectRatio: 1,
        description: 'XSmall square image for lists and cards',
    },
    square_s: {
        key: 'square_s',
        name: 'Square Small',
        width: 150,
        height: 150,
        aspectRatio: 1,
        description: 'Small square image for lists and cards',
    },
    square_m: {
        key: 'square_m',
        name: 'Square Medium',
        width: 300,
        height: 300,
        aspectRatio: 1,
        description: 'Medium square image for card displays',
    },
    square_l: {
        key: 'square_l',
        name: 'Square Large',
        width: 600,
        height: 600,
        aspectRatio: 1,
        description: 'Large square image for detail views',
    },

    // Logo formats
    logo_receipt: {
        key: 'logo_receipt',
        name: 'Logo Receipt',
        width: 450,
        height: 150,
        aspectRatio: 3,
        description: 'Logo format for receipts and print materials',
    },
    logo_ordernow_s: {
        key: 'logo_ordernow_s',
        name: 'Logo OrderNow Small',
        width: 150,
        height: 40,
        aspectRatio: 3.75,
        description: 'Logo small format for OrderNow',
    },
    logo_ordernow_m: {
        key: 'logo_ordernow_m',
        name: 'Logo OrderNow Medium',
        width: 225,
        height: 60,
        aspectRatio: 3.75,
        description: 'Logo medium format for OrderNow',
    },
    logo_ordernow_l: {
        key: 'logo_ordernow_l',
        name: 'Logo OrderNow Large',
        width: 300,
        height: 80,
        aspectRatio: 3.75,
        description: 'Logo large format for OrderNow',
    },

    // Mobile intro formats
    mobile_intro_standard: {
        key: 'mobile_intro_standard',
        name: 'Mobile Intro Standard',
        width: 750,
        height: 1334,
        aspectRatio: 9 / 16,
        description: 'Standard mobile intro size, adaptable for most devices',
    },
    mobile_intro_wide: {
        key: 'mobile_intro_wide',
        name: 'Mobile Intro Wide',
        width: 828,
        height: 1792,
        aspectRatio: 9 / 19.5,
        description: 'Wide mobile intro size for modern tall screens',
    },
} as const;

/**
 * Get all default sizes that should always be generated
 */
export const getDefaultImageSizes = (): StandardImageSize[] => {
    return Object.values(STANDARD_IMAGE_SIZES).filter(size => size.isDefault);
};

/**
 * Get sizes by aspect ratio for grouping
 */
export const getSizesByAspectRatio = (): Record<
    string,
    StandardImageSize[]
> => {
    const grouped: Record<string, StandardImageSize[]> = {};

    Object.values(STANDARD_IMAGE_SIZES).forEach(size => {
        const ratio = size.aspectRatio.toString();
        if (!grouped[ratio]) {
            grouped[ratio] = [];
        }
        grouped[ratio].push(size);
    });

    return grouped;
};

/**
 * Get sizes by keys
 */
export const getSizesByKeys = (keys: string[]): StandardImageSize[] => {
    return keys.map(key => STANDARD_IMAGE_SIZES[key]).filter(Boolean);
};

/**
 * Convert standard sizes to TargetSize format for image editor
 */
export const standardSizesToTargetSizes = (sizeKeys: string[]) => {
    return sizeKeys
        .map(key => STANDARD_IMAGE_SIZES[key])
        .filter(Boolean)
        .map(size => ({
            width: size.width,
            height: size.height,
            name: size.key, // Use key as name for consistency
        }));
};

/**
 * Get all available size keys for selection
 */
export const getAvailableSizeKeys = (): string[] => {
    return Object.keys(STANDARD_IMAGE_SIZES);
};

/**
 * Predefined size combinations for common use cases
 */
export const PREDEFINED_SIZE_COMBINATIONS = {
    // Core working combinations
    squares: ['square_s', 'square_m', 'square_l'],
    logos: ['logo_receipt'],
    order_now_logos: ['logo_ordernow'],
    mobile_intros: ['mobile_intro_standard', 'mobile_intro_wide'],
} as const;

export type SizeCombinationKey = keyof typeof PREDEFINED_SIZE_COMBINATIONS;
