# Sanitized Input Components

> **Prevent invisible character issues from copy-pasted content**

This package provides pre-wrapped versions of common input components with automatic text sanitization. It solves the critical problem of invisible characters (zero-width spaces, control characters, etc.) being copied from Excel, PDF, Word, and other documents into your application's input fields.

## 🚨 Problem It Solves

When users copy text from external sources (Excel, PDF, Word), invisible Unicode characters can be included:

- Zero-width spaces (U+200B)
- Non-breaking spaces (U+00A0)
- Byte order marks (U+FEFF)
- Control characters
- Various other whitespace characters

These invisible characters can cause:

- ❌ Database issues
- ❌ API failures
- ❌ POS/cash register crashes
- ❌ Validation errors
- ❌ Search/filter problems

## ✅ Solution

Use sanitized input components that automatically clean user input in real-time.

## 📦 Quick Start

### Option 1: Import Sanitized Components

**Before:**

```tsx
import { Autocomplete, TextField } from '@mui/material';

<TextField
  label="Display Name"
  value={displayName}
  onChange={e => setDisplayName(e.target.value)}
/>;
```

**After:**

```tsx
import {
  SanitizedAutocomplete,
  SanitizedTextField,
} from '~/components/atoms/inputs/sanitized';

<SanitizedTextField
  label="Display Name"
  value={displayName}
  onChange={e => setDisplayName(e.target.value)}
/>;
```

### Option 2: Alias Import (Drop-in Replacement)

```tsx
import {
  SanitizedAutocomplete as Autocomplete,
  SanitizedTextField as TextField,
} from '~/components/atoms/inputs/sanitized';

// Use exactly as before - no other code changes needed
<TextField label="Name" value={name} onChange={handleChange} />;
```

## 📚 Available Components

### MUI Components

```tsx
import {
  SanitizedAutocomplete, // Autocomplete with free text
  SanitizedFilledInput,
  SanitizedInput,
  SanitizedOutlinedInput,
  SanitizedTextField, // Single-line text
  SanitizedTextFieldIdentifier, // SKU/codes (auto-uppercase)
  SanitizedTextFieldMultiline, // Multiline text (preserves line breaks)
} from '~/components/atoms/inputs/sanitized';
```

### React-Admin Components

```tsx
import {
  SanitizedAutocompleteArrayInput,
  SanitizedAutocompleteInput,
  SanitizedPasswordInput,
  SanitizedTextInput, // Single-line text
  SanitizedTextInputIdentifier, // SKU/codes (auto-uppercase)
  SanitizedTextInputMultiline, // Multiline text
} from '~/components/atoms/inputs/sanitized';
```

### Custom Components

```tsx
import {
  SanitizedCodeInput,
  SanitizedCustomInput, // CustomInput wrapper
  SanitizedMuiCustomInput, // MuiCustomInput wrapper
  SanitizedPhoneNumberInput,
} from '~/components/atoms/inputs/sanitized';
```

## 🎯 Usage Examples

### Example 1: MUI TextField (Local State)

```tsx
import { useState } from 'react';

import { SanitizedTextField } from '~/components/atoms/inputs/sanitized';

function MyComponent() {
  const [name, setName] = useState('');

  return (
    <SanitizedTextField
      label="Product Name"
      value={name}
      onChange={e => setName(e.target.value)}
      required
    />
  );
}
```

### Example 2: MUI Autocomplete (Freesolo)

```tsx
import { SanitizedAutocomplete } from '~/components/atoms/inputs/sanitized';

<SanitizedAutocomplete
  freeSolo
  options={existingItems}
  value={itemName}
  onInputChange={(e, newValue) => setItemName(newValue)}
  renderInput={params => <TextField {...params} label="Item" />}
/>;
```

### Example 3: React-Admin Form

```tsx
import { SimpleForm } from 'react-admin';

import { SanitizedTextInput } from '~/components/atoms/inputs/sanitized';

<SimpleForm>
  <SanitizedTextInput source="name" label="Name" required />
  <SanitizedTextInput source="email" label="Email" />
</SimpleForm>;
```

### Example 4: Multiline Text

```tsx
import { SanitizedTextFieldMultiline } from '~/components/atoms/inputs/sanitized';

<SanitizedTextFieldMultiline
  label="Description"
  value={description}
  onChange={e => setDescription(e.target.value)}
  multiline
  rows={4}
/>;
```

### Example 5: Identifiers (SKU, Codes)

```tsx
import { SanitizedTextFieldIdentifier } from '~/components/atoms/inputs/sanitized';

// Automatically converts to uppercase and sanitizes
<SanitizedTextFieldIdentifier
  label="SKU"
  value={sku}
  onChange={e => setSku(e.target.value)}
/>;
```

## 🔧 Advanced Usage

### Create Custom Sanitized Component

```tsx
import { withSanitization } from '~/components/atoms/inputs/sanitized';
import MyCustomInput from './MyCustomInput';

const SanitizedMyCustomInput = withSanitization(MyCustomInput, {
  handlers: ['onChange'],
  trimWhitespace: true,
  removeInvisibleChars: true,
  normalizeSpaces: true,
});
```

### Custom Sanitization Options

```tsx
import { TextField } from '@mui/material';

import { withSanitization } from '~/components/atoms/inputs/sanitized';

const StrictTextField = withSanitization(TextField, {
  trimWhitespace: true,
  removeInvisibleChars: true,
  normalizeSpaces: true,
  uppercase: true, // Convert to uppercase
  handlers: ['onChange', 'onBlur'], // Sanitize on both events
  debug: true, // Log sanitization actions
});
```

### Using Sanitization Presets

```tsx
import {
  IDENTIFIER_PRESET,
  MULTI_LINE_PRESET,
  SINGLE_LINE_PRESET,
} from '~/components/atoms/inputs/sanitized';
import { withSanitization } from '~/utils/withSanitization';

// Single-line input (default)
const Input1 = withSanitization(TextField, SINGLE_LINE_PRESET);

// Multiline input (preserves line breaks)
const Input2 = withSanitization(TextField, MULTI_LINE_PRESET);

// Identifier input (uppercase, strict)
const Input3 = withSanitization(TextField, IDENTIFIER_PRESET);
```

### Direct Sanitization (Without Component Wrapper)

```tsx
import { sanitizeTextInput } from '~/components/atoms/inputs/sanitized';

// Sanitize before saving to database
const handleSubmit = data => {
  const cleanData = {
    ...data,
    name: sanitizeTextInput(data.name),
    description: sanitizeTextInput(data.description, {
      preserveLineBreaks: true,
    }),
  };

  saveToDatabase(cleanData);
};
```

## 🛡️ What Gets Sanitized?

| Character Type              | Action             | Example                               |
| --------------------------- | ------------------ | ------------------------------------- |
| Leading/trailing whitespace | Removed            | `"  Hello  "` → `"Hello"`             |
| Zero-width space (U+200B)   | Removed            | `"Hello​World"` → `"HelloWorld"`      |
| Non-breaking space (U+00A0) | Converted to space | `"Hello World"` → `"Hello World"`     |
| Multiple spaces             | Normalized         | `"Hello    World"` → `"Hello World"`  |
| Control characters          | Removed            | `"Hello\u0000World"` → `"HelloWorld"` |
| Byte order mark (U+FEFF)    | Removed            | Invisible BOM removed                 |

## 📋 Migration Guide

### Gradual Migration (Recommended)

**Step 1:** Start with critical inputs (product names, SKUs, etc.)

```tsx
// Before
import { TextField } from '@mui/material';

// After
import { SanitizedTextField as TextField } from '~/components/atoms/inputs/sanitized';
```

**Step 2:** Test thoroughly in development

**Step 3:** Roll out to production

**Step 4:** Migrate other inputs as needed

### Selective Usage

You don't need to sanitize ALL inputs. Focus on:

- ✅ Product names
- ✅ Category names
- ✅ SKUs and identifiers
- ✅ Customer/merchant names
- ✅ Any text sent to POS/cash registers
- ✅ Free-text inputs (autocomplete)
- ❌ Numbers (not affected by invisible chars)
- ❌ Booleans
- ❌ Dates

## 🐛 Troubleshooting

### Issue: TypeScript errors

**Solution:** Make sure you're importing from the correct path:

```tsx
// Correct
import { SanitizedTextField } from '~/components/atoms/inputs/sanitized';

// Incorrect
import { SanitizedTextField } from '~/components/atoms/inputs';
```

### Issue: Sanitization not working

**Solution:** Check that you're using the sanitized version:

```tsx
// NOT sanitized
import { TextField } from '@mui/material';

// Sanitized
import { SanitizedTextField } from '~/components/atoms/inputs/sanitized';
```

### Issue: Want to see what's being sanitized

**Solution:** Enable debug mode:

```tsx
import { withSanitization } from '~/utils/withSanitization';

const DebugTextField = withSanitization(TextField, {
  debug: true, // Will log to console when sanitization occurs
});
```

## 📖 API Reference

### SanitizationOptions

```typescript
interface SanitizationOptions {
  trimWhitespace?: boolean; // Default: true
  removeInvisibleChars?: boolean; // Default: true
  normalizeSpaces?: boolean; // Default: true
  preserveLineBreaks?: boolean; // Default: false
  uppercase?: boolean; // Default: false
  lowercase?: boolean; // Default: false
}
```

### WithSanitizationConfig

```typescript
interface WithSanitizationConfig extends SanitizationOptions {
  handlers?: ('onChange' | 'onInputChange' | 'onBlur')[];
  debug?: boolean;
  customSanitizer?: (value: any) => any;
}
```

## 🎓 Best Practices

1. **Use sanitized components for user-facing inputs**
   - Especially inputs that accept copy-pasted content

2. **Choose the right variant**
   - `SanitizedTextField` for single-line text
   - `SanitizedTextFieldMultiline` for descriptions
   - `SanitizedTextFieldIdentifier` for SKUs/codes

3. **Test with real-world data**
   - Copy text from Excel, PDF, Word
   - Test with various languages

4. **Don't over-sanitize**
   - Numbers, booleans, dates don't need sanitization
   - Only sanitize text inputs

5. **Monitor in production**
   - Enable debug mode temporarily if issues arise
   - Check logs for sanitization patterns

## �️ What Gets Sanitized?

### Characters Removed (Invisible/Problematic)

| Character Type               | Unicode                      | Source         | Example                                     |
| ---------------------------- | ---------------------------- | -------------- | ------------------------------------------- |
| **Zero-width characters**    |
| Zero-width space             | U+200B                       | PDF/HTML       | `"Hello​World"` → `"HelloWorld"`            |
| Zero-width non-joiner        | U+200C                       | PDF/HTML       | Removed                                     |
| Zero-width joiner            | U+200D                       | PDF/HTML       | Removed                                     |
| Word joiner                  | U+2060                       | PDF            | Removed                                     |
| Byte order mark (BOM)        | U+FEFF                       | Files          | `"﻿Hello"` → `"Hello"`                      |
| **Direction marks**          |
| Left-to-right mark           | U+200E                       | HTML/Word      | Removed                                     |
| Right-to-left mark           | U+200F                       | HTML/Word      | Removed                                     |
| Directional embedding        | U+202A-U+202E                | HTML/Word      | Removed                                     |
| **Whitespace normalization** |
| Leading/trailing spaces      | -                            | All            | `"  Hello  "` → `"Hello"`                   |
| Non-breaking space           | U+00A0                       | Word/Excel     | `"Hello World"` → `"Hello World"` (regular) |
| Multiple spaces              | -                            | All            | `"Hello    World"` → `"Hello World"`        |
| Various Unicode spaces       | U+1680, U+2000-U+200A, etc.  | Various        | Converted to regular space                  |
| **Other invisible**          |
| Soft hyphen                  | U+00AD                       | PDF            | `"Prod­uct"` → `"Product"`                  |
| Control characters           | U+0000-U+001F, U+007F-U+009F | Various        | Removed                                     |
| Invisible operators          | U+2061-U+2064                | Technical docs | Removed                                     |
| Format characters            | U+206A-U+206F                | Various        | Removed                                     |

### Characters Preserved (Visible)

| Type                     | Unicode Range           | Examples            | Status                      |
| ------------------------ | ----------------------- | ------------------- | --------------------------- |
| **Emojis**               | U+1F300-U+1F9FF, others | 😀 🎉 ❤️ ☕ 🎁      | ✅ Kept                     |
| **Latin accents**        | U+00C0-U+024F           | é ñ ü ç à ö         | ✅ Kept                     |
| **Thai**                 | U+0E00-U+0E7F           | สวัสดี ขอบคุณ       | ✅ Kept                     |
| **Greek**                | U+0370-U+03FF           | Γεια σου Ελλάδα     | ✅ Kept                     |
| **Cyrillic (Bulgarian)** | U+0400-U+04FF           | Здравей България    | ✅ Kept                     |
| **Chinese**              | U+4E00-U+9FFF           | 你好 世界           | ✅ Kept                     |
| **Japanese**             | U+3040-U+30FF, others   | こんにちは カタカナ | ✅ Kept                     |
| **Arabic**               | U+0600-U+06FF           | مرحبا العالم        | ✅ Kept                     |
| **Korean**               | U+AC00-U+D7AF           | 안녕하세요          | ✅ Kept                     |
| **Hebrew**               | U+0590-U+05FF           | שלום                | ✅ Kept                     |
| **Normal spaces**        | U+0020                  | Regular space       | ✅ Kept (inside text)       |
| **Newlines**             | U+000A                  | Line breaks         | ✅ Kept (in multiline mode) |

### Before/After Examples

```typescript
import { sanitizeTextInput } from '~/components/atoms/inputs/sanitized';

// PDF paste with invisible characters
sanitizeTextInput('\uFEFF  Product\u200BName\u00AD\u200E  ');
// Output: "ProductName"

// Word document with emojis and accents
sanitizeTextInput('  Café ☕ Münchën\u00A0Special\u200E  ');
// Output: "Café ☕ Münchën Special"

// Excel with international characters
sanitizeTextInput('  สินค้า\u200Bพิเศษ 😀  ');
// Output: "สินค้าพิเศษ 😀"

// Mixed content (Thai + Greek + emoji + invisible)
sanitizeTextInput('  สวัสดี\u200BΓεια\u200E😀  ');
// Output: "สวัสดีΓεια😀"

// SKU from PDF
sanitizeTextInput('SKU-\u200B123\u00AD456\u200E');
// Output: "SKU-123456"
```

## 🔗 Related Files

- **Core Utility:** `src/utils/sanitizeTextInput.ts`
- **HOC Wrapper:** `src/utils/withSanitization.tsx`
- **MUI Components:** `src/components/atoms/inputs/sanitized/mui.ts`
- **React-Admin Components:** `src/components/atoms/inputs/sanitized/react-admin.ts`
- **Custom Components:** `src/components/atoms/inputs/sanitized/custom.ts`

## 📝 Contributing

When adding new sanitized components:

1. Add to appropriate file (`mui.ts`, `react-admin.ts`, or `custom.ts`)
2. Export from `index.ts`
3. Add usage examples to this README
4. Test with copy-pasted content from various sources

---

**Last Updated:** October 27, 2025
