import { useCallback, useEffect, useMemo, useState } from 'react';

import { UploadedFile, UrlGenerationOptions } from '../types/fileUpload';
import { getSecureFileUrl } from '../utils/bucketManager';

interface UseFileUrlReturn {
  url: string | null;
  loading: boolean;
  error: Error | null;
  refresh: () => void;
}

// Internal cache to track URL generation timestamps and URLs for fallback
const urlTimestamps = new Map<string, number>();
const urlCache = new Map<string, string>();

// Request deduplication to prevent multiple simultaneous requests for the same file
const pendingRequests = new Map<string, Promise<string>>();

// Cache cleanup configuration
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

// Periodic cache cleanup to prevent memory leaks
// Note: Only clean up non-blob URLs to avoid conflicts with InMemoryFileManager
setInterval(() => {
  const now = Date.now();

  for (const [key, timestamp] of urlTimestamps.entries()) {
    if (now - timestamp > CACHE_TTL) {
      const url = urlCache.get(key);
      // Don't revoke blob URLs - let InMemoryFileManager handle those
      if (url && !url.startsWith('blob:')) {
        // Only clean up non-blob URLs (Firebase storage URLs, etc.)
      }
      urlCache.delete(key);
      urlTimestamps.delete(key);
    }
  }
}, CLEANUP_INTERVAL);

/**
 * Clear cache entries for a specific file when it transitions from temp to permanent
 * This should be called by the file upload system when files are moved
 * Note: Only clears cache entries - does NOT revoke blob URLs (InMemoryFileManager owns those)
 */
export const clearFileUrlCache = (file: UploadedFile) => {
  // Clear both temp and permanent cache entries for this file
  const tempKey = `temp:${file.t}:${file.f}.${file.e}:`;
  const permKey = `perm:${file.t}:${file.f}.${file.e}:`;

  // Clear all variants for this file
  const clearedKeys: string[] = [];
  for (const [key] of urlCache.entries()) {
    if (key.startsWith(tempKey) || key.startsWith(permKey)) {
      urlCache.delete(key);
      urlTimestamps.delete(key);
      clearedKeys.push(key);
    }
  }
};

/**
 * Custom hook for managing file URLs with secure access and temp file blocking
 * Uses the new SecureFileManager for proper security and caching
 */
export function useFileUrl(
  file: UploadedFile | null,
  options?: UrlGenerationOptions | null
): UseFileUrlReturn {
  const [url, setUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Memoize the options to prevent unnecessary re-renders
  // Handle explicit null to prevent URL generation
  const stableOptions = useMemo(
    () => options,
    [
      options === null ? 'null' : 'not-null', // Track null explicitly
      options?.imageVariant,
      options?.forceRefresh,
      options?.context?.accountId,
      options?.context?.sellpointId,
      options?.context?.customPath,
    ]
  );

  // Create a more comprehensive cache key that includes bucket type and variant
  const fileKey = useMemo(() => {
    if (!file) return null;

    // If options is explicitly null, don't generate a fileKey
    if (options === null) {
      return null;
    }

    const bucketType = file.x ? 'temp' : 'perm';
    const variant = stableOptions?.imageVariant || 'default';
    return `${bucketType}:${file.t}:${file.f}.${file.e}:${variant}`;
  }, [
    file?.f,
    file?.e,
    file?.t,
    file?.x,
    stableOptions?.imageVariant,
    options,
  ]);

  const generateUrl = useCallback(async () => {
    if (!file || !fileKey) {
      setUrl(null);
      setError(null);
      return;
    }

    // If stableOptions is explicitly null, don't generate URL yet
    if (stableOptions === null) {
      setUrl(null);
      setError(null);
      return;
    }

    // Check if we already have a cached URL for this file key
    const cachedUrl = urlCache.get(fileKey);

    if (cachedUrl && !stableOptions?.forceRefresh) {
      setUrl(cachedUrl);
      return;
    }

    // Check if there's already a pending request for this file key
    const pendingRequest = pendingRequests.get(fileKey);
    if (pendingRequest && !stableOptions?.forceRefresh) {
      try {
        const url = await pendingRequest;
        setUrl(url);
        return;
      } catch (err) {
        // If pending request failed, continue with new request
      }
    }

    // Capture the current fileKey to prevent race conditions
    const requestFileKey = fileKey;

    console.log(
      '🚀 [useFileUrl] Starting URL generation for:',
      file.f,
      'key:',
      fileKey
    );
    setLoading(true);
    setError(null);

    // Create a new request promise and cache it to prevent duplicates
    const requestPromise = getSecureFileUrl(file, stableOptions);
    pendingRequests.set(fileKey, requestPromise);

    try {
      const newUrl = await requestPromise;
      console.log(
        '🔍 [useFileUrl] Generated new URL:',
        newUrl,
        'for key:',
        requestFileKey,
        'File:',
        file.f
      );

      // Only update state if this request is still for the current fileKey
      // This prevents race conditions where an old request completes after a new one
      if (fileKey === requestFileKey) {
        setUrl(newUrl);
      }

      // Always cache the URL regardless of whether we set it
      urlCache.set(requestFileKey, newUrl);
      urlTimestamps.set(requestFileKey, Date.now());
    } catch (err) {
      const error = err as Error;

      // Only update error state if this request is still for the current fileKey
      if (fileKey === requestFileKey) {
        setError(error);
        setUrl(null);
      }
    } finally {
      setLoading(false);
      // Remove the pending request
      pendingRequests.delete(requestFileKey);
    }
  }, [file?.f, file?.e, file?.t, file?.x, fileKey, stableOptions]);

  const refresh = useCallback(() => {
    if (fileKey) {
      urlTimestamps.delete(fileKey);
      urlCache.delete(fileKey);
      pendingRequests.delete(fileKey); // Clear any pending requests too
    }
    generateUrl();
  }, [generateUrl, fileKey]);

  useEffect(() => {
    generateUrl();
  }, [generateUrl]);

  // Detect bucket transitions (temp to permanent) and refresh URLs
  useEffect(() => {
    if (!file || !fileKey) return;

    const handleBucketTransition = () => {
      // If file was temp but now is permanent, or vice versa, refresh URL
      const currentBucketType = file.x ? 'temp' : 'perm';
      const cachedKey = urlCache.get(fileKey);

      if (cachedKey) {
        // Extract bucket type from cached key
        const keyParts = fileKey.split(':');
        const keyBucketType = keyParts[0];

        if (keyBucketType !== currentBucketType) {
          // Bucket type changed, clear old cache and regenerate
          urlCache.delete(fileKey);
          urlTimestamps.delete(fileKey);
          generateUrl();
        }
      }
    };

    handleBucketTransition();
  }, [file?.x, fileKey, generateUrl]); // React to changes in the temp flag

  // Note: Blob URL cleanup is handled by InMemoryFileManager
  // Don't revoke blob URLs here to avoid conflicts

  return { url, loading, error, refresh };
}

/**
 * Example usage in a component:
 *
 * const MyComponent = ({ file }: { file: UploadedFile }) => {
 *   const { url, loading, error, refresh } = useFileUrl(file);
 *
 *   if (loading) return <Spinner />;
 *   if (error) return <Button onClick={refresh}>Retry</Button>;
 *   if (!url) return null;
 *
 *   return <img src={url} alt={file.f} />;
 * };
 */
