import { useEffect, useState } from 'react';
import {
  Dnd<PERSON>ontext,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  createSnapModifier,
  restrictToFirstScrollableAncestor,
} from '@dnd-kit/modifiers';
import { Box, Button } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { SaveButton, useRedirect } from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  ConflictAwareDraggableTable,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import ModalTitleWithMenu from '~/components/molecules/ModalTitleWithMenu';
import { useConflictDetectionOptional } from '~/hooks/useConflictDetection';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { RESOURCES } from '~/providers/resources';
import { BackgroundGrid } from '../../components/molecules/BackgroundGrid';
import ModalHeader from '../../components/molecules/ModalHeader';
import AddNewTable from '../../components/organisms/table-dnd/AddNewTable';
import TableDndSideMenu from '../../components/organisms/table-dnd/TableDndSideMenu';
import { Coordinates, Table } from '../../components/organisms/table-dnd/types';
import { elementsCollide } from '../../utils/checkCollision';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import { SectionEditDialog } from './SectionEditDialog';

export const GRID_SIZE = 10;
export const GRID_WIDTH = 102;
export const GRID_HEIGHT = 62;
export const TABLES_BREAKPOINT = 40;
export const TABLE_SIZE = 10;
export const SMALL_TABLE_SIZE = 8;

function SectionEditInner() {
  const redirect = useRedirect();
  const { setValue, watch, getValues } = useFormContext();

  // Get conflict detection context to track changes manually
  const conflictCtx = useConflictDetectionOptional();

  // TODO: ASTA FACE RERENDER-URI INCONTINUU.
  const record = getValues();
  const [showAddTable, setShowAddTable] = useState<boolean>(false);
  const [tables, setTables] = useState<Table[]>([]);
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [nextTableNumber, setNextTableNumber] = useState<number>(0);

  const snapToGrid = createSnapModifier(GRID_SIZE);
  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor));

  useEffect(() => {
    setTables(record?.items || []);
  }, [record]);

  useEffect(() => {
    const subscription = watch(value => {
      setTables([...value.items]);
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (tables.length > 0) {
      const highestNumber = Math.max(...tables.map(table => table.number));
      setNextTableNumber(highestNumber + 1);
    }
  }, [tables]);

  const getCoordFromDelta = (
    oldCoord: Coordinates,
    deltaX: number,
    deltaY: number
  ): Coordinates => {
    return {
      startX: +oldCoord.startX + deltaX,
      startY: +oldCoord.startY + deltaY,
      endX: +oldCoord.endX + deltaX,
      endY: +oldCoord.endY + deltaY,
    };
  };

  const updateTable = (index: number, table: Partial<Table>) => {
    const tmp = { ...tables[index], ...table };
    setValue(`items.${index}`, tmp, {
      shouldDirty: true,
    });

    // Track changes for conflict detection
    // Track each changed property at the table level
    if (conflictCtx) {
      Object.keys(table).forEach(key => {
        conflictCtx.trackChange(`items[${index}].${key}`, (table as any)[key]);
      });
    }
  };

  const deleteTable = () => {
    if (selectedTable == null) return;

    const tmp = [...tables];
    tmp.splice(selectedTable, 1);

    setValue('items', tmp, {
      shouldDirty: true,
    });

    // Track the entire items array change for deletion
    if (conflictCtx) {
      conflictCtx.trackChange('items', tmp);
    }

    // Reset selected table since it no longer exists
    setSelectedTable(null);
  };

  const elementsCollideHelper = (newCoord: Coordinates, index: number) => {
    for (let i = 0; i < tables.length; i++) {
      let collision = false;
      for (let j = 0; j < tables.length; j++) {
        if (i === j) continue;
        const pos1 = index === i ? newCoord : tables[i].position;
        const pos2 = index === j ? newCoord : tables[j].position;

        if (elementsCollide(pos1, pos2)) {
          collision = true;
          break;
        }
      }

      setTables(prev => {
        const t = [...prev];
        t[i].error = collision;
        return t;
      });
    }

    setSelectedTable(index);
  };

  const onDragEndHelper = (index: number, deltaX: number, deltaY: number) => {
    const newCoord = getCoordFromDelta(tables[index].position, deltaX, deltaY);
    elementsCollideHelper(newCoord, index);
    updateTable(index, { position: newCoord });
  };

  const addTable = (name: number, tag: string | undefined = undefined) => {
    // remove empty tag
    if (
      tag !== undefined &&
      typeof tag === 'string' &&
      tag.trim().length === 0
    ) {
      tag = undefined;
    }
    // check if table with same number or tag already exists
    for (let i = 0; i < tables.length; i++) {
      if (tables[i].number === name) {
        return false;
      }
    }

    let tableSize = TABLE_SIZE;
    if (tables.length >= TABLES_BREAKPOINT) {
      tableSize = SMALL_TABLE_SIZE;
    }

    const position = {
      startX: 0,
      startY: 0,
      endX: GRID_SIZE * tableSize,
      endY: GRID_SIZE * tableSize,
    };

    const newItems = [
      ...tables,
      {
        number: name,
        tag,
        position,
        shape: 'square',
      },
    ];

    setValue(`items`, newItems, {
      shouldDirty: true,
    });

    // Track the entire items array change for addition
    if (conflictCtx) {
      conflictCtx.trackChange('items', newItems);
    }

    elementsCollideHelper(position, tables.length);
    return true;
  };

  const handleClose = () => {
    redirect('list', RESOURCES.FLOOR_PLANS);
  };
  const { t } = useTranslation();

  const [sectionEditModalOpen, setSectionEditModalOpen] = useState(false);

  const { sellPointId } = useGlobalResourceFilters();

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={
          <ModalTitleWithMenu
            name={record?.name}
            id={record?.id}
            editButtonText={t('floorPlansPage.editFloorPlan')}
            deleteButtonText={t('floorPlansPage.deleteFloorPlan')}
            editModal={
              <SectionEditDialog
                open={sectionEditModalOpen}
                id={record?.id}
                onClose={() => setSectionEditModalOpen(false)}
              />
            }
            setOpenEditModal={setSectionEditModalOpen}
            deleteOptions={{
              mutationMode: 'pessimistic',
              mutationOptions: { meta: { sellPointId: sellPointId } },
            }}
          />
        }
      >
        <Button
          onClick={() => setShowAddTable(true)}
          // @ts-ignore
          variant="contained-light"
        >
          {t('floorPlansPage.addTable')}
        </Button>
        <SaveButton
          type="button"
          disabled={tables.some(el => el.error)}
          icon={<></>}
          sx={{ ml: { xs: 1, md: 2 } }}
          label={t('shared.save')}
        />
      </ModalHeader>

      <Box
        sx={{
          flex: 1,
          width: '100%',
          height: 'fit-content',
          pt: { xs: '115px', sm: '20px' },
          pb: '30px',
          pl: { xs: '15px', sm: '155px', xl: 0 },
          pr: { xs: '15px', xl: 0 },
          overflowX: 'auto',
        }}
      >
        <TableDndSideMenu
          table={selectedTable !== null ? tables[selectedTable] : null}
          updateTable={(newTable: any) => {
            updateTable(Number(selectedTable), newTable);
            elementsCollideHelper(
              tables[Number(selectedTable)].position,
              Number(selectedTable)
            );
          }}
          deleteTable={deleteTable}
        />

        {/* div needed in order to deselect table on click outside */}
        <Box
          onClick={() => {
            setSelectedTable(null);
          }}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
        />
        <Box
          sx={{
            position: 'relative',
            width: 'fit-content',
            mx: 'auto',
            overflowX: 'auto',
            borderStyle: 'solid',
            borderWidth: '20px 60px 20px 20px',
            borderColor: 'black',
            borderRadius: '20px',
            zIndex: 10,
          }}
        >
          <Box
            sx={{
              position: 'relative',
              width: GRID_WIDTH * GRID_SIZE,
              height: GRID_HEIGHT * GRID_SIZE,
              overflow: 'auto',
            }}
          >
            <DndContext
              sensors={sensors}
              onDragStart={({ active }) => {
                setSelectedTable(Number(active.id) - 1);
              }}
              onDragEnd={({ active, delta }) => {
                onDragEndHelper(Number(active.id) - 1, delta.x, delta.y);
              }}
              onDragMove={({ active, delta }) => {
                const newCoord = getCoordFromDelta(
                  tables[Number(active.id) - 1].position,
                  delta.x,
                  delta.y
                );
                elementsCollideHelper(newCoord, Number(active.id) - 1);
              }}
              modifiers={[snapToGrid, restrictToFirstScrollableAncestor]}
            >
              {tables.map((table, index) => {
                return (
                  <ConflictAwareDraggableTable
                    key={index}
                    table={table}
                    index={index}
                    record={record}
                    selectedTable={selectedTable}
                    tables={tables}
                    setSelectedTable={setSelectedTable}
                    updateTable={updateTable}
                    elementsCollideHelper={elementsCollideHelper}
                    t={t}
                  />
                );
              })}

              <BackgroundGrid
                tileWidth={GRID_SIZE}
                tileHeight={GRID_SIZE}
                onClick={() => setSelectedTable(null)}
                style={{ borderRadius: 0 }}
              />
            </DndContext>
          </Box>
        </Box>
      </Box>

      <AddNewTable
        open={showAddTable}
        handleClose={() => setShowAddTable(false)}
        addTable={addTable}
        nextTableNumber={nextTableNumber}
      />
    </>
  );
}

export default function SectionEdit(props: { sellPointId: string }) {
  const { sellPointId } = props;

  if (!sellPointId) return null;

  return (
    <>
      <EditDialog
        {...getFullscreenModalProps()}
        mutationMode="pessimistic"
        mutationOptions={{ meta: { sellPointId: sellPointId } }}
        queryOptions={{ meta: { sellPointId: sellPointId } }}
      >
        <ConflictAwareSimpleForm
          toolbar={false}
          sx={{
            p: '0 !important',
            height: '100vh',
            '> div': { height: '100%' },
          }}
          translationNamespace="shared"
          queryMeta={{ sellPointId: sellPointId }}
          conflictConfig={{
            // Tables in the items array use 'number' as their identifier, not 'id'
            // This enables granular conflict detection: User A can move table 1 while User B moves table 2
            arrayIdFields: { items: 'number' },
            // Exclude runtime UI state fields from conflict detection
            excludeFields: ['error'],
          }}
        >
          <SectionEditInner />
        </ConflictAwareSimpleForm>
      </EditDialog>
    </>
  );
}
