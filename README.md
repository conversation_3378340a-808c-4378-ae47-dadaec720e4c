## How to run

After having cloned the repository, run the following commands at the react-admin root:

```sh
npm install

npm run dev
```

## Available Scripts

In the project directory, you can run:

### `npm run dev`

Runs the app in the development mode.<br>
Starts a local web server with Hot Module Replacement for development, and will automatically change when code changes.

### `npm run build`

Builds the app for production to the `dist` folder.<br>
It correctly bundles React in production mode and optimizes the build for the best performance.<br>
This also increments the version number automatically.

### `npm run build:dev`

Builds the app for development/testing to the `dist` folder.<br>
Similar to `npm run build` but does not increment the version number.

### `npm run build:analyze`

Builds the app and analyzes the bundle size.<br>
Useful for identifying large dependencies and optimizing bundle size.

### `npm run build:profile`

Builds the app with profiling enabled.<br>
Use this to analyze build performance and identify bottlenecks.

### `npm run preview`

Starts a local web server that serves the built solution from `dist` folder for previewing.<br>

- You need to run build before preview.
- Preview will always preview the latest build, and will not update automatically when code changes.

### `npm run format`

Formats all code files using <PERSON><PERSON><PERSON>.<br>
Automatically fixes formatting issues in JS, JSX, TS, TSX, JSON, CSS, SCSS, and MD files.

### `npm run format:check`

Checks if all code files are properly formatted without making changes.<br>
Useful for CI/CD pipelines to enforce code formatting standards.
