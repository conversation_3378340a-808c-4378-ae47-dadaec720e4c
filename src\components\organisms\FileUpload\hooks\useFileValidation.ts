import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FileUploadConfig, ValidationError, ValidationState } from '../types';
import {
  filterValidFiles,
  getValidationSummary,
  groupValidationErrors,
  hasValidationErrors,
  validateFile,
  validateFiles,
} from '../utils/fileUploadValidation';

/**
 * Hook for managing file validation
 */
export const useFileValidation = (config: FileUploadConfig) => {
  const { t } = useTranslation();
  const [state, setState] = useState<ValidationState>({
    errors: [],
    warnings: [],
    isValid: true,
  });

  // Validate a single file
  const validateSingleFile = useCallback(
    (file: File, existingFiles: File[] = []): ValidationError[] => {
      // Create a translation wrapper that ensures string return
      const tWrapper = (
        key: string,
        defaultValue: string = '',
        options?: any
      ): string => {
        const result = t(key, defaultValue, options);
        return typeof result === 'string' ? result : defaultValue;
      };
      return validateFile(file, config, existingFiles, tWrapper);
    },
    [config, t]
  );

  // Validate multiple files
  const validateMultipleFiles = useCallback(
    (files: File[], existingFiles: File[] = []): ValidationError[] => {
      // Create a translation wrapper that ensures string return
      const tWrapper = (
        key: string,
        defaultValue: string = '',
        options?: any
      ): string => {
        const result = t(key, defaultValue, options);
        return typeof result === 'string' ? result : defaultValue;
      };
      return validateFiles(files, config, existingFiles, tWrapper);
    },
    [config, t]
  );

  // Update validation state
  const updateValidationState = useCallback(
    (errors: ValidationError[]) => {
      const grouped = groupValidationErrors(errors);

      setState({
        errors: grouped.errors,
        warnings: grouped.warnings,
        isValid: !grouped.hasErrors,
      });

      // Call validation error callback if provided
      if (errors.length > 0 && config.callbacks?.onValidationError) {
        config.callbacks.onValidationError(errors);
      }
    },
    [config.callbacks]
  );

  // Validate files and update state
  const validate = useCallback(
    (files: File[], existingFiles: File[] = []): ValidationError[] => {
      const errors = validateMultipleFiles(files, existingFiles);
      updateValidationState(errors);
      return errors;
    },
    [validateMultipleFiles, updateValidationState]
  );

  // Clear validation state
  const clearValidation = useCallback(() => {
    setState({
      errors: [],
      warnings: [],
      isValid: true,
    });
  }, []);

  // Add validation error
  const addValidationError = useCallback((error: ValidationError) => {
    setState(prev => {
      const newErrors = [...prev.errors, ...prev.warnings, error];
      const grouped = groupValidationErrors(newErrors);

      return {
        errors: grouped.errors,
        warnings: grouped.warnings,
        isValid: !grouped.hasErrors,
      };
    });
  }, []);

  // Remove validation error by ID
  const removeValidationError = useCallback((errorId: string) => {
    setState(prev => {
      const allErrors = [...prev.errors, ...prev.warnings];
      const filteredErrors = allErrors.filter(error => error.id !== errorId);
      const grouped = groupValidationErrors(filteredErrors);

      return {
        errors: grouped.errors,
        warnings: grouped.warnings,
        isValid: !grouped.hasErrors,
      };
    });
  }, []);

  // Get valid files from a list
  const getValidFiles = useCallback(
    (files: File[]): File[] => {
      const allErrors = [...state.errors, ...state.warnings];
      return filterValidFiles(
        files,
        allErrors,
        config.validation?.allowInvalidFiles
      );
    },
    [state.errors, state.warnings, config.validation?.allowInvalidFiles]
  );

  // Check if specific file is valid
  const isFileValid = useCallback(
    (file: File): boolean => {
      const allErrors = [...state.errors, ...state.warnings];
      const fileErrors = allErrors.filter(
        error => error.file === file && error.severity === 'error'
      );
      return fileErrors.length === 0;
    },
    [state.errors, state.warnings]
  );

  // Get errors for specific file
  const getFileErrors = useCallback(
    (file: File): ValidationError[] => {
      const allErrors = [...state.errors, ...state.warnings];
      return allErrors.filter(error => error.file === file);
    },
    [state.errors, state.warnings]
  );

  // Computed values
  const hasErrors = useMemo(() => state.errors.length > 0, [state.errors]);
  const hasWarnings = useMemo(
    () => state.warnings.length > 0,
    [state.warnings]
  );
  const totalErrorCount = useMemo(
    () => state.errors.length + state.warnings.length,
    [state.errors, state.warnings]
  );

  const validationSummary = useMemo(() => {
    const allErrors = [...state.errors, ...state.warnings];
    return getValidationSummary(allErrors);
  }, [state.errors, state.warnings]);

  // Group errors by file
  const errorsByFile = useMemo(() => {
    const allErrors = [...state.errors, ...state.warnings];
    const grouped = new Map<File | undefined, ValidationError[]>();

    allErrors.forEach(error => {
      const file = error.file;
      if (!grouped.has(file)) {
        grouped.set(file, []);
      }
      grouped.get(file)!.push(error);
    });

    return grouped;
  }, [state.errors, state.warnings]);

  // Get errors that are not file-specific (general errors)
  const generalErrors = useMemo(() => {
    const allErrors = [...state.errors, ...state.warnings];
    return allErrors.filter(error => !error.file);
  }, [state.errors, state.warnings]);

  // Auto-validate when config changes
  useEffect(() => {
    // Clear validation when config changes significantly
    clearValidation();
  }, [
    config.fileType,
    config.maxFiles,
    config.maxSize,
    config.acceptedTypes,
    clearValidation,
  ]);

  return {
    // State
    errors: state.errors,
    warnings: state.warnings,
    isValid: state.isValid,

    // Actions
    validate,
    validateSingleFile,
    validateMultipleFiles,
    clearValidation,
    addValidationError,
    removeValidationError,

    // Utilities
    getValidFiles,
    isFileValid,
    getFileErrors,

    // Computed values
    hasErrors,
    hasWarnings,
    totalErrorCount,
    validationSummary,
    errorsByFile,
    generalErrors,
  };
};
