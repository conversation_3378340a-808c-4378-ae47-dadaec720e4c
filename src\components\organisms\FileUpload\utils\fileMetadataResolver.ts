import { getMetadata, ref } from 'firebase/storage';

import { PrivateFileContext, UploadedFile } from '../types/fileUpload';
import { getStorageInstanceForFile } from './bucketManager';
import { getDestinationPath } from './urlGeneration';

/**
 * Combined file metadata cache entry for immutable files
 * Stores both filename and size information together for efficiency
 */
interface FileMetadataCacheEntry {
  realName: string;
  originalSize?: number;
  variantsSize?: number;
  cached: number; // Timestamp when cached
}

/**
 * Unified file metadata cache leveraging the fact that uploaded files are immutable
 * Once metadata is resolved, it never changes, so we can cache indefinitely
 */
class FileMetadataCache {
  private cache = new Map<string, FileMetadataCacheEntry>();
  private readonly MAX_ENTRIES = 1000; // Reasonable limit

  private getCacheKey(
    file: UploadedFile,
    context?: PrivateFileContext
  ): string {
    const contextKey = context
      ? `${context.sellpointId || ''}-${context.customPath || ''}`
      : '';
    return `${file.f}.${file.e}-${file.t}-${contextKey}`;
  }

  get(
    file: UploadedFile,
    context?: PrivateFileContext
  ): FileMetadataCacheEntry | null {
    const key = this.getCacheKey(file, context);
    const entry = this.cache.get(key);
    return entry || null;
  }

  set(
    file: UploadedFile,
    realName: string,
    originalSize?: number,
    variantsSize?: number,
    context?: PrivateFileContext
  ): void {
    // Enforce size limit with simple LRU eviction
    if (this.cache.size >= this.MAX_ENTRIES) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    const key = this.getCacheKey(file, context);
    this.cache.set(key, {
      realName,
      originalSize,
      variantsSize,
      cached: Date.now(),
    });
  }

  clear(): void {
    this.cache.clear();
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.MAX_ENTRIES,
    };
  }
}

// Global cache instance
const fileMetadataCache = new FileMetadataCache();

/**
 * Extract filename without extension from metadata realName field
 */
const extractFilenameWithoutExtension = (realName: string): string => {
  const lastDotIndex = realName.lastIndexOf('.');
  return lastDotIndex > 0 ? realName.substring(0, lastDotIndex) : realName;
};

/**
 * Core function to get complete file metadata from Firebase Storage or temporary file data
 */
export async function getFileMetadata(
  file: UploadedFile,
  context?: PrivateFileContext
): Promise<{
  realName: string;
  originalSize?: number;
  variantsSize?: number;
} | null> {
  // For temporary files, get data from in-memory sources
  if (file.x) {
    const realName = file.rn || `${file.f}.${file.e}`;
    let originalSize: number | undefined;
    let variantsSize: number | undefined;

    // Get size info from in-memory data if available
    if (file.inMemoryData) {
      originalSize = file.inMemoryData.metadata.fileSize;

      // Calculate variants size from image metadata
      if (file.inMemoryData.metadata.imageMetadata?.variants) {
        variantsSize = file.inMemoryData.metadata.imageMetadata.variants.reduce(
          (total, variant) => total + variant.size,
          0
        );
      }
    }

    return {
      realName,
      originalSize,
      variantsSize: variantsSize && variantsSize > 0 ? variantsSize : undefined,
    };
  }

  // For permanent files, check cache first
  const cached = fileMetadataCache.get(file, context);
  if (cached) {
    return {
      realName: cached.realName,
      originalSize: cached.originalSize,
      variantsSize: cached.variantsSize,
    };
  }

  try {
    // Get file reference based on file type
    const storage = getStorageInstanceForFile(file.t, false);
    let filePath: string;

    if (file.t === 'i') {
      // For images, try to get metadata from original file in folder structure
      filePath = `i/${file.f}/original.${file.e}`;
    } else {
      // For other files, use destination path logic
      filePath = getDestinationPath(file, context);
    }

    const fileRef = ref(storage, filePath);
    const metadata = await getMetadata(fileRef);

    const realName = metadata.customMetadata?.realName || `${file.f}.${file.e}`;
    const originalSize = metadata.customMetadata?.originalSize
      ? parseInt(metadata.customMetadata.originalSize, 10)
      : undefined;
    const variantsSize = metadata.customMetadata?.variantsSize
      ? parseInt(metadata.customMetadata.variantsSize, 10)
      : undefined;

    // Cache the result
    fileMetadataCache.set(file, realName, originalSize, variantsSize, context);

    return {
      realName,
      originalSize,
      variantsSize,
    };
  } catch (error) {
    console.warn('Failed to retrieve file metadata:', error);
    // Fallback to constructed values
    const fallbackName = `${file.f}.${file.e}`;
    return {
      realName: fallbackName,
    };
  }
}

// =============================================================================
// FILENAME API (maintains compatibility with existing useRealFileName hooks)
// =============================================================================

/**
 * Get the real filename from Storage metadata for permanent files
 * For temporary files, returns the filename from the file object immediately
 */
export const getRealFileName = async (
  file: UploadedFile,
  context?: PrivateFileContext,
  options?: { withExtension?: boolean }
): Promise<string> => {
  const { withExtension = false } = options || {};

  const metadata = await getFileMetadata(file, context);
  if (!metadata) {
    const fallback = file.f;
    return withExtension ? `${fallback}.${file.e}` : fallback;
  }

  return withExtension
    ? metadata.realName
    : extractFilenameWithoutExtension(metadata.realName);
};

/**
 * Get the real filename with extension
 */
export const getRealFileNameWithExtension = async (
  file: UploadedFile,
  context?: PrivateFileContext
): Promise<string> => {
  return getRealFileName(file, context, { withExtension: true });
};

/**
 * Get both real filename and size information (for backward compatibility)
 */
export async function getRealFileNameAndSizes(
  file: UploadedFile,
  withExtension: boolean = true,
  context?: PrivateFileContext
): Promise<{
  realName: string;
  originalSize?: number;
  variantsSize?: number;
} | null> {
  const metadata = await getFileMetadata(file, context);
  if (!metadata) {
    return null;
  }

  return {
    realName: withExtension
      ? metadata.realName
      : extractFilenameWithoutExtension(metadata.realName),
    originalSize: metadata.originalSize,
    variantsSize: metadata.variantsSize,
  };
}

// =============================================================================
// FILE SIZE API (maintains compatibility with existing size utilities)
// =============================================================================

/**
 * Get file size information from Storage metadata for permanent files
 * For temporary files, returns the size from in-memory data immediately
 */
export const getFileSizeInfo = async (
  file: UploadedFile,
  context?: PrivateFileContext
): Promise<{
  original: number | null;
  variants: number | null;
}> => {
  // For permanent files with legacy sizes metadata, return immediately
  if (file.sizes) {
    return {
      original: file.sizes.original,
      variants: file.sizes.variants || null,
    };
  }

  const metadata = await getFileMetadata(file, context);
  if (!metadata) {
    return {
      original: null,
      variants: null,
    };
  }

  return {
    original: metadata.originalSize || null,
    variants: metadata.variantsSize || null,
  };
};

/**
 * Check if file has size information available
 */
export const hasFileSizeInfo = (file: UploadedFile): boolean => {
  // Temporary files with in-memory data always have size info
  if (file.x && file.inMemoryData) {
    return true;
  }

  // Permanent files with legacy sizes metadata have size info
  if (file.sizes) {
    return true;
  }

  // Check if we have cached metadata
  const cached = fileMetadataCache.get(file);
  return (
    cached !== null &&
    (cached.originalSize !== undefined || cached.variantsSize !== undefined)
  );
};

// =============================================================================
// BATCH OPERATIONS
// =============================================================================

/**
 * Preload metadata for multiple files (batch optimization)
 */
export const preloadFileMetadata = async (
  files: UploadedFile[],
  context?: PrivateFileContext
): Promise<
  Map<
    string,
    { realName: string; originalSize?: number; variantsSize?: number }
  >
> => {
  const results = new Map();

  // Separate files that need fetching from those with immediate data
  const immediateFiles = files.filter(f => f.x || f.sizes);
  const fetchFiles = files.filter(f => !f.x && !f.sizes);

  // Handle immediate files
  for (const file of immediateFiles) {
    const key = `${file.f}.${file.e}`;
    const metadata = await getFileMetadata(file, context);
    if (metadata) {
      results.set(key, metadata);
    }
  }

  // Process files that need fetching in smaller batches
  const batchSize = 5;
  for (let i = 0; i < fetchFiles.length; i += batchSize) {
    const batch = fetchFiles.slice(i, i + batchSize);

    const promises = batch.map(async file => {
      try {
        const metadata = await getFileMetadata(file, context);
        const key = `${file.f}.${file.e}`;
        if (metadata) {
          results.set(key, metadata);
        }
        return { key, metadata };
      } catch (error) {
        console.warn(`Failed to get metadata for file ${file.f}:`, error);
        const key = `${file.f}.${file.e}`;
        const fallback = { realName: `${file.f}.${file.e}` };
        results.set(key, fallback);
        return { key, metadata: fallback };
      }
    });

    await Promise.all(promises);
  }

  return results;
};

/**
 * Preload filenames for multiple files (backward compatibility)
 */
export const preloadFilenames = async (
  files: UploadedFile[],
  context?: PrivateFileContext
): Promise<Map<string, string>> => {
  const metadataMap = await preloadFileMetadata(files, context);
  const filenameMap = new Map<string, string>();

  metadataMap.forEach((metadata, key) => {
    filenameMap.set(key, extractFilenameWithoutExtension(metadata.realName));
  });

  return filenameMap;
};

/**
 * Preload file sizes for multiple files (backward compatibility)
 */
export const preloadFileSizes = async (
  files: UploadedFile[],
  context?: PrivateFileContext
): Promise<
  Map<string, { original: number | null; variants: number | null }>
> => {
  const metadataMap = await preloadFileMetadata(files, context);
  const sizesMap = new Map();

  metadataMap.forEach((metadata, key) => {
    sizesMap.set(key, {
      original: metadata.originalSize || null,
      variants: metadata.variantsSize || null,
    });
  });

  return sizesMap;
};

// =============================================================================
// CACHE MANAGEMENT
// =============================================================================

/**
 * Clear metadata cache for specific file or all files
 */
export const clearFileMetadataCache = (
  file?: UploadedFile,
  context?: PrivateFileContext
): void => {
  if (file) {
    const key = fileMetadataCache['getCacheKey'](file, context);
    fileMetadataCache['cache'].delete(key);
  } else {
    fileMetadataCache.clear();
  }
};

/**
 * Get cache statistics for debugging
 */
export const getFileMetadataCacheStats = () => {
  return fileMetadataCache.getStats();
};

// Backward compatibility exports
export const clearFilenameCache = clearFileMetadataCache;
export const clearFileSizeCache = clearFileMetadataCache;
export const getFilenameCacheStats = getFileMetadataCacheStats;
export const getFileSizeCacheStats = getFileMetadataCacheStats;

// Export the cache instance for testing
export { fileMetadataCache };
