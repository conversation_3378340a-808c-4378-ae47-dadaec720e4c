// =============================================================================
// FILE UPLOAD COMPONENT - PUBLIC API
// =============================================================================
//
// Centralized file upload system with the following capabilities:
//
// 🔧 COMPONENTS:
//   - FileUploadComponent: Main upload component with drag & drop
//   - RaFileUploadComponent: React-Admin wrapper
//
// 🎣 HOOKS:
//   - Component hooks: For internal FileUpload usage
//   - Global hooks: For external consumption (useInMemoryFileCleanup, etc.)
//
// 🛠️ UTILITIES:
//   - Configuration: Config validation and merging
//   - Validation: File validation and error handling
//   - Storage: Firebase Storage integration
//   - Processing: Client-side image processing
//
// 💾 CORE MANAGERS:
//   - fileUploadManager: Singleton for upload operations
//   - inMemoryFileManager: Blob URL lifecycle management
//
// 📁 TYPES:
//   - UploadedFile, FileUploadConfig, ValidationError, etc.
//
// =============================================================================

// Main Components
export { default as FileUploadComponent } from './FileUploadComponent';
export { default as RaFileUploadComponent } from './RaFileUploadComponent';

// =============================================================================
// TYPES
// =============================================================================

// Component Types
export type {
  FileUploadConfig,
  FileUploadComponentProps,
  RaFileUploadComponentProps,
  ValidationError,
  ImageConfig,
  ValidationConfig,
  UIConfig,
  CallbackConfig,
  UploadState,
  FileListState,
  ImageEditorState,
  ValidationState,
} from './types';

// All file upload types (UploadedFile, etc.)
export * from './types/fileUpload';

// Client Image Processor Types
export type { ProcessedImageResult } from './utils/clientImageProcessor';

// =============================================================================
// HOOKS
// =============================================================================

// Component Hooks (for internal FileUpload component usage)
export { useFileList } from './hooks/useFileList';
export { useFileValidation } from './hooks/useFileValidation';
export { useFileUpload } from './hooks/useFileUpload';
export { useImageEditor } from './hooks/useImageEditor';
export { useFileSize, useFileSizes } from './hooks/useFileSize';

// Display and URL hooks
export { useFileUrl, clearFileUrlCache } from './hooks/useFileUrl';
export {
  useRealFileName,
  useRealFileNames,
  useFileNameFromBatch,
  useDisplayFileName,
} from './hooks/useRealFileName';
export { useBlobUrlErrorRecovery } from './hooks/useBlobUrlErrorRecovery';

// Global Hooks (for external consumption outside FileUpload component)
export { useInMemoryFileCleanup } from './globalHooks/useInMemoryFileManagement';
export { useTempFileCleanup } from './globalHooks/useTempFileCleanup';

// =============================================================================
// CORE MANAGERS
// =============================================================================

export { fileUploadManager } from './core/FileUploadManager';
export { inMemoryFileManager } from './core/InMemoryFileManager';
export type { FileLifecycleCallbacks } from './core/FileUploadManager';
export type { MemoryUsageStats } from './core/InMemoryFileManager';

// =============================================================================
// UTILITIES
// =============================================================================

// Configuration Utilities
export {
  mergeWithDefaults,
  validateConfig,
  createConfig,
  DEFAULT_CONFIGS,
} from './utils/fileUploadConfig';

// Validation Utilities
export {
  validateFile,
  validateFiles,
  filterValidFiles,
  hasValidationErrors,
  groupValidationErrors,
  getValidationSummary,
  validateFileTypeConfiguration,
} from './utils/fileUploadValidation';

// File Helper Utilities
export {
  generateFileId,
  isImageFile as isImageFileType,
  isVideoFile as isVideoFileType,
  canPreviewFile as canPreviewFileType,
  getFileExtension,
  mapFileTypeToCode,
  getMimeTypeFromExtension,
  isFileTypeAccepted,
  debounce,
  isValidUploadedFile,
} from './utils/fileUploadHelpers';

// Preview Utilities (for UploadedFile objects)
export { isImageFile, isVideoFile, canPreviewFile } from './utils/previewUtils';

// Image Editor Utilities
export {
  getPublicImageEditorConfig,
  shouldEditPublicImage,
  getAspectRatioGroups,
  isMultiAspectRatioConfig,
  getEditingDimensions,
  getEditingDimensionsForGroup,
  loadImageFromFile,
  canvasToFile,
  canvasToMultipleFiles,
  cleanupImageUrl,
} from './utils/imageEditorUtils';

// Storage and Bucket Management
export * from './utils/bucketManager';

// URL Generation
export * from './utils/urlGeneration';

// File Metadata Resolution
export * from './utils/fileMetadataResolver';

// General File Utilities
export * from './utils/fileUtils';

// Image Variants and Standard Image Processing
export * from './utils/imageVariants';
export * from './utils/standardImageEditor';

// Client Image Processing
export {
  generateImageVariant,
  generateThumbnail,
  processImageWithVariants,
  processMultipleImages,
  getExtensionFromMimeType,
  createVariantFilename,
  checkImageTransparency,
} from './utils/clientImageProcessor';

// Size Utilities
export {
  formatFileSize as formatFileSizeUtil,
  getBatchFileSizes,
  getFileSize,
  getVariantFileSize,
  getFileSizes,
  getFileSizesAsync,
  hasFileSizeInfo,
  hasAnyFileSizeInfo,
  fetchAndCacheFileSizeInfo,
} from './utils/fileSizeUtils';

// =============================================================================
// UI COMPONENTS
// =============================================================================

// Sub-components
export { FileDropzone } from './components/FileDropzone/FileDropzone';
export { FileList } from './components/FileList/FileList';
export { FileItem } from './components/FileList/FileItem';
export { ValidationDisplay } from './components/FileValidation/ValidationDisplay';

// File Preview Components
export { FilePreviewModal } from './components/FilePreviewModal';
export {
  FilePreviewInline,
  clearFilePreviewCacheForFile,
  cleanupFilePreviewCache,
} from './components/FilePreviewInline';
export { FilePreviewImage } from './components/FilePreviewImage';

// Image Editor Components
export { ImageEditorModal } from './components/ImageEditorModal';
