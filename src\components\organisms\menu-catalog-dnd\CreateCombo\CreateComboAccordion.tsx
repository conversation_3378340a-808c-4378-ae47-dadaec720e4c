import { SyntheticEvent } from 'react';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import MuiCustomInput from '~/components/atoms/inputs/MuiCustomInput';
import ControlledRadioInputGroup from '~/components/molecules/controlled-input-groups/ControlledRadioInputGroup';
import SliderInputGroup from '../../../molecules/input-groups/SliderInputGroup';
import CreateComboItemsGrid from './CreateComboItemsGrid';
import { ComboStep } from './EditOrCreateComboModal';

export interface CreateComboAccordionProps {
  index: number;
  step: ComboStep;
  isExpanded: boolean;
  setOpenStep: (open: boolean) => void;
  updateStepField: (fieldName: keyof ComboStep, value: any) => void;
  deleteStep: () => void;
}

export default function CreateComboAccordion({
  isExpanded,
  step,
  index,
  setOpenStep,
  updateStepField,
  deleteStep,
}: CreateComboAccordionProps) {
  const { t } = useTranslation();
  return (
    <Accordion
      expanded={isExpanded}
      onChange={(_: SyntheticEvent, expanded: boolean) => {
        if (expanded) setOpenStep(true);
        else setOpenStep(false);
      }}
      sx={{
        boxShadow: 'none',
        border: 'solid 1px',
        borderColor: isExpanded ? 'primary.main' : 'custom.gray400',
        bgcolor: isExpanded ? 'primary.veryLight' : 'transparent',
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
      >
        <Typography variant="h5">
          {index} {isExpanded ? '' : step.name}
        </Typography>
      </AccordionSummary>
      <AccordionDetails sx={{ px: 5 }}>
        <MuiCustomInput
          label={t('menu.stepNameMessage')}
          onChange={ev => {
            updateStepField('name', ev.target.value);
          }}
          value={step.name}
        />
        <SliderInputGroup
          title={t('menu.noOfItemsSelected')}
          values={step.selection}
          changeValues={selection => {
            updateStepField('selection', selection);
          }}
        />
        <ControlledRadioInputGroup
          title={t('menu.stepType')}
          value={step.type}
          setValue={value => {
            updateStepField('type', value);
          }}
          choices={[
            {
              id: 'required',
              name: t('menu.required'),
              description: t('menu.requiredDescription'),
            },
            {
              id: 'optional',
              name: t('menu.optional'),
              description: t('menu.optionalDescription'),
            },
          ]}
        />

        <CreateComboItemsGrid
          items={step.items}
          setItems={(items: { id: string; type: string }[]) => {
            updateStepField('items', items);
          }}
        />
        {index > 1 && (
          <Button
            variant="text"
            sx={{ width: '100%', color: 'error.main', marginTop: 2 }}
            onClick={deleteStep}
          >
            Delete step
          </Button>
        )}
      </AccordionDetails>
    </Accordion>
  );
}
