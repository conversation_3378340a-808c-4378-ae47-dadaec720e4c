interface AppLoaderProps {
  id?: string;
}

/**
 * Shared application loader component
 * Uses the same CSS as index.html and auth.html loaders from /public/loader.css
 * The CSS is already loaded via <link> tag in index.html, so no import is needed
 */
export const AppLoader = ({ id = 'app-loader' }: AppLoaderProps) => {
  const initialLoader = document.getElementById('initial-loader');
  console.log(
    '[AppLoader] Rendered - id:',
    id,
    'initial-loader exists:',
    !!initialLoader,
    'initial-loader has hidden class:',
    initialLoader?.classList.contains('hidden')
  );

  return (
    <div id={id} className="loading-state">
      <div className="loader">
        <div>
          <img
            src="/assets/logo/SELIO_LOGO_BLACK.svg"
            width="52px"
            className="spinner-logo"
          />
        </div>
        <div className="square" id="sq1"></div>
        <div className="square" id="sq2"></div>
        <div className="square" id="sq3"></div>
        <div className="square" id="sq4"></div>
        <div className="square" id="sq5"></div>
        <div className="square" id="sq6"></div>
        <div className="square" id="sq7"></div>
        <div className="square" id="sq8"></div>
        <div className="square" id="sq9"></div>
      </div>
    </div>
  );
};
