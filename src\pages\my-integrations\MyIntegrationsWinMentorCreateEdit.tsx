import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Autocomplete,
  Box,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  SaveButton,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';
import {
  useGetListLocationsLive,
  useGetListPrepStationsLive,
} from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';

// Types
interface PaymentTypeCollector {
  name: string;
  paymentTypes: string[];
  symbol: string;
}

interface PrepStation {
  [key: string]: string;
}

interface LocationData {
  monetaryBookSymbol: string;
  monetaryNumberPrefix: string;
  compCtStockSymbol: string;
  pmsCtStockSymbol: string;
  usesWMEnterprise: boolean;
  prepStations: PrepStation;
  paymentTypeCollectors: Record<string, PaymentTypeCollector>;
}

interface SellPoints {
  [locationId: string]: LocationData;
}

interface GiftCardConfig {
  code: string;
  symbol: string;
}

interface GiftCards {
  '@physical': GiftCardConfig;
  '@digital': GiftCardConfig;
}

interface TipsConfig {
  code: string;
  symbol: string;
}

interface WinMentorFormValues {
  itemsCodeField: string;
  modifiersCodeField: string;
  tips: TipsConfig;
  giftCards: GiftCards;
  sellPoints: SellPoints;
}

type CodeFieldType = 'sku' | 'gtin' | 'ean';

interface CodeFieldOption {
  value: CodeFieldType;
  label: string;
}

// Constants
const CODE_FIELD_OPTIONS: CodeFieldOption[] = [
  { value: 'sku', label: 'SKU' },
  { value: 'gtin', label: 'GTIN' },
  { value: 'ean', label: 'EAN' },
] as const;

const WINMENTOR_PAYMENT_MAPPINGS: Record<string, string[]> = {
  Cash: ['cash'],
  BonValoric: ['mealTicket', 'valueTicket', 'voucher'],
  Cec: ['giftCard', 'wireTransfer', '3rdParty', 'cashless'],
  Card: ['card', 'tapToPay', 'online'],
};

const DEFAULT_COLLECTOR_SYMBOLS: Record<string, string> = {
  BonValoric: 'BONVALORIC',
  Card: 'CARD',
  Cash: '',
  Cec: 'CEC',
};

const DEFAULT_PAYMENT_TYPE_COLLECTORS: Record<string, PaymentTypeCollector> = {
  Cash: {
    name: 'Casa numerar',
    paymentTypes: WINMENTOR_PAYMENT_MAPPINGS.Cash,
    symbol: DEFAULT_COLLECTOR_SYMBOLS.Cash,
  },
  Card: {
    name: '',
    paymentTypes: WINMENTOR_PAYMENT_MAPPINGS.Card,
    symbol: DEFAULT_COLLECTOR_SYMBOLS.Card,
  },
  BonValoric: {
    name: '',
    paymentTypes: WINMENTOR_PAYMENT_MAPPINGS.BonValoric,
    symbol: DEFAULT_COLLECTOR_SYMBOLS.BonValoric,
  },
  Cec: {
    name: '',
    paymentTypes: WINMENTOR_PAYMENT_MAPPINGS.Cec,
    symbol: DEFAULT_COLLECTOR_SYMBOLS.Cec,
  },
};

// Custom hook for form management
const useWinMentorForm = () => {
  const { setValue, getValues, watch } = useFormContext<WinMentorFormValues>();

  const updateLocationField = useCallback(
    (locationId: string, field: keyof LocationData, value: any) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return;
      }

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          [field]: value,
        },
      });
    },
    [setValue, getValues]
  );

  const updatePaymentCollector = useCallback(
    (locationId: string, collectorKey: string, field: string, value: any) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return;
      }

      const currentCollectors = currentLocation.paymentTypeCollectors || {};

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          paymentTypeCollectors: {
            ...currentCollectors,
            [collectorKey]: {
              ...currentCollectors[collectorKey],
              [field]: value,
            },
          },
        },
      });
    },
    [setValue, getValues]
  );

  const addPrepStationToLocation = useCallback(
    (locationId: string, prepStationName: string) => {
      if (!prepStationName?.trim()) return false;

      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return false;
      }

      const currentPrepStations = currentLocation.prepStations || {};

      // Check for duplicates
      if (currentPrepStations[prepStationName]) {
        console.warn(
          `Prep station ${prepStationName} already exists for location ${locationId}`
        );
        return false;
      }

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          prepStations: {
            ...currentPrepStations,
            [prepStationName]: '',
          },
        },
      });

      return true;
    },
    [setValue, getValues]
  );

  const removePrepStationFromLocation = useCallback(
    (locationId: string, prepStationName: string) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return;
      }

      const currentPrepStations = { ...currentLocation.prepStations };
      delete currentPrepStations[prepStationName];

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          prepStations: currentPrepStations,
        },
      });
    },
    [setValue, getValues]
  );

  const updatePrepStationSymbol = useCallback(
    (locationId: string, prepStationName: string, symbol: string) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return;
      }

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          prepStations: {
            ...currentLocation.prepStations,
            [prepStationName]: symbol,
          },
        },
      });
    },
    [setValue, getValues]
  );

  const updateTips = useCallback(
    (field: keyof TipsConfig, value: string) => {
      const currentTips = getValues('tips') || { code: '', symbol: '' };
      setValue('tips', {
        ...currentTips,
        [field]: value,
      });
    },
    [setValue, getValues]
  );

  const updateGiftCard = useCallback(
    (
      key: '@physical' | '@digital',
      field: keyof GiftCardConfig,
      value: string
    ) => {
      const currentGiftCards = getValues('giftCards') || {
        '@physical': { code: '', symbol: '' },
        '@digital': { code: '', symbol: '' },
      };

      setValue('giftCards', {
        ...currentGiftCards,
        [key]: {
          ...currentGiftCards[key],
          [field]: value,
        },
      });
    },
    [setValue, getValues]
  );

  return {
    updateLocationField,
    updatePaymentCollector,
    addPrepStationToLocation,
    removePrepStationFromLocation,
    updatePrepStationSymbol,
    updateTips,
    updateGiftCard,
    watch,
    setValue,
    getValues,
  };
};

// Reusable payment field component
const PaymentField = ({
  label,
  value,
  onChange,
  tooltip,
  required = false,
}: {
  label: string;
  value: string;
  onChange: (value: string) => void;
  tooltip: string;
  required?: boolean;
}) => (
  <TextField
    label={label}
    value={value}
    onChange={e => onChange(e.target.value)}
    fullWidth
    size="small"
    required={required}
    slotProps={{
      input: {
        endAdornment: (
          <Tooltip title={tooltip}>
            <IconButton size="small">
              <InfoOutlinedIcon color="disabled" sx={{ fontSize: '18px' }} />
            </IconButton>
          </Tooltip>
        ),
      },
    }}
  />
);

// Component for tips configuration
const TipsSection = () => {
  const { updateTips, watch } = useWinMentorForm();
  const { t } = useTranslation('');
  const tips = watch('tips') || { code: '', symbol: '' };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('myIntegrations.tips')}
      </Typography>
      <Grid container spacing={2}>
        <Grid size={6}>
          <TextField
            label={t('myIntegrations.code')}
            value={tips.code}
            onChange={e => updateTips('code', e.target.value)}
            required
            fullWidth
            size="small"
          />
        </Grid>
        <Grid size={6}>
          <TextField
            label={t('myIntegrations.symbol')}
            value={tips.symbol}
            onChange={e => updateTips('symbol', e.target.value)}
            fullWidth
            size="small"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

// Component for managing gift cards
const GiftCardsSection = () => {
  const { updateGiftCard, watch } = useWinMentorForm();
  const { t } = useTranslation('');
  const { theme } = useTheme();
  const giftCards = watch('giftCards') || {
    '@physical': { code: '', symbol: '' },
    '@digital': { code: '', symbol: '' },
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('myIntegrations.giftCards')}
      </Typography>
      <Grid container spacing={0}>
        {/* Physical Gift Cards */}
        <Grid
          size={{
            xs: 12,
            md: 6
          }}>
          <Box
            sx={{
              p: { xs: 0, md: 2 },
              pl: { xs: 0, md: 0 },
              pt: { xs: 2, md: 2 },
              pb: { xs: 1, md: 2 },
              borderRight: {
                xs: 'none',
                md: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
              },
              borderBottom: {
                xs: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                md: 'none',
              },
            }}
          >
            <Grid container spacing={2}>
              <Grid size={12}>
                <Typography
                  variant="body1"
                  sx={{ fontWeight: 'medium', mb: 1 }}
                >
                  {t('myIntegrations.physical')}
                </Typography>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label={t('myIntegrations.code')}
                  value={giftCards['@physical']?.code || ''}
                  onChange={e =>
                    updateGiftCard('@physical', 'code', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label={t('myIntegrations.symbol')}
                  value={giftCards['@physical']?.symbol || ''}
                  onChange={e =>
                    updateGiftCard('@physical', 'symbol', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        </Grid>

        {/* Digital Gift Cards */}
        <Grid
          size={{
            xs: 12,
            md: 6
          }}>
          <Box
            sx={{
              p: { xs: 0, md: 2 },
              pr: { xs: 0, md: 0 },
              pt: { xs: 1, md: 2 },
              pb: { xs: 2, md: 2 },
            }}
          >
            <Grid container spacing={2}>
              <Grid size={12}>
                <Typography
                  variant="body1"
                  sx={{ fontWeight: 'medium', mb: 1 }}
                >
                  {t('myIntegrations.digital')}
                </Typography>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label={t('myIntegrations.code')}
                  value={giftCards['@digital']?.code || ''}
                  onChange={e =>
                    updateGiftCard('@digital', 'code', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label={t('myIntegrations.symbol')}
                  value={giftCards['@digital']?.symbol || ''}
                  onChange={e =>
                    updateGiftCard('@digital', 'symbol', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

// Component for managing locations with integrated prep stations
const LocationsSection = () => {
  const {
    updateLocationField,
    updatePaymentCollector,
    addPrepStationToLocation,
    removePrepStationFromLocation,
    updatePrepStationSymbol,
    watch,
    setValue,
  } = useWinMentorForm();

  const {
    data: locationsList,
    error: locationsError,
    isLoading: locationsLoading,
  } = useGetListLocationsLive({
    filter: { _d: false },
  });
  const { data: prepStationsList } = useGetListPrepStationsLive();
  const { t } = useTranslation('');
  const locations = watch('sellPoints') || {};
  const { theme } = useTheme();

  // State for prep station input values
  const [prepStationToAdd, setPrepStationToAdd] = useState<
    Record<string, string>
  >({});

  // Initialize locations from available locations list
  const initializeLocations = useCallback(() => {
    if (locationsList && Object.keys(locations).length === 0) {
      const initialLocations: Record<string, LocationData> = {};
      locationsList.forEach((location: any) => {
        initialLocations[location.id] = {
          monetaryBookSymbol: '',
          monetaryNumberPrefix: '',
          compCtStockSymbol: '',
          pmsCtStockSymbol: '',
          usesWMEnterprise: false,
          prepStations: {},
          paymentTypeCollectors: { ...DEFAULT_PAYMENT_TYPE_COLLECTORS },
        };
      });
      setValue('sellPoints', initialLocations);
    }
  }, [locationsList, locations, setValue]);

  // Initialize locations when component mounts or locationsList changes
  useEffect(() => {
    initializeLocations();
  }, [initializeLocations]);

  // Enhanced prep station addition with proper state management
  const handleAddPrepStation = useCallback(
    (locationId: string, prepStationName: string) => {
      const trimmedName = prepStationName?.trim();
      if (!trimmedName) return;

      const success = addPrepStationToLocation(locationId, trimmedName);
      if (success) {
        setPrepStationToAdd(prev => ({ ...prev, [locationId]: '' }));
      }
    },
    [addPrepStationToLocation]
  );

  // Get prep station names for autocomplete, excluding already added ones for this location
  const getPrepStationOptions = useCallback(
    (locationId: string) => {
      const allPrepStations =
        prepStationsList?.map((station: any) => station.name) || [];
      const locationData = locations[locationId] || {};
      const addedPrepStations = Object.keys(locationData.prepStations || {});

      return [...new Set(allPrepStations)]
        .filter(name => !addedPrepStations.includes(name))
        .sort();
    },
    [prepStationsList, locations]
  );

  // Memoize location names to prevent unnecessary re-renders
  const locationNamesMap = useMemo(() => {
    if (!locationsList?.length) return {};
    return locationsList.reduce(
      (acc: Record<string, string>, location: any) => {
        acc[location.id] = location.name;
        return acc;
      },
      {}
    );
  }, [locationsList]);

  // Memoize location rows for better performance
  const locationRows = useMemo(() => {
    if (!locationsList?.length) return [];

    const locationsPerRow = 3;
    const rows = [];
    for (let i = 0; i < locationsList.length; i += locationsPerRow) {
      rows.push(locationsList.slice(i, i + locationsPerRow));
    }
    return rows;
  }, [locationsList]);

  // Loading and error states
  if (locationsLoading) {
    return (
      <Box
        sx={{
          border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
          borderRadius: 2,
          p: 2,
        }}
      >
        <Typography variant="h6">{t('shared.location_few')}</Typography>
        <Typography color="text.secondary" sx={{ py: 2 }}>
          Loading locations...
        </Typography>
      </Box>
    );
  }

  if (locationsError) {
    return (
      <Box
        sx={{
          border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
          borderRadius: 2,
          p: 2,
        }}
      >
        <Typography variant="h6">{t('shared.location_few')}</Typography>
        <Typography color="error" sx={{ py: 2 }}>
          Error loading locations: {locationsError.message}
        </Typography>
      </Box>
    );
  }

  if (!locationsList || locationsList.length === 0) {
    return (
      <Box
        sx={{
          border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
          borderRadius: 2,
          p: 2,
        }}
      >
        <Typography variant="h6">{t('shared.location_few')}</Typography>
        <Typography color="text.secondary" sx={{ py: 2 }}>
          {t('myIntegrations.noLocationsAvailable')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
        borderRadius: 2,
        p: 2,
      }}
    >
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('shared.location_few')}
      </Typography>
      {locationRows.map((row, rowIndex) => (
        <Box key={rowIndex}>
          <Grid container spacing={0} sx={{ minHeight: 'fit-content' }}>
            {row.map((location: any, colIndex: number) => {
              const locationData = locations[location.id] || {};
              const isFirstInRow = colIndex === 0;
              const isLastInRow = colIndex === row.length - 1;

              return (
                <Grid
                  key={location.id}
                  size={{
                    xs: 12,
                    md: 6,
                    xl: 4
                  }}>
                  <Box
                    sx={{
                      p: { xs: 0, md: 2 }, // No padding on mobile, padding on desktop
                      // Remove left padding for first item on md+ (when showing 2 or 3 per row)
                      pl: { xs: 0, md: isFirstInRow ? 0 : 2 },
                      // Remove right padding for last item on md+ (when showing 2 or 3 per row)
                      pr: { xs: 0, md: isLastInRow ? 0 : 2 },
                      // Add specific padding for top and bottom on mobile
                      pt: { xs: 2, md: 2 },
                      pb: { xs: colIndex < row.length - 1 ? 1 : 2, md: 2 },
                      // Remove backgroundColor to inherit from parent container
                      borderRight: {
                        xs: 'none',
                        // Show border on md (2 cols) if not last, and on xl (3 cols) if not last
                        md: !isLastInRow
                          ? `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`
                          : 'none',
                      },
                      borderBottom: {
                        // On mobile, show border between locations except for last
                        xs:
                          colIndex < row.length - 1
                            ? `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`
                            : 'none',
                        md: 'none',
                      },
                      height: { md: '100%' },
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    {/* Location Header */}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: { xs: 1, md: 2 }, // Reduced margin on mobile
                      }}
                    >
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {locationNamesMap[location.id] || location.name}
                      </Typography>

                      {/* WinMentor Version Toggle moved to header */}
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{ color: 'text.secondary', fontSize: '0.75rem' }}
                        >
                          WM Classic
                        </Typography>
                        <Switch
                          checked={locationData?.usesWMEnterprise || false}
                          onChange={e => {
                            updateLocationField(
                              location.id,
                              'usesWMEnterprise',
                              e.target.checked
                            );
                          }}
                          size="small"
                        />
                        <Typography
                          variant="body2"
                          sx={{ color: 'text.secondary', fontSize: '0.75rem' }}
                        >
                          Enterprise
                        </Typography>
                      </Box>
                    </Box>

                    {/* Rest of the location content - flex-grow to fill remaining space */}
                    <Box
                      sx={{
                        flexGrow: 1,
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      {/* Monetary and Consumption Ticket Section */}
                      <Typography
                        variant="body1"
                        sx={{ mb: 1, fontWeight: 'medium' }}
                      >
                        Monetary and Consumption Ticket
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1,
                          mb: { xs: 1, md: 2 }, // Reduced margin on mobile
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: { xs: 'column', md: 'row' }, // Stack on mobile, side by side on desktop
                            gap: 1,
                          }}
                        >
                          <TextField
                            label={t('myIntegrations.monetaryBookSymbol')}
                            value={locationData?.monetaryBookSymbol || ''}
                            onChange={e =>
                              updateLocationField(
                                location.id,
                                'monetaryBookSymbol',
                                e.target.value
                              )
                            }
                            required
                            fullWidth
                            size="small"
                            slotProps={{
                              input: {
                                endAdornment: (
                                  <Tooltip
                                    title={t(
                                      'myIntegrations.monetaryBookSymbolTooltip'
                                    )}
                                  >
                                    <IconButton size="small">
                                      <InfoOutlinedIcon
                                        color="disabled"
                                        sx={{ fontSize: '18px' }}
                                      />
                                    </IconButton>
                                  </Tooltip>
                                ),
                              },
                            }}
                          />
                          <TextField
                            label={t('myIntegrations.monetaryNumberPrefix')}
                            value={locationData?.monetaryNumberPrefix || ''}
                            onChange={e =>
                              updateLocationField(
                                location.id,
                                'monetaryNumberPrefix',
                                e.target.value
                              )
                            }
                            required
                            fullWidth
                            size="small"
                            slotProps={{
                              input: {
                                endAdornment: (
                                  <Tooltip
                                    title={t(
                                      'myIntegrations.monetaryNumberPrefixTooltip'
                                    )}
                                  >
                                    <IconButton size="small">
                                      <InfoOutlinedIcon
                                        color="disabled"
                                        sx={{ fontSize: '18px' }}
                                      />
                                    </IconButton>
                                  </Tooltip>
                                ),
                              },
                            }}
                          />
                        </Box>
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: { xs: 'column', md: 'row' }, // Stack on mobile, side by side on desktop
                            gap: 1,
                          }}
                        >
                          <TextField
                            label="Comp Consumption Stock Symbol"
                            value={locationData?.compCtStockSymbol || ''}
                            onChange={e =>
                              updateLocationField(
                                location.id,
                                'compCtStockSymbol',
                                e.target.value
                              )
                            }
                            fullWidth
                            size="small"
                            slotProps={{
                              input: {
                                endAdornment: (
                                  <Tooltip title="If this value is provided it will generate only one consumption ticket for all the comp reasons and all the prep stations. If this value is not provided for each comp reason and prep station a consumption ticket will be generated.">
                                    <IconButton size="small">
                                      <InfoOutlinedIcon
                                        color="disabled"
                                        sx={{ fontSize: '18px' }}
                                      />
                                    </IconButton>
                                  </Tooltip>
                                ),
                              },
                            }}
                          />
                          <TextField
                            label="PMS Consumption Stock Symbol"
                            value={locationData?.pmsCtStockSymbol || ''}
                            onChange={e =>
                              updateLocationField(
                                location.id,
                                'pmsCtStockSymbol',
                                e.target.value
                              )
                            }
                            fullWidth
                            size="small"
                            slotProps={{
                              input: {
                                endAdornment: (
                                  <Tooltip title="If this value is provided it will generate only one consumption ticket for all the prep stations of PMS sales. If this value is not provided for each prep station from PMS sales a consumption ticket will be generated.">
                                    <IconButton size="small">
                                      <InfoOutlinedIcon
                                        color="disabled"
                                        sx={{ fontSize: '18px' }}
                                      />
                                    </IconButton>
                                  </Tooltip>
                                ),
                              },
                            }}
                          />
                        </Box>
                      </Box>

                      {/* WinMentor Cassa Names Section */}
                      <Typography
                        variant="body1"
                        sx={{ mb: 1, fontWeight: 'medium' }}
                      >
                        Cassa Names
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1,
                          mb: { xs: 1, md: 2 }, // Reduced margin on mobile
                        }}
                      >
                        <PaymentField
                          label="Cash"
                          value={
                            locationData?.paymentTypeCollectors?.Cash?.name ||
                            ''
                          }
                          onChange={value =>
                            updatePaymentCollector(
                              location.id,
                              'Cash',
                              'name',
                              value
                            )
                          }
                          tooltip="Linked payment types: cash"
                        />

                        {/* Show additional payment collectors only for Enterprise */}
                        {locationData?.usesWMEnterprise && (
                          <>
                            <PaymentField
                              label="Card"
                              value={
                                locationData?.paymentTypeCollectors?.Card
                                  ?.name || ''
                              }
                              onChange={value =>
                                updatePaymentCollector(
                                  location.id,
                                  'Card',
                                  'name',
                                  value
                                )
                              }
                              tooltip="Linked payment types: card, tapToPay, online"
                            />
                            <PaymentField
                              label="BonValoric"
                              value={
                                locationData?.paymentTypeCollectors?.BonValoric
                                  ?.name || ''
                              }
                              onChange={value =>
                                updatePaymentCollector(
                                  location.id,
                                  'BonValoric',
                                  'name',
                                  value
                                )
                              }
                              tooltip="Linked payment types: mealTicket, valueTicket, voucher"
                            />
                            <PaymentField
                              label="Cec"
                              value={
                                locationData?.paymentTypeCollectors?.Cec
                                  ?.name || ''
                              }
                              onChange={value =>
                                updatePaymentCollector(
                                  location.id,
                                  'Cec',
                                  'name',
                                  value
                                )
                              }
                              tooltip="Linked payment types: giftCard, wireTransfer, 3rdParty, cashless"
                            />
                          </>
                        )}
                      </Box>

                      {/* Prep Stations Section - flex-grow to push to bottom */}
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography
                          variant="body1"
                          sx={{ mb: 1, fontWeight: 'medium' }}
                        >
                          {t('myIntegrations.prepStations')}
                        </Typography>
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 1,
                          }}
                        >
                          {Object.entries(locationData?.prepStations || {}).map(
                            ([prepStationName, symbol]: [string, any]) => (
                              <Box
                                key={prepStationName}
                                sx={{
                                  // Remove the background color
                                  borderRadius: 1,
                                }}
                              >
                                <Grid
                                  container
                                  spacing={1}
                                  alignItems="center"
                                  sx={{ p: 0 }}
                                >
                                  <Grid size={6}>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontWeight: 'medium' }}
                                    >
                                      {prepStationName}
                                    </Typography>
                                  </Grid>
                                  <Grid size={5}>
                                    <TextField
                                      label={t('myIntegrations.symbol')}
                                      value={symbol || ''}
                                      onChange={e =>
                                        updatePrepStationSymbol(
                                          location.id,
                                          prepStationName,
                                          e.target.value
                                        )
                                      }
                                      fullWidth
                                      size="small"
                                      sx={{
                                        backgroundColor:
                                          theme.palette.mode === 'dark'
                                            ? '#343439'
                                            : 'white',
                                      }}
                                    />
                                  </Grid>
                                  <Grid
                                    sx={{
                                      display: 'flex',
                                      justifyContent: 'center',
                                    }}
                                    size={1}>
                                    <IconButton
                                      onClick={() =>
                                        removePrepStationFromLocation(
                                          location.id,
                                          prepStationName
                                        )
                                      }
                                      color="error"
                                      size="small"
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </Grid>
                                </Grid>
                              </Box>
                            )
                          )}

                          {/* Add new prep station */}
                          <Grid container spacing={1} alignItems="center">
                            <Grid size={11}>
                              <Autocomplete
                                options={getPrepStationOptions(location.id)}
                                freeSolo
                                size="small"
                                value={prepStationToAdd[location.id] || ''}
                                inputValue={prepStationToAdd[location.id] || ''}
                                onInputChange={(event, newInputValue) => {
                                  setPrepStationToAdd({
                                    ...prepStationToAdd,
                                    [location.id]: newInputValue,
                                  });
                                }}
                                onChange={(event, value) => {
                                  if (value && typeof value === 'string') {
                                    setPrepStationToAdd({
                                      ...prepStationToAdd,
                                      [location.id]: value,
                                    });
                                  }
                                }}
                                renderInput={params => (
                                  <TextField
                                    {...params}
                                    label={t('myIntegrations.addPrepStation')}
                                    placeholder={t(
                                      'myIntegrations.typePrepStationName'
                                    )}
                                    onKeyDown={e => {
                                      if (e.key === 'Enter') {
                                        e.preventDefault();
                                        const prepStationName =
                                          prepStationToAdd[location.id]?.trim();
                                        if (prepStationName) {
                                          handleAddPrepStation(
                                            location.id,
                                            prepStationName
                                          );
                                        }
                                      }
                                    }}
                                  />
                                )}
                                disablePortal
                              />
                            </Grid>
                            <Grid sx={{ display: 'flex', justifyContent: 'center' }} size={1}>
                              <IconButton
                                size="small"
                                disabled={
                                  !prepStationToAdd[location.id]?.trim()
                                }
                                onClick={() => {
                                  const prepStationName =
                                    prepStationToAdd[location.id]?.trim();
                                  if (prepStationName) {
                                    handleAddPrepStation(
                                      location.id,
                                      prepStationName
                                    );
                                  }
                                }}
                              >
                                <AddIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </Grid>
              );
            })}
          </Grid>

          {/* Enhanced horizontal divider logic - show divider between rows on all screen sizes when there are multiple rows */}
          {rowIndex < locationRows.length - 1 && (
            <Box
              sx={{
                borderBottom: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                my: { xs: 2, md: 3 }, // Slightly more margin on md+ screens
                mx: { xs: 0, md: -2 }, // Extend divider to full width on md+ by compensating for padding
              }}
            />
          )}
        </Box>
      ))}
    </Box>
  );
};

interface MyIntegrationsWinMentorCreateEditProps {
  setOpen?: (open: boolean) => void;
  partnerExtraData?: any;
  mode?: 'create' | 'edit';
}

export const MyIntegrationsWinMentorCreateEdit = ({
  setOpen,
  partnerExtraData,
  mode = 'create',
}: MyIntegrationsWinMentorCreateEditProps) => {
  const { t } = useTranslation('');
  const resource = useResourceContext();
  const record = useRecordContext();
  const redirect = useRedirect();
  const { setValue, watch } = useFormContext<WinMentorFormValues>();
  const { theme } = useTheme();

  const handleClose = () => {
    if (mode === 'create' && setOpen) {
      setOpen(false);
    } else {
      redirect('list', resource, record?.id, undefined, {
        _scrollToTop: false,
      });
    }
  };

  const updateBasicField = useCallback(
    (field: keyof WinMentorFormValues, value: string) => {
      setValue(field, value);
    },
    [setValue]
  );

  const itemsCodeField = watch('itemsCodeField');
  const modifiersCodeField = watch('modifiersCodeField');

  // Determine title based on mode
  const getTitle = () => {
    if (mode === 'create') {
      return `${t('myIntegrations.createIntegration')} ${partnerExtraData?.name || ''}`;
    }
    return `${t('shared.edit')} Integration - ${record?.name || ''}`;
  };

  return (
    <>
      <ModalHeader handleClose={handleClose} title={getTitle()}>
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          p: 3,
          width: '100%',
        }}
      >
        {/* First Row: Items & Modifiers + Tips */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {/* Items and Modifiers - Column 1 */}
          <Grid
            size={{
              xs: 12,
              md: 6
            }}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <Typography variant="h6" sx={{ mb: 2 }}>
                {t('myIntegrations.itemsAndModifiers')}
                <Tooltip title={t('myIntegrations.itemsCodeTooltip')}>
                  <IconButton size="small">
                    <InfoOutlinedIcon
                      color="disabled"
                      sx={{ fontSize: '18px' }}
                    />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Grid container spacing={2}>
                <Grid size={6}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="items-code-field-label">
                      {t('myIntegrations.itemsCode')}
                    </InputLabel>
                    <Select
                      labelId="items-code-field-label"
                      value={itemsCodeField || ''}
                      label="Items Code Field"
                      onChange={e =>
                        updateBasicField('itemsCodeField', e.target.value)
                      }
                      required
                    >
                      {CODE_FIELD_OPTIONS.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={6}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="modifiers-code-field-label">
                      {t('myIntegrations.modifiersCode')}
                    </InputLabel>
                    <Select
                      labelId="modifiers-code-field-label"
                      value={modifiersCodeField || ''}
                      label="Modifiers Code Field"
                      onChange={e =>
                        updateBasicField('modifiersCodeField', e.target.value)
                      }
                      required
                    >
                      {CODE_FIELD_OPTIONS.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </Grid>

          {/* Tips - Column 2 */}
          <Grid
            size={{
              xs: 12,
              md: 6
            }}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <TipsSection />
            </Box>
          </Grid>
        </Grid>

        {/* Second Row: Gift Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid size={12}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <GiftCardsSection />
            </Box>
          </Grid>
        </Grid>

        {/* Third Row: Locations */}
        <LocationsSection />
      </Box>
    </>
  );
};
