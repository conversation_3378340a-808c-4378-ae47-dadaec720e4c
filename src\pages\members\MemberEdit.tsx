import { Box, Typography, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  email,
  ReferenceInput,
  required,
  SaveButton,
  useGetRecordId,
  useRedirect,
  useUnique,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import { validatePhone } from '~/components/atoms/inputs/PhoneNumberInput';
import {
  ConflictAwareInput,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import { RESOURCES } from '~/providers/resources';
import { validateName } from '~/utils/validateName';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';

const MemberEditInner = () => {
  const recordId = useGetRecordId();
  const unique = useUnique({ filter: { id_neq: recordId } });
  const redirect = useRedirect();
  const { t } = useTranslation('');
  const handleClose = () => {
    redirect('list', RESOURCES.TEAM_MEMBERS);
  };
  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('members.editTeamMember')}
      >
        <SaveButton type="button" icon={<></>} label={t('shared.save')} />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 6,
        }}
      >
        <Subsection title={t('members.personalInformation')}>
          <ConflictAwareInput>
            <CustomInput
              source="firstName"
              type="text"
              ui="custom"
              sanitize="singleLine"
              label={t('members.firstName')}
              placeholder="John"
              isRequired
              validate={required()}
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              source="lastName"
              type="text"
              ui="custom"
              sanitize="singleLine"
              label={t('members.lastName')}
              placeholder="Smith"
              validate={required()}
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              source="displayName"
              type="text"
              ui="custom"
              sanitize="singleLine"
              label={t('members.displayName')}
              placeholder="Johnie"
              validate={[required(), unique(), validateName]}
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              type="phone"
              source="phone"
              label={t('members.phone')}
              placeholder="720 123 456"
              validate={[required(), validatePhone, unique()]}
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              source="email"
              type="text"
              ui="custom"
              sanitize="singleLine"
              label="E-mail"
              disabled={true}
              validate={[required(), email(), unique()]}
              placeholder="<EMAIL>"
            />
          </ConflictAwareInput>
          {/* @ts-ignore */}
          <Typography variant="label" fontWeight={200}>
            {t('members.emailDescription')}
          </Typography>
        </Subsection>

        <Subsection title={t('members.permissions')}>
          <ConflictAwareInput>
            <ReferenceInput source="roleId" reference={RESOURCES.PERMISSIONS}>
              <CustomInput
                source="roleId"
                type="autocomplete"
                ui="custom"
                label={t('members.role')}
                optionText="name"
                optionValue="id"
                placeholder={t('members.cashierWaiterBartender')}
                selectOnFocus={false}
                validate={[required()]}
              />
            </ReferenceInput>
          </ConflictAwareInput>
          <ConflictAwareInput>
            <ReferenceInput
              source="sellPointIds"
              reference={RESOURCES.LOCATIONS}
              filter={{ _d: false }}
            >
              <CustomInput
                source="sellPointIds"
                type="autocompleteArray"
                ui="custom"
                label={t('shared.location_few')}
                optionText="name"
                optionValue="id"
                placeholder={t('reportsPage.all')}
              />
            </ReferenceInput>
          </ConflictAwareInput>
        </Subsection>
        <Subsection
          title={t('members.credentials')}
          subtitle={t('members.credentialsDescription')}
        >
          <ConflictAwareInput>
            <CustomInput
              type="code"
              source="pin"
              label={t('members.personalPasscode')}
              digits={6}
              validate={[required(), unique()]}
            />
          </ConflictAwareInput>
          {/* @ts-ignore */}
          <Typography variant="label" fontWeight={200}>
            {t('members.personalPasscodeDescription')}
          </Typography>
          <Typography
            // @ts-ignore
            variant="label"
            fontWeight={200}
            display="block"
            mt={1}
          >
            {t('members.personalPasscodeDescription2')}
          </Typography>
        </Subsection>
      </Box>
    </>
  );
};

export const MemberEdit = () => {
  return (
    <EditDialog {...getFullscreenModalProps()} mutationMode="pessimistic">
      <ConflictAwareSimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        translationNamespace="shared"
      >
        <MemberEditInner />
      </ConflictAwareSimpleForm>
    </EditDialog>
  );
};
