import ReactDOM from 'react-dom/client';

import App from './App';

import './styles/globals.css';

import { I18nextProvider } from 'react-i18next';

import i18n from '../i18';
import { initializePWA } from './utils/registerPWA';

console.log('[Main] Initializing React app');
console.log(
  '[Main] User is authenticated (gateway passed), initializing PWA...'
);

// Initialize PWA for authenticated users
// If we reached this file, the user is authenticated (gateway pattern ensures this)
initializePWA().catch(error => {
  console.error('[Main] Failed to initialize PWA:', error);
});

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <I18nextProvider i18n={i18n}>
    <App />
  </I18nextProvider>
);
