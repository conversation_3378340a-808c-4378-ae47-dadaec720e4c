import { ResourcesInfo } from '../resources';

export const LOCAL_STORAGE_SELECTED_ACCOUNT_KEY = 'selectedAccountId';
export const FIREBASE_RTDB_EUROPE_WEST1_DOMAIN =
    'europe-west1.firebasedatabase.app';
export const LOCAL_FORAGE_DATABASE_NAME = 'selio';
export const BROADCAST_CHANNEL_NAME = `selio-broadcast-channel-${import.meta.env.MODE}`;

// Generate or retrieve persistent tab ID
// Stored in sessionStorage to survive page refreshes but not tab closes
const getOrCreateTabId = (): string => {
    const SESSION_STORAGE_KEY = 'selio-browser-tab-id';
    try {
        const existingId = sessionStorage.getItem(SESSION_STORAGE_KEY);
        if (existingId) {
            return existingId;
        }
        const newId = Math.random().toString(36).substring(2, 15);
        sessionStorage.setItem(SESSION_STORAGE_KEY, newId);
        return newId;
    } catch (error) {
        // Fallback if sessionStorage is not available
        console.warn('sessionStorage not available, using random tab ID');
        return Math.random().toString(36).substring(2, 15);
    }
};

export const BROWSER_TAB_ID = getOrCreateTabId();

export const ORDER_NOW_DEFAULT_SCHEDULE = Array(7)
    .fill(null)
    .map(() => ({ timeSlots: [] }));
