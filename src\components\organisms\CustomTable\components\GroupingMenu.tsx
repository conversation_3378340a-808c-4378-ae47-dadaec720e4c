import React, { useEffect, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import DragHandleRoundedIcon from '@mui/icons-material/DragHandleRounded';
import LayersIcon from '@mui/icons-material/Layers';
import {
  Button,
  Checkbox,
  FormControlLabel,
  Menu,
  MenuItem,
  Theme,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import styles from '../styles.module.css';
import { FieldOption } from '../types/globals';

interface GroupingMenuItem {
  label: string;
  value: string;
  isChecked: boolean;
}

const getCheckedValues = (
  items: { isChecked: boolean; value: string }[]
): string[] => {
  return items.filter(item => item.isChecked).map(item => item.value);
};

const GroupingMenu = ({
  groupingOptions,
  onChangeGrouping,
  groupingItemsState,
  fields,
}: {
  groupingOptions?: { label: string; value: string }[];
  onChangeGrouping?: (items: string[]) => void;
  groupingItemsState: string[];

  fields: FieldOption[];
}) => {
  const [anchorElGrouping, setAnchorElGrouping] = useState<null | HTMLElement>(
    null
  );

  const [groupingItems, setGroupingItems] = useState<GroupingMenuItem[]>([]);

  const checkedValues = getCheckedValues(fields);

  const { t } = useTranslation();

  useEffect(() => {
    if (groupingOptions) {
      setGroupingItems(
        groupingOptions.map(option => ({
          label: option.label,
          value: option.value,
          isChecked: groupingItemsState?.includes(option.value) ?? false,
        }))
      );
    }
  }, []);

  useEffect(() => {
    if (groupingOptions) {
      setGroupingItems(prevItems => {
        const newItems = groupingOptions.map(option => {
          const existingItem = prevItems.find(
            item => item.value === option.value
          );
          return {
            label: option.label,
            value: option.value,
            isChecked: existingItem ? existingItem.isChecked : false,
          };
        });

        return newItems;
      });
    }
  }, []);

  const handleOpenGrouping = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorElGrouping(event.currentTarget);
  };

  const handleCloseGrouping = () => {
    setAnchorElGrouping(null);
  };

  const handleToggleGrouping = (index: number) => {
    setGroupingItems(prevItems => {
      let newItems = [...prevItems];
      const toggledItem = { ...newItems[index] };
      toggledItem.isChecked = !toggledItem.isChecked;

      newItems = newItems.filter(item => item.value !== toggledItem.value);

      if (toggledItem.isChecked) {
        const checkedItems = newItems.filter(item => item.isChecked);
        const uncheckedItems = newItems.filter(item => !item.isChecked);
        newItems = [...checkedItems, toggledItem, ...uncheckedItems];
      } else {
        const checkedItems = newItems.filter(item => item.isChecked);
        const uncheckedItems = newItems.filter(item => !item.isChecked);
        newItems = [...checkedItems, toggledItem, ...uncheckedItems];
      }

      const checkedValues = newItems
        .filter(item => item.isChecked)
        .map(item => item.value);

      onChangeGrouping && onChangeGrouping(checkedValues);
      return newItems;
    });
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    setGroupingItems(prevItems => {
      const items = [...prevItems];
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);

      const checkedValues = items
        .filter(item => item.isChecked)
        .map(item => item.value);

      onChangeGrouping && onChangeGrouping(checkedValues);
      return items;
    });
  };

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <>
      <Button
        className={styles.filterButton}
        onClick={handleOpenGrouping}
        sx={{
          whiteSpace: 'nowrap',
          fontSize: { xs: '12px', sm: '14px' },
          px: { xs: 0, sm: 1 },
          mr: { xs: '-24px', sm: 0 },
          '&:hover': { bgcolor: 'transparent', color: '#0046a8' },
        }}
        startIcon={<LayersIcon />}
      >
        {isXSmall ? '' : t('shared.groupBy')}
      </Button>
      <Menu
        anchorEl={anchorElGrouping}
        open={Boolean(anchorElGrouping)}
        onClose={handleCloseGrouping}
      >
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="grouping-menu">
            {provided => (
              <div ref={provided.innerRef} {...provided.droppableProps}>
                {groupingItems.map((item, index) => {
                  const isDisabled = !checkedValues.includes(item.value);

                  return (
                    <Draggable
                      key={item.value}
                      draggableId={item.value}
                      index={index}
                      isDragDisabled={isDisabled}
                    >
                      {provided => (
                        <MenuItem
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            opacity: isDisabled ? 0.5 : 1,
                            pointerEvents: isDisabled ? 'none' : 'auto',
                          }}
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          onClick={e => e.stopPropagation()}
                        >
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={item.isChecked}
                                onChange={() => handleToggleGrouping(index)}
                              />
                            }
                            label={item.label}
                          />
                          <DragHandleRoundedIcon sx={{ color: 'gray' }} />
                        </MenuItem>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </Menu>
    </>
  );
};

export default GroupingMenu;
