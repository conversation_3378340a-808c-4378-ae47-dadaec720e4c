/**
 * NOTE FOR DEVELOPERS:
 * During hot reload in dev, if you save presenceManager.ts, Vite will do a full
 * page reload (not Fast Refresh) because FirebaseContext exports a hook.
 * This causes all Firebase connections to reset, which is expected behavior.
 * In production, this doesn't happen - users only get full reloads on manual refresh,
 * which is handled correctly by the presence system.
 */

import {
  Database,
  DatabaseReference,
  onDisconnect,
  OnDisconnect,
  onValue,
  push,
  ref,
  remove,
  serverTimestamp,
  set,
} from 'firebase/database';

/**
 * Represents the state of a presence session for a specific user on a specific account.
 * Each unique combination of RTDB URL + Account + User has its own PresenceState.
 */
interface PresenceState {
  // Identification
  rtdbUrl: string;
  rtdb: Database;
  accountId: string;
  userId: string;

  // Current session connection tracking
  // This tracks the current connection node for THIS browser tab/session
  // If network flickers, we remove the old one before creating a new one
  currentConnectionRef: DatabaseReference | null;

  // Listener management
  connectedListenerUnsubscribe: (() => void) | null;

  // OnDisconnect handlers
  // These are registered server-side and MUST be canceled when switching accounts
  onDisconnectConnection: OnDisconnect | null;
  onDisconnectLastOnline: OnDisconnect | null;
}

/**
 * PresenceManager handles user presence tracking in Firebase RTDB.
 *
 * Key features:
 * - Tracks presence per RTDB instance + account + user combination
 * - Prevents duplicate connection nodes on network flickers
 * - Properly cleans up on account switches and logouts
 * - Cancels onDisconnect handlers to prevent stale server-side actions
 *
 * Usage:
 * - Call setup() when user logs in or switches accounts
 * - Call cleanup() when switching accounts or logging out
 * - Cleanup is idempotent and safe to call multiple times
 */
class PresenceManager {
  // Map key format: "${rtdbUrl}:${accountId}:${userId}"
  private presenceStates: Map<string, PresenceState> = new Map();

  /**
   * Generate a unique key for tracking presence state.
   * Uses combination of RTDB URL, account ID, and user ID.
   */
  private getKey(rtdbUrl: string, accountId: string, userId: string): string {
    return `${rtdbUrl}:${accountId}:${userId}`;
  }

  /**
   * Set up presence tracking for a user on a specific account.
   *
   * Creates a .info/connected listener that:
   * - Creates a connection node when connected
   * - Sets up onDisconnect handlers to clean up automatically
   * - Removes old connection before creating new one (prevents duplicates)
   *
   * @param rtdb - Firebase RTDB instance
   * @param rtdbUrl - RTDB URL (used for tracking)
   * @param accountId - Account ID
   * @param userId - User ID
   */
  setup(
    rtdb: Database,
    rtdbUrl: string,
    accountId: string,
    userId: string
  ): void {
    const key = this.getKey(rtdbUrl, accountId, userId);

    // Check if already set up (prevent duplicate setup)
    if (this.presenceStates.has(key)) {
      console.log('[PresenceManager] Already set up for', key);
      return;
    }

    console.log('[PresenceManager] Setting up presence for', key);

    // Create presence state
    const state: PresenceState = {
      rtdbUrl,
      rtdb,
      accountId,
      userId,
      currentConnectionRef: null,
      connectedListenerUnsubscribe: null,
      onDisconnectConnection: null,
      onDisconnectLastOnline: null,
    };

    // Set up .info/connected listener
    const connectedRef = ref(rtdb, '.info/connected');
    const unsubscribe = onValue(connectedRef, snapshot => {
      const connected = snapshot.val() === true;
      console.log(
        `[PresenceManager] Connection state changed for ${key}:`,
        connected
      );

      if (connected) {
        // Connected - set up or refresh presence
        this.handleReconnection(state, accountId, userId);
      } else {
        // Disconnected - onDisconnect handlers will fire automatically on server
        console.log(
          '[PresenceManager] Disconnected - onDisconnect handlers will clean up'
        );
      }
    });

    state.connectedListenerUnsubscribe = unsubscribe;
    this.presenceStates.set(key, state);
  }

  /**
   * Handle connection or reconnection.
   *
   * This is called when .info/connected becomes true.
   * It removes any old connection from this session before creating a new one,
   * preventing duplicate connection nodes from network flickers.
   *
   * @param state - Presence state
   * @param accountId - Account ID
   * @param userId - User ID
   */
  private handleReconnection(
    state: PresenceState,
    accountId: string,
    userId: string
  ): void {
    console.log('[PresenceManager] Handling connection/reconnection');

    // STEP 1: Remove old connection from THIS SESSION (if exists)
    if (state.currentConnectionRef) {
      console.log(
        '[PresenceManager] Removing old connection before creating new one'
      );

      // Cancel its onDisconnect handler
      if (state.onDisconnectConnection) {
        state.onDisconnectConnection
          .cancel()
          .catch(err =>
            console.error(
              '[PresenceManager] Error canceling old onDisconnect:',
              err
            )
          );
      }

      // Remove the old connection node
      remove(state.currentConnectionRef).catch(err =>
        console.error('[PresenceManager] Error removing old connection:', err)
      );
    }

    // STEP 2: Create NEW connection node
    const connectionsRef = ref(
      state.rtdb,
      `accounts/${accountId}/presence/${userId}/connections`
    );
    const newConnectionRef = push(connectionsRef);

    // STEP 3: Set up onDisconnect handlers for NEW connection
    state.onDisconnectConnection = onDisconnect(newConnectionRef);
    state.onDisconnectConnection
      .remove()
      .catch(err =>
        console.error(
          '[PresenceManager] Error setting onDisconnect for connection:',
          err
        )
      );

    const lastOnlineRef = ref(
      state.rtdb,
      `accounts/${accountId}/presence/${userId}/lastOnlineAt`
    );
    state.onDisconnectLastOnline = onDisconnect(lastOnlineRef);
    state.onDisconnectLastOnline
      .set(serverTimestamp())
      .catch(err =>
        console.error(
          '[PresenceManager] Error setting onDisconnect for lastOnline:',
          err
        )
      );

    // STEP 4: Set the connection value
    // 0 = POS, 1 = Manager
    set(newConnectionRef, 1).catch(err =>
      console.error('[PresenceManager] Error setting connection value:', err)
    );

    // STEP 5: Track this new connection
    state.currentConnectionRef = newConnectionRef;

    console.log('[PresenceManager] New connection created and tracked');
  }

  /**
   * Clean up presence for a specific user on a specific account.
   *
   * This method:
   * 1. Cancels onDisconnect handlers (prevents stale server-side actions)
   * 2. Removes the current connection node
   * 3. Sets lastOnlineAt timestamp
   * 4. Unsubscribes from .info/connected listener
   * 5. Removes from tracking Map
   *
   * This method is idempotent - safe to call multiple times.
   *
   * @param rtdbUrl - RTDB URL
   * @param accountId - Account ID
   * @param userId - User ID
   */
  cleanup(rtdbUrl: string, accountId: string, userId: string): void {
    const key = this.getKey(rtdbUrl, accountId, userId);
    const state = this.presenceStates.get(key);

    if (!state) {
      console.log('[PresenceManager] No presence to cleanup for', key);
      return; // Idempotent - already cleaned or never set up
    }

    console.log('[PresenceManager] Starting cleanup for', key);

    // STEP 1: Cancel onDisconnect handlers (CRITICAL!)
    // This tells Firebase server: "Don't execute these when I disconnect"
    if (state.onDisconnectConnection) {
      state.onDisconnectConnection
        .cancel()
        .catch(err =>
          console.error(
            '[PresenceManager] Error canceling connection onDisconnect:',
            err
          )
        );
      state.onDisconnectConnection = null;
    }

    if (state.onDisconnectLastOnline) {
      state.onDisconnectLastOnline
        .cancel()
        .catch(err =>
          console.error(
            '[PresenceManager] Error canceling lastOnline onDisconnect:',
            err
          )
        );
      state.onDisconnectLastOnline = null;
    }

    // STEP 2: Remove current connection node (manual cleanup)
    if (state.currentConnectionRef) {
      remove(state.currentConnectionRef).catch(err =>
        console.error('[PresenceManager] Error removing connection node:', err)
      );
      state.currentConnectionRef = null;
    }

    // STEP 3: Set lastOnlineAt timestamp (mark as offline)
    const lastOnlineRef = ref(
      state.rtdb,
      `accounts/${accountId}/presence/${userId}/lastOnlineAt`
    );
    set(lastOnlineRef, serverTimestamp()).catch(err =>
      console.error('[PresenceManager] Error setting lastOnlineAt:', err)
    );

    // STEP 4: Unsubscribe from .info/connected listener
    if (state.connectedListenerUnsubscribe) {
      state.connectedListenerUnsubscribe();
      state.connectedListenerUnsubscribe = null;
    }

    // STEP 5: Remove from tracking Map
    this.presenceStates.delete(key);

    console.log('[PresenceManager] Cleanup complete for', key);
  }

  /**
   * Clean up all tracked presences.
   * Useful for complete shutdown or logout.
   */
  cleanupAll(): void {
    console.log('[PresenceManager] Cleaning up all presences');
    const keys = Array.from(this.presenceStates.keys());
    keys.forEach(key => {
      const parts = key.split(':');
      if (parts.length === 3) {
        this.cleanup(parts[0], parts[1], parts[2]);
      }
    });
  }

  /**
   * Get the current state for debugging purposes.
   * @returns Map of all tracked presence states
   */
  getState(): Map<string, PresenceState> {
    return this.presenceStates;
  }
}

// Export a singleton instance
export const presenceManager = new PresenceManager();
