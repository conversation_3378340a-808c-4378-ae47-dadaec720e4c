/**
 * PWA Icon Generator Script
 *
 * This script helps you generate PWA icons from your SVG logo.
 *
 * PREREQUISITES:
 * Install sharp: npm install -D sharp
 *
 * USAGE:
 * node scripts/generatePWAIcons.js
 *
 * This will generate all required PWA icons in the public folder.
 *
 * NOTE: Since you have SVG logos, you might want to:
 * 1. Use an online tool like https://www.pwabuilder.com/imageGenerator
 * 2. Or convert your SVG to PNG manually in a design tool
 * 3. Or install sharp and use this script
 *
 * For best results, start with a PNG version of your logo at least 512x512px
 */

import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SOURCE_LOGO_LIGHT = path.join(__dirname, '../public/assets/logo/SELIO_LOGO_BLACK.svg');
const SOURCE_LOGO_DARK = path.join(__dirname, '../public/assets/logo/SELIO_LOGO_WHITE.svg');
const OUTPUT_DIR = path.join(__dirname, '../public');

const ICON_CONFIGS = [
    // Light theme icons (black logo on white/transparent background)
    {
        source: 'light',
        configs: [
            { name: 'pwa-192x192.png', size: 192, padding: 0 },
            { name: 'pwa-512x512.png', size: 512, padding: 0 },
            { name: 'pwa-maskable-192x192.png', size: 192, padding: 20 },
            { name: 'pwa-maskable-512x512.png', size: 512, padding: 51 },
            { name: 'apple-touch-icon.png', size: 180, padding: 0 },
        ]
    },
    // Dark theme icons (white logo on dark background)
    {
        source: 'dark',
        configs: [
            { name: 'pwa-192x192-dark.png', size: 192, padding: 0 },
            { name: 'pwa-512x512-dark.png', size: 512, padding: 0 },
            { name: 'pwa-maskable-192x192-dark.png', size: 192, padding: 20 },
            { name: 'pwa-maskable-512x512-dark.png', size: 512, padding: 51 },
        ]
    }
];

async function generateIcons() {
    console.log('🎨 Generating PWA icons for light and dark themes...\n');

    if (!fs.existsSync(SOURCE_LOGO_LIGHT) || !fs.existsSync(SOURCE_LOGO_DARK)) {
        console.error(`❌ Source logos not found`);
        console.log(`   Light: ${SOURCE_LOGO_LIGHT}`);
        console.log(`   Dark: ${SOURCE_LOGO_DARK}`);
        console.log('\n📝 Please provide PNG logo files or convert your SVGs to PNG first.');
        console.log('   You can use an online tool like https://www.pwabuilder.com/imageGenerator');
        return;
    }

    try {
        for (const iconSet of ICON_CONFIGS) {
            const sourceLogo = iconSet.source === 'light' ? SOURCE_LOGO_LIGHT : SOURCE_LOGO_DARK;
            const bgColor = iconSet.source === 'light'
                ? { r: 255, g: 255, b: 255, alpha: 1 }  // White background for light theme
                : { r: 0, g: 0, b: 0, alpha: 1 };        // Black background for dark theme

            console.log(`\n📦 Generating ${iconSet.source} theme icons...`);

            for (const icon of iconSet.configs) {
                const outputPath = path.join(OUTPUT_DIR, icon.name);

                // Calculate content size (with padding for maskable icons)
                const contentSize = icon.size - (icon.padding * 2);

                // Create a solid background canvas
                const canvas = sharp({
                    create: {
                        width: icon.size,
                        height: icon.size,
                        channels: 4,
                        background: bgColor
                    }
                });

                // Resize the logo
                const logoBuffer = await sharp(sourceLogo)
                    .resize(contentSize, contentSize, {
                        fit: 'contain',
                        background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent for the logo itself
                    })
                    .png()
                    .toBuffer();

                // Composite the logo on top of the background
                const xOffset = icon.padding || 0;
                const yOffset = icon.padding || 0;

                await canvas
                    .composite([{
                        input: logoBuffer,
                        top: yOffset,
                        left: xOffset
                    }])
                    .png()
                    .toFile(outputPath);

                console.log(`   ✅ ${icon.name}`);
            }
        }

        console.log('\n🎉 All icons generated successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Check the generated icons in the public/ folder');
        console.log('   2. Light theme icons use black logo on white background');
        console.log('   3. Dark theme icons use white logo on black background');
        console.log('   4. Run: npm run build && npm run preview');
        console.log('   5. Test on mobile device with both light and dark themes');

    } catch (error) {
        console.error('❌ Error generating icons:', error.message);
        console.log('\n💡 Tip: If sharp fails to install, you can:');
        console.log('   1. Use https://www.pwabuilder.com/imageGenerator');
        console.log('   2. Or manually create the icons in a design tool');
        console.log('   3. Create separate icons for -dark.png variants');
    }
}

// Run the generator
generateIcons();
