import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import PageTitle from '../../components/molecules/PageTitle';
import ExtraChargesCreate from './ExtraChargesCreate';
import ExtraChargesEdit from './ExtraChargesEdit';
import ExtraChargesList from './ExtraChargesList';

export default function ExtraChargesPage() {
  const { t } = useTranslation();
  const { sellPointId, isLoading } = useGlobalResourceFilters();

  if (isLoading || !sellPointId) {
    return null;
  }

  return (
    <Box p={2}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('extraCharges.title')}
        description={
          <>
            {t('extraCharges.description')}
            <br />
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />

      <ExtraChargesList sellPointId={sellPointId} />
      <ExtraChargesEdit sellPointId={sellPointId} />
      <ExtraChargesCreate sellPointId={sellPointId} />
    </Box>
  );
}
