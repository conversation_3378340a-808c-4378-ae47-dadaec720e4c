import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import SimpleTable from '~/components/organisms/CustomTable/otherTables/SimpleTable';
import { useTheme } from '~/contexts/ThemeContext';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import { ColumnConfig } from '../../../types/globals';
import SubFiltersDisplay from '../reports/report-transactions/components/SubFiltersTransactions';
import ExtraDataInvoices from './ExtraDataInvoices';

interface TableRow {
  id: string;
  billId: string;
  billDate: string;
  createdBy: string;
  billName: string;
  billClosedAt: number;
  series: string;
  number: string;
  closedAt: number;
  closedBy: string;
  closedFrom: string;
  closedWith: string;
  covers: number;
  dinningOption: string;
  extraCharges?: any;
  extraChargesValue: number;
  items: any;
  itemsValue: number;
  openedAt: number;
  openedBy: string;
  billFiscalNumber: string;
  discountsValue: number;
  owner: string;
  payments: any;
  paymentsValue: number;
  printedBills?: any;
  section: string;
  source: string;
  transfers: any[];
  subTotalValue: number;
  billPaymentsValue: number;
  createdAt: number;
}

export default function InvoicesTable({
  tableData,
  currency,
}: {
  tableData: any;
  currency?: string;
}) {
  const { theme } = useTheme();
  const { t } = useTranslation('');
  console.log(tableData);

  const transactionsTablConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'billDate',
      textAlign: 'start',
      label: 'Bill Date',
      render: (row: TableRow, _, __, isSelected: any) => {
        const openedTime = new Date(row.createdAt);
        const timeString = openedTime.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        });
        const date = row.billDate.split('-');

        return (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <img
                src={'/assets/icons/invoice.svg'}
                alt={`${row.billId} invoice`}
                style={{
                  width: '20px',
                  filter: isSelected
                    ? 'brightness(0) saturate(100%) invert(100%) sepia(7%) saturate(0%) hue-rotate(39deg) brightness(106%) contrast(109%)'
                    : 'brightness(0) saturate(100%) invert(77%) sepia(0%) saturate(230%) hue-rotate(85deg) brightness(91%) contrast(83%)',
                }}
              />
              <Typography
                sx={{
                  fontSize: '14px',
                  color: isSelected ? '#FFFFFF' : '#AAAAAA',
                }}
              >
                {`${date[2]}/${date[1]}`}
                {`, ${timeString}`}
              </Typography>
            </Box>
          </>
        );
      },
    },
    {
      id: 'series',
      textAlign: 'start',
      label: 'Series',
      render: (row: TableRow, _, __, isSelected: any) => {
        return (
          <Typography
            sx={{
              fontWeight: 500,
              color: isSelected ? '#FFFFFF' : 'inherit',
            }}
          >
            {row.series + '-' + row.number}
          </Typography>
        );
      },
    },
    {
      id: 'owner',
      textAlign: 'start',
      label: 'Owner',
      render: (row: TableRow, _, __, isSelected: any) => {
        return (
          <Typography
            sx={{
              color: isSelected ? '#FFFFFF' : '#AAAAAA',
              fontWeight: 300,
              fontSize: '14px',
            }}
          >
            {t('reports.emmitedBy')}
            <span
              style={{
                fontWeight: 500,
                color: isSelected
                  ? '#FFFFFF'
                  : theme.palette.mode == 'light'
                    ? 'black '
                    : '#fff ',
              }}
            >
              {' '}
              {row.createdBy}
            </span>
          </Typography>
        );
      },
    },
    {
      id: 'billName',
      textAlign: 'start',
      label: 'Bill Name',
      render: (row: TableRow, _, __, isSelected: any) => {
        return (
          <Typography
            sx={{
              fontWeight: 500,
              color: isSelected ? '#FFFFFF' : 'inherit',
            }}
          >
            {row.billName}
          </Typography>
        );
      },
    },

    {
      id: 'billFiscalNumber',
      textAlign: 'start',
      label: 'Fiscal Receipt',
      render: (row: TableRow, _, __, isSelected: any) => {
        if (!row.billFiscalNumber) return;
        return (
          <Typography
            sx={{
              color: isSelected ? '#FFFFFF' : '#AAAAAA',
              fontWeight: 300,
              fontSize: '14px',
            }}
          >
            {t('reports.fiscalReceipt')}
            <span
              style={{
                fontWeight: 500,
                color: isSelected
                  ? '#FFFFFF'
                  : theme.palette.mode == 'light'
                    ? 'black '
                    : '#fff ',
              }}
            >
              #{row.billFiscalNumber === '-1' ? 'Demo' : row.billFiscalNumber}
            </span>
          </Typography>
        );
      },
    },
    {
      id: 'billPaymentsValue',
      textAlign: 'start',
      label: 'Total Value',
      render: (row: TableRow, _, __, isSelected: any) => {
        return (
          <Typography
            sx={{
              color: isSelected
                ? '#FFFFFF'
                : theme.palette.mode == 'light'
                  ? 'black'
                  : '#fff ',
              fontSize: '14px',
              textDecoration: row.closedWith === 'comp' ? 'line-through' : '',
            }}
          >
            {formatAndDivideNumber(row.billPaymentsValue)}
          </Typography>
        );
      },
    },
  ];

  return (
    <>
      <Box sx={{ py: 7, width: '100%' }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            flexDirection: { xs: 'column-reverse', md: 'row' },
            alignItems: { xs: 'end', lg: 'center' },
            mb: 2,
          }}
        >
          <SubFiltersDisplay />
        </Box>
        <SimpleTable
          hasFilters={false}
          config={transactionsTablConfig}
          data={tableData.sort(
            (a: TableRow, b: TableRow) => b.createdAt - a.createdAt
          )}
          searchMaxWidth={500}
          separateFirstColumn={true}
          searchPlaceHolder={t('reports.searchInvoices')}
          enableSidePanel={true}
          renderSidePanel={rowData => (
            <ExtraDataInvoices extraData={{ ...rowData, currency }} />
          )}
        />
      </Box>
    </>
  );
}
