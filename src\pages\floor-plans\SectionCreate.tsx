import { Box } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  maxValue,
  minValue,
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useRedirect,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import { validateName } from '~/utils/validateName';
import { CustomInput } from '../../components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';
import {
  GRID_SIZE,
  SMALL_TABLE_SIZE,
  TABLE_SIZE,
  TABLES_BREAKPOINT,
} from './SectionEdit';

function SectionCreateInner(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const { setValue, getValues } = useFormContext();

  /**
   * This function is called before submitting a form. It creates an array of table objects based
   * on the number of tables specified in the form.
   */
  const beforeHandleSubmit = () => {
    const values = getValues();
    const tableSize =
      values.tablesNo > TABLES_BREAKPOINT ? SMALL_TABLE_SIZE : TABLE_SIZE;
    const maxPerRow = values.tablesNo > TABLES_BREAKPOINT ? 11 : 9;

    const items = [];
    for (let i = 0; i < values.tablesNo; i++) {
      const startX = ((i % maxPerRow) * (tableSize + 1) + 2) * GRID_SIZE;
      const startY =
        (Math.floor(i / maxPerRow) * (tableSize + 1) + 1) * GRID_SIZE;

      const newTable = {
        number: i + 1,
        shape: 'square',
        position: {
          startX,
          startY,
          endX: startX + tableSize * GRID_SIZE,
          endY: startY + tableSize * GRID_SIZE,
        },
      };
      items.push(newTable);
    }

    setValue('active', false);
    setValue('items', items);
  };

  const handleClose = () => {
    redirect('list', RESOURCES.FLOOR_PLANS);
  };

  const { t } = useTranslation();

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('floorPlansPage.addNewSection')}
      >
        <SaveButton
          onClick={beforeHandleSubmit}
          type="button"
          label={t('shared.save')}
          alwaysEnable
          icon={<></>}
        />
      </ModalHeader>
      <Box sx={{ p: 2, width: '100%' }}>
        <ReferenceInput source="sellPointId" reference={RESOURCES.LOCATIONS}>
          <CustomInput
            type="select"
            source="sellPointId"
            label={t('shared.location')}
            optionText="name"
            optionValue="id"
            placeholder="None"
            defaultValue={sellPointId}
            readOnly
            roundedCorners="top"
          />
        </ReferenceInput>
        <CustomInput
          type="text"
          sanitize="singleLine"
          ui="custom"
          isRequired
          source="name"
          label={t('shared.name')}
          validate={[required(), validateName]}
        />
        <CustomInput
          type="text"
          sanitize="singleLine"
          ui="custom"
          source="label"
          label={t('floorPlansPage.prefix')}
        />
        <CustomInput
          type="number"
          sanitize="singleLine"
          ui="custom"
          isRequired
          source="tablesNo"
          label={t('floorPlansPage.numberOfTables')}
          validate={[required(), minValue(1), maxValue(45)]}
          roundedCorners="bottom"
        />
      </Box>
    </>
  );
}

export default function SectionCreate(props: { sellPointId: string }) {
  const { sellPointId } = props;
  if (!sellPointId) {
    return null;
  }
  return (
    <CreateDialog
      maxWidth="sm"
      fullWidth
      mutationOptions={{ meta: { sellPointId: sellPointId } }}
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <SectionCreateInner sellPointId={sellPointId} />
      </SimpleForm>
    </CreateDialog>
  );
}
