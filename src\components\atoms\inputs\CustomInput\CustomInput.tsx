import { useCallback, useMemo, useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import {
  Box,
  IconButton,
  Theme,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { get } from 'lodash';
import { useTranslate } from 'react-admin';
import { useFormContext, useWatch } from 'react-hook-form';

import { useTheme } from '~/contexts';
import { sanitizeTextInput } from '~/utils/sanitizeTextInput';
import {
  detectRequired,
  getInputComponent,
  getInputTypeSpecificProps,
  getSanitizationPreset,
} from './helpers';

import type { CustomInputProps } from './types';

/**
 * CustomInput - Universal React-Admin input wrapper with optional custom UI and sanitization
 *
 * Features:
 * - Supports all 22 React-Admin input types
 * - Two UI modes: 'original' (standard RA) or 'custom' (label left, input right)
 * - Optional text sanitization with 3 presets: 'singleLine', 'multiLine', 'identifier'
 * - Works in all contexts: SimpleForm, ArrayInput, TranslatableInputs, ReferenceInput, Modals
 * - Supports temp fields pattern for modal buffering (recommended for modals)
 *
 * USAGE IN FORMS:
 * ===============
 * @example
 * // Custom UI with sanitization (default)
 * <CustomInput type="text" source="name" sanitize="singleLine" />
 *
 * @example
 * // Original RA UI with sanitization
 * <CustomInput type="text" source="name" sanitize="singleLine" ui="original" />
 *
 * @example
 * // Select with custom UI and validation
 * <CustomInput
 *   type="select"
 *   source="category"
 *   choices={categories}
 *   validate={required()}
 * />
 *
 * USAGE IN MODALS WITH TEMP FIELDS (Recommended Pattern):
 * ========================================================
 * Use temp field names to buffer changes before committing to actual form path.
 * This pattern eliminates useState, provides automatic validation, and enables
 * all React-Admin features (FileInput, ImageInput, validation, formatting, etc.)
 *
 * @example
 * // Modal using temp fields (from AddCategoryGroupModal.tsx pattern)
 * function AddCategoryGroupModal({ path, onClose }) {
 *   const { setValue, getValues } = useFormContext();
 *   const tempPrefix = '__modal_temp';
 *
 *   // Pre-populate temp fields for edit mode (optional)
 *   useEffect(() => {
 *     const existingData = getValues(path);
 *     if (existingData) {
 *       setValue(tempPrefix, existingData);
 *     }
 *   }, [path]);
 *
 *   const handleSave = () => {
 *     // Transfer from temp fields to actual path
 *     const tempData = getValues(tempPrefix);
 *     setValue(path, tempData, { shouldDirty: true });
 *
 *     // Optional: Clear temp fields
 *     setValue(tempPrefix, {}, { shouldDirty: false });
 *     onClose();
 *   };
 *
 *   return (
 *     <Dialog open onClose={onClose}>
 *       <SimpleForm toolbar={false}>
 *         <CustomInput
 *           type="text"
 *           source={`${tempPrefix}.displayName`}
 *           label="Name"
 *           validate={required()}
 *           sanitize="singleLine"
 *         />
 *
 *         <CustomInput
 *           type="select"
 *           source={`${tempPrefix}.color`}
 *           label="Color"
 *           choices={colorChoices}
 *         />
 *
 *         <CustomInput
 *           type="image"
 *           source={`${tempPrefix}.images`}
 *           label="Images"
 *           multiple
 *         />
 *
 *         <Button onClick={handleSave}>Save</Button>
 *       </SimpleForm>
 *     </Dialog>
 *   );
 * }
 *
 * TEMP FIELDS BENEFITS:
 * - ✅ No useState needed - form state manages everything
 * - ✅ Automatic validation with visual feedback
 * - ✅ All React-Admin input types work (FileInput, ImageInput, etc.)
 * - ✅ Built-in formatting, parsing, sanitization
 * - ✅ Automatic dirty/error tracking via formState
 * - ✅ Changes buffered until explicitly committed
 * - ✅ Can cancel by discarding temp fields
 * - ✅ Complex data types supported (arrays, objects, files)
 *
 * MIGRATION FROM MUI COMPONENTS:
 * ==============================
 * @example
 * // BEFORE: Using native MUI with useState
 * const [state, setState] = useState({ name: '' });
 * <TextField
 *   value={state.name}
 *   onChange={(e) => setState(prev => ({ ...prev, name: e.target.value }))}
 * />
 *
 * // AFTER: Using CustomInput with temp fields
 * <CustomInput
 *   type="text"
 *   source="__modal_temp.name"
 *   sanitize="singleLine"
 * />
 */
export const CustomInput = ({
  type,
  source,
  ui = 'custom',
  sanitize,
  validate,
  parse,
  label,
  helperText,
  sx,
  locale,
  ...props
}: CustomInputProps) => {
  const { theme } = useTheme();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const custom = theme.palette.custom;

  // Extract common props
  const {
    placeholder,
    disabled,
    isRequired: isRequiredProp,
    readOnly,
    multiline,
    variant,
  } = props as any;

  // Determine if locale should be passed to the component
  // Only date, datetime, time, and number inputs support locale
  const supportsLocale = ['date', 'datetime', 'time', 'number'].includes(type);
  const localeProps = supportsLocale && locale ? { locale } : {};

  // Create sanitizing parse function
  // For multiline inputs, we'll sanitize on blur instead of onChange to preserve Enter key
  const sanitizingParse = useMemo(() => {
    if (!sanitize || multiline) return parse;
    const preset = getSanitizationPreset(sanitize);
    if (!preset) return parse;

    return (value: any) => {
      const sanitized = sanitizeTextInput(value, preset);
      return parse ? parse(sanitized) : sanitized;
    };
  }, [sanitize, parse, multiline]);

  // Get the React-Admin input component
  const InputComponent = getInputComponent(type);
  const typeSpecificProps = getInputTypeSpecificProps(type);

  // ORIGINAL UI MODE: Just render RA component with sanitization
  // Also force original mode for fileUpload with default variant (needs full layout space)
  if (ui === 'original' || (type === 'fileUpload' && variant === 'default')) {
    return (
      <InputComponent
        source={source}
        label={label}
        validate={validate}
        parse={sanitizingParse}
        helperText={helperText}
        placeholder={placeholder}
        disabled={disabled}
        sx={sx}
        {...typeSpecificProps}
        {...localeProps}
        {...props}
      />
    );
  }

  // CUSTOM UI MODE: Access form state without registering field
  const {
    formState: { errors },
    setValue,
  } = useFormContext();
  const translate = useTranslate();
  const fieldError = get(errors, source!);
  const fieldValue = useWatch({ name: source! });
  const [focused, setFocused] = useState(false);
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);

  // Parse React-Admin's error format: @@react-admin@@{"message":"ra.validation.required"}
  const parseErrorMessage = (error: any): string => {
    if (!error?.message || typeof error.message !== 'string') return '';

    const message = error.message;

    // Check if it's React-Admin's special format
    if (message.startsWith('@@react-admin@@')) {
      try {
        const jsonStr = message.substring('@@react-admin@@'.length);
        const errorData = JSON.parse(jsonStr);

        // Handle two cases:
        // 1. errorData is a string: "ra.validation.required"
        // 2. errorData is an object: {message: "ra.validation.required", args: {...}}
        if (typeof errorData === 'string') {
          return translate(errorData);
        } else if (errorData && typeof errorData.message === 'string') {
          return translate(errorData.message, errorData.args || {});
        }
      } catch (e) {
        console.warn(
          '[CustomInput] Failed to parse React-Admin error format:',
          message,
          e
        );
        return message;
      }
    }

    // Fallback to the raw message
    return message;
  };

  // Detect if field is required
  const isFieldRequired = isRequiredProp ?? detectRequired(validate);

  // Handle focus/blur
  const handleFocus = () => setFocused(true);
  const handleBlur = (event: any) => {
    setFocused(false);

    // For multiline with sanitization, manually trigger sanitization on blur
    if (multiline && sanitize) {
      const currentValue = event.target.value;
      const preset = getSanitizationPreset(sanitize);
      if (preset) {
        const sanitized = sanitizeTextInput(currentValue, preset);
        if (sanitized !== currentValue) {
          setValue(source!, sanitized);
        }
      }
    }
  };

  // Handle tooltip toggle for mobile
  const handleTooltipToggle = useCallback(() => {
    setIsTooltipOpen(true);
  }, []);

  // Handle tooltip close
  const handleTooltipClose = useCallback(() => {
    setIsTooltipOpen(false);
  }, []);

  // Handle mouse enter
  const handleTooltipMouseEnter = useCallback(() => {
    setIsTooltipOpen(true);
  }, []);

  // Determine if field is disabled
  const isDisabled = disabled || readOnly;

  // Disabled background color
  const disabledBg =
    theme.palette.mode == 'light'
      ? custom.fieldBg
      : theme.palette.background.tinted;

  // Tooltip content for errors/helper text
  const tooltipContent =
    parseErrorMessage(fieldError) ||
    (typeof helperText === 'string' ? helperText : '') ||
    '';

  return (
    <Box
      sx={{
        width: '100%',
        position: 'relative',
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        border: `1px solid ${custom.gray400}`,
        borderBottom: focused
          ? `1px solid ${theme.palette.primary.main}`
          : `1px solid ${custom.gray400}`,
        zIndex: focused ? 2 : 0,
        marginTop: '-1px',
        bgcolor:
          isDisabled && theme.palette.mode === 'light'
            ? 'custom.gray400'
            : 'custom.fieldBg',
        backgroundImage: isDisabled
          ? `repeating-linear-gradient(135deg,transparent,transparent 1px,${disabledBg} 1px,${disabledBg} 5px,transparent 5px,transparent 6px,${disabledBg} 6px,${disabledBg} 10px)`
          : 'none',
        backgroundSize: '7px 7px',
        '& .MuiFormControl-root': {
          p: 0,
          m: 0,
        },
        ...(type === 'checkboxGroup' && {
          '& .MuiFormGroup-root': {
            flex: 1,
            pl: 2,
          },
          '& .MuiFormControlLabel-root': {
            flex: 'unset',
            pl: 'unset',
          },
        }),
        ...(type === 'boolean' && {
          '& .MuiSwitch-root': {
            mt: isXSmall ? 0 : 0.5,
          },
        }),

        ...sx,
      }}
    >
      {/* Label Section */}
      <Box
        sx={{
          width: isXSmall ? '100%' : '200px',
          minWidth: isXSmall ? '100%' : '200px',
          minHeight: isXSmall ? '35px' : '45px',
          bgcolor: !!fieldError
            ? 'error.light'
            : focused
              ? 'primary.light'
              : 'background.tinted',
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'space-between',
          px: 2,
          py: 1,
          flexShrink: 0,
        }}
      >
        <Typography
          variant="body2"
          color={!!fieldError ? 'error.main' : 'inherit'}
          sx={{ fontWeight: 500, mt: isXSmall ? 0 : 0.5 }}
        >
          {label}
          {isFieldRequired ? ' *' : ''}
        </Typography>

        {(helperText || fieldError) && (
          <Tooltip
            title={tooltipContent}
            arrow
            placement="top"
            open={isTooltipOpen}
            onClose={handleTooltipClose}
            disableFocusListener
            disableHoverListener
            disableTouchListener
          >
            <IconButton
              size="small"
              onClick={handleTooltipToggle}
              onMouseEnter={handleTooltipMouseEnter}
              onMouseLeave={handleTooltipClose}
              sx={{ ml: 1, p: '0 !important', mt: isXSmall ? 0 : 0.5 }}
            >
              <InfoIcon
                fontSize="small"
                color={fieldError ? 'error' : 'disabled'}
              />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* Input Section */}
      <InputComponent
        sx={{
          flex: 1,
          pl: ['radio', 'checkboxGroup', 'boolean'].includes(type) ? 2 : 0,
          '.MuiOutlinedInput-notchedOutline': {
            border: 0,
            borderColor: 'transparent !important',
          },
          ...((type === 'autocompleteArray' || type === 'autocomplete') && {
            '& .MuiAutocomplete-endAdornment': {
              right: '7px !important',
            },
          }),
          ...(type === 'password' && {
            '& .MuiInputBase-root ': {
              pr: '7px !important',
            },
          }),
        }}
        source={source}
        label=""
        onFocus={handleFocus}
        onBlur={handleBlur}
        helperText={false} // Hide default helper text since we're using tooltip
        validate={validate}
        parse={sanitizingParse}
        placeholder={type === 'phone' ? placeholder : undefined}
        disabled={disabled}
        {...typeSpecificProps}
        {...localeProps}
        {...(type !== 'phone' && type !== 'code' && type !== 'fileUpload'
          ? { ...props, placeholder: undefined }
          : props)}
      />

      {/* Custom Placeholder - positioned absolutely for consistent display */}
      {!focused &&
        (type === 'fileUpload'
          ? Array.isArray(fieldValue) && fieldValue.length === 0
          : !fieldValue) &&
        placeholder &&
        type !== 'phone' &&
        type !== 'code' &&
        type !== 'select' &&
        type !== 'selectArray' &&
        type !== 'autocomplete' &&
        type !== 'autocompleteArray' &&
        type !== 'radio' &&
        type !== 'checkboxGroup' &&
        !(type === 'fileUpload' && variant !== 'compact') && (
          <Typography
            fontSize="14px"
            color="custom.fadedText"
            sx={{
              position: 'absolute',
              top: isXSmall ? '35px' : 0,
              ml: isXSmall ? 2 : 27,
              mt: 1.5,
              zIndex: -1,
              pointerEvents: 'none',
            }}
          >
            {placeholder}
          </Typography>
        )}
    </Box>
  );
};
