import { useMemo } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import type { NutritionalValues } from './NutritionalValuesModal';

const defaultValues: NutritionalValues = {
  portion: {
    energy: '',
    fats: 0,
    saturatedFats: 0,
    carbs: 0,
    sugars: 0,
    fiber: 0,
    protein: 0,
    salt: 0,
  },
  per100g: {
    energy: '',
    fats: 0,
    saturatedFats: 0,
    carbs: 0,
    sugars: 0,
    fiber: 0,
    protein: 0,
    salt: 0,
  },
};

interface NutritionalValuesContentProps {
  value: NutritionalValues | undefined;
  onChange: (value: NutritionalValues) => void;
  onClose: () => void;
}

/**
 * NutritionalValuesContent - Table content for editing nutritional values
 * Designed to be used inside a PopoverInput or standalone
 */
export default function NutritionalValuesContent({
  value,
  onChange,
  onClose,
}: NutritionalValuesContentProps) {
  const { t } = useTranslation();
  const values = value || defaultValues;

  const handleChange = (
    section: 'portion' | 'per100g',
    field: string,
    inputValue: string
  ) => {
    const newValues = {
      ...values,
      [section]: {
        ...values[section],
        [field]:
          field === 'energy'
            ? inputValue
            : inputValue === ''
              ? 0
              : parseFloat(inputValue),
      },
    };
    onChange(newValues);
  };

  const textFieldProps = useMemo(
    () => ({
      InputProps: {
        sx: {
          height: '40px',
          '& input': {
            textAlign: 'right',
            padding: '4px 8px',
          },
        },
      },
      variant: 'outlined' as const,
      size: 'small' as const,
      placeholder: t('shared.enterValue'),
      sx: {
        width: '100%',
        '& .MuiOutlinedInput-root': {
          height: '40px',
          '& fieldset': {
            borderColor: 'transparent',
          },
          '&:hover fieldset': {
            borderColor: 'rgba(0, 0, 0, 0.23)',
          },
          '&.Mui-focused fieldset': {
            borderColor: '#1976d2',
          },
        },
      },
    }),
    [t]
  );

  const fields = [
    { key: 'energy', type: 'text' },
    { key: 'fats', type: 'number' },
    { key: 'saturatedFats', type: 'number' },
    { key: 'carbs', type: 'number' },
    { key: 'sugars', type: 'number' },
    { key: 'fiber', type: 'number' },
    { key: 'protein', type: 'number' },
    { key: 'salt', type: 'number' },
  ];

  const fieldLabels: Record<string, string> = {
    energy: 'nutritionalValues.energy',
    fats: 'nutritionalValues.fat',
    saturatedFats: 'nutritionalValues.saturatedFat',
    carbs: 'nutritionalValues.carbohydrates',
    sugars: 'nutritionalValues.sugar',
    fiber: 'nutritionalValues.fiber',
    protein: 'nutritionalValues.protein',
    salt: 'nutritionalValues.salt',
  };

  return (
    <Box sx={{ p: 2 }}>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          size="small"
          sx={{
            border: '1px solid rgba(224, 224, 224, 1)',
            '& .MuiTableCell-root': {
              borderRight: '1px solid rgba(224, 224, 224, 1)',
              padding: '4px 8px',
            },
            '& .MuiTableCell-root:has(.MuiTextField-root)': {
              padding: 0,
            },
            '& .MuiTableCell-root:last-child': {
              borderRight: 'none',
            },
            '& .MuiTableHead-root .MuiTableRow-root': {
              backgroundColor: '#f5f5f5',
            },
          }}
        >
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', width: '40%' }}></TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '12px' }}>
                {t('nutritionalValues.portion')}
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '12px' }}>
                {t('nutritionalValues.per100g')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fields.map(({ key, type }) => (
              <TableRow key={key}>
                <TableCell component="th" scope="row" sx={{ fontSize: '13px' }}>
                  {t(fieldLabels[key])}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type={type}
                    value={
                      type === 'number'
                        ? (values.portion[
                            key as keyof typeof values.portion
                          ] as number) || ''
                        : values.portion[key as keyof typeof values.portion]
                    }
                    onChange={e => handleChange('portion', key, e.target.value)}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type={type}
                    value={
                      type === 'number'
                        ? (values.per100g[
                            key as keyof typeof values.per100g
                          ] as number) || ''
                        : values.per100g[key as keyof typeof values.per100g]
                    }
                    onChange={e => handleChange('per100g', key, e.target.value)}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
        <Button variant="contained" size="small" onClick={onClose}>
          {t('shared.done')}
        </Button>
      </Box>
    </Box>
  );
}
