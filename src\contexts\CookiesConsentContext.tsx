import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
} from 'react';

import 'vanilla-cookieconsent/dist/cookieconsent.css';

import { useTranslation } from 'react-i18next';
import * as CookieConsent from 'vanilla-cookieconsent';

import { useTheme } from './ThemeContext';

interface CookiesConsentContextProps {
  handleInsideButtonClick: () => void;
}

interface CookieConsentTranslations {
  title: string;
  description: string;
  acceptAllBtn: string;
  acceptNecessaryBtn: string;
  savePreferencesBtn: string;
  closeIconLabel: string;
  serviceCounterLabel: string;
  description2: string;
  moreInformation: string;
  privacyNotice: string;
  necessary: string;
  necessaryDescription: string;
  alwaysEnabled: string;
  analytics: string;
  analyticsDescription: string;
  functionality: string;
  functionalityDescription: string;
  retargeting: string;
  retargetingDescription: string;
}

declare global {
  interface Window {
    initCookieConsent: (translations?: CookieConsentTranslations) => any;
  }
}

const CookiesConsentContext = createContext<
  CookiesConsentContextProps | undefined
>(undefined);

const CookiesConsentProvider = ({ children }: any) => {
  const outsideButtonRef = useRef<HTMLButtonElement>(null);
  const { theme } = useTheme();
  const { t } = useTranslation();
  const handleInsideButtonClick = useCallback(() => {
    if (outsideButtonRef.current) {
      outsideButtonRef.current.click();
    }
  }, []);

  useEffect(() => {
    if (theme.palette.mode === 'light') {
      document.documentElement.classList.remove('cc--darkmode');
    } else {
      document.documentElement.classList.add('cc--darkmode');
    }
  }, [theme]);

  useEffect(() => {
    // Create translations object from i18n
    const translations = {
      title: t('cookiesConsent.title'),
      description: t('cookiesConsent.description'),
      acceptAllBtn: t('cookiesConsent.acceptAllBtn'),
      acceptNecessaryBtn: t('cookiesConsent.acceptNecessaryBtn'),
      savePreferencesBtn: t('cookiesConsent.savePreferencesBtn'),
      closeIconLabel: t('cookiesConsent.closeIconLabel'),
      serviceCounterLabel: t('cookiesConsent.serviceCounterLabel'),
      description2: t('cookiesConsent.description2'),
      moreInformation: t('cookiesConsent.moreInformation'),
      privacyNotice: t('cookiesConsent.privacyNotice'),
      necessary: t('cookiesConsent.necessary'),
      necessaryDescription: t('cookiesConsent.necessaryDescription'),
      alwaysEnabled: t('cookiesConsent.alwaysEnabled'),
      analytics: t('cookiesConsent.analytics'),
      analyticsDescription: t('cookiesConsent.analyticsDescription'),
      functionality: t('cookiesConsent.functionality'),
      functionalityDescription: t('cookiesConsent.functionalityDescription'),
      retargeting: t('cookiesConsent.retargeting'),
      retargetingDescription: t('cookiesConsent.retargetingDescription'),
    };

    // Use shared configuration from the global function
    if (window.initCookieConsent) {
      CookieConsent.run(window.initCookieConsent(translations));
    }
  }, [t]);

  return (
    <CookiesConsentContext.Provider value={{ handleInsideButtonClick }}>
      {children}
      {/* Workaround to make the modal popup on click */}
      <button
        ref={outsideButtonRef}
        type="button"
        style={{ display: 'none' }}
        data-cc="show-preferencesModal"
      />
    </CookiesConsentContext.Provider>
  );
};

const useCookiesConsent = (): CookiesConsentContextProps => {
  const context = useContext(CookiesConsentContext);
  if (!context) {
    throw new Error(
      'useCookiesConsent must be used within a CookiesConsentProvider'
    );
  }
  return context;
};

export { CookiesConsentProvider, useCookiesConsent };
