import { useCallback } from 'react';
import { Box, Typography } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  email,
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useGetIdentity,
  useGetOne,
  useRedirect,
  useUnique,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { validatePhone } from '~/components/atoms/inputs/PhoneNumberInput';
import { RESOURCES } from '~/providers/resources';
import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import { validateName } from '~/utils/validateName';

export const MemberCreate = () => {
  const redirect = useRedirect();
  const { t } = useTranslation('');
  const unique = useUnique();

  const { data: identity, isLoading: identityIsLoading } = useGetIdentity();
  const { data: member, isLoading: memberIsLoading } = useGetOne(
    RESOURCES.TEAM_MEMBERS,
    { id: identity?.id },
    { enabled: !identityIsLoading }
  );

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.TEAM_MEMBERS);
  }, [redirect]);

  if (memberIsLoading) {
    return null;
  }

  return (
    <CreateDialog
      {...getFullscreenModalProps()}
      mutationOptions={{
        meta: {
          memberId: identity?.id,
          memberName: member?.displayName ?? identity?.fullName,
        },
      }}
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={handleClose}
          title={t('members.addTeamMember')}
        >
          <SaveButton
            type="button"
            icon={<></>}
            alwaysEnable
            label={t('shared.save')}
          />
        </ModalHeader>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            maxWidth: '800px',
            width: '90%',
            mx: 'auto',
            my: 8,
            gap: 6,
          }}
        >
          <Subsection title={t('members.personalInformation')}>
            <CustomInput
              type="text"
              ui="custom"
              sanitize="singleLine"
              source="firstName"
              label={t('members.firstName')}
              placeholder="John"
              isRequired
              validate={required()}
            />
            <CustomInput
              type="text"
              ui="custom"
              sanitize="singleLine"
              source="lastName"
              label={t('members.lastName')}
              placeholder="Smith"
              validate={required()}
            />
            <CustomInput
              type="text"
              ui="custom"
              sanitize="singleLine"
              source="displayName"
              label={t('members.displayName')}
              placeholder="Johnie"
              validate={[required(), unique(), validateName]}
            />
            <CustomInput
              type="phone"
              source="phone"
              label={t('members.phone')}
              placeholder="720 123 456"
              validate={[required(), validatePhone, unique()]}
            />
            <CustomInput
              type="text"
              ui="custom"
              sanitize="singleLine"
              source="email"
              label="E-mail"
              validate={[required(), email(), unique()]}
              placeholder="<EMAIL>"
            />
            {/* @ts-ignore */}
            <Typography variant="label" fontWeight={200}>
              {t('members.emailDescription')}
            </Typography>
          </Subsection>

          <Subsection title={t('members.permissions')}>
            <ReferenceInput source="roleId" reference={RESOURCES.PERMISSIONS}>
              <CustomInput
                source="roleId"
                type="autocomplete"
                ui="custom"
                label={t('members.role')}
                optionText="name"
                optionValue="id"
                placeholder={t('members.cashierWaiterBartender')}
                selectOnFocus={false}
                validate={[required()]}
              />
            </ReferenceInput>
            <ReferenceInput
              source="sellPointIds"
              reference={RESOURCES.LOCATIONS}
              filter={{ _d: false }}
            >
              <CustomInput
                source="sellPointIds"
                type="autocompleteArray"
                ui="custom"
                label={t('shared.location_few')}
                optionText="name"
                optionValue="id"
                placeholder={t('reportsPage.all')}
              />
            </ReferenceInput>
            {/* commenting out this code for now because we removed pay type - but leaving it in as an example.
            this input changes form based on another input value */}
            {/* <ReferenceInput source="payTypeId" reference="payTypes">
              <CustomInput
                type="select"
                label="Pay type"
                optionText="name"
                optionValue="id"
                placeholder="None"
              />
            </ReferenceInput> */}
            {/* <FormDataConsumer>
              {({ formData, ...rest }) => (
                <>
                  {formData.payTypeId === '39281039123' && (
                    <CustomInput
                      type='number'
                      label='Hourly rate'
                      source='rate'
                      {...rest}
                    />
                  )}
                  {formData.payTypeId === 'daskj1l2kj3l' && (
                    <CustomInput
                      type='number'
                      label='Annual salary'
                      source='salary'
                      {...rest}
                    />
                  )}
                  {formData.payTypeId === 'daskj1l2kj3l' && (
                    <CustomInput
                      type='number'
                      label='Weekly hours'
                      source='hours'
                      {...rest}
                    />
                  )}
                </>
              )}
            </FormDataConsumer> */}
          </Subsection>
          <Subsection
            title={t('members.credentials')}
            subtitle={t('members.credentialsDescription')}
          >
            <CustomInput
              ui="custom"
              type="code"
              source="pin"
              label={t('members.personalPasscode')}
              digits={6}
              validate={[required(), unique()]}
            />
            {/* @ts-ignore */}
            <Typography variant="label" fontWeight={200}>
              {t('members.personalPasscodeDescription')}
            </Typography>
            <Typography
              // @ts-ignore
              variant="label"
              fontWeight={200}
              display="block"
              mt={1}
            >
              {t('members.personalPasscodeDescription2')}
            </Typography>
          </Subsection>
        </Box>
      </SimpleForm>
    </CreateDialog>
  );
};
