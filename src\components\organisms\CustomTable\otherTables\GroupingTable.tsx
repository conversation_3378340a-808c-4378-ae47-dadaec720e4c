import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import {
  Box,
  Button,
  IconButton,
  SxProps,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts/ThemeContext';
import FieldsMenu from '../components/FieldsMenu';
import GroupingMenu from '../components/GroupingMenu';
import InfoModal from '../components/InfoModal';
import TableInput from '../components/TableInput';
import styles from '../styles.module.css';
import { ColumnConfig, FieldOption } from '../types/globals';

type RowData = {
  [key: string]: any;
  items?: RowData[];
  subItems?: RowData[];
  extraData?: { [key: string]: any };
};

type CustomTableProps<T extends RowData> = {
  config: ColumnConfig<T>[];
  data: T[];
  scrollable?: boolean;
  fields: FieldOption[];
  searchBar?: boolean;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  enableInfoModal?: boolean;
  filter?: boolean;
  scrollHeight?: string;
  columnsToFilter?: Array<string>;
  groupingOptions?: { label: string; value: string }[];
  groupingItems?: string[];
  separateFirstColumn?: boolean;
  fixedFirstColumn?: boolean;
  onChangeGrouping?: (items: any[]) => void;
  renderModalContent?: (rowData: T) => ReactNode;
  exportCSV?: boolean;
  handleExport?: () => void;
};

const GroupingTable = <T extends RowData>({
  config,
  data,
  scrollable,
  searchBar = true,
  columnsToFilter,
  enableInfoModal,
  renderModalContent,
  scrollHeight,
  fields,
  separateFirstColumn,
  fixedFirstColumn,
  setFields,
  groupingOptions,
  onChangeGrouping,
  groupingItems,
  exportCSV,
  handleExport,
}: CustomTableProps<T>) => {
  const { theme } = useTheme();
  const [selectedRowData, setSelectedRowData] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');

  const [expandAll, setExpandAll] = useState(false);

  const toggleAllRowsExpansion = () => {
    setExpandAll(prev => !prev);

    const expandRecursively = (
      rows: RowData[],
      parentKey = ''
    ): Record<string, boolean> => {
      let newState: Record<string, boolean> = {};

      rows.forEach((row, index) => {
        const rowKey = `${parentKey}-${index}`;
        newState[rowKey] = !expandAll;

        if (row.subItems && row.subItems.length > 0) {
          newState = {
            ...newState,
            ...expandRecursively(row.subItems, rowKey),
          };
        }
      });

      return newState;
    };

    setExpandedRows(expandRecursively(data));
  };

  const filterHierarchy = (rows: RowData[], query: string): RowData[] => {
    const filteredRows = rows
      ?.map((row): RowData | null => {
        const filteredSubItems = row.subItems
          ? filterHierarchy(row.subItems, query)
          : [];

        const matchesRow = JSON.stringify(row).toLowerCase().includes(query);

        if (matchesRow || filteredSubItems.length > 0) {
          return {
            ...row,
            subItems: filteredSubItems,
          };
        }
        return null;
      })
      .filter((row): row is RowData => row !== null);

    return filteredRows;
  };

  const filteredData = useMemo(() => {
    if (searchQuery.trim().length < 2) return data;

    const normalizedQuery = searchQuery.trim().toLowerCase();
    return filterHierarchy(data, normalizedQuery);
  }, [searchQuery, data]);

  useEffect(() => {
    setExpandedRows(
      Object.fromEntries(data?.map((_, index) => [index, false]))
    );
  }, [data]);

  const toggleRowExpansion = (rowIndex: number | string) => {
    setExpandedRows(prev => ({
      ...prev,
      [rowIndex]: !prev[rowIndex],
    }));
  };

  const handleRowClick = (row: T) => {
    if (enableInfoModal && row) {
      setSelectedRowData(row);
      setIsModalOpen(true);
    }
  };

  const closeModal = () => setIsModalOpen(false);

  const renderRows = (rows: RowData[], level = 0, parentKey = '') => {
    return rows?.map((row: any, rowIndex: number) => {
      const rowKey = `${parentKey}-${rowIndex}`;
      const isLastRow = rowIndex === rows.length - 1;
      const onClick = () => {
        if (level === 0 && isLastRow && searchQuery.length < 2) return;
        if (row.groupedBy) return toggleRowExpansion(rowKey);

        handleRowClick(row);
      };

      return (
        <React.Fragment key={rowKey}>
          <TableRow
            onClick={onClick}
            sx={{
              '@media print': {
                color: 'black !important',
              },
              backgroundColor: level === 0 ? 'inherit' : '',
              ...(isLastRow &&
                searchQuery.length > 1 &&
                row.length === 1 && {
                  display: 'none',
                }),

              ...(isLastRow &&
                searchQuery.length < 2 &&
                level === 0 && {
                  position: 'sticky',
                  bottom: '-1px',
                  backgroundColor:
                    theme.palette.mode == 'light' ? '#F2F2F2' : '#26262B',
                  zIndex: 10,
                  '@media print': {
                    position: 'initial',
                    backgroundColor: '#F2F2F2',
                    color: 'black !important',
                  },
                }),
              '&:hover': {
                backgroundColor:
                  !row.subItems && enableInfoModal
                    ? theme.palette.mode == 'light'
                      ? '#F2F2F2'
                      : '#26262B'
                    : '',
              },
            }}
          >
            {config?.map((column, colIndex) => {
              const isFirstColumn = colIndex === 0;
              const isGroupChild =
                groupingItems && groupingItems.length > 0 && !row?.groupedBy;

              const isColumnAllowed =
                isFirstColumn ||
                colIndex === config.length - 1 ||
                (fields.some(f => f.value === column.id && f.isChecked) &&
                  (!columnsToFilter ||
                    columnsToFilter.includes(column.id as string)));

              return isColumnAllowed ? (
                <TableCell
                  key={`${rowKey}-${String(column.id)}`}
                  sx={{
                    '@media print': {
                      color: 'black !important',
                    },
                    ...(isLastRow &&
                      searchQuery.length < 2 &&
                      level === 0 && { fontWeight: '600 !important' }),
                    borderRight:
                      isFirstColumn && separateFirstColumn
                        ? '1px #cecece dashed'
                        : '',
                    whiteSpace:
                      row.subItems && row.subItems.length > 0 && isFirstColumn
                        ? 'nowrap'
                        : '',
                    backgroundColor: isGroupChild
                      ? 'rgba(227, 237, 255, 0.4)'
                      : '',
                    fontWeight:
                      (row.subItems && row.subItems.length > 0) ||
                      (isLastRow && level === 0 && !searchQuery)
                        ? 600
                        : '',
                    cursor:
                      (!isLastRow && row.subItems && row.subItems.length > 0) ||
                      enableInfoModal
                        ? 'pointer'
                        : '',
                    textAlign: column.textAlign ? column.textAlign : 'center',
                    paddingLeft: level === 0 ? 1 : `${level * 2}rem`,
                    ...(isFirstColumn && {
                      width: '1%',
                      whiteSpace: 'nowrap',
                      ...(fixedFirstColumn && {
                        position: 'sticky',
                        left: '-1px !important',
                        backgroundColor:
                          isLastRow && level === 0
                            ? theme.palette.mode == 'light'
                              ? '#F2F2F2'
                              : '#26262B'
                            : theme.palette.mode == 'light'
                              ? 'white'
                              : '#13131A',
                        zIndex: 1,
                        borderRight: '1px #cecece dashed',
                      }),
                    }),
                  }}
                >
                  {row.subItems &&
                    row.subItems.length > 0 &&
                    colIndex === 0 && (
                      <IconButton sx={{ p: 0.5, mr: 0.5 }}>
                        {expandedRows[rowKey] ? (
                          <ExpandLessIcon />
                        ) : (
                          <ExpandMoreIcon />
                        )}
                      </IconButton>
                    )}
                  {column.render
                    ? column.render(row, rowIndex, colIndex)
                    : row[column.id as keyof RowData] || ''}
                </TableCell>
              ) : null;
            })}
          </TableRow>

          {expandedRows[rowKey] &&
            row.subItems &&
            renderRows(row.subItems, level + 1, rowKey)}
        </React.Fragment>
      );
    });
  };

  const { t } = useTranslation();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        justifyContent: 'space-between',
        width: '100%',
      }}
    >
      <Box sx={{ width: '100%' }}>
        <Box
          sx={{
            width: '100%',
            alignItems: 'center',
            gap: 1,
            justifyContent: 'space-between',
            display: 'flex',
            mb: { xs: 1.5, sm: 1 },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            {searchBar && <TableInput onChange={setSearchQuery} />}
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: exportCSV ? '1fr 1fr 1fr' : '1fr 1fr',
              }}
            >
              {groupingOptions && (
                <GroupingMenu
                  fields={fields}
                  groupingOptions={groupingOptions}
                  groupingItemsState={groupingItems || []}
                  onChangeGrouping={onChangeGrouping}
                />
              )}
              {columnsToFilter && (
                <FieldsMenu
                  fields={fields}
                  setFields={setFields}
                  config={config}
                  columnsToFilter={columnsToFilter}
                />
              )}
              {exportCSV && (
                <Button
                  sx={{ padding: 0, maxWidth: '70px' }}
                  className="do-not-print"
                  onClick={() => {
                    handleExport?.();
                  }}
                >
                  <FileDownloadIcon
                    color="primary"
                    sx={{ width: 20, height: 20 }}
                  />
                  {!isXSmall && 'Export'}
                </Button>
              )}
            </Box>
          </Box>
        </Box>

        <TableContainer
          className={styles.table}
          sx={
            {
              overflowY: scrollable ? 'visible' : 'visible',
              overflowX: 'auto !important',
              width: '100%',
              maxWidth: { xs: 'calc(100vw - 20px)' },
              '@media print': {
                overflowX: 'visible !important',
                maxWidth: {
                  xs: '100%!important',
                },
              },
              maxHeight:
                searchQuery.length < 2 &&
                scrollable &&
                groupingItems &&
                groupingItems.length === 0
                  ? {
                      sm: scrollHeight ? scrollHeight : 'calc(100vh - 300px)',
                      xs: 'calc(100vh - 370px)',
                    }
                  : undefined,
            } as SxProps<Theme>
          }
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {config?.map((column, index) => {
                  const isFirstColumn = index === 0;
                  const isColumnAllowed =
                    isFirstColumn ||
                    index === config.length - 1 ||
                    (fields.some(f => f.value === column.id && f.isChecked) &&
                      (!columnsToFilter ||
                        columnsToFilter.includes(column.id as string)));

                  return isColumnAllowed ? (
                    <TableCell
                      key={column.id as string}
                      sx={{
                        '@media print': {
                          backgroundColor: '#FFFFFF !important',
                          color: 'black !important',
                        },
                        maxWidth: { xs: '100px', sm: '' },
                        borderRight:
                          isFirstColumn && separateFirstColumn
                            ? '1px #cecece dashed'
                            : '',
                        backgroundColor:
                          theme.palette.mode == 'light' ? '#F2F2F2' : '#26262B',
                        cursor: enableInfoModal ? 'pointer' : 'default',
                        ...(isFirstColumn && {
                          width: '1%',
                          ...(fixedFirstColumn && {
                            position: 'sticky',
                            left: '-1px !important',
                            backgroundColor:
                              theme.palette.mode == 'light'
                                ? '#F2F2F2'
                                : '#26262B',
                            zIndex: 10,
                            borderRight: '1px #cecece dashed',
                          }),
                        }),
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          textAlign:
                            index === 0
                              ? 'start'
                              : column.textAlign
                                ? column.textAlign
                                : 'end',
                          justifyContent:
                            index === 0
                              ? groupingItems && groupingItems.length > 0
                                ? 'space-between'
                                : column.textAlign
                                  ? column.textAlign
                                  : 'start'
                              : 'end',
                          width: '100%',
                          // whiteSpace: 'nowrap',
                        }}
                      >
                        <Typography variant="body2">{column.label}</Typography>

                        {groupingItems &&
                          groupingItems?.length > 0 &&
                          index === 0 && (
                            <Box
                              sx={{
                                position: 'relative',
                                whiteSpace: 'nowrap',
                              }}
                            >
                              <IconButton
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  cursor: 'pointer',
                                }}
                                onClick={toggleAllRowsExpansion}
                              >
                                {expandAll ? (
                                  <ExpandLessIcon />
                                ) : (
                                  <ExpandMoreIcon />
                                )}
                              </IconButton>
                            </Box>
                          )}
                      </Box>
                    </TableCell>
                  ) : null;
                })}
              </TableRow>
            </TableHead>

            <TableBody>
              {filteredData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={config.length} align="center">
                    <Box sx={{ width: '100%', textAlign: 'center' }}>
                      {t('reportsPage.noData')}
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                renderRows(filteredData)
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {enableInfoModal && selectedRowData && (
          <InfoModal
            open={isModalOpen}
            extraData={selectedRowData}
            onClose={closeModal}
          >
            {renderModalContent?.(selectedRowData)}
          </InfoModal>
        )}
      </Box>
    </Box>
  );
};

export default GroupingTable;
