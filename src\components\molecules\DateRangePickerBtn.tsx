import { forwardRef } from 'react';
import { Button, Typography } from '@mui/material';
import { usePickerContext } from '@mui/x-date-pickers/hooks';
import { useTranslation } from 'react-i18next';

import type {
  FieldType,
  SingleInputDateRangeFieldProps,
} from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

interface DateRangeButtonFieldProps extends SingleInputDateRangeFieldProps {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

type DateRangeButtonFieldComponent = ((
  props: DateRangeButtonFieldProps & React.RefAttributes<HTMLDivElement>
) => React.JSX.Element) & { fieldType?: FieldType };

const DateRangeButtonField = forwardRef(
  (props: DateRangeButtonFieldProps, ref: React.Ref<HTMLElement>) => {
    const {
      setOpen,
      label,
      id,
      disabled,
      fullWidth,
      inputProps: { 'aria-label': ariaLabel } = {},
    } = props;

    const pickerContext = usePickerContext();
    const { t } = useTranslation('');

    return (
      <Button
        fullWidth={fullWidth}
        //@ts-ignore
        variant="transparent"
        id={id}
        disabled={disabled}
        ref={pickerContext?.triggerRef}
        aria-label={ariaLabel}
        onClick={() => setOpen?.(prev => !prev)}
      >
        <Typography variant="body2" color="custom.gray600">
          {t('dashboard.date')}
        </Typography>
        <Typography variant="body2" ml={1}>
          {label}
        </Typography>
      </Button>
    );
  }
) as DateRangeButtonFieldComponent;

DateRangeButtonField.fieldType = 'single-input';

export default DateRangeButtonField;
