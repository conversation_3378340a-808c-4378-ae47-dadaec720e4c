/**
 * Firebase Configuration
 *
 * This file imports the shared Firebase core (app, auth, appCheck) from firebase-core.ts
 * and adds app-specific services like Firestore, Storage, Functions, and RTDB.
 *
 * The firebase-core module is shared between the gateway and main app,
 * while these services are only loaded with the main React app.
 */

import { connectAuthEmulator, onAuthStateChanged } from 'firebase/auth';
import { Database, getDatabase } from 'firebase/database';
import {
  connectFirestoreEmulator,
  initializeFirestore,
  persistentLocalCache,
} from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { FirebaseStorage, getStorage } from 'firebase/storage';

import {
  cleanupTempFiles,
  clearPrivateFileCache,
} from '~/components/organisms/FileUpload/utils/bucketManager';
import { app, appCheck, auth } from '../firebase-core';

const USE_EMULATORS = import.meta.env.VITE_USE_FIREBASE_EMULATORS === 'true';

// Re-export core instances for backward compatibility
export { app as firebaseApp, auth, appCheck };

// Initialize Firestore with persistence
const db = initializeFirestore(app, {
  ignoreUndefinedProperties: true,
  localCache: persistentLocalCache({
    cacheSizeBytes: 100 * 1024 * 1024, // 100 MB
  }),
});

export { db };

const storage = getStorage(app);

// Function to get a cached storage instance for different buckets
const storageInstances: { [bucketName: string]: FirebaseStorage } = {};
const getStorageInstance = (bucketName?: string): FirebaseStorage => {
  // If no bucket name provided, return default storage
  if (!bucketName) {
    return storage;
  }

  try {
    if (!storageInstances[bucketName]) {
      storageInstances[bucketName] = getStorage(app, `gs://${bucketName}`);
    }
    return storageInstances[bucketName];
  } catch (error) {
    console.error(
      `Failed to initialize storage for bucket ${bucketName}:`,
      error
    );
    // Return default storage as fallback
    return storage;
  }
};

// Function to get a cached RTDB instance
const rtdbInstances: { [url: string]: Database } = {};
const getRtdbInstance = (url: string | null): Database | null => {
  if (!url) return null;

  try {
    if (!rtdbInstances[url]) {
      rtdbInstances[url] = getDatabase(app, url);
    }
    return rtdbInstances[url];
  } catch (error) {
    console.error('Failed to initialize RTDB:', error);
    return null;
  }
};

const functions = getFunctions(
  app,
  import.meta.env.VITE_NODE_ENV === 'prod' ? 'europe-west1' : 'europe-west3'
);

// Helper function to wait for auth initialization
const waitForAuthInit = () => {
  return new Promise<void>(resolve => {
    const unsubscribe = onAuthStateChanged(auth, () => {
      unsubscribe();
      resolve();
    });
  });
};

// Setup cache cleanup on auth state changes
onAuthStateChanged(auth, user => {
  if (!user) {
    // User logged out, clear all caches and temp files
    clearPrivateFileCache();
    cleanupTempFiles();
  }
});

if (USE_EMULATORS) {
  console.warn('--- USING FIREBASE EMULATORS ---');
  connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
  connectFirestoreEmulator(db, 'localhost', 8080);
}

export {
  storage,
  getStorageInstance,
  getRtdbInstance,
  waitForAuthInit,
  functions,
};
