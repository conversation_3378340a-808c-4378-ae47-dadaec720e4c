import { unSanitizeRtdbObjectKeys } from '~/utils/rtdbKeySanitizer';
import { reportCommonFields, reportSpecificFields } from './constants';
import {
  GetReportsDataFromRealtime,
  GetReportsDataFromStorage,
  OmitKeysWithTypeTransform,
  Report,
  ReportDateInfo,
  ReportType,
} from './types';
import { getDateIntervalBreakdown } from './utils/getDateIntervalBreakdown';
import { mergeObjectsSummingNumbers } from './utils/mergeObjectsSummingNumbers';
import { transformHierarchicalKeyedObjectToFlatArray } from './utils/transformHierarchicalKeyedObjectToFlatArray';

export async function getReport<K extends keyof ReportType>(
  accountId: string,
  sellPointId: string,
  reportType: K,
  startDate: string,
  endDate: string,
  locale: string,
  backend: {
    storageBucket?: unknown;
    database?: unknown;
    getReportFromStorageFn?: GetReportsDataFromStorage;
    getReportFromRealtimeFn?: GetReportsDataFromRealtime;
  },
  storageData?: Array<unknown>,
  realtimeData?: Record<string, unknown>
): Promise<Array<OmitKeysWithTypeTransform<Report<K>>>> {
  // reportHierarchical is an object that contains all the data in a hierarchical way
  const reportHierarchical: {
    [reportType: string]: {
      [reportDate: string]: Record<string, unknown>;
    };
  } = {};
  // transform the startDate and endDate into Date objects
  const startDateDate = new Date(startDate);
  const endDateDate = new Date(endDate);
  // create local variables to store the data from the storage and realtime
  let storageDataLocal: Array<unknown> = [];
  let realtimeDataLocal: Record<string, unknown> = {};

  // check if the backend has storageBucket and fnGetReportFromStorage
  if (
    backend.storageBucket !== undefined &&
    typeof backend.getReportFromStorageFn === 'function'
  ) {
    // parse the interval requested into groups of dates (year, quarter, month, week, day)
    const reportDates = await getDateIntervalBreakdown(
      startDateDate,
      endDateDate,
      locale
    );
    // create an array with all the files that we need to get from the storage
    const filesNeededFromStorage = reportDates.map(
      date =>
        `accounts/${accountId}/sellPoints/${sellPointId}/reports/${date}/${reportType}.json`
    );
    storageDataLocal = await backend.getReportFromStorageFn(
      backend.storageBucket,
      filesNeededFromStorage
    );
  } else if (storageData !== undefined && storageData.length > 0) {
    // if the storageData is not undefined and has data, we are going to use it
    storageDataLocal = storageData;
  }
  // storageDataLocal is an array which contains all the contents of the requested files
  // each file content is an item in the array
  // we are going to iterate over each file content
  if (storageDataLocal.length > 0) {
    console.info(
      `getReport storage: ${accountId} - ${sellPointId} - ${reportType} - ${startDate} - ${endDate}`
    );
    for (const storageFileContent of storageDataLocal) {
      // each storageFileContent is an array of close of day objects
      // we are going to iterate over each close of day object
      for (const closeOfDayContent of storageFileContent as Array<
        Record<string, unknown>
      >) {
        for (const closeOfDayData of Object.values(closeOfDayContent)) {
          // each closeOfDayData is an object that has key value pairs
          // where the key is the reportDate and the value is the reportDateData
          const closeOfDayDataTyped = closeOfDayData as Record<string, unknown>;
          // merge the current data into the reportHierarchical object
          mergeWithSumCurrentDataIntoReportHierarchical(
            reportType,
            startDateDate,
            endDateDate,
            closeOfDayDataTyped,
            reportHierarchical
          );
        }
      }
    }
  }

  // check if the backend has database and fnGetReportFromRealtime
  if (
    backend.database !== undefined &&
    typeof backend.getReportFromRealtimeFn === 'function'
  ) {
    // get the data from the realtime database
    realtimeDataLocal = await backend.getReportFromRealtimeFn(
      backend.database,
      `accounts/${accountId}/sellPoints/${sellPointId}/reports/${reportType}`
    );
  } else if (
    realtimeData !== undefined &&
    Object.keys(realtimeData).length > 0
  ) {
    realtimeDataLocal = realtimeData;
  }
  // realtimeDataLocal is an object that contains all the dates for this report type still in realtime
  if (Object.keys(realtimeDataLocal).length > 0) {
    console.info(
      `getReport rtdb: ${accountId} - ${sellPointId} - ${reportType} - ${startDate} - ${endDate}`
    );
    // merge the current data into the reportHierarchical object
    mergeWithSumCurrentDataIntoReportHierarchical(
      reportType,
      startDateDate,
      endDateDate,
      realtimeDataLocal,
      reportHierarchical
    );
  }

  // Safety check: if reportSpecificFields[reportType] is undefined, return empty array
  if (!reportSpecificFields[reportType]) {
    console.warn(
      `Report type '${reportType}' is not supported. Available types: ${Object.keys(reportSpecificFields).join(', ')}`
    );
    return [] as Array<OmitKeysWithTypeTransform<Report<K>>>;
  }

  // Data received is sanitized so we need to unsanitize before processing it
  const unsanitizedReportHierarchical =
    unSanitizeRtdbObjectKeys(reportHierarchical);

  // transform the reportHierarchical object into a flat array with the specific group by fields
  const reportFlatArray =
    Object.keys(unsanitizedReportHierarchical).length === 0
      ? []
      : transformHierarchicalReportToFlatArray(
          unsanitizedReportHierarchical,
          reportCommonFields,
          reportSpecificFields[reportType] as Array<string>
        );

  return reportFlatArray as Array<OmitKeysWithTypeTransform<Report<K>>>;
}

function mergeWithSumCurrentDataIntoReportHierarchical(
  reportType: keyof ReportType,
  startDate: Date,
  endDate: Date,
  data: Record<string, unknown>,
  reportHierarchical: Record<string, unknown>
): void {
  // data is an object that contains all the dates for this report type
  // we are going to iterate over each date
  for (const [reportDate, reportDateData] of Object.entries(data)) {
    const reportDateDate = new Date(reportDate);
    // check if the reportDate is in the interval requested
    if (reportDateDate >= startDate && reportDateDate <= endDate) {
      // reportDateData is an object that contains the report type (field reportType) and the report data (field report)
      // recast the reportDateData to the correct type
      const reportDateDataTyped = reportDateData as Record<string, unknown>;
      // get the report type
      const currentReportType =
        reportDateDataTyped.reportType as keyof ReportType;
      // check if the reportType is the one requested
      if (reportType !== currentReportType) {
        continue;
      }
      // get the report date info
      const currentReportDateInfoData =
        reportDateDataTyped.dateInfo as ReportDateInfo;
      // get the report date day of week
      const currentReportDateInfoDayOfWeek = new Date(
        Number(currentReportDateInfoData.year),
        Number(currentReportDateInfoData.month) - 1,
        Number(currentReportDateInfoData.day)
      ).getDay();
      // get the report data - we do not need to make a deep clone of the object because we are going to merge it
      // and is going to be cloned in the merge function
      const currentReportData = reportDateDataTyped.report as Record<
        string,
        unknown
      >;
      // create a current reportHierarchical object that will be added to the reportHierarchical object
      const currentReportHierarchical = {
        [currentReportType]: {
          [reportDate]: {
            [`@${currentReportDateInfoDayOfWeek}`]: {
              [`@${currentReportDateInfoData.year}`]: {
                [`@${currentReportDateInfoData.quarter}`]: {
                  [`@${currentReportDateInfoData.month}`]: {
                    [`@${currentReportDateInfoData.week}`]: {
                      [`@${currentReportDateInfoData.weekYear}`]: {
                        [`@${currentReportDateInfoData.day}`]:
                          currentReportData,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      };
      // merge the currentReportHierarchical object into the reportHierarchical object
      mergeObjectsSummingNumbers(reportHierarchical, currentReportHierarchical);
    }
  }
}

function transformHierarchicalReportToFlatArray(
  input: Record<string, unknown>,
  levels: string[],
  reportLevels: string[]
): Array<Record<string, unknown>> {
  const reportLevelsDataField = 'data';
  const results: Array<Record<string, unknown>> = [];
  transformHierarchicalKeyedObjectToFlatArray(
    input,
    0,
    {},
    levels,
    results,
    reportLevelsDataField
  );
  results.forEach(result => {
    const report: Array<Record<string, unknown>> = [];
    transformHierarchicalKeyedObjectToFlatArray(
      result[reportLevelsDataField] as Record<string, unknown>,
      0,
      {},
      reportLevels,
      report
    );
    result.report = report;
    delete result.data;
  });
  return results;
}
