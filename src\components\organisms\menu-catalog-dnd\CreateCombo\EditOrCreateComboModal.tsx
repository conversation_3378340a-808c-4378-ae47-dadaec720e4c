import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import {
  Box,
  Button,
  Dialog,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import MuiCustomInput from '~/components/atoms/inputs/MuiCustomInput';
import { menuColors } from '~/data/menu-colors';
import { useGetListVatsLive } from '~/providers/resources';
import isEqual from '~/providers/utils/isEqual';
import { generateFirestoreId } from '~/utils/generateFirestoreId';
import getFullscreenModalProps from '../../../../utils/getFullscreenModalProps';
import HelpSuggestion from '../../../molecules/HelpSuggestion';
import ModalHeader from '../../../molecules/ModalHeader';
import Subsection from '../../../molecules/Subsection';
import {
  createStandardImageConfig,
  FileUploadComponent,
  resolveImageEditorConfig,
  UploadedFile,
} from '../../FileUpload';
import AgeRestrictionModal from '../add-item-modal/AgeRestrictionModal';
import ColorPicker from '../add-item-modal/ColorPicker';
import { MuiTranslatableInputs } from '../MuiTranslatableInputs';
import { Coordinates } from '../types';
import CreateComboAccordion from './CreateComboAccordion';

interface CreateComboModalProps {
  path: string;
  data: any;
  tileIndex: number | null;
  initialValues: any;
  create?: boolean;
  edit?: boolean;
  position?: Coordinates;
  onClose: () => void;
  displayGroupColor?: string;
  trackParentChange?: (path: string, value: any) => void;
  remoteDataAfterMerge?: any; // Remote data provided after "Keep My Changes and Merge"
  remoteDataAfterGetTheirChanges?: any; // Remote data provided after "Get Their Changes"
}

export interface ComboStep {
  name: string;
  type: string;
  selection: {
    min: number;
    max: number;
  };
  items: { id: string; type: string }[];
}

interface ComboState {
  id?: string;
  price?: number;
  displayName?: string;
  vat?: number;
  measureUnit?: string;
  type?: string;
  steps: ComboStep[];
  active?: boolean;
  color?: string;
  image?: string;
  images?: UploadedFile[];
  age?: number;
  kitchenName?: string;
}

const emptyStep: ComboStep = {
  name: '',
  type: 'required',
  selection: {
    min: 1,
    max: 9,
  },
  items: [],
};

const isPriceValid = (value: any) => {
  return (
    (typeof value === 'string' && value.trim() !== '') ||
    (typeof value === 'number' && !isNaN(value))
  );
};

export default function EditOrCreateComboModal({
  path,
  data,
  position,
  create = false,
  initialValues,
  edit = false,
  tileIndex,
  onClose,
  displayGroupColor,
  trackParentChange,
  remoteDataAfterMerge,
  remoteDataAfterGetTheirChanges,
}: CreateComboModalProps) {
  const { setValue, watch } = useFormContext();
  const { t } = useTranslation();

  console.log(data);

  const [openStep, setOpenStep] = useState<number>(0);
  const [checkValidation, setCheckValidation] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const isInDisplayGroup = useMemo(() => {
    return path.includes('items');
  }, [path]);

  const initialComboState = useMemo(() => {
    return edit
      ? initialValues
      : {
          age: undefined,
          color: menuColors[0],
          price: undefined,
          displayName: '',
          measureUnit: 'BUC',
          type: 'productCombo',
          active: true,
          steps: [emptyStep],
          vat: undefined,
        };
  }, [edit, initialValues]);

  const [comboState, setComboState] = useState<ComboState>(initialComboState);

  // Track the actual remote/database value (what's saved in the database)
  const remoteValueRef = useRef(initialComboState);
  // Track the last saved/merged baseline (for hasChanges comparison)
  const savedValueRef = useRef(initialComboState);
  // Track user's current pending edits
  const userPendingChangesRef = useRef<ComboState | null>(null);
  // Flag to prevent sync loop when we're making local updates
  const isUpdatingFromLocal = useRef(false);

  // Watch for changes to the form data at this path
  const formDataAtPath = watch(path);

  // Sync local state when form data changes (e.g., from conflict resolution)
  useEffect(() => {
    // Skip sync if we're currently making a local update
    if (isUpdatingFromLocal.current) {
      console.log(
        '[EditOrCreateComboModal] Skipping sync - local update in progress'
      );
      return;
    }

    if (
      edit &&
      formDataAtPath &&
      Array.isArray(formDataAtPath) &&
      tileIndex !== null
    ) {
      const formItem = formDataAtPath[tileIndex];

      // Check if it's truly different from current state
      const isExternalChange = !isEqual(formItem, comboState);

      if (isExternalChange && formItem) {
        console.log(
          '[EditOrCreateComboModal] Form data changed externally (conflict resolution), syncing state'
        );
        setComboState(formItem);

        // Only update savedValueRef for hasChanges comparison
        // remoteValueRef should remain the true remote value
        savedValueRef.current = formItem;

        // Check if merged state has changes against remote baseline
        const stillHasChanges = !isEqual(formItem, remoteValueRef.current);
        setHasChanges(stillHasChanges);

        // Clear pending changes since we're now synced with form
        userPendingChangesRef.current = null;

        console.log(
          '[EditOrCreateComboModal] After merge sync, hasChanges:',
          stillHasChanges
        );
      }
    }
  }, [formDataAtPath, edit, tileIndex, comboState]);

  // Initialize hasChanges based on remoteValueRef (the true remote baseline)
  useEffect(() => {
    if (edit && remoteValueRef.current) {
      const initialHasChanges = !isEqual(comboState, remoteValueRef.current);
      setHasChanges(initialHasChanges);
    } else {
      setHasChanges(false);
    }
  }, [edit, comboState]);

  const updateValue = (field: string, value: unknown) => {
    setCheckValidation(false);
    setComboState(prev => {
      const newState = { ...prev, ...{ [field]: value } };

      // Check for changes if in edit mode
      if (edit && remoteValueRef.current) {
        const hasChanged = !isEqual(newState, remoteValueRef.current);
        setHasChanges(hasChanged);

        if (hasChanged) {
          // IMPORTANT: Update pending changes ref BEFORE calling setValue
          // This ensures the sync effect can detect this as an internal change
          userPendingChangesRef.current = newState;

          // Update form for conflict detection - need to update the array with the item at tileIndex
          if (trackParentChange && tileIndex !== null) {
            // Set flag to prevent sync loop
            isUpdatingFromLocal.current = true;

            const newData = [...data];
            newData[tileIndex] = newState;
            setValue(path, newData, { shouldDirty: true });
            trackParentChange(path, newData);

            // Clear flag after a short delay to allow the update to complete
            setTimeout(() => {
              isUpdatingFromLocal.current = false;
            }, 50);
          }
        } else {
          userPendingChangesRef.current = null;
        }
      }

      return newState;
    });
  };

  const handleClose = () => {
    // If there were unsaved changes, revert to the remote state (what's in the database)
    if (edit && hasChanges && remoteValueRef.current && tileIndex !== null) {
      const newData = [...data];
      newData[tileIndex] = remoteValueRef.current;
      setValue(path, newData, { shouldDirty: false });
      trackParentChange?.(path, newData);
    }
    // Clear pending changes
    userPendingChangesRef.current = null;
    onClose();
  };

  // Watch for remote data updates after "Keep My Changes and Merge"
  useEffect(() => {
    if (remoteDataAfterMerge && edit && tileIndex !== null) {
      console.log(
        '[EditOrCreateComboModal] Remote data received after merge, updating remote baseline'
      );
      // Extract the combo data at this path from the remote data
      const pathParts = path.split('.');
      let remoteData = remoteDataAfterMerge;

      for (const part of pathParts) {
        const match = part.match(/(.+)\[(\d+)\]/);
        if (match) {
          remoteData = remoteData?.[match[1]]?.[parseInt(match[2])];
        } else {
          remoteData = remoteData?.[part];
        }
        if (!remoteData) break;
      }

      if (Array.isArray(remoteData) && remoteData[tileIndex]) {
        const remoteCombo = remoteData[tileIndex];
        remoteValueRef.current = remoteCombo;
        console.log(
          '[EditOrCreateComboModal] Remote baseline updated - cancel will revert to remote value'
        );

        // Recompute hasChanges against the new remote baseline
        const stillHasChanges = !isEqual(comboState, remoteCombo);
        setHasChanges(stillHasChanges);
        console.log(
          '[EditOrCreateComboModal] After remote baseline update, hasChanges:',
          stillHasChanges
        );
      }
    }
  }, [remoteDataAfterMerge, edit, path, tileIndex, comboState]);

  // Watch for remote data updates after "Get Their Changes"
  useEffect(() => {
    if (remoteDataAfterGetTheirChanges && edit && tileIndex !== null) {
      console.log(
        '[EditOrCreateComboModal] Remote data received after Get Their Changes, updating remote baseline'
      );
      // Extract the combo data at this path from the remote data
      const pathParts = path.split('.');
      let remoteData = remoteDataAfterGetTheirChanges;

      for (const part of pathParts) {
        const match = part.match(/(.+)\[(\d+)\]/);
        if (match) {
          remoteData = remoteData?.[match[1]]?.[parseInt(match[2])];
        } else {
          remoteData = remoteData?.[part];
        }
        if (!remoteData) break;
      }

      if (Array.isArray(remoteData) && remoteData[tileIndex]) {
        const remoteCombo = remoteData[tileIndex];
        // Update BOTH refs to the same remote data when accepting their changes
        remoteValueRef.current = remoteCombo;
        savedValueRef.current = remoteCombo;
        console.log(
          '[EditOrCreateComboModal] Both baselines updated with remote data - hasChanges should be false'
        );

        // Since we're showing fresh remote data, hasChanges should be false
        setHasChanges(false);
        console.log(
          '[EditOrCreateComboModal] After Get Their Changes, hasChanges: false'
        );
      }
    }
  }, [remoteDataAfterGetTheirChanges, edit, path, tileIndex]);

  const createCombo = () => {
    if (
      !comboState.displayName?.trim() ||
      !comboState.price ||
      comboState.vat === undefined
    ) {
      setCheckValidation(true);
      return;
    }

    if (edit) {
      const newData = [...data];
      newData[tileIndex ?? 0] = comboState;
      setValue(path, newData);
      // Track the change
      trackParentChange?.(path, newData);
    } else {
      const id = generateFirestoreId();
      let tmp = [...data];

      tmp.push({
        ...comboState,
        id,
        ...(isInDisplayGroup ? {} : { position }),
      });

      // if we are inside display group, we need to arrange items
      // first by type and then by display name
      if (isInDisplayGroup) {
        tmp.sort((a, b) => {
          const typeA = a.type ?? 'product';
          const typeB = b.type ?? 'product';

          if (typeA === 'displayGroup' && typeB !== 'displayGroup') return -1;
          if (typeA !== 'displayGroup' && typeB === 'displayGroup') return 1;

          return a.displayName.localeCompare(b.displayName);
        });

        // Remove position if we're inside display group (position exists on element
        // because we add it artificially to know where to display them in the table)
        tmp = tmp.map(item => {
          const { position, ...rest } = item;
          return rest;
        });
      }

      setValue(path, tmp);
      // Track the change
      trackParentChange?.(path, tmp);
    }

    onClose();
  };

  const addNewStep = () => {
    setComboState(prev => {
      const newState = { ...prev, ...{ steps: [...prev.steps, emptyStep] } };

      // Check for changes if in edit mode
      if (edit && remoteValueRef.current) {
        const hasChanged = !isEqual(newState, remoteValueRef.current);
        setHasChanges(hasChanged);

        // Track the change - update the array with the item at tileIndex
        if (hasChanged && trackParentChange && tileIndex !== null) {
          // Update pending changes ref BEFORE setValue
          userPendingChangesRef.current = newState;

          // Set flag to prevent sync loop
          isUpdatingFromLocal.current = true;

          const newData = [...data];
          newData[tileIndex] = newState;
          setValue(path, newData, { shouldDirty: true });
          trackParentChange(path, newData);

          // Clear flag after a short delay
          setTimeout(() => {
            isUpdatingFromLocal.current = false;
          }, 50);
        }
      }

      return newState;
    });
    setOpenStep(comboState.steps.length);
  };

  const deleteStep = (stepIndex: number) => {
    setComboState(prev => {
      const updatedSteps = [...prev.steps];
      updatedSteps.splice(stepIndex, 1);
      const newState = { ...prev, ...{ steps: updatedSteps } };

      // Check for changes if in edit mode
      if (edit && remoteValueRef.current) {
        const hasChanged = !isEqual(newState, remoteValueRef.current);
        setHasChanges(hasChanged);

        // Track the change - update the array with the item at tileIndex
        if (hasChanged && trackParentChange && tileIndex !== null) {
          // Update pending changes ref BEFORE setValue
          userPendingChangesRef.current = newState;

          // Set flag to prevent sync loop
          isUpdatingFromLocal.current = true;

          const newData = [...data];
          newData[tileIndex] = newState;
          setValue(path, newData, { shouldDirty: true });
          trackParentChange(path, newData);

          // Clear flag after a short delay
          setTimeout(() => {
            isUpdatingFromLocal.current = false;
          }, 50);
        }
      }

      return newState;
    });
    if (stepIndex === openStep) {
      setOpenStep(stepIndex - 1);
    }
  };

  const updateStepField = (
    stepIndex: number,
    fieldName: keyof ComboStep,
    value: any
  ) => {
    setComboState(prev => {
      const updatedSteps = [...prev.steps];
      updatedSteps[stepIndex] = {
        ...updatedSteps[stepIndex],
        [fieldName]: value,
      };
      const newState = {
        ...prev,
        steps: updatedSteps,
      };

      // Check for changes if in edit mode
      if (edit && remoteValueRef.current) {
        const hasChanged = !isEqual(newState, remoteValueRef.current);
        setHasChanges(hasChanged);

        // Track the change - update the array with the item at tileIndex
        if (hasChanged && trackParentChange && tileIndex !== null) {
          // Update pending changes ref BEFORE setValue
          userPendingChangesRef.current = newState;

          // Set flag to prevent sync loop
          isUpdatingFromLocal.current = true;

          const newData = [...data];
          newData[tileIndex] = newState;
          setValue(path, newData, { shouldDirty: true });
          trackParentChange(path, newData);

          // Clear flag after a short delay
          setTimeout(() => {
            isUpdatingFromLocal.current = false;
          }, 50);
        }
      }

      return newState;
    });
  };

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      label: `${vat.value}%`,
    }));
  }, [vats]);

  const [isOpenAgeModal, setIsOpenAgeModal] = useState(false);

  const saveAgeRestriction = (age: number) => {
    updateValue('age', age);
    setIsOpenAgeModal(false);
  };

  return (
    <Dialog open onClose={handleClose} {...getFullscreenModalProps()}>
      <ModalHeader
        handleClose={handleClose}
        title={create ? t('menu.newComboProduct') : t('menu.editComboProduct')}
      >
        <Button
          variant="contained"
          onClick={createCombo}
          disabled={edit && !hasChanges}
        >
          {create ? t('shared.create') : t('shared.save')}
        </Button>
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 6,
        }}
      >
        <Subsection title={t('menu.details')}>
          <Grid container pl={0} spacing={3}>
            <Grid
              size={{
                xs: 12,
                sm: 8,
              }}
            >
              <MuiCustomInput
                fullWidth
                label={t('menu.displayName')}
                value={comboState.displayName}
                onChange={ev => {
                  updateValue('displayName', ev.target.value);
                }}
                error={checkValidation && !comboState.displayName}
              />
              <MuiCustomInput
                label={t('shared.price')}
                type="number"
                value={comboState.price ? comboState.price / 10000 : ''}
                onChange={event => {
                  const value = parseFloat(event.target.value);
                  updateValue('price', Math.round(value * 10000));
                }}
                error={checkValidation && !isPriceValid(comboState.price)}
              />
              <MuiCustomInput
                type="autocomplete-select"
                options={vatsChoices}
                getOptionLabel={option => option.label}
                label={t('shared.tva')}
                value={
                  vatsChoices.find(option => option.id == comboState.vat) ??
                  null
                }
                // @ts-ignore
                onChange={(_, newValue) => {
                  updateValue('vat', newValue?.id ?? 0);
                }}
                error={checkValidation && comboState.vat === undefined}
                renderInput={params => (
                  <TextField
                    {...params}
                    label={t('menu.tvaTaxes')}
                    fullWidth
                    required
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                sm: 4,
              }}
            >
              <ColorPicker
                color={displayGroupColor || comboState.color}
                setColor={color => updateValue('color', color)}
                disabledColorPicker={!!displayGroupColor || isInDisplayGroup}
              />
            </Grid>
          </Grid>
        </Subsection>

        <Subsection title={t('menu.media')}>
          <Grid container pl={0} spacing={3}>
            <Grid size={12}>
              <FileUploadComponent
                value={
                  Array.isArray(comboState.images) ? comboState.images : []
                }
                onChange={(files: UploadedFile[]) =>
                  updateValue('images', files)
                }
                config={{
                  fileType: 'images',
                  multiple: false,
                  maxFiles: 1,
                  maxSize: 20 * 1024 * 1024, // 2MB - reasonable for menu items
                  acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
                  imageConfig: (() => {
                    const resolved = resolveImageEditorConfig(
                      createStandardImageConfig.squares()
                    );
                    return {
                      targetSizes: resolved.targetSizes,
                      autoGenerateThumbnail: true,
                      quality: 0.8,
                      editorConfig: resolved, // Pass the full resolved config as editorConfig
                    };
                  })(),
                  ui: {
                    disabled: false,
                    readOnly: false,
                  },
                }}
              />
            </Grid>
          </Grid>
        </Subsection>

        <Subsection title="Restrictions">
          <Grid size={12}>
            <Box
              onClick={() => setIsOpenAgeModal(true)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                border: 'solid 1px',
                borderColor: 'custom.gray400',
                borderRadius: 2,
                height: '56px',
                paddingX: 2,
                ':hover': {
                  cursor: 'pointer',
                  borderColor: 'custom.gray800',
                },
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                <Typography color="textSecondary" fontSize="14px">
                  {t('menu.ageRestriction')}
                </Typography>
                <Tooltip title={t('menu.ageRestrictionTooltip')}>
                  <IconButton>
                    <InfoOutlinedIcon color="disabled" />
                  </IconButton>
                </Tooltip>
              </Box>
              {comboState.age ? (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <Box>
                    <Typography
                      color="textSecondary"
                      fontSize="15px"
                      textAlign="left"
                      fontWeight={'bold'}
                    >
                      {comboState.age === 18
                        ? t('menu.ageRestriction18')
                        : `${t('menu.ageRestriction')} ${comboState.age}`}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography color="primary" fontSize="14px">
                  {t('shared.set')}
                </Typography>
              )}
              {comboState.age ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {/* <KeyboardArrowRightIcon color="disabled" />
                    {state.age} */}
                  <img src="/assets/icons/check.svg" alt="check-icon" />
                </Box>
              ) : (
                ''
              )}
            </Box>
          </Grid>
        </Subsection>

        <Subsection title="Kitchen Name">
          <Grid size={12}>
            <TextField
              fullWidth
              value={comboState.kitchenName || ''}
              onChange={event => updateValue('kitchenName', event.target.value)}
              label={t('menu.kitchenName')}
              slotProps={{
                input: {
                  endAdornment: (
                    <Tooltip title={t('menu.kitchenNameTooltip')}>
                      <IconButton>
                        <InfoOutlinedIcon color="disabled" />
                      </IconButton>
                    </Tooltip>
                  ),
                },
              }}
            />
          </Grid>
        </Subsection>

        <Subsection title="Name and Description Variations">
          <Grid size={12}>
            <MuiTranslatableInputs
              title={false}
              state={comboState}
              updateValue={updateValue}
              locales={['ro', 'en']}
            />
          </Grid>
        </Subsection>

        <Subsection title={t('menu.steps')}>
          {comboState.steps.map((step: ComboStep, index: number) => {
            return (
              <CreateComboAccordion
                key={index}
                index={index + 1}
                step={step}
                isExpanded={openStep === index}
                setOpenStep={(open: boolean) => {
                  if (open) setOpenStep(index);
                  else setOpenStep(-1);
                }}
                updateStepField={(fieldName: keyof ComboStep, value: any) =>
                  updateStepField(index, fieldName, value)
                }
                deleteStep={() => deleteStep(index)}
              />
            );
          })}
        </Subsection>

        <Button variant="outlined" sx={{ width: '100%' }} onClick={addNewStep}>
          {t('menu.addNewStep')}
        </Button>

        <HelpSuggestion>
          {/* @ts-ignore */}
          <Button variant="contained-light">
            <PlayCircleOutlineIcon sx={{ mr: 1 }} />
            {t('menu.learnAboutMenus')}
          </Button>
        </HelpSuggestion>
      </Box>
      {isOpenAgeModal && (
        <AgeRestrictionModal
          age={comboState.age}
          onClose={e => {
            e?.stopPropagation();
            setIsOpenAgeModal(false);
          }}
          onSave={saveAgeRestriction}
        />
      )}
    </Dialog>
  );
}
