import { useEffect, useState } from 'react';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Menu,
  MenuItem,
  Typography,
} from '@mui/material';
import { useDelete, useGetList, useNotify, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import EditMenuDialog from './EditMenuCatalog';

interface CatalogTitleTreeProps {
  name: string;
  id: number;
  items: any[];
  tree: number[];
  setCurrentTree: (index: number) => void;
  isPosLayout: boolean;
}

interface Breadcrumb {
  displayName: string;
  index: number;
}

export default function CatalogTitleTree({
  name,
  id,
  items,
  tree,
  setCurrentTree,
  isPosLayout,
}: CatalogTitleTreeProps) {
  const { t } = useTranslation('');
  const [breadcrumbs, setBreadcrumbs] = useState<Array<Breadcrumb>>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [deleteMenu, { isLoading }] = useDelete(RESOURCES.HOSPITALITY_CATALOGS);
  const notify = useNotify();
  const redirect = useRedirect();

  useEffect(() => {
    const breadcrumbsList: Array<Breadcrumb> = [];
    let tmp: any = items;
    tree.forEach((el, index) => {
      tmp = tmp[el];
      breadcrumbsList.push({ displayName: tmp.displayName, index: index + 1 });
      tmp = tmp.items;
    });

    setBreadcrumbs(breadcrumbsList);
  }, [tree, items]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteMenu = () => {
    if (!id) return;

    deleteMenu(RESOURCES.HOSPITALITY_CATALOGS, { id })
      .then(() => {
        notify(t('menu.menuDeleted'), { type: 'success' });
        redirect('list', RESOURCES.HOSPITALITY_CATALOGS);
      })
      .catch(err => {
        console.error('Error deleting the menu:', err);
        notify('Error deleting menu', { type: 'error' });
      });
  };

  const open = Boolean(anchorEl);

  return (
    <Box
      sx={{
        '& div:last-of-type': {
          color: 'primary.main',
        },
        ...flexStyles,
      }}
    >
      <div>
        <Typography
          variant="h3"
          display="inline-block"
          onClick={handleMenuOpen}
          sx={{ cursor: 'pointer', fontSize: { xs: '12px', md: '16px' } }}
        >
          {name}
        </Typography>
      </div>

      {breadcrumbs.map((el, index) => (
        <Box key={`${el.displayName}-${index}`} sx={flexStyles}>
          <ChevronRightIcon fontSize="small" />
          <Typography
            variant="h3"
            display="inline-block"
            onClick={() => setCurrentTree(el.index)}
            sx={{ cursor: 'pointer' }}
          >
            {el.displayName}
          </Typography>
        </Box>
      ))}
      {openEditModal && (
        <EditMenuDialog menuId={id} onClose={() => setOpenEditModal(false)} />
      )}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        MenuListProps={{
          autoFocusItem: false,
        }}
      >
        {isPosLayout && [
          <MenuItem
            key="goToFirstPage"
            onClick={() => {
              setCurrentTree(0);
              handleMenuClose();
            }}
          >
            {t('menu.goToFirstPage')}
          </MenuItem>,
          <Divider
            key="posLayoutDivider"
            sx={{ borderColor: 'rgba(0, 0, 0, 0.05)', my: 1, mx: 1 }}
          />,
        ]}
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            setOpenEditModal(true);
          }}
        >
          {t('menu.editMenu')}
        </MenuItem>

        <Divider sx={{ borderColor: 'rgba(0, 0, 0, 0.05)', my: 1, mx: 1 }} />

        <MenuItem
          onClick={() => setOpenConfirmDialog(true)}
          sx={{ color: '#DC4437' }}
        >
          {t('menu.deleteMenu')}
        </MenuItem>
      </Menu>

      <Dialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
      >
        <DialogTitle>{`${t('menu.delete?')} ${name}?`}</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            {t('menu.deleteConfirmation')}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'space-between', px: 3, pb: 2 }}>
          <Button
            variant="outlined"
            onClick={() => setOpenConfirmDialog(false)}
            color="primary"
          >
            {t('shared.cancel')}
          </Button>
          <Button
            variant="contained"
            onClick={handleDeleteMenu}
            disabled={isLoading}
          >
            {isLoading ? t('menu.deleting') : t('shared.confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const flexStyles = {
  display: 'flex',
  alignItems: 'center',
  gap: 1,
  flexWrap: 'wrap',
};
