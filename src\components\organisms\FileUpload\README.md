# FileUpload Component System

## Overview

The FileUpload component system provides a complete, modern file upload solution with drag & drop, validation, preview, and Firebase Storage integration.

## Quick Start

```typescript
import { FileUploadComponent, UploadedFile } from '~/components/organisms/FileUpload';

function MyComponent() {
  const [files, setFiles] = useState<UploadedFile[]>([]);

  return (
    <FileUploadComponent
      value={files}
      onChange={setFiles}
      config={{
        accept: ['image/*'],
        maxFiles: 5,
        maxSize: 10 * 1024 * 1024, // 10MB
        enableImageEditor: true
      }}
    />
  );
}
```

## Architecture

### 📁 Directory Structure

```
src/components/organisms/FileUpload/
├── core/               # Core managers
│   ├── FileUploadManager.ts      # Main upload logic
│   └── InMemoryFileManager.ts    # Memory management
├── utils/              # Utilities
│   ├── bucketManager.ts          # Storage operations
│   ├── urlGeneration.ts          # URL handling
│   ├── fileMetadataResolver.ts   # Metadata ops
│   ├── clientImageProcessor.ts   # Image processing
│   ├── fileUtils.ts              # File utilities
│   ├── fileSizeUtils.ts          # Size handling
│   └── validation/               # Validation logic
├── types/              # Type definitions
│   ├── fileUpload.ts             # Core types
│   └── index.ts                  # Type exports
├── hooks/              # Component hooks
│   ├── useFileList.ts            # File list state
│   ├── useFileUpload.ts          # Upload logic
│   ├── useFileValidation.ts      # Validation
│   ├── useImageEditor.ts         # Image editing
│   └── useFileLifecycle.ts       # Lifecycle mgmt
├── globalHooks/        # External hooks
│   ├── useInMemoryFileManagement.ts
│   └── useTempFileCleanup.ts
├── components/         # UI components
│   ├── FileDropzone/             # Drop zone
│   ├── FileList/                 # File listing
│   ├── FilePreview*/             # Preview components
│   └── ImageEditorModal.tsx      # Image editor
├── FileUploadComponent.tsx       # Main component
├── RaFileUploadComponent.tsx     # React Admin wrapper
└── index.ts                      # Public API
```

### 🔧 Core Components

- **FileUploadComponent**: Main upload component with drag & drop
- **RaFileUploadComponent**: React-Admin wrapper

### 🎣 Hooks

#### Component Hooks (Internal)

- `useFileList`: File list state management
- `useFileUpload`: Upload operations (now uses in-memory storage for temp files)
- `useFileValidation`: File validation
- `useImageEditor`: Image editing functionality

#### Global Hooks (External)

- `useInMemoryFileCleanup`: Memory cleanup utilities
- `useTempFileCleanup`: Temporary file cleanup

### 💾 Core Managers

- **fileUploadManager**: Singleton for upload operations
- **inMemoryFileManager**: Blob URL lifecycle management

### 🛠️ Utilities

- **Configuration**: Config validation and merging
- **Validation**: File validation and error handling
- **Storage**: Firebase Storage integration
- **Processing**: Client-side image processing
- **Metadata**: File metadata resolution
- **URL Generation**: Secure URL handling

## Usage Examples

### Basic File Upload

```typescript
import { FileUploadComponent } from '~/components/organisms/FileUpload';

<FileUploadComponent
  value={files}
  onChange={setFiles}
  config={{
    accept: ['image/*', 'application/pdf'],
    maxFiles: 3,
    maxSize: 5 * 1024 * 1024 // 5MB
  }}
/>
```

### React Admin Integration

```typescript
import { RaFileUploadComponent } from '~/components/organisms/FileUpload';

<RaFileUploadComponent
  source="documents"
  config={{
    accept: ['application/pdf'],
    maxFiles: 1
  }}
/>
```

### Custom Validation

```typescript
import { FileUploadComponent, createConfig } from '~/components/organisms/FileUpload';

const config = createConfig.images({
  validation: {
    customValidator: (file) => {
      if (file.name.includes('temp')) {
        return 'Temporary files not allowed';
      }
      return null;
    }
  }
});

<FileUploadComponent value={files} onChange={setFiles} config={config} />
```

### Image Editor

```typescript
import { FileUploadComponent } from '~/components/organisms/FileUpload';

<FileUploadComponent
  value={images}
  onChange={setImages}
  config={{
    accept: ['image/*'],
    enableImageEditor: true,
    imageEditor: {
      aspectRatios: [
        { label: 'Square', value: 1 },
        { label: 'Landscape', value: 16/9 }
      ]
    }
  }}
/>
```

## Configuration

### File Type Configs

```typescript
import { createConfig } from '~/components/organisms/FileUpload';

// Pre-built configs
const imageConfig = createConfig.images();
const documentConfig = createConfig.documents();
const videoConfig = createConfig.videos();

// Custom config
const customConfig = createConfig.custom({
  accept: ['image/*'],
  maxFiles: 10,
  maxSize: 20 * 1024 * 1024,
  validation: {
    requireAltText: true,
  },
});
```

### Validation Options

```typescript
interface ValidationConfig {
  maxSize?: number;
  minSize?: number;
  maxFiles?: number;
  minFiles?: number;
  requireAltText?: boolean;
  customValidator?: (file: File) => string | null;
}
```

## File States

### UploadedFile Type

```typescript
interface UploadedFile {
  f: string; // Filename
  t: 'public' | 'private'; // File type
  x?: boolean; // Is temporary
  size?: number; // File size
  url?: string; // Preview URL
  alt?: string; // Alt text
  metadata?: any; // Additional metadata
}
```

### File Lifecycle

1. **Selection**: User selects files
2. **Validation**: Files validated against config
3. **Preview**: Generate preview URLs (in-memory for temporary files)
4. **Upload**: Create in-memory files or upload to Firebase Storage
5. **Processing**: Generate variants/thumbnails (client-side)
6. **Cleanup**: Clean up temporary resources

## Best Practices

### Memory Management

```typescript
import { useInMemoryFileCleanup } from '~/components/organisms/FileUpload';

function MyComponent() {
  // Automatically cleanup memory when component unmounts
  useInMemoryFileCleanup(files);

  return <FileUploadComponent {...props} />;
}
```

### Error Handling

```typescript
import { validateFiles } from '~/components/organisms/FileUpload';

const validationResult = validateFiles(files, config);
if (validationResult.hasErrors) {
  console.log('Validation errors:', validationResult.errors);
}
```

### Performance Optimization

- Use `enableLazyLoading` for large file lists
- Set appropriate `maxFiles` limits
- Configure `thumbnailGeneration` for images
- Use `memoryManagement` options for cleanup

## Migration Guide

### From Legacy Components

If migrating from `MuiFileUploadInput` or similar legacy components:

```typescript
// Old way ❌
<MuiFileUploadInput
  fileType="private"
  onChange={handleChange}
/>

// New way ✅
<FileUploadComponent
  value={files}
  onChange={handleChange}
  config={createConfig.images({ fileType: 'private' })}
/>
```

### From Old Hooks

```typescript
// Old way ❌
import { useFileSize } from '~/hooks/useFileSize';

// New way ✅
import { useFileSize } from '~/components/organisms/FileUpload';
```

## Troubleshooting

### Common Issues

1. **Memory Leaks**: Ensure proper cleanup with `useInMemoryFileCleanup`
2. **Validation Errors**: Check file size and type constraints
3. **Upload Failures**: Verify Firebase configuration
4. **Preview Issues**: Check file permissions and URLs

### Debug Mode

Set `NODE_ENV=development` to enable debug logging.

## API Reference

See the [full API documentation](./index.ts) for complete type definitions and exports.
