import { useMemo, useState } from 'react';
import { Box, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useTheme } from '~/contexts/ThemeContext';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import { getReportDataHelper } from '~/fake-provider';
import { useFirebase } from '~/contexts/FirebaseContext';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import ExtraDataTransactions from '../reports/report-transactions/components/ExtraDataTransactions';


export default function ExtraDataInvoices({
  extraData,
}: {
  extraData?: { [key: string]: any };
}) {
  const { t } = useTranslation('');
  const { details: fbDetails } = useFirebase();
  const { dateRange, sellPointId } = useGlobalResourceFilters();
  const [transactionData, setTransactionData] = useState<any>();

  const handleSaveInvoice = async () => {
    if (extraData?.url) {
      try {
        const response = await fetch(extraData.url);
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `invoice-${extraData.billName || Date.now()}.png`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Failed to download invoice:', error);
      }
    }
  };
  console.log(extraData);

  const rowData = useMemo(() => {
    const customerNameValue = extraData?.billName;

    const coupons = extraData?.coupons
      ? Object.values(extraData?.coupons).map((coupon: any) => {
          return {
            name: coupon.name,
            type: coupon.type,
            value: formatAndDivideNumber(coupon.value),
          };
        })
      : undefined;

    const extraCharges = extraData?.extraCharges
      ? Object.values(extraData?.extraCharges).map((extra: any) => {
          return {
            name: extra.name,
            quantity: extra.quantity / 1000,
            value: formatAndDivideNumber(extra.value),
          };
        })
      : undefined;

    const giftCards = extraData?.giftCards
      ? Object.values(extraData?.giftCards)?.map((giftCard: any) => {
          return {
            name: giftCard.name,
            type: giftCard.type,
            value: formatAndDivideNumber(giftCard.value),
          };
        })
      : undefined;

    const rowDataFiltered = {
      ...extraData,
      billName: extraData?.billName || '-',
      totalValue: formatAndDivideNumber(extraData?.totalValue),
      closedAt: formatTimestamp(extraData?.billClosedAt),
      closedFrom: extraData?.closedFrom || '-',
      series: extraData?.series || '-',
      number: extraData?.number || '-',
      createdBy: extraData?.createdBy || '-',
      billFiscalNumber: extraData?.billFiscalNumber || '-',
      billPaymentsValue: extraData?.billPaymentsValue || '-',
      source:
        extraData?.source && extraData?.source === '@pos'
          ? 'Direct Selio Pos'
          : extraData?.source || '-',
      owner: extraData?.owner,
      covers: extraData?.covers || '-',
      id: extraData?.id,
      closedWith: extraData?.closedWith,
      diningOption: extraData?.dinningOption || '-',
      voidReason: extraData?.voidReason,
      tipsPercentage: extraData?.tipsPercentage,
      tipsValue: extraData?.tipsValue,
      note: extraData?.note,
      subTotalValue: formatAndDivideNumber(extraData?.subTotalValue),
      section: extraData?.section,
      closedBy: extraData?.closedBy,
      vatNoOnReceipt: extraData?.vatNoOnReceipt,
      closedWithCompReason: extraData?.closedWithCompReason,
      openedAt: extraData?.openedAt || undefined,
      orderDiscount: extraData?.orderDiscount
        ? extraData?.orderDiscount / 100
        : undefined,
      orderDiscountValue: formatAndDivideNumber(extraData?.orderDiscountValue),
      extraCharges,
      coupons,
      giftCards,
    };

    return rowDataFiltered;
  }, [extraData]);

  function formatTimestamp(timestamp: number) {
    const date = new Date(timestamp * 1000);
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
      .format(date)
      .replace(',', '');
  }

  const handleSeeTransaction = async () => {
    if (transactionData) {
      setTransactionData(undefined);
      return;
    }
    if (!dateRange[0] || !dateRange[1]) return;
    const data = await getReportDataHelper({
      database: fbDetails.rtdb!,
      startDate: dateRange[0]?.format('YYYY-MM-DD'),
      accountId: fbDetails.selectedAccount!,
      sellPointId: sellPointId,
      // we add 1 second because end date is always 23:59:59 and we want it to be next day
      endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
      reportType: 'transactions',
    });
    data.forEach((item: any) => {
      item.report.forEach((report: any) => {
        if (report.id === extraData?.billId) {
          console.log(report);
          setTransactionData(report);
        }
      });
    });
  };

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '500px',
        mx: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        backgroundColor: 'background.paper',
        borderRadius: 2,
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            justifyContent: 'space-between',
            width: '100%',
            flexDirection: { xs: 'column', md: 'row' },
          }}
        >
          <Button
            // @ts-ignore
            variant="contained-light"
            sx={{ width: '100%', height: '52px' }}
            onClick={handleSaveInvoice}
          >
            Save Invoice
          </Button>
          <Button
            // @ts-ignore
            variant="contained-light"
            display='none'
            sx={{ width: '100%', height: '52px' }}
            onClick={handleSeeTransaction}
          >
            {transactionData ? 'Hide Transaction' : 'See Transaction'}
          </Button>
        </Box>
      </Box>
      {transactionData && (
          <ExtraDataTransactions extraData={transactionData} />
      )}
      {/* Action Buttons */}
      
      <img
        src={extraData?.url || ''}
        alt={'invoice-icon'}
        style={{ width: '100%', height: '100%' }}
      />
    </Box>
  );
}
