import React, { createContext, useContext, useEffect, useState } from 'react';
import { IdTokenResult, onAuthStateChanged, User } from 'firebase/auth';
import { Database } from 'firebase/database';
import { useLocation } from 'react-router-dom';

import cleanupActions from '~/providers/utils/cleanupActions';
import isEqual from '~/providers/utils/isEqual';
import {
  auth,
  getRtdbInstance,
  waitForAuthInit,
} from '../configs/firebaseConfig';
import { accountStorage } from '../providers/utils/accountStorage';
import { LOCAL_STORAGE_SELECTED_ACCOUNT_KEY } from '../providers/utils/constants';
import { getDatabaseUrlFromAccountId } from '../providers/utils/getDatabaseUrlFromAccountId';
import { presenceManager } from '../providers/utils/presenceManager';

interface FirebaseContextDetailsType {
  user: User | null;
  availableAccounts: string[];
  selectedAccount: string | null;
  rtdbUrl: string | null;
  rtdb: Database | null;
}

interface FirebaseContextType {
  loading: boolean;
  details: FirebaseContextDetailsType;
  setSelectedAccount: (accountId: string) => Promise<void>;
}

interface AccountClaimData {
  [accountId: string]: unknown;
}

const FirebaseContext = createContext<FirebaseContextType | null>(null);

// Helper function to extract accounts from claims
const getAccountsFromClaims = (idToken: IdTokenResult): string[] => {
  const accountClaims = idToken.claims?.a as AccountClaimData | undefined;
  if (!accountClaims || typeof accountClaims !== 'object') {
    return [];
  }
  const accounts = Object.keys(accountClaims).sort();
  return accounts;
};

export const FirebaseProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [loading, setLoading] = useState(true);
  const [firebaseContextInitialized, setFirebaseContextInitialized] =
    useState(false);
  const [details, setDetails] = useState<FirebaseContextDetailsType>({
    user: null,
    availableAccounts: [],
    selectedAccount: null,
    rtdbUrl: null,
    rtdb: null,
  });
  const location = useLocation();
  const [wasLoggedIn, setWasLoggedIn] = useState(false);

  // Update rtdbUrl after initialization
  useEffect(() => {
    const initializeFirebaseContext = async () => {
      await waitForAuthInit().catch(() => {});
      const user = auth.currentUser ?? null;
      const availableAccounts = user
        ? getAccountsFromClaims(
            (await user.getIdTokenResult(false).catch(() => {}))!
          )
        : [];
      const selectedAccount = accountStorage.getSelectedAccount();
      const rtdbUrl = await getDatabaseUrlFromAccountId(selectedAccount!).catch(
        () => null
      );
      const rtdb = getRtdbInstance(rtdbUrl);
      setDetails(() => ({
        user,
        availableAccounts,
        selectedAccount,
        rtdbUrl,
        rtdb,
      }));
      setFirebaseContextInitialized(true);
    };
    initializeFirebaseContext();
  }, []);

  // Handle account selection
  const handleSetSelectedAccount = async (accountId: string) => {
    if (accountId === details.selectedAccount) {
      return;
    }

    if (!details.availableAccounts.includes(accountId)) {
      throw new Error('Invalid account selection');
    }

    console.log('Setting selected account to:', accountId);
    cleanupActions.runAll();
    accountStorage.setSelectedAccount(accountId);
    const rtdbUrl = await getDatabaseUrlFromAccountId(accountId).catch(
      () => null
    );
    const rtdb = getRtdbInstance(rtdbUrl);
    setDetails({
      ...details,
      selectedAccount: accountId,
      rtdbUrl: rtdbUrl,
      rtdb: rtdb,
    });
  };

  // Function to update available accounts
  const updateFirebaseState = React.useCallback(
    async (currentUser: User | null) => {
      console.log('Update auth state called');
      const newAuthDetails = { ...details };
      let somethingChanged = false;
      if (
        (!currentUser && details.user) ||
        (currentUser && !details.user) ||
        (currentUser && details.user && currentUser.uid !== details.user.uid)
      ) {
        somethingChanged = true;
        newAuthDetails.user = currentUser;
        if (!currentUser && details.user) setWasLoggedIn(true);
      }
      if (!currentUser) {
        if (details.availableAccounts.length > 0) {
          somethingChanged = true;
          newAuthDetails.availableAccounts = [];
        }
        if (details.selectedAccount) {
          somethingChanged = true;
          newAuthDetails.selectedAccount = null;
        }
      } else {
        try {
          const idToken = await currentUser.getIdTokenResult(false);
          const accounts = getAccountsFromClaims(idToken);

          // Update available accounts if changed
          if (!isEqual(accounts, details.availableAccounts)) {
            somethingChanged = true;
            newAuthDetails.availableAccounts = accounts;
          }

          // Validate current selection against available accounts
          if (details.selectedAccount && accounts.length > 0) {
            const isSelectedAccountValid = accounts.includes(
              details.selectedAccount
            );
            if (!isSelectedAccountValid) {
              console.warn(
                '[FirebaseContext] Selected account not in available accounts, clearing selection',
                { selected: details.selectedAccount, available: accounts }
              );
              somethingChanged = true;
              newAuthDetails.selectedAccount = null;
            }
          }
        } catch (error) {
          console.error(
            '[FirebaseContext] Error getting accounts from claims:',
            error
          );
          if (details.availableAccounts.length > 0) {
            somethingChanged = true;
            newAuthDetails.availableAccounts = [];
          }
          if (details.selectedAccount) {
            somethingChanged = true;
            newAuthDetails.selectedAccount = null;
          }
        }
      }
      if (somethingChanged) {
        // Only run cleanup if the user changed or the selected account changed
        // Don't run cleanup just because available accounts list changed
        const userChanged = newAuthDetails.user?.uid !== details.user?.uid;
        const selectedAccountChanged =
          newAuthDetails.selectedAccount !== details.selectedAccount;

        if (userChanged || selectedAccountChanged) {
          console.log(
            '[FirebaseContext] Running cleanup due to user or account change'
          );
          cleanupActions.runAll();
        }

        console.log(
          'Something changed',
          'old details:',
          details,
          'new details:',
          newAuthDetails
        );
        const cachedSelectedAccount = accountStorage.getSelectedAccount();
        if (cachedSelectedAccount !== newAuthDetails.selectedAccount) {
          accountStorage.setSelectedAccount(newAuthDetails.selectedAccount);
        }
        newAuthDetails.rtdbUrl = await getDatabaseUrlFromAccountId(
          newAuthDetails.selectedAccount!
        ).catch(() => null);
        newAuthDetails.rtdb = getRtdbInstance(newAuthDetails.rtdbUrl);
        console.log('Updating auth details:', newAuthDetails);
        setDetails(newAuthDetails);
      }
    },
    [details]
  );

  useEffect(() => {
    if (!firebaseContextInitialized) {
      console.log('Auth not initialized yet, waiting... 1');
      return; // Don't set up listeners until auth is initialized
    }
    console.log('Setting up auth and token change listeners');

    // Auth state listener
    const authUnsubscribe = onAuthStateChanged(auth, async currentUser => {
      console.log(
        'Auth state changed, currentUser:',
        currentUser?.uid || 'null'
      );
      await updateFirebaseState(currentUser);
    });

    // Token change listener
    // !!! CHECK IF we need if (currentUser) check
    const tokenUnsubscribe = auth.onIdTokenChanged(async currentUser => {
      console.log('Token changed');
      if (currentUser) {
        console.log('Token changed for user:', currentUser.uid);
        await updateFirebaseState(currentUser);
      }
    });

    // Storage event handler for REAL browser storage events (from other tabs)
    // This will NOT be triggered by our own code
    // But it WILL be triggered by other tabs that change localStorage
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === LOCAL_STORAGE_SELECTED_ACCOUNT_KEY) {
        console.log('Storage event triggered for selected account');
        // Only update if the value is different
        if (event.newValue !== details.selectedAccount) {
          console.log('Selected account changed in another tab');
          // When this happens, we should update our state
          // but NOT update localStorage again
          setDetails({ ...details, selectedAccount: event.newValue });
        }
      }
    };

    // Add storage event listener for cross-tab communication
    console.log('Adding storage event listener');
    window.addEventListener('storage', handleStorageChange);

    console.log('Loading complete');
    setLoading(false);

    // Cleanup
    return () => {
      console.log('******* Running cleanup actions from FirebaseContext');
      cleanupActions.runAll();
      console.log('Cleaning up auth and token change listeners');
      authUnsubscribe();
      tokenUnsubscribe();
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [firebaseContextInitialized, details, updateFirebaseState]);

  // Effect to manage presence system
  useEffect(() => {
    // Setup presence if all required information is available
    if (
      details.user?.uid &&
      details.selectedAccount &&
      details.rtdb &&
      details.rtdbUrl
    ) {
      presenceManager.setup(
        details.rtdb,
        details.rtdbUrl,
        details.selectedAccount,
        details.user.uid
      );

      // Cleanup: capture current values in closure for proper cleanup
      const rtdbUrl = details.rtdbUrl;
      const accountId = details.selectedAccount;
      const userId = details.user.uid;

      return () => {
        presenceManager.cleanup(rtdbUrl, accountId, userId);
      };
    }
  }, [
    details.user?.uid,
    details.selectedAccount,
    details.rtdb,
    details.rtdbUrl,
  ]);

  // Effect to register presence cleanup with cleanupActions
  useEffect(() => {
    // Register cleanup function to be called before logout or account switching
    const cleanupFn = () => {
      if (details.user?.uid && details.selectedAccount && details.rtdbUrl) {
        presenceManager.cleanup(
          details.rtdbUrl,
          details.selectedAccount,
          details.user.uid
        );
      }
    };

    // register() returns an unregister function
    const unregister = cleanupActions.register(cleanupFn);

    return unregister;
  }, [details.user?.uid, details.selectedAccount, details.rtdbUrl]);

  if (!firebaseContextInitialized) {
    console.log('Auth not initialized yet, waiting... 2');
    return null;
  }

  const value = {
    loading,
    details,
    setSelectedAccount: handleSetSelectedAccount,
  };

  // Auth state changes are now handled at the index.html level
  // If user logs out, Firebase auth will detect it and index.html will redirect to /auth
  // We keep this check only for the case where user was logged in and then logs out
  // This ensures immediate redirect on logout without waiting for page refresh
  if (details.user === null && wasLoggedIn && location.pathname !== '/auth') {
    console.log('User logged out, redirecting to auth');
    window.location.pathname = '/auth';
    return null;
  }

  return (
    <FirebaseContext.Provider value={value}>
      {children}
    </FirebaseContext.Provider>
  );
};

// Custom hook to use the Firebase Auth context
export const useFirebase = () => {
  const context = useContext(FirebaseContext);
  if (!context) {
    throw new Error('useFirebase must be used within a FirebaseAuthProvider');
  }
  return context;
};
