import { useMemo, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import BreadCrumbs from '~/components/molecules/BreadCrumbs';
import ExtraDataCard from '~/components/molecules/ExtraDataCard';
import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupReport } from '~/fake-provider/reports/groupReport';
import FormattedFilters from '~/pages/reports/report-item-sales/components/FormattedFilters';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import replaceNumberWithPercentage from '~/utils/replaceNumbersWithPercentage';

type TableRow = {
  id: string;
  quantity: string;
  measureUnit: string;
  value: string | number;
  discountsValue: number;
  price: number;
  promotionsValue: number;
  discountName: string;
  promotionName: string;
  name: string;
  couponsValue: number;
  netValue: number;
  variant: string;
  items?: TableRow[];
  subItems?: TableRow[];
};

export default function ExtraDataModifierModal({
  extraData,
}: {
  extraData?: { [key: string]: any };
}) {
  const { t } = useTranslation();
  const extraDataItemSalesConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'id',
      label: t('reportsPage.modifierName'),
      textAlign: 'start',
      render: (row: TableRow, rowIndex: number) => {
        const isCombo = row.variant !== '@none';
        return (
          <>
            <Typography
              fontWeight={rowIndex === 0 ? 500 : 300}
              sx={{
                py: 0.5,
                fontWeight: 500,
                fontSize: { xs: '11px', sm: '16px' },
                whiteSpace: 'wrap',
                minWidth: { xs: '100px', sm: '320px' },
              }}
              py={rowIndex === 0 ? 0 : 3}
            >
              {rowIndex === 0
                ? capitalize(row.name?.toLowerCase())
                : isCombo
                  ? 'Combo'
                  : `Regular`}{' '}
              {rowIndex !== 0 && (
                <span>@{formatAndDivideNumber(row.price)}</span>
              )}
            </Typography>
            {rowIndex !== 0 && isCombo && (
              <Typography
                fontWeight={rowIndex === 0 ? 500 : 300}
                sx={{
                  py: 0.2,
                  fontSize: { xs: '11px', sm: '16px' },
                  whiteSpace: 'wrap',
                  minWidth: { xs: '100px', sm: '320px' },
                }}
                py={rowIndex === 0 ? 0 : 3}
              >
                {capitalize(row.variant)}
              </Typography>
            )}
            {rowIndex !== 0 &&
              row?.discountName &&
              row?.discountName !== '@none' && (
                <Typography sx={{ fontSize: '12px', color: 'gray' }}>
                  Discount: {replaceNumberWithPercentage(row?.discountName)}
                </Typography>
              )}
            {rowIndex !== 0 &&
              row?.promotionName &&
              row?.promotionName !== '@none' && (
                <Typography sx={{ fontSize: '12px', color: 'gray' }}>
                  Discount: {replaceNumberWithPercentage(row?.promotionName)}
                </Typography>
              )}
            {rowIndex === 0 && extraData?.rowData?.parentFields && (
              <BreadCrumbs parentFields={extraData.rowData.parentFields} />
            )}
          </>
        );
      },
    },
    {
      id: 'quantity',
      label: t('reportsPage.itemsComped'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {formatNumberIntl(Number(row.quantity) / 1000)}
          </Typography>
        );
      },
    },

    {
      id: 'measureUnit',
      label: t('reportsPage.unit'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row.measureUnit && row.measureUnit === 'undefined'
              ? ''
              : row.measureUnit}
          </Typography>
        );
      },
    },
    {
      id: 'value',
      label: t('reportsPage.grossComps'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {formatAndDivideNumber(Number(row.value))}
          </Typography>
        );
      },
    },
    {
      id: 'discountsValue',
      label: t('reportsPage.discounts'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row?.discountsValue
              ? '- ' + formatAndDivideNumber(row?.discountsValue)
              : ''}
          </Typography>
        );
      },
    },
    {
      id: 'couponsValue',
      label: t('reportsPage.coupons'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row?.couponsValue
              ? '- ' + formatAndDivideNumber(row?.couponsValue)
              : ''}
          </Typography>
        );
      },
    },
    {
      id: 'promotionsValue',
      label: t('reportsPage.promotions'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </Typography>
        );
      },
    },
    {
      id: 'netValue',
      label: t('reportsPage.netComps'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {formatAndDivideNumber(row.netValue)}
          </Typography>
        );
      },
    },
  ];

  const [fields, setFields] = useState<FieldOption[]>(
    extraDataItemSalesConfig.map(col => ({
      value: col.id as string,
      isChecked: true,
    }))
  );

  const { itemsData, totalItemsData } = useMemo(() => {
    if (!extraData?.rawData || !extraData?.filters) return { itemsData: [] };

    const fields = [
      {
        field: 'id',
        operator: '==',
        value: extraData.rowData.id,
      },
      {
        field: 'groupId',
        operator: '==',
        value: extraData.rowData.groupId,
      },
      {
        field: 'measureUnit',
        operator: '==',
        value: extraData.rowData.measureUnit,
      },
      {
        field: 'prepStation',
        operator: '==',
        value: extraData.rowData.prepStation,
      },
    ];

    const filteredFields = fields.filter(item => {
      return item.value;
    });

    const rawDataFiltered = filterReport(
      extraData.reportType,
      extraData.rawData,
      extraData.composedFilters,
      //@ts-ignore
      filteredFields
    );

    const groupedTableData = groupReport(
      extraData?.reportType,
      rawDataFiltered,
      [],
      [
        'groupId',
        'id',
        'measureUnit',
        'variant',
        'prepStation',
        'prepStationId',
        'price',
        'vat',
        'value',
        'discountName',
        'promotionName',
      ]
    );

    let itemsData = groupedTableData[0].report;

    itemsData = itemsData.filter(item => {
      const variant = item.variant?.toLowerCase() || '';
      return (!/\bno\b/.test(variant) && !/\ballergy\b/.test(variant) && item.price);
    });

    const totalItemsDataObject = mergeAndSumObjects(itemsData);
    totalItemsDataObject.name = extraData.rowData.name;
    totalItemsDataObject.price = '';
    const totalItemsData = [totalItemsDataObject, ...itemsData];
    if (!itemsData) return { itemsData: [], totalItemsData: [] };

    return { itemsData, totalItemsData };
  }, [extraData]);

  const calculateCardValues = (itemsData: TableRow[]) => {
    const totalSold = itemsData.reduce(
      (sum, item) => sum + Number(item.quantity || 0),
      0
    );
    const discountCount = itemsData
      .filter(item => Number(item.discountsValue) > 0)
      .reduce((sum, item) => sum + Number(item.quantity), 0);
    const couponCount = itemsData
      .filter(item => Number(item.couponsValue) > 0)
      .reduce((sum, item) => sum + Number(item.quantity), 0);
    const promotionCount = itemsData
      .filter(item => Number(item.promotionsValue) > 0)
      .reduce((sum, item) => sum + Number(item.quantity), 0);

    return {
      totalSold: formatNumberIntl(totalSold, true),
      withDiscounts: discountCount / 1000,
      withCoupons: couponCount / 1000,
      withPromotions: promotionCount / 1000,
    };
  };

  const cardValues = calculateCardValues(itemsData);

  const cardsConfig: { title: string; value: string | number }[] = [
    { title: 'Total Comped', value: cardValues.totalSold },
    { title: 'w/ Discounts', value: cardValues.withDiscounts },
    // { title: 'w/ Coupons', value: cardValues.withCoupons },
    { title: 'w/ Promotions', value: cardValues.withPromotions },
  ];

  return (
    <Box sx={{ width: '100%', maxWidth: '1500px', mx: 'auto' }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          maxWidth: '1000px',
          width: '100%',
          mx: 'auto',
          mb: 8,
          gap: 1,
        }}
      >
        <Typography sx={{ fontSize: 18, fontWeight: 300, textAlign: 'center' }}>
          {t('reportsPage.all')}
          <span style={{ fontWeight: 700 }}>
            {' '}
            {capitalize(extraData?.rowData.name?.toLowerCase()) ||
              extraData?.rowData.id}{' '}
          </span>{' '}
          {t('reportsPage.compedFrom')}
        </Typography>
      </Box>
      <FormattedFilters formattedFilters={extraData?.formattedFilters} />
      <Box
        sx={{
          width: '100%',
          display: { xs: 'grid', sm: 'flex' },
          gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'unset' },
          gap: 8,
          mt: 4,
          alignItems: 'center',
          justifyContent: { xs: 'center', sm: 'center' },
          pb: 2,
          borderBottom: '2px solid #F2F2F2',
          '@media print': {
            borderBottom: '2px solid black',
          },
        }}
      >
        {cardsConfig.map(
          (item: { title: string; value: string | number }, index: number) => {
            const isLastItem =
              extraData?.attributes &&
              index === extraData.attributes.length - 1;

            return (
              <Box
                key={index}
                sx={{
                  gridColumn: isLastItem ? '1 / -1' : 'auto',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ExtraDataCard item={item} />
              </Box>
            );
          }
        )}
      </Box>

      <CustomTable
        fields={fields}
        setFields={setFields}
        filter={false}
        extraDataFirstRow={true}
        searchBar={false}
        fixedFirstColumn={true}
        config={extraDataItemSalesConfig}
        data={totalItemsData || []}
        alignLastColumnRight={false}
      />
    </Box>
  );
}
