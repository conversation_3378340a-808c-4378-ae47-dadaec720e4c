import { Fragment, useMemo } from 'react';
import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import {
  BulkDeleteWithConfirmButton,
  CreateButton,
  DataTable,
  FunctionField,
  List,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { ValueFieldWithError } from '~/components/atoms/ValueFieldWithError';
import AreaItemsChips from '~/components/molecules/AreaItemsChips';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import {
  RESOURCES,
  resourcesInfo,
  useGetListTeamMembersLive,
} from '~/providers/resources';
import { getValueFromRecord } from '~/utils/getValueFromRecord';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { useTheme } from '../../contexts';

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ textAlign: 'center' }}>
      <LocationAndDateSelectors isDate={false} hideShadow />
      <img src="/assets/icons/working-area-assignment.svg" width="45px" />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('workingArea.noWorkingAreasYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('workingArea.noWorkingAreasYetDescription')}
      </Typography>
      <CreateButton
        variant="contained"
        label={t('workingArea.createWorkingArea')}
      />
    </div>
  );
};

const AssetBulkActionButtons = (props: any) => (
  <Fragment>
    <BulkDeleteWithConfirmButton {...props} />
  </Fragment>
);

export const TeamMemberWorkingAreaList = (props: { sellPointId: string }) => {
  const { sellPointId } = props;
  const { theme } = useTheme();
  const { t } = useTranslation('');

  const { data: teamMembers } = useGetListTeamMembersLive();
  const teamMemberIdToName = useMemo(() => {
    if (!teamMembers) return {};
    return teamMembers.reduce((acc: any, teamMember: any) => {
      acc[teamMember.id] = teamMember.displayName;
      return acc;
    }, {});
  }, [teamMembers]);

  const filters = useMemo(
    () => [
      <CustomSearchInput
        placeholder={t('members.filterTeamMembers')}
        key="search-input"
        source="q"
        alwaysOn
      />,
    ],
    [t]
  );
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  return (
    <List
      resource={RESOURCES.TEAM_MEMBERS_WORKING_AREA}
      sort={resourcesInfo[RESOURCES.TEAM_MEMBERS_WORKING_AREA].defaultSort}
      pagination={false}
      perPage={Number.MAX_SAFE_INTEGER}
      component="div"
      exporter={false}
      actions={false}
      empty={<CustomEmpty />}
      queryOptions={{ meta: { sellPointId: sellPointId } }}
      filter={{ sellPointId: sellPointId }}
      sx={{
        '& .RaList-noActions': {
          mt: 0,
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <LocationAndDateSelectors isDate={false} hideShadow />
        <CreateButton
          variant="contained"
          label={t('workingArea.createWorkingArea')}
          {...(isXSmall ? {} : { icon: <></> })}
        />
      </Box>
      {isXSmall ? (
        <MobileGrid>
          <MobileCard
            cardClick="edit"
            actions={true}
            titleRender={(record: any) => {
              const teamMember = getValueFromRecord(
                teamMemberIdToName,
                record.id
              );
              return (
                <ValueFieldWithError
                  value={teamMember}
                  errorMessage={t('members.missingRole')}
                />
              );
            }}
          >
            <FunctionField
              textAlign="right"
              render={record => {
                return <AreaItemsChips areaItems={record.areaItems} readOnly />;
              }}
            />
          </MobileCard>
        </MobileGrid>
      ) : (
        <DataTable
          rowClick="edit"
          sx={{
            '& .RaBulkActionsToolbar-topToolbar': {
              backgroundColor: 'transparent',
            },
            marginTop: '10px',
            '& .RaDataTable-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
            '& .RaDatagrid-cell': {
              textAlign: 'right',
            },
          }}
          bulkActionButtons={<AssetBulkActionButtons />}
        >
          <DataTable.Col label={t('workingArea.member')}>
            <FunctionField
              textAlign="left"
              render={record => {
                const teamMember = getValueFromRecord(
                  teamMemberIdToName,
                  record.id
                );
                return (
                  <ValueFieldWithError
                    value={teamMember}
                    errorMessage={t('members.missingRole')}
                  />
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('workingArea.workingArea')} align="left">
            <FunctionField
              textAlign="right"
              render={record => {
                return <AreaItemsChips areaItems={record.areaItems} readOnly />;
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('prepStations.actions')} align="right">
            <ActionsField
              hasEdit={true}
              hasDelete={true}
              deleteMutationMode="pessimistic"
              deleteMutationOptions={{ meta: { sellPointId: sellPointId } }}
              recordNameField="displayName"
            />
          </DataTable.Col>
        </DataTable>
      )}
    </List>
  );
};
