import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  AttachFile as AttachFileIcon,
  FileUpload,
  Info as InfoIcon,
} from '@mui/icons-material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Box,
  Chip,
  CircularProgress,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';

import { FileDropzone } from './components/FileDropzone/FileDropzone';
import { FileList } from './components/FileList/FileList';
import { FilePreviewModal } from './components/FilePreviewModal';
import { ValidationDisplay } from './components/FileValidation/ValidationDisplay';
import { ImageEditorModal } from './components/ImageEditorModal';
import { inMemoryFileManager } from './core/InMemoryFileManager';
import { useFileList } from './hooks/useFileList';
import { useFileUpload } from './hooks/useFileUpload';
import { useFileValidation } from './hooks/useFileValidation';
import { useImageEditor } from './hooks/useImageEditor';
import { useDisplayFileName } from './hooks/useRealFileName';
import { FileUploadComponentProps } from './types';
import { UploadedFile } from './types/fileUpload';
import { mergeWithDefaults, validateConfig } from './utils/fileUploadConfig';
import { formatFileSize, getFileExtensions } from './utils/fileUploadHelpers';
import { canPreviewFile } from './utils/previewUtils';

/**
 * Individual file chip component that handles filename resolution
 */
const FileChip: React.FC<{
  file: any;
  index: number;
  onRemove?: (index: number) => void;
  onPreview?: (file: any) => void;
  disabled?: boolean;
  privateContext?: any;
}> = ({ file, index, onRemove, onPreview, disabled, privateContext }) => {
  // Use the display filename hook which handles both temp and permanent files
  const displayFileName = useDisplayFileName(file, privateContext);
  const extension = file.e ? `.${file.e}` : '';

  // Function to truncate from middle if needed
  const getTruncatedLabel = (name: string, ext: string) => {
    const fullName = `${name}${ext}`;
    // If the full name is short enough, return as is
    if (fullName.length <= 20) {
      return fullName;
    }

    // For longer names, show first part + ... + extension
    const maxStartLength = Math.max(8, 20 - ext.length - 3); // 3 for "..."
    const truncatedName =
      name.length > maxStartLength
        ? `${name.substring(0, maxStartLength)}...${ext}`
        : fullName;

    return truncatedName;
  };

  const displayLabel = getTruncatedLabel(displayFileName, extension);
  const fullName = `${displayFileName}${extension}`;

  return (
    <Chip
      key={`${file.f}-${index}`}
      label={displayLabel}
      title={fullName} // Show full name on hover
      onDelete={!disabled && onRemove ? () => onRemove(index) : undefined}
      onClick={
        canPreviewFile(file) && !disabled && onPreview
          ? () => onPreview(file)
          : undefined
      }
      size="small"
      variant="outlined"
      sx={{
        cursor: canPreviewFile(file) && !disabled ? 'pointer' : 'default',
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        maxWidth: '100%',
        height: '32px',
        '& .MuiChip-label': {
          fontWeight: 500,
          fontSize: '0.875rem',
          textAlign: 'left',
          paddingLeft: '8px',
          paddingRight: '8px',
        },
        '&:hover': !disabled
          ? {
              backgroundColor: 'action.hover',
              borderColor: 'primary.main',
            }
          : {},
      }}
    />
  );
};

/**
 * Main FileUploadComponent - replaces MuiFileUploadInput with modular architecture
 */
const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  value = [],
  onChange,
  config: userConfig,
  label,
  helperText,
  error = false,
  errorText,
  sx,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  // Set up translation function for placeholders
  React.useEffect(() => {
    // Wrapper to adapt react-i18next signature to placeholder translation signature
    const translationWrapper = (
      key: string,
      defaultValue?: string,
      options?: any
    ): string => {
      const result = t(key, defaultValue || key, options);
      return typeof result === 'string' ? result : String(result);
    };
  }, [t]);

  // Merge user config with defaults
  const config = useMemo(() => mergeWithDefaults(userConfig), [userConfig]);

  // Validate configuration
  const configErrors = useMemo(() => validateConfig(config), [config]);

  // State
  const [previewFile, setPreviewFile] = useState<any>(null);
  const [isFileListExpanded, setIsFileListExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const textFieldRef = useRef<HTMLDivElement>(null);

  // Initialize hooks
  const fileList = useFileList(value, onChange);

  // Callbacks to track drag state
  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);
  const validation = useFileValidation(config);
  const upload = useFileUpload(config);

  // Clean up in-memory files on unmount
  useEffect(() => {
    return () => {
      inMemoryFileManager.forceCleanup();
    };
  }, []);

  // Protect active files from blob URL cleanup
  useEffect(() => {
    const activeFileIds = fileList.files
      .filter(file => file.inMemoryData) // Only protect in-memory files
      .map(file => file.f);

    if (activeFileIds.length > 0) {
      console.log(
        '🔒 [FileUploadComponent] Protecting active files:',
        activeFileIds
      );
      inMemoryFileManager.markFilesAsActive(activeFileIds);
    }

    // Cleanup function to unmark files when component unmounts or file list changes
    return () => {
      if (activeFileIds.length > 0) {
        console.log(
          '🔓 [FileUploadComponent] Unprotecting files:',
          activeFileIds
        );
        activeFileIds.forEach(fileId =>
          inMemoryFileManager.unmarkFileAsActive(fileId)
        );
      }
    };
  }, [fileList.files]);

  // Update image editor with correct callback
  const imageEditorWithCallback = useImageEditor(config, {
    onImageEdit: config.callbacks?.onImageEdit,
    onImageEditCancel: config.callbacks?.onImageEditCancel,
    onImageEditStart: config.callbacks?.onImageEditStart,
    onUploadImageWithCroppedVariants: async (
      originalFile: File,
      croppedFiles: File[]
    ): Promise<void> => {
      const uploadedFile = await upload.uploadImageWithCroppedVariants(
        originalFile,
        croppedFiles
      );
      fileList.addFile(uploadedFile);
    },
  });

  // Handle file selection
  const handleFilesSelected = useCallback(
    async (selectedFiles: File[]) => {
      if (selectedFiles.length === 0) return;

      // Close dropdown when uploading new files
      if (isFileListExpanded) {
        setIsFileListExpanded(false);
      }

      // Validate files and get errors synchronously
      const validationErrors = validation.validate(
        selectedFiles,
        fileList.files.map(() => (({}) as File))
      );

      // Check for validation errors using the returned errors, not the state
      const hasValidationErrors = validationErrors.some(
        error => error.severity === 'error'
      );

      if (hasValidationErrors && !config.validation?.allowInvalidFiles) {
        return; // Stop if there are errors and invalid files are not allowed
      }

      // Get valid files by filtering out files with errors
      const errorFiles = new Set(
        validationErrors
          .filter(error => error.severity === 'error' && error.file)
          .map(error => error.file!)
      );

      const validFiles = config.validation?.allowInvalidFiles
        ? selectedFiles
        : selectedFiles.filter(file => !errorFiles.has(file));

      if (validFiles.length === 0) return;

      try {
        // Check if any files need image editing
        const filesToEdit = await Promise.all(
          validFiles.map(async file => ({
            file,
            needsEditing:
              await imageEditorWithCallback.shouldFileBeEdited(file),
          }))
        );

        const imagesToEdit = filesToEdit
          .filter(f => f.needsEditing)
          .map(f => f.file);
        const filesToUploadDirectly = filesToEdit
          .filter(f => !f.needsEditing)
          .map(f => f.file);

        // Upload files that don't need editing
        if (filesToUploadDirectly.length > 0) {
          const uploadedFiles = await upload.upload(filesToUploadDirectly);
          fileList.addFiles(uploadedFiles);
        }

        // Start editing process for images that need editing
        if (imagesToEdit.length > 0) {
          await imageEditorWithCallback.startEditingProcess(imagesToEdit);
        }

        // Input will be reset automatically by the FileDropzone component
      } catch (error) {
        console.error('Error handling file selection:', error);
      }
    },
    [config, validation, fileList, upload, imageEditorWithCallback]
  );

  // Handle file removal
  const handleRemoveFile = useCallback(
    (index: number) => {
      const fileToRemove = fileList.files[index];
      fileList.removeFile(index);

      // Call lifecycle callback
      if (fileToRemove) {
        config.callbacks?.onFileDeleted?.(fileToRemove);
      }

      // Close expanded list if only one file or no files remain
      if (fileList.files.length <= 2) {
        // Will be 1 after removal
        setIsFileListExpanded(false);
      }
    },
    [fileList, config.callbacks]
  );

  // Handle file removal by file object (more robust than index-based)
  const handleRemoveFileByIdentifier = useCallback(
    (file: UploadedFile) => {
      fileList.removeFileByIdentifier(file);

      // Call lifecycle callback
      config.callbacks?.onFileDeleted?.(file);

      // Close expanded list if only one file or no files remain
      if (fileList.files.length <= 2) {
        // Will be 1 after removal
        setIsFileListExpanded(false);
      }
    },
    [fileList, config.callbacks]
  );

  // Handle file preview
  const handleFilePreview = useCallback((file: any) => {
    setPreviewFile(file);
  }, []);

  // Handle close preview
  const handleClosePreview = useCallback(() => {
    setPreviewFile(null);
  }, []);

  // Generate computed helper text (matches FileDropzone description)
  const computedHelperText = useMemo(() => {
    if (helperText) return helperText;

    // Generate dynamic description matching FileDropzone
    const fileWord = config.multiple
      ? config.maxFiles && config.maxFiles > 1
        ? t('fileUpload.files', 'files')
        : t('fileUpload.files', 'files')
      : t('fileUpload.file', 'file');

    const extensions = getFileExtensions(config.acceptedTypes || []);
    const extensionsText =
      extensions.length > 0
        ? extensions.join(', ').toUpperCase()
        : config.acceptedTypes && config.acceptedTypes.length > 0
          ? config.acceptedTypes
              .map(type => type.split('/')[1]?.toUpperCase() || type)
              .join(', ')
          : '';

    const sizeText = config.maxSize
      ? ` ${t('fileUpload.maxSize', 'max size')} ${formatFileSize(config.maxSize)}`
      : '';
    const countText =
      config.maxFiles && config.multiple
        ? ` (${t('fileUpload.upTo', 'up to')} ${config.maxFiles})`
        : '';

    if (extensionsText && sizeText) {
      return t(
        'fileUpload.uploadWithExtensionsAndSize',
        'Upload {{fileWord}}{{countText}} with extensions: {{extensions}} and {{sizeText}}',
        { fileWord, countText, extensions: extensionsText, sizeText }
      );
    } else if (extensionsText) {
      return t(
        'fileUpload.uploadWithExtensions',
        'Upload {{fileWord}}{{countText}} with extensions: {{extensions}}',
        { fileWord, countText, extensions: extensionsText }
      );
    } else if (sizeText) {
      return t(
        'fileUpload.uploadWithSize',
        'Upload {{fileWord}}{{countText}} with {{sizeText}}',
        { fileWord, countText, sizeText }
      );
    }

    // Fallback to simple message
    return config.multiple
      ? t('fileUpload.dropMultiple', 'Drop files here or click to browse')
      : t('fileUpload.dropSingle', 'Drop a file here or click to browse');
  }, [config, helperText, t]);

  // Show configuration errors in development
  useEffect(() => {
    if (configErrors.length > 0 && process.env.NODE_ENV === 'development') {
      console.warn('FileUploadComponent configuration errors:', configErrors);
    }
  }, [configErrors]);

  // Close expanded file list when files count becomes 1 or 0
  useEffect(() => {
    if (fileList.fileCount <= 1) {
      setIsFileListExpanded(false);
    }
  }, [fileList.fileCount]);

  // Handle keyboard events and click outside for compact variant dropdown
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFileListExpanded) {
        setIsFileListExpanded(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (isFileListExpanded && !isDragging) {
        const target = event.target as Element;
        // Don't close if clicking within dropdown or dnd-kit overlay
        if (
          !target.closest('[data-file-dropdown]') &&
          !target.closest('[data-dnd-kit-draggable-drag-handle]') &&
          !target.closest('.dnd-kit-overlay')
        ) {
          setIsFileListExpanded(false);
        }
      }
    };

    if (isFileListExpanded) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isFileListExpanded]);

  // Handle toggle of file list expansion
  const handleToggleFileList = useCallback(() => {
    setIsFileListExpanded(prev => !prev);
  }, []);

  // Handle blur to close dropdown
  const handleTextFieldBlur = useCallback(() => {
    // Don't close if dragging
    if (isDragging) {
      return;
    }
    // Small delay to allow clicking inside the dropdown
    setTimeout(() => {
      if (!isDragging) {
        setIsFileListExpanded(false);
      }
    }, 150);
  }, [isDragging]);

  // Handle tooltip toggle for mobile
  const handleTooltipToggle = useCallback(() => {
    setIsTooltipOpen(true);
  }, []);

  // Handle tooltip close
  const handleTooltipClose = useCallback(() => {
    setIsTooltipOpen(false);
  }, []);

  // Handle mouse enter
  const handleTooltipMouseEnter = useCallback(() => {
    setIsTooltipOpen(true);
  }, []);

  // Render compact variant
  if (config.ui?.variant === 'compact') {
    const shouldShowDropdown =
      fileList.fileCount > 1 && isFileListExpanded && !!textFieldRef.current;

    return (
      <>
        <Box
          data-file-dropdown
          sx={{ width: '100%', position: 'relative', ...sx }}
          ref={textFieldRef}
        >
          <TextField
            fullWidth
            variant="outlined"
            label={label}
            placeholder={undefined}
            value=""
            disabled={config.ui?.disabled || upload.uploading}
            error={error}
            helperText={errorText || computedHelperText}
            slotProps={{
              input: {
                readOnly: true,
                startAdornment: fileList.hasFiles ? (
                  <InputAdornment
                    position="start"
                    sx={{
                      maxWidth: 'calc(100% - 60px)',
                      width: 'calc(100% - 60px)',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        overflow: 'hidden',
                        minHeight: 36,
                        py: 0.5,
                      }}
                    >
                      {fileList.fileCount === 1 && (
                        // Single file: Show file chip (regardless of multiple setting)
                        (<>
                          {fileList.files.map((file, index) => (
                            <FileChip
                              key={`${file.f}-${index}`}
                              file={file}
                              index={index}
                              onRemove={
                                !config.ui?.disabled && !config.ui?.readOnly
                                  ? handleRemoveFile
                                  : undefined
                              }
                              onPreview={
                                !config.ui?.disabled
                                  ? handleFilePreview
                                  : undefined
                              }
                              disabled={config.ui?.disabled}
                              privateContext={config.privateContext}
                            />
                          ))}
                        </>)
                      )}
                      {fileList.fileCount > 1 && (
                        // Multiple files: Show summary chip with count
                        (<Chip
                          label={t(
                            'fileUpload.filesUploadedCount',
                            '{{count}} file{{count, plural, one {} other {s}}} uploaded',
                            { count: fileList.fileCount }
                          )}
                          onClick={
                            !config.ui?.disabled
                              ? handleToggleFileList
                              : undefined
                          }
                          onBlur={handleTextFieldBlur}
                          size="medium"
                          variant="outlined"
                          sx={{
                            cursor: !config.ui?.disabled
                              ? 'pointer'
                              : 'default',
                            minHeight: 32,
                            height: 32,
                            fontSize: '0.875rem',
                            backgroundColor: 'background.paper',
                            border: '1px solid',
                            borderColor: 'divider',
                            '& .MuiChip-label': {
                              fontWeight: 500,
                              paddingX: 1.5,
                              fontSize: '0.875rem',
                            },
                            ...(!config.ui?.disabled && {
                              '&:hover': {
                                backgroundColor: 'action.hover',
                                borderColor: 'primary.main',
                              },
                            }),
                          }}
                        />)
                      )}
                    </Box>
                  </InputAdornment>
                ) : undefined,
                endAdornment: !config.ui?.readOnly ? (
                  <InputAdornment position="end">
                    <Box
                      sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                    >
                      {/* Info icon with helper text tooltip */}
                      {computedHelperText && (
                        <Tooltip
                          title={computedHelperText}
                          arrow
                          placement="top"
                          open={isTooltipOpen}
                          onClose={handleTooltipClose}
                          disableFocusListener
                          disableHoverListener
                          disableTouchListener
                        >
                          <IconButton
                            size="small"
                            onClick={handleTooltipToggle}
                            onMouseEnter={handleTooltipMouseEnter}
                            onMouseLeave={handleTooltipClose}
                            disabled={config.ui?.disabled}
                          >
                            <InfoOutlinedIcon
                              fontSize="small"
                              sx={{
                                cursor:
                                  config.ui?.disabled || upload.uploading
                                    ? 'not-allowed'
                                    : 'pointer',
                                color: config.ui?.disabled
                                  ? 'action.disabled'
                                  : 'action.active',
                              }}
                            />
                          </IconButton>
                        </Tooltip>
                      )}

                      {/* File input and browse icon */}
                      <FileDropzone
                        variant="compact"
                        onFilesSelected={handleFilesSelected}
                        disabled={config.ui?.disabled || upload.uploading}
                        multiple={config.multiple}
                        acceptedTypes={config.acceptedTypes}
                        maxFiles={config.maxFiles}
                        maxSize={config.maxSize}
                        uploading={upload.uploading}
                      >
                        {upload.uploading ? (
                          <CircularProgress size={20} />
                        ) : (
                          <FileUpload
                            fontSize="medium"
                            sx={{
                              cursor:
                                config.ui?.disabled || upload.uploading
                                  ? 'not-allowed'
                                  : 'pointer',
                              color: config.ui?.disabled
                                ? 'action.disabled'
                                : 'action.active',
                            }}
                          />
                        )}
                      </FileDropzone>
                    </Box>
                  </InputAdornment>
                ) : undefined,
              },
              inputLabel: {
                shrink: fileList.hasFiles ? true : undefined,
              },
            }}
            sx={{
              cursor: 'default',
              '& .MuiOutlinedInput-input': {
                cursor: 'default',
                display: fileList.hasFiles ? 'none' : 'block', // Show input when empty to display placeholder
              },
              '& .MuiOutlinedInput-root': {
                cursor: 'default',
                paddingRight: '14px',
                paddingLeft: '14px',
                // Add focus styling when dropdown is open
                ...(isFileListExpanded
                  ? {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px',
                      },
                    }
                  : {}),
              },
              '& .MuiInputAdornment-positionStart': {
                maxHeight: 'none',
                alignItems: 'stretch',
              },
              '& .MuiInputAdornment-positionEnd': {
                position: 'absolute',
                right: '9px',
                top: '50%',
                transform: 'translateY(-50%)',
                pointerEvents: 'none',
                '& > *': {
                  pointerEvents: 'auto',
                },
              },
              // Style the label when dropdown is open (like focused state)
              ...(isFileListExpanded
                ? {
                    '& .MuiInputLabel-root': {
                      color: theme.palette.primary.main,
                    },
                  }
                : {}),
            }}
          />

          {/* Validation display */}
          <ValidationDisplay
            errors={validation.errors}
            warnings={validation.warnings}
            onDismissError={validation.removeValidationError}
          />

          {/* Modals */}
          <FilePreviewModal file={previewFile} onClose={handleClosePreview} />
          <ImageEditorModal
            file={imageEditorWithCallback.currentFile}
            isOpen={imageEditorWithCallback.isOpen}
            onClose={imageEditorWithCallback.closeEditor}
            onSave={imageEditorWithCallback.saveEdits}
            onCancel={imageEditorWithCallback.cancelEdits}
            config={imageEditorWithCallback.editorConfig || {}}
            fileType={config.fileType}
            uploading={upload.uploading}
            batchInfo={imageEditorWithCallback.batchInfo}
            aspectRatioInfo={imageEditorWithCallback.aspectRatioInfo}
          />
        </Box>
        {/* Expandable file list for multiple files - Overlay dropdown style using Portal */}
        {shouldShowDropdown &&
          createPortal(
            <Box
              data-file-dropdown
              sx={{
                position: 'fixed',
                top: `${textFieldRef.current!.getBoundingClientRect().bottom - 3}px`,
                left: `${textFieldRef.current!.getBoundingClientRect().left}px`,
                width: `${textFieldRef.current!.getBoundingClientRect().width}px`,
                zIndex: 1500, // Higher than TranslatableInputs tabs
                display: 'flex',
                flexDirection: 'column',
                gap: 0.5,
                maxHeight: '300px',
                overflowY: 'auto',
                overflowX: 'hidden',
                p: 1,
                border: `1px solid ${theme.palette.divider}`,
                borderTop: 'none', // Remove top border to connect with input
                borderRadius: '4px',
                backgroundColor: theme.palette.background.paper,
                boxShadow: theme.shadows[8], // Add elevation like a dropdown
                animation: 'fadeIn 0.2s ease-in-out',
                '@keyframes fadeIn': {
                  from: {
                    opacity: 0,
                    transform: 'translateY(-10px)',
                  },
                  to: {
                    opacity: 1,
                    transform: 'translateY(0)',
                  },
                },
                // Let drag handles control their own touch behavior
              }}
            >
              <FileList
                files={fileList.files}
                onRemoveFile={handleRemoveFile}
                onRemoveFileByIdentifier={handleRemoveFileByIdentifier}
                onReorderFiles={
                  fileList.fileCount > 1 ? fileList.reorderFiles : undefined
                }
                onFilePreview={handleFilePreview}
                disabled={config.ui?.disabled}
                readOnly={config.ui?.readOnly}
                multiple={config.multiple}
                variant="compact"
                privateFileContext={config.privateContext}
                showSummary={false}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
              />
            </Box>,
            document.body
          )}
      </>
    );
  }

  // Render default variant
  return (
    <Box sx={{ width: '100%' }}>
      {/* Upload Area - Hidden in disabled and readOnly modes */}
      {!config.ui?.disabled && !config.ui?.readOnly && (
        <Box sx={{ position: 'relative' }}>
          <FileDropzone
            onFilesSelected={handleFilesSelected}
            disabled={config.ui?.disabled || upload.uploading}
            multiple={config.multiple}
            acceptedTypes={config.acceptedTypes}
            maxFiles={config.maxFiles}
            maxSize={config.maxSize}
            placeholder={config.ui?.placeholder}
            error={error}
            uploading={upload.uploading}
          />

          {/* Validation display overlays the dropzone */}
          {(validation.errors.length > 0 || validation.warnings.length > 0) && (
            <Box
              sx={{
                position: 'absolute',
                top: '2px',
                left: '2px',
                right: '2px',
                bottom: '2px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '2px',
                zIndex: 10,
              }}
            >
              <ValidationDisplay
                errors={validation.errors}
                warnings={validation.warnings}
                onDismissError={validation.removeValidationError}
              />
            </Box>
          )}
        </Box>
      )}

      {/* File List */}
      <FileList
        files={fileList.files}
        onRemoveFile={handleRemoveFile}
        onRemoveFileByIdentifier={handleRemoveFileByIdentifier}
        onReorderFiles={
          fileList.fileCount > 1 ? fileList.reorderFiles : undefined
        }
        onFilePreview={handleFilePreview}
        disabled={config.ui?.disabled}
        readOnly={config.ui?.readOnly}
        multiple={config.multiple}
        variant="default"
        privateFileContext={config.privateContext}
        fileType={config.fileType}
      />

      {/* Show "No files uploaded" message in disabled and readOnly modes */}
      {fileList.isEmpty && (config.ui?.disabled || config.ui?.readOnly) && (
        <Box sx={{ mt: 0 }}>
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: config.ui?.disabled ? 0.6 : 1,
              backgroundColor:
                config.ui?.disabled || config.ui?.readOnly
                  ? theme.palette.action.hover
                  : 'transparent',
              minHeight: 56,
            }}
          >
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{ fontStyle: 'italic' }}
            >
              {t('fileUpload.noFilesUploaded', 'No files uploaded')}
            </Typography>
          </Paper>
        </Box>
      )}

      {/* Show external error text */}
      {errorText && (
        <Typography
          variant="caption"
          color="error"
          sx={{ mt: 1, display: 'block' }}
        >
          {errorText}
        </Typography>
      )}

      {/* Modals */}
      <FilePreviewModal file={previewFile} onClose={handleClosePreview} />
      <ImageEditorModal
        file={imageEditorWithCallback.currentFile}
        isOpen={imageEditorWithCallback.isOpen}
        onClose={imageEditorWithCallback.closeEditor}
        onSave={imageEditorWithCallback.saveEdits}
        onCancel={imageEditorWithCallback.cancelEdits}
        config={imageEditorWithCallback.editorConfig || {}}
        fileType={config.fileType}
        uploading={upload.uploading}
        batchInfo={imageEditorWithCallback.batchInfo}
        aspectRatioInfo={imageEditorWithCallback.aspectRatioInfo}
      />
    </Box>
  );
};

export default FileUploadComponent;
