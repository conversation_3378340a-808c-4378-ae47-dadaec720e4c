import { useCallback, useEffect, useMemo, useState } from 'react';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import {
  Box,
  IconButton,
  InputAdornment,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  useLocaleState,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import { CustomInputContainer } from '~/components/atoms/inputs/CustomInputContainer';
import {
  createChipSelectionRenderer,
  SelectArrayInputOptionRenderer,
} from '~/components/atoms/inputs/SelectArrayInputRenderers';
import {
  ConflictAwareInput,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import ModalHeader from '~/components/molecules/ModalHeader';
import { PublicNameAndDescriptionSection } from '~/components/molecules/PublicNameAndDescriptionSection';
import { FilePreviewInline } from '~/components/organisms/FileUpload';
import {
  createStandardImageConfig,
  resolveImageEditorConfig,
} from '~/components/organisms/FileUpload/utils/standardImageEditor';
import NewFeatureTag from '~/components/organisms/menu-catalog-dnd/add-item-modal/NewFeatureTag';
import {
  useGetListHospitalityCatalogsLive,
  useGetListHospitalityCategoriesLive,
  useGetListMeasureUnits,
  useGetListVatsLive,
} from '~/providers/resources';
import { getCurrencySymbolByCode } from '~/providers/utils/getCurrencySymbol';
import getFullscreenModalProps from '~/utils/getFullscreenModalProps';
import NutritionalValuesContent from '../../components/organisms/menu-catalog-dnd/add-item-modal/NutritionalValuesContent';
import { NutritionalValues } from '../../components/organisms/menu-catalog-dnd/add-item-modal/NutritionalValuesModal';
import { MenuItem as MenuItemI } from '../../components/organisms/menu-catalog-dnd/types';

interface EditItemModalProps {
  path?: string;
  initialValues?: MenuItemI;
}

const PriceVariationsSection = (props: any) => {
  const { t } = useTranslation('');
  const { catalogSpecific, getCatalogName, vatsChoices } = props;

  // TODO! something about the flicker ... this whole part doesn't render because temporarily there are no vats
  if (vatsChoices.length === 0) return null;

  // Sort entries by catalog name
  const sortedEntries = Object.entries(catalogSpecific).sort(([idA], [idB]) => {
    const nameA = getCatalogName(idA) || '';
    const nameB = getCatalogName(idB) || '';
    return nameA.localeCompare(nameB);
  });

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="h5">{t('itemLibrary.variations')}</Typography>
      {sortedEntries.map(
        ([catalogId, catalogSpecificValues]: [string, any]) => {
          const catalogName = getCatalogName(catalogId);
          if (!catalogName) return null;
          return (
            <CustomInputContainer
              key={catalogId}
              title={catalogName}
              roundedCorners="top"
            >
              <ConflictAwareInput>
                <CustomInput
                  ui="custom"
                  type="number"
                  defaultValue={catalogSpecificValues.price}
                  source={`catalogSpecific.${catalogId}.price`}
                  label={t('shared.price')}
                  variant="outlined"
                  format={value => value / 10000}
                  parse={value => Math.floor(value * 10000)}
                  validate={[required()]}
                  slotProps={{
                    input: {
                      endAdornment: getCurrencySymbolByCode('RON'),
                    },
                  }}
                  roundedCorners="none"
                />
              </ConflictAwareInput>
              <ConflictAwareInput>
                <CustomInput
                  ui="custom"
                  type="select"
                  source={`catalogSpecific.${catalogId}.vat`}
                  label={t('shared.tva')}
                  choices={vatsChoices}
                  translateChoice={false}
                  validate={required()}
                  roundedCorners="bottom"
                />
              </ConflictAwareInput>
            </CustomInputContainer>
          );
        }
      )}
    </Box>
  );
};

// Component to properly handle image URL display for uploaded files
const ItemImageDisplay = ({ images }: { images: any[] }) => {
  const theme = useTheme();

  // Memoize the first image to ensure we get the correct one when the array changes
  const firstImage = useMemo(() => {
    return images && images.length > 0 ? images[0] : null;
  }, [images]);

  // Create a stable key to force re-render when the image changes to avoid stale blob URLs
  const imageKey = useMemo(() => {
    if (!firstImage) return 'no-image';
    // Create a key that includes both filename and last modified info to handle cache issues
    // Remove Date.now() to prevent unnecessary re-renders that cause layout shifts
    return `${firstImage.f}-${firstImage.e}-${firstImage.x ? 'temp' : 'perm'}`;
  }, [firstImage]);

  // Don't clear cache during image editing - let the system handle this naturally
  // The cache clearing was causing blob URLs to become invalid while still being used

  // Show the first image or placeholder
  return (
    <FilePreviewInline
      key={imageKey} // Force re-render with fresh blob URLs when image changes
      files={firstImage ? [firstImage] : []} // Pass only the first image or empty array for placeholder
      width={140}
      height={140}
      borderRadius={1.5}
      imageVariant="square_s"
      fileType="images"
    />
  );
};

const ItemLibraryEditInner = () => {
  const [locale] = useLocaleState();
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');
  const { setValue, getValues, watch } = useFormContext();

  // Watch form values with explicit dependencies to ensure proper updates
  const catalogSpecific = watch('catalogSpecific') || {};
  const age = watch('age');
  const dietaryPreferences = watch('dietaryPreferences') || [];
  const allergens = watch('allergens') || [];
  const nutritionalValues = watch('nutritionalValues');
  const images = watch('images') || [];
  const sku = watch('sku');
  const gtin = watch('gtin');
  const ean = watch('ean');

  // Track images for debugging but don't use these values for key generation
  // The ItemImageDisplay component now handles its own re-rendering logic
  const imagesLength = images.length;
  const firstImageId = images[0] ? `${images[0].f}-${images[0].e}` : null;

  const { data: catalogs } = useGetListHospitalityCatalogsLive();
  const getCatalogName = useCallback(
    (catalogId: string) => {
      const catalog = catalogs?.find((c: any) => c.id === catalogId);
      if (catalog) {
        return catalog.name;
      } else {
        return undefined;
      }
    },
    [catalogs]
  );

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      name: `${vat.value}%`,
    }));
  }, [vats]);

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  const updateValue = (field: string, value: unknown) => {
    setValue(field, value, { shouldDirty: true });
  };

  const generateUnixTimestamp = (field: string) => {
    const timestamp = Math.floor(Date.now() / 1000);
    updateValue(field, timestamp.toString());
  };

  const dietaryPreferencesOptions = [
    'dairy-free',
    'gluten-free',
    'halal',
    'kosher',
    'nut-free',
    'vegan',
    'vegetarian',
    'low-sugar',
    'low-carb',
    'low-sodium',
  ];

  const allergensOptions = [
    'celery',
    'crustaceans',
    'eggs',
    'fish',
    'gluten',
    'lupin',
    'milk',
    'molluscs',
    'mustard',
    'peanuts',
    'sesame',
    'soy',
    'sulphites',
    'tree-nuts',
  ];

  const handleMultiSelectChange = (
    event: any,
    name: 'dietaryPreferences' | 'allergens'
  ) => {
    const {
      target: { value },
    } = event;
    updateValue(name, typeof value === 'string' ? value.split(',') : value);
  };

  const { data: categories } = useGetListHospitalityCategoriesLive({
    filter: { _d: false },
  });

  const { data: measureUnits } = useGetListMeasureUnits();
  const MeasureUnitsOptionRenderer = () => {
    const record = useRecordContext();
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}
      >
        <span>
          {record!.name[locale]}{' '}
          {record!.symbol[locale] ? `(${record!.symbol[locale]})` : ''}
        </span>
        <span>{t(`measureUnits.types.${record!.type}`)}</span>
      </Box>
    );
  };

  const measureUnitsOptionText = <MeasureUnitsOptionRenderer />;

  interface MeasureUnitChoice {
    name: Record<string, string>;
    symbol: Record<string, string>;
    type: string;
  }

  const measureUnitsInputText = (choice: MeasureUnitChoice): string =>
    `${choice.name[locale]}${choice.symbol[locale] ? ` (${choice.symbol[locale]})` : ''}`;

  const measureUnitsMatchSuggestion = (
    filter: string,
    choice: MeasureUnitChoice
  ): boolean => {
    return `${choice.name[locale]}${choice.symbol[locale] ? ` (${choice.symbol[locale]})` : ''}`
      .toLocaleLowerCase()
      .includes(filter.toLowerCase());
  };

  // Age restriction choices
  const ageRestrictionChoices = useMemo(
    () => [
      { id: 0, name: t('menu.noRestriction') },
      { id: 16, name: t('menu.ageRestriction16', '16+') },
      { id: 18, name: t('menu.ageRestriction18') },
      { id: 21, name: t('menu.ageRestriction21', '21+') },
    ],
    [t]
  );

  // Memoize choices for dietary preferences
  const dietaryPreferencesChoices = useMemo(
    () =>
      dietaryPreferencesOptions.map(key => ({
        id: key,
        name: t(`add-item.dietary-preferences.${key}.title`),
        description: t(`add-item.dietary-preferences.${key}.description`),
      })),
    [t]
  );

  // Memoize choices for allergens
  const allergensChoices = useMemo(
    () =>
      allergensOptions.map(key => ({
        id: key,
        name: t(`add-item.allergens.${key}.title`),
        description: t(`add-item.allergens.${key}.description`),
      })),
    [t]
  );

  // Selection renderers - chips with simple translation function
  const renderDietaryPreferencesSelection = useMemo(
    () =>
      createChipSelectionRenderer(id =>
        t(`add-item.dietary-preferences.${id}.title`)
      ),
    [t]
  );

  const renderAllergensSelection = useMemo(
    () =>
      createChipSelectionRenderer(id => t(`add-item.allergens.${id}.title`)),
    [t]
  );

  return (
    <>
      <ModalHeader handleClose={handleClose} title={t('itemsLibrary.editItem')}>
        <SaveButton type="button" label={t('shared.save')} icon={<></>} />
      </ModalHeader>
      <Box
        sx={{
          padding: 3,
          width: '100%',
          maxWidth: '800px',
          margin: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column-reverse', md: 'row' },
            gap: 2,
            alignItems: 'flex-start',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ width: '100%' }}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5">{t('menu.details')}</Typography>
            </Box>
            <Box>
              <ConflictAwareInput>
                <CustomInput
                  type="text"
                  ui="custom"
                  source="name"
                  label={t('shared.name')}
                  sanitize="singleLine"
                  validate={[required()]}
                  roundedCorners="top"
                />
              </ConflictAwareInput>
            </Box>
            <Box>
              <ConflictAwareInput>
                <CustomInput
                  type="autocomplete"
                  ui="custom"
                  defaultValue=""
                  source="groupId"
                  label={t('shared.category_capitalize')}
                  choices={categories}
                  isRequired
                />
              </ConflictAwareInput>
            </Box>
            <Box>
              <ConflictAwareInput>
                <CustomInput
                  type="autocomplete"
                  ui="custom"
                  defaultValue=""
                  source="measureUnit"
                  label={t('measure-units.title')}
                  choices={measureUnits}
                  optionText={measureUnitsOptionText}
                  inputText={measureUnitsInputText}
                  matchSuggestion={measureUnitsMatchSuggestion}
                  isRequired
                  roundedCorners="bottom"
                />
              </ConflictAwareInput>
            </Box>
          </Box>
          <Box
            sx={{
              flexShrink: 0,
              mt: { xs: 0, md: '36px' },
              width: { xs: '100%', md: 'auto' },
              display: 'flex',
              justifyContent: { xs: 'center', md: 'flex-start' },
            }}
          >
            <ItemImageDisplay images={images} />
          </Box>
        </Box>

        <CustomDivider />
        <PriceVariationsSection
          catalogSpecific={catalogSpecific}
          getCatalogName={getCatalogName}
          vatsChoices={vatsChoices}
        />
        <CustomDivider />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">
            {t('itemLibrary.identifications')}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: { xs: 1, sm: 2 },
            }}
          >
            <ConflictAwareInput>
              <CustomInput
                type="number"
                ui="custom"
                sanitize="identifier"
                source="sku"
                label="SKU"
                slotProps={{
                  input: {
                    endAdornment: !sku ? (
                      <InputAdornment position="end" sx={{ marginRight: 0 }}>
                        <Tooltip title={t('shared.generate') || 'Generate'}>
                          <IconButton
                            onClick={() => generateUnixTimestamp('sku')}
                            size="small"
                            sx={{ marginRight: '-12px' }}
                          >
                            <AutorenewIcon />
                          </IconButton>
                        </Tooltip>
                      </InputAdornment>
                    ) : null,
                  },
                }}
                forceMobileUi
                roundedCorners="both"
              />
            </ConflictAwareInput>
            <ConflictAwareInput>
              <CustomInput
                type="number"
                ui="custom"
                sanitize="identifier"
                source="gtin"
                label="GTIN"
                slotProps={{
                  input: {
                    endAdornment: !gtin ? (
                      <InputAdornment position="end" sx={{ marginRight: 0 }}>
                        <Tooltip title={t('shared.generate') || 'Generate'}>
                          <IconButton
                            onClick={() => generateUnixTimestamp('gtin')}
                            size="small"
                            sx={{ marginRight: '-12px' }}
                          >
                            <AutorenewIcon />
                          </IconButton>
                        </Tooltip>
                      </InputAdornment>
                    ) : null,
                  },
                }}
                forceMobileUi
                roundedCorners="both"
              />
            </ConflictAwareInput>
            <ConflictAwareInput>
              <CustomInput
                type="number"
                ui="custom"
                sanitize="identifier"
                source="ean"
                label="EAN"
                slotProps={{
                  input: {
                    endAdornment: !ean ? (
                      <InputAdornment position="end" sx={{ marginRight: 0 }}>
                        <Tooltip title={t('shared.generate') || 'Generate'}>
                          <IconButton
                            onClick={() => generateUnixTimestamp('ean')}
                            size="small"
                            sx={{ marginRight: '-12px' }}
                          >
                            <AutorenewIcon />
                          </IconButton>
                        </Tooltip>
                      </InputAdornment>
                    ) : null,
                  },
                }}
                forceMobileUi
                roundedCorners="both"
              />
            </ConflictAwareInput>
          </Box>
        </Box>
        <CustomDivider />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">{t('menu.media')}</Typography>
          <ConflictAwareInput>
            <CustomInput
              type="fileUpload"
              ui="original"
              source="images"
              label={t('menu.media')}
              config={{
                fileType: 'images',
                multiple: false,
                maxFiles: 1,
                maxSize: 20 * 1024 * 1024, // 20MB
                acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
                imageConfig: (() => {
                  const resolved = resolveImageEditorConfig(
                    createStandardImageConfig.squares()
                  );
                  return {
                    targetSizes: resolved.targetSizes,
                    autoGenerateThumbnail: true,
                    quality: 0.85,
                    editorConfig: resolved,
                  };
                })(),
                ui: {
                  disabled: false,
                  readOnly: false,
                },
              }}
            />
          </ConflictAwareInput>
        </Box>
        <CustomDivider />

        <Box>
          <Typography variant="h5">{t('menu.ordering')}</Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: {
                xs: 'flex-start',
                md: 'center',
              },
              justifyContent: 'space-between',
              mt: 3,
            }}
          >
            <ConflictAwareInput>
              <CustomInput
                type="modernBoolean"
                ui="custom"
                source="excludedFromDiscount"
                label={t('menu.excludedFromDiscount')}
                description={t('menu.excludedFromDiscountTooltip')}
                badge={<NewFeatureTag />}
              />
            </ConflictAwareInput>
          </Box>
        </Box>

        <Box>
          <CustomDivider />
        </Box>

        {/* Restrictions Section */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">Restrictions</Typography>

          {/* Age Restriction */}
          <ConflictAwareInput>
            <CustomInput
              type="select"
              ui="custom"
              source="age"
              label={t('menu.ageRestriction')}
              choices={ageRestrictionChoices}
              defaultValue={0}
              helperText={t('menu.ageRestrictionTooltip')}
              roundedCorners="both"
            />
          </ConflictAwareInput>
        </Box>

        <CustomDivider />

        <PublicNameAndDescriptionSection
          catalogSpecific={catalogSpecific}
          getCatalogName={getCatalogName}
        />

        <Box>
          <CustomDivider />
        </Box>

        <Box>
          <Typography variant="h5">
            {t('menu.nutritionalInformation')}
          </Typography>
        </Box>

        <Box>
          <ConflictAwareInput>
            <CustomInput
              type="number"
              ui="custom"
              sanitize="singleLine"
              source="calories"
              fullWidth
              label={t('menu.calorieCount')}
              roundedCorners="top"
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              type="selectArray"
              ui="custom"
              source="dietaryPreferences"
              fullWidth
              label={t('menu.dietaryPreferences')}
              roundedCorners="none"
              choices={dietaryPreferencesChoices}
              optionText={<SelectArrayInputOptionRenderer />}
              options={{
                renderValue: renderDietaryPreferencesSelection,
              }}
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              type="selectArray"
              ui="custom"
              source="allergens"
              fullWidth
              label={t('menu.allergens')}
              roundedCorners="none"
              choices={allergensChoices}
              optionText={<SelectArrayInputOptionRenderer />}
              options={{
                renderValue: renderAllergensSelection,
              }}
            />
          </ConflictAwareInput>

          {/* Nutritional Values - New Popover Version */}
          <ConflictAwareInput>
            <CustomInput
              type="popover"
              ui="custom"
              source="nutritionalValues"
              label={t('menu.nutritionalValues')}
              roundedCorners="bottom"
              renderContent={(
                value: NutritionalValues | undefined,
                onChange: (value: NutritionalValues) => void,
                onClose: () => void
              ) => (
                <NutritionalValuesContent
                  value={value}
                  onChange={onChange}
                  onClose={onClose}
                />
              )}
              renderSummary={(value: NutritionalValues | undefined) =>
                value ? '✓ ' + t('shared.set') : undefined
              }
              placeholder={t('shared.clickToSet', 'Click to set')}
              popoverWidth={500}
            />
          </ConflictAwareInput>
        </Box>

        <Box>
          <CustomDivider />
        </Box>
      </Box>
    </>
  );
};

export const ItemLibraryEdit = ({ initialValues }: EditItemModalProps) => {
  return (
    <EditDialog {...getFullscreenModalProps()} mutationMode="pessimistic">
      <ConflictAwareSimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        translationNamespace="itemLibrary"
        conflictConfig={{
          excludeFields: [
            '_u',
            '_c',
            'lastUpdatedAt',
            'accountId',
            'businessType',
          ],
        }}
      >
        <ItemLibraryEditInner />
      </ConflictAwareSimpleForm>
    </EditDialog>
  );
};
