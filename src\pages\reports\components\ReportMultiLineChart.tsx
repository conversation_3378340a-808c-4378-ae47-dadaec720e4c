import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { useGetList } from 'react-admin';
import { Line } from 'react-chartjs-2';

import { useTheme } from '~/contexts';

const sumReducer = (accumulator: number, currentValue: number) =>
  accumulator + currentValue;

const formatToAMPM = (hour: string | number) => {
  const h = Number(hour);
  const suffix = h >= 12 ? 'PM' : 'AM';
  const formattedHour = h === 0 ? 12 : h > 12 ? h - 12 : h;
  return `${formattedHour} ${suffix}`;
};

export default function ReportMultiLineChart({
  datasets,
  labels,
  formatData = el => el,
}: {
  datasets: any;
  labels: string[];
  formatData?: (el: string | number) => string | number;
}) {
  const chartRef = useRef(null);
  const { getChartColours } = useTheme();
  const colors = getChartColours(datasets.length, 3);

  const data: any = useMemo(
    () => ({
      labels,
      datasets: datasets.map((dataset: any, index: number) => ({
        label: dataset.label,
        data: dataset.data,
        borderColor: colors[index],
        backgroundColor: colors[index] + '33',
        borderWidth: 2,
        pointRadius: 3,
      })),
    }),
    [datasets, labels]
  );
  const sumDataRef = useRef<any[]>([]);

  const sumData = useMemo(() => {
    const data = datasets.map((dataset: any) => ({
      datasetLabel: dataset.label,
      value: dataset.data.reduce(sumReducer, 0),
    }));
    sumDataRef.current = data;
    return data;
  }, [datasets]);

  useEffect(() => {
    setHoverData(sumDataRef.current);
  }, [sumData]);

  const [hoverData, setHoverData] = useState(sumData);

  const verticalLinePlugin = {
    id: 'verticalLineCursor',
    afterDraw: (chart: any) => {
      if (chart.tooltip?._active?.length) {
        const x = chart.tooltip._active[0].element.x;
        const yAxis = chart.scales.y;
        const ctx = chart.ctx;

        ctx.save();
        ctx.beginPath();
        ctx.moveTo(x, yAxis.top);
        ctx.lineTo(x, yAxis.bottom);
        ctx.lineWidth = 1;
        ctx.strokeStyle = 'rgb(0, 0, 0)';
        ctx.stroke();
        ctx.restore();

        const tooltipData = chart.tooltip.dataPoints.map((point: any) => ({
          datasetLabel: point.dataset.label,
          value: point.raw,
        }));
        setHoverData(tooltipData);
      } else {
        setHoverData(sumDataRef.current);
      }
    },
  };

  const options: any = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'transparent',
        bodyColor: 'transparent',
        titleColor: 'transparent',
        displayColors: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#9CA3AF',
          callback: (_value: any, index: number) => formatToAMPM(labels[index]),
        },
      },
      y: {
        grid: {
          color: 'rgba(203, 213, 225, 0.3)',
        },
        ticks: {
          color: '#9CA3AF',
          beginAtZero: true,
        },
      },
    },
  };

  return (
    <Box
      sx={{
        display: { xs: 'none', md: 'block' },
        '@media print': {
          display: 'block !important',
          backgroundColor: '#FFFFFF !important',
          color: 'black !important',
        },
      }}
    >
      <Box mb={2} sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {hoverData.map((dataPoint: any, index: number) => (
          <Box
            key={`${dataPoint.datasetLabel}-${index}`}
            display="flex"
            alignItems="center"
            sx={{ mr: 2 }}
          >
            <Box
              sx={{
                width: '14px',
                height: '14px',
                backgroundColor: data.datasets[index]?.borderColor || 'gray',
                borderRadius: '50%',
              }}
            />
            <Typography
              sx={{
                color: data.datasets[index]?.borderColor || 'gray',
                // textShadow: '.5px .5px  rgba(0, 0, 0, .7)',
                fontSize: '14px',
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
              variant="caption"
              ml={1}
            >
              {dataPoint.datasetLabel}
            </Typography>
            <Typography
              sx={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
              variant="caption"
              ml={0.5}
              width="100px"
            >
              – {formatData(dataPoint.value)}
            </Typography>
          </Box>
        ))}
      </Box>

      <Box
        sx={{
          width: '100%',
          height: '200px',
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
      >
        <Line
          ref={chartRef}
          data={data}
          options={options}
          plugins={[verticalLinePlugin]}
        />
      </Box>
    </Box>
  );
}
