import { useGetListLive } from '@react-admin/ra-realtime';
import { AddSearchMethodResourceOptions } from '@react-admin/ra-search';
import { GetListParams, RaRecord, useGetList } from 'react-admin';

export type ResourceInfoCommon = {
  businessType?: 'h' | 'r'; // hospitality, retail
  dataProvider: 'local' | 'firestore' | 'hybrid' | 'storage';
  field?: string;
  hasLiveListener: boolean;
  hasGlobalListener: boolean;
  hasSoftDelete: boolean;
  isSearchable: boolean;
  needsMetaFields?: string[];
  pathTemplate: string;
  searchOptions?: AddSearchMethodResourceOptions;
  type: 'collection' | 'embeddedMap' | 'embeddedArray' | 'list';
  defaultSort: { field: string; order: 'ASC' | 'DESC' };
  defaultFilter?: Record<string, any>;
  version: number;
  uploadedFileFields?: string[]; // File field patterns to process for lifecycle callbacks
  useVersioning: boolean; // Enable version-based sync with sharded counter
};

export type ResourceInfoCommonCollectionWithLiveListener =
  ResourceInfoCommon & {
    dataProvider: 'hybrid';
    type: 'collection';
    hasLiveListener: true;
    hasSoftDelete: true;
  };

export type ResourceInfoCommonCollectionWithoutLiveListener =
  ResourceInfoCommon & {
    dataProvider: 'firestore';
    type: 'collection';
    hasLiveListener: false;
    hasGlobalListener: false;
    hasSoftDelete?: boolean; // Soft delete can be optional if no live listener
  };

export type ResourceInfoCommonNonCollectionWithListener = ResourceInfoCommon & {
  dataProvider: 'hybrid';
  type: 'embeddedMap' | 'embeddedArray';
  hasLiveListener: true; // Optional for non-collection types
  hasSoftDelete?: boolean; // Optional for non-collection types
};

export type ResourceInfoCommonNonCollectionWithoutListener =
  ResourceInfoCommon & {
    dataProvider: 'firestore';
    type: 'embeddedMap' | 'embeddedArray';
    hasLiveListener: false;
    hasGlobalListener: false;
    hasSoftDelete?: boolean; // Optional for non-collection types
  };

export type ResourceInfoCollection = (
  | ResourceInfoCommonCollectionWithoutLiveListener
  | ResourceInfoCommonCollectionWithLiveListener
) & {
  businessType?: 'h' | 'r'; // hospitality, retail
  field?: undefined;
  type: 'collection';
};

export type ResourceInfoEmbeddedMap = (
  | ResourceInfoCommonNonCollectionWithListener
  | ResourceInfoCommonNonCollectionWithoutListener
) & {
  field: string;
  type: 'embeddedMap';
};

export type ResourceInfoEmbeddedArray = (
  | ResourceInfoCommonNonCollectionWithListener
  | ResourceInfoCommonNonCollectionWithoutListener
) & {
  businessType?: undefined;
  field: string;
  hasSoftDelete: false;
  isSearchable: false;
  searchOptions?: undefined;
  type: 'embeddedArray';
};

export type ResourceInfoLocalList = ResourceInfoCommon & {
  dataProvider: 'local';
  type: 'list';
  hasLiveListener: false;
  hasGlobalListener: false;
  hasSoftDelete: false;
  isSearchable: false;
};

export type ResourceInfoStorageList = ResourceInfoCommon & {
  dataProvider: 'storage';
  type: 'list';
  hasLiveListener: false;
  hasGlobalListener: false;
  hasSoftDelete: false;
  isSearchable: false;
};

export type ResourcesInfo = {
  accounts: ResourceInfoCollection;
  customers: ResourceInfoCollection;
  discounts: ResourceInfoEmbeddedMap;
  devices: ResourceInfoEmbeddedMap;
  'extra-charges': ResourceInfoEmbeddedMap;
  'floor-plans': ResourceInfoEmbeddedArray;
  'gift-cards': ResourceInfoCollection;
  'hospitality-catalogs': ResourceInfoCollection;
  'hospitality-categories': ResourceInfoEmbeddedMap;
  'hospitality-items': ResourceInfoCollection;
  'hospitality-modifiers': ResourceInfoCollection;
  'hospitality-modifier-sets': ResourceInfoEmbeddedMap;
  locations: ResourceInfoCollection;
  'measure-units': ResourceInfoLocalList;
  'my-integrations': ResourceInfoEmbeddedMap;
  'payment-types': ResourceInfoEmbeddedMap;
  'prep-stations': ResourceInfoEmbeddedMap;
  permissions: ResourceInfoEmbeddedMap;
  'team-members': ResourceInfoEmbeddedMap;
  'team-members-working-area': ResourceInfoStorageList;
  tips: ResourceInfoEmbeddedArray;
  vats: ResourceInfoEmbeddedArray;
};

// TODO! define type to use only the keys of ResourcesInfo
export const RESOURCES = {
  ACCOUNTS: 'accounts',
  CUSTOMERS: 'customers',
  DISCOUNTS: 'discounts',
  DEVICES: 'devices',
  EXTRA_CHARGES: 'extra-charges',
  FLOOR_PLANS: 'floor-plans',
  GIFT_CARDS: 'gift-cards',
  HOSPITALITY_CATALOGS: 'hospitality-catalogs',
  HOSPITALITY_CATEGORIES: 'hospitality-categories',
  HOSPITALITY_ITEMS: 'hospitality-items',
  HOSPITALITY_MODIFIERS: 'hospitality-modifiers',
  HOSPITALITY_MODIFIER_SETS: 'hospitality-modifier-sets',
  LOCATIONS: 'locations',
  MEASURE_UNITS: 'measure-units',
  MY_INTEGRATIONS: 'my-integrations',
  PAYMENT_TYPES: 'payment-types',
  PREP_STATIONS: 'prep-stations',
  PERMISSIONS: 'permissions',
  TEAM_MEMBERS: 'team-members',
  TEAM_MEMBERS_WORKING_AREA: 'team-members-working-area',
  TIPS: 'tips',
  VATS: 'vats',
} as const;

export const resourcesInfo: ResourcesInfo = {
  accounts: {
    dataProvider: 'hybrid',
    type: 'collection',
    pathTemplate: 'accounts', // No {accountId} needed here
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: true,
    isSearchable: false,
    defaultSort: { field: 'name', order: 'ASC' },
    defaultFilter: { _d: false },
    version: 11,
    useVersioning: true,
  },
  customers: {
    dataProvider: 'firestore',
    type: 'collection',
    pathTemplate: 'accounts/{accountId}/customers',
    hasLiveListener: false,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    defaultSort: { field: '_c', order: 'DESC' },
    version: 11,
    useVersioning: false,
  },
  discounts: {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/sellPoints/{sellPointId}',
    field: 'discounts',
    hasLiveListener: true,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    needsMetaFields: ['sellPointId'],
    defaultSort: { field: 'id', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  devices: {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/devices/default',
    field: 'devices',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: false,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: '_c', order: 'DESC' },
    version: 11,
    useVersioning: false,
  },
  'extra-charges': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/sellPoints/{sellPointId}',
    field: 'extraCharges',
    hasLiveListener: true,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    needsMetaFields: ['sellPointId'],
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'floor-plans': {
    dataProvider: 'hybrid',
    type: 'embeddedArray',
    pathTemplate:
      'accounts/{accountId}/sellPoints/{sellPointId}/sections/default',
    field: 'sections',
    hasLiveListener: true,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    needsMetaFields: ['sellPointId'],
    defaultSort: { field: '_index', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'gift-cards': {
    dataProvider: 'firestore',
    type: 'collection',
    pathTemplate: 'accounts/{accountId}/giftCards',
    hasLiveListener: false,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    defaultSort: { field: '_c', order: 'DESC' },
    version: 11,
    useVersioning: false,
  },
  'hospitality-catalogs': {
    dataProvider: 'hybrid',
    type: 'collection',
    pathTemplate: 'accounts/{accountId}/catalogs/hospitality/catalogs',
    businessType: 'h',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: true,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'edit',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    defaultFilter: { _d: false },
    version: 11,
    uploadedFileFields: ['*.images'], // Find any object with images field (recursive)
    useVersioning: true,
  },
  'hospitality-categories': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/groups/hospitality',
    field: 'groups',
    hasLiveListener: true,
    hasGlobalListener: true,
    businessType: 'h',
    hasSoftDelete: true,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'hospitality-items': {
    dataProvider: 'hybrid',
    type: 'collection',
    pathTemplate: 'accounts/{accountId}/items/hospitality/items',
    businessType: 'h',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: true,
    isSearchable: false,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    uploadedFileFields: ['images'], // Simple direct field
    useVersioning: true,
  },
  'hospitality-modifiers': {
    dataProvider: 'hybrid',
    type: 'collection',
    pathTemplate: 'accounts/{accountId}/modifiers/hospitality/modifiers',
    businessType: 'h',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: true,
    isSearchable: false,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: true,
  },
  'hospitality-modifier-sets': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/modifierSets/hospitality',
    field: 'modifierSets',
    hasLiveListener: true,
    hasGlobalListener: true,
    businessType: 'h',
    hasSoftDelete: false,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  locations: {
    dataProvider: 'hybrid',
    type: 'collection',
    pathTemplate: 'accounts/{accountId}/sellPoints',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: true,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: true,
  },
  'measure-units': {
    dataProvider: 'local',
    type: 'list',
    pathTemplate: '',
    hasLiveListener: false,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    defaultSort: { field: 'order', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'my-integrations': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}',
    field: 'integrations',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: false,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    uploadedFileFields: ['sellPoints.*.logo', 'sellPoints.*.intro'], // Nested patterns with wildcards
    useVersioning: false,
  },
  'payment-types': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/sellPoints/{sellPointId}',
    field: 'paymentTypes',
    hasLiveListener: true,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    needsMetaFields: ['sellPointId'],
    defaultSort: { field: 'order', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'prep-stations': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}',
    field: 'prepStations',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: false,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'show',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  permissions: {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}',
    field: 'roles',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: false,
    isSearchable: true,
    searchOptions: {
      label: 'name',
      redirect: 'edit',
    },
    defaultSort: { field: 'name', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'team-members': {
    dataProvider: 'hybrid',
    type: 'embeddedMap',
    pathTemplate: 'accounts/{accountId}/members/default',
    field: 'members',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: false,
    isSearchable: true,
    searchOptions: {
      label: 'displayName',
      redirect: 'show',
    },
    defaultSort: { field: 'displayName', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  'team-members-working-area': {
    dataProvider: 'storage',
    type: 'list',
    pathTemplate: `accounts/{accountId}/sellPoints/{sellPointId}/${RESOURCES.TEAM_MEMBERS_WORKING_AREA}.json`,
    hasLiveListener: false,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    needsMetaFields: ['sellPointId'],
    defaultSort: { field: 'id', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  tips: {
    dataProvider: 'hybrid',
    type: 'embeddedArray',
    pathTemplate: 'accounts/{accountId}/sellPoints/{sellPointId}',
    field: 'tips',
    hasLiveListener: true,
    hasGlobalListener: false,
    hasSoftDelete: false,
    isSearchable: false,
    needsMetaFields: ['sellPointId'],
    defaultSort: { field: 'value', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
  vats: {
    dataProvider: 'hybrid',
    type: 'embeddedArray',
    pathTemplate: 'accounts/{accountId}',
    field: 'taxes.vat',
    hasLiveListener: true,
    hasGlobalListener: true,
    hasSoftDelete: false,
    isSearchable: false,
    defaultSort: { field: 'value', order: 'ASC' },
    version: 11,
    useVersioning: false,
  },
};

export const getDefaultResourceInfo = (
  resource: string
): ResourceInfoCollection => ({
  dataProvider: 'firestore',
  type: 'collection',
  pathTemplate: `accounts/{accountId}/${resource}`,
  hasLiveListener: false,
  hasGlobalListener: false,
  hasSoftDelete: false,
  isSearchable: false,
  defaultSort: { field: '_c', order: 'DESC' },
  version: 11,
  useVersioning: false,
});

export function createResourceHookLive<T extends RaRecord = any>(
  resource: keyof ResourcesInfo
) {
  return function useResourceLive(params?: Partial<GetListParams>) {
    const resourceInfo = resourcesInfo[resource];

    const defaultParams: GetListParams = {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
      sort: resourceInfo.defaultSort,
      filter: resourceInfo.defaultFilter || {},
    };

    // Merge default params with custom params
    const mergedParams: GetListParams = {
      ...defaultParams,
      ...params,
      filter: {
        ...defaultParams.filter,
        ...(params?.filter || {}),
      },
    };

    return useGetListLive<T>(resource, mergedParams);
  };
}
export function createResourceHook<T extends RaRecord = any>(
  resource: keyof ResourcesInfo
) {
  return function useResource(params?: Partial<GetListParams>) {
    const resourceInfo = resourcesInfo[resource];

    const defaultParams: GetListParams = {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
      sort: resourceInfo.defaultSort,
      filter: resourceInfo.defaultFilter || {},
    };

    // Merge default params with custom params
    const mergedParams: GetListParams = {
      ...defaultParams,
      ...params,
      filter: {
        ...defaultParams.filter,
        ...(params?.filter || {}),
      },
    };

    return useGetList<T>(resource, mergedParams);
  };
}

export const useGetListAccountsLive = createResourceHookLive(
  RESOURCES.ACCOUNTS
);
export const useGetListDiscountsLive = createResourceHookLive(
  RESOURCES.DISCOUNTS
);
export const useGetListDevicesLive = createResourceHookLive(RESOURCES.DEVICES);
export const useGetListExtraChargesLive = createResourceHookLive(
  RESOURCES.EXTRA_CHARGES
);
export const useGetListFloorPlansLive = createResourceHookLive(
  RESOURCES.FLOOR_PLANS
);
export const useGetListFloorPlans = createResourceHook(RESOURCES.FLOOR_PLANS);
export const useGetListHospitalityCatalogsLive = createResourceHookLive(
  RESOURCES.HOSPITALITY_CATALOGS
);
export const useGetListHospitalityCategoriesLive = createResourceHookLive(
  RESOURCES.HOSPITALITY_CATEGORIES
);
export const useGetListHospitalityItemsLive = createResourceHookLive(
  RESOURCES.HOSPITALITY_ITEMS
);
export const useGetListHospitalityModifierSetsLive = createResourceHookLive(
  RESOURCES.HOSPITALITY_MODIFIER_SETS
);
export const useGetListLocationsLive = createResourceHookLive(
  RESOURCES.LOCATIONS
);
export const useGetListMeasureUnits = createResourceHook(
  RESOURCES.MEASURE_UNITS
);
export const useGetListMyIntegrationsLive = createResourceHookLive(
  RESOURCES.MY_INTEGRATIONS
);
export const useGetListPermissionsLive = createResourceHookLive(
  RESOURCES.PERMISSIONS
);
export const useGetListPrepStationsLive = createResourceHookLive(
  RESOURCES.PREP_STATIONS
);
export const useGetListPaymentTypesLive = createResourceHookLive(
  RESOURCES.PAYMENT_TYPES
);
export const useGetListTeamMembersLive = createResourceHookLive(
  RESOURCES.TEAM_MEMBERS
);
export const useGetListTeamMembers = createResourceHook(RESOURCES.TEAM_MEMBERS);
export const useGetListTipsLive = createResourceHookLive(RESOURCES.TIPS);
export const useGetListVatsLive = createResourceHookLive(RESOURCES.VATS);
