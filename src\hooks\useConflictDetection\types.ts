/**
 * Resource-Agnostic Conflict Detection Types
 *
 * These types support field-level conflict detection for any nested data structure.
 * The system identifies entities by the presence of an `id` field.
 */

/**
 * Configuration options for the conflict detection system
 */
export interface ConflictDetectionConfig {
  /** Which field identifies entities (default: 'id') */
  idField?: string;
  /**
   * Path-specific ID fields for arrays where entities use a different identifier.
   * Maps array path to the field name that identifies entities within that array.
   * Example: { 'items': 'number' } - tables in items array use 'number' as identifier
   */
  arrayIdFields?: Record<string, string>;
  /** Array paths that contain entity references (id/type only) and should NOT register occurrences */
  referenceArrayPaths?: string[];
  /** Fields to exclude from tracking (e.g., '_u', '_c' metadata fields) */
  excludeFields?: string[];
  /** Enable debug logging */
  debug?: boolean;
}

/**
 * Represents one occurrence of an entity in the data tree
 */
export interface EntityOccurrence {
  /** JSON path like "pages[0].items[2]" or "categories[0].products[1]" */
  path: string;
  /** ID of parent entity, or 'ROOT' if at top level */
  parentEntityId: string;
}

/**
 * Complete record for a tracked entity
 */
export interface EntityRecord {
  /** Entity's unique identifier */
  id: string;
  /** All locations where this entity appears (keyed by parentEntityId:path) */
  occurrences: Map<string, EntityOccurrence>;
  /** Snapshot of entity's own fields at form open (excludes nested entities) */
  ownFieldsAtFormOpen: Record<string, any>;
}

/**
 * Tracks arrays that don't contain entities (items without id)
 */
export interface NoIdArrayRecord {
  /** Path to the array */
  path: string;
  /** ID of containing entity, or 'ROOT' */
  parentEntityId: string;
  /** Hash of array structure for change detection */
  signatureAtFormOpen: string;
}

/**
 * A local change made by the current user
 */
export interface LocalChange {
  /** Entity ID that was changed */
  entityId: string;
  /** Path of the entity occurrence that was changed */
  entityPath?: string;
  /** Field within the entity (e.g., 'price', 'name') */
  fieldPath: string;
  /** Value when form was opened */
  originalValue: any;
  /** Current value after user edit */
  currentValue: any;
  /** When the change was made */
  timestamp: number;
}

/**
 * A remote change received from another user
 */
export interface RemoteChange {
  /** Entity ID that was changed */
  entityId: string;
  /** Path of the entity occurrence that was changed */
  entityPath?: string;
  /** Field within the entity */
  fieldPath: string;
  /** Previous value */
  oldValue: any;
  /** New value from remote */
  newValue: any;
  /** User ID who made the change */
  userId: string;
  /** When the change was received */
  timestamp: number;
}

/**
 * Field-level conflict between local and remote changes
 */
export interface FieldConflict {
  type: 'field_conflict';
  /** Entity ID with the conflict */
  entityId: string;
  /** Path of the entity occurrence that was changed */
  entityPath?: string;
  /** Field that has conflicting values */
  fieldPath: string;
  /** User's local value */
  localValue: any;
  /** Remote/server value */
  remoteValue: any;
  /** Original value when form opened */
  originalValue: any;
}

/**
 * Conflict when entity was deleted remotely but has local changes
 */
export interface EntityDeletedConflict {
  type: 'entity_deleted';
  /** Entity ID that was deleted */
  entityId: string;
  /** Occurrence path for repeated IDs */
  entityPath?: string;
  /** Local changes that will be lost */
  localChanges: LocalChange[];
}

/**
 * Conflict when a no-ID array structure changed
 */
export interface NoIdArrayConflict {
  type: 'no_id_array_conflict';
  /** Path to the array */
  path: string;
  /** Parent entity ID */
  parentEntityId: string;
  /** Description for UI */
  description?: string;
}

/**
 * Union of all conflict types
 */
export type Conflict =
  | FieldConflict
  | EntityDeletedConflict
  | NoIdArrayConflict;

/**
 * Auto-merge for non-conflicting remote changes
 */
export interface AutoMerge {
  /** Entity ID that was updated */
  entityId: string;
  /** Path of the entity occurrence that was updated */
  entityPath?: string;
  /** Field that was updated */
  fieldPath: string;
  /** New value applied */
  newValue: any;
  /** Previous value */
  oldValue: any;
}

/**
 * Result from processing a remote update
 */
export interface ProcessUpdateResult {
  /** Conflicts that need user resolution */
  conflicts: Conflict[];
  /** Changes that were auto-merged (no conflict) */
  autoMerges: AutoMerge[];
  /** All remote changes detected */
  remoteChanges: RemoteChange[];
}

/**
 * Complete tracker state
 */
export interface TrackerState {
  /** All tracked entities (entityId → EntityRecord) */
  entities: Map<string, EntityRecord>;
  /** All no-ID arrays (path → NoIdArrayRecord) */
  noIdArrays: Map<string, NoIdArrayRecord>;
  /** Local changes by current user (entityId:fieldPath → LocalChange) */
  localChanges: Map<string, LocalChange>;
  /** Remote changes from other users (entityId:fieldPath → RemoteChange) */
  remoteChanges: Map<string, RemoteChange>;
  /** Current conflicts requiring resolution */
  conflicts: Conflict[];
  /** Full record snapshot when form opened */
  formOpenSnapshot: any;
  /** Latest server data (updated on each remote change) */
  latestServerSnapshot: any;
  /** Timestamp when form was opened */
  formOpenTime: number;
  /** Field used to identify entities */
  idField: string;
  /** Path-specific ID fields for arrays */
  arrayIdFields: Record<string, string>;
  /** Array paths that contain entity references (id/type only) */
  referenceArrayPaths: string[];
  /** Fields excluded from tracking */
  excludeFields: string[];
}

/**
 * Callbacks for viewport registration (modals/sub-forms)
 */
export interface ViewportCallbacks {
  /** Called when entity's path changes due to reordering */
  onPathChange: (newPath: string) => void;
  /** Called when entity is deleted by another user */
  onEntityDeleted: () => void;
  /** Called when this specific occurrence is removed (entity still exists elsewhere) */
  onOccurrenceRemoved?: () => void;
  /** Parent entity ID for multi-occurrence tracking */
  parentEntityId?: string;
}

/**
 * Context value exposed by ConflictDetectionProvider
 */
export interface ConflictDetectionContextValue {
  // ═══════════════════════════════════════════════════════════════════════════
  // State
  // ═══════════════════════════════════════════════════════════════════════════

  /** Current tracker state */
  state: TrackerState;
  /** Whether there are unresolved conflicts */
  hasConflicts: boolean;
  /** List of current conflicts */
  conflicts: Conflict[];
  /** Whether form has been initialized */
  isInitialized: boolean;

  // ═══════════════════════════════════════════════════════════════════════════
  // Initialization
  // ═══════════════════════════════════════════════════════════════════════════

  /** Initialize tracking for a record */
  initializeForRecord: (record: any) => void;
  /** Re-initialize after save (new baseline) */
  reinitializeAfterSave: (savedRecord: any) => void;
  /** Update server snapshot without resetting everything (preserves remoteChanges for visual indicators) */
  updateServerSnapshot: (newData: any) => void;
  /** Start applying remote changes (disables local change tracking) */
  startApplyingRemoteChanges: () => void;
  /** Stop applying remote changes (re-enables local change tracking) */
  stopApplyingRemoteChanges: () => void;

  // ═══════════════════════════════════════════════════════════════════════════
  // Remote Update Processing
  // ═══════════════════════════════════════════════════════════════════════════

  /** Process a remote update and detect conflicts */
  processRemoteUpdate: (newData: any, userId: string) => ProcessUpdateResult;

  // ═══════════════════════════════════════════════════════════════════════════
  // Change Tracking
  // ═══════════════════════════════════════════════════════════════════════════

  /** Track a field change (automatically finds entity from path) */
  trackChange: (fullPath: string, newValue: any) => void;
  /** Check if form has any local changes */
  hasLocalChanges: () => boolean;
  /** Clear all local changes (used when accepting remote changes) */
  clearAllLocalChanges: () => void;
  /** Clear local changes only for entities involved in the given conflicts */
  clearLocalChangesForConflicts: (conflicts: Conflict[]) => void;

  // ═══════════════════════════════════════════════════════════════════════════
  // Conflict Resolution
  // ═══════════════════════════════════════════════════════════════════════════

  /** Resolve a single conflict */
  resolveConflict: (conflict: Conflict, resolution: 'local' | 'remote') => void;
  /** Resolve all conflicts with same strategy */
  resolveAllConflicts: (resolution: 'local' | 'remote') => void;

  // ═══════════════════════════════════════════════════════════════════════════
  // Queries
  // ═══════════════════════════════════════════════════════════════════════════

  /** Check if a specific field has a conflict */
  hasFieldConflict: (entityId: string, fieldPath: string) => boolean;
  /** Check if a field was updated remotely (for visual indicators) */
  wasFieldUpdatedRemotely: (entityId: string, fieldPath: string) => boolean;
  /** Check if any field of an entity was updated remotely */
  wasEntityUpdatedRemotely: (entityId: string) => boolean;
  /** Get remote change info for an entity (userId and timestamp) */
  getEntityRemoteInfo: (
    entityId: string
  ) => { userId?: string; timestamp?: number } | null;
  /** Get the remote value for a field */
  getFieldRemoteValue: (entityId: string, fieldPath: string) => any;
  /** Get current path for an entity */
  getEntityPath: (entityId: string, parentEntityId?: string) => string | null;
  /** Check if a field has local changes (is dirty) */
  isFieldDirty: (entityId: string, fieldPath: string) => boolean;
  /** Check if any field of an entity has local changes */
  isEntityDirty: (entityId: string) => boolean;
  /** Get entity info by ID */
  getEntity: (entityId: string) => EntityRecord | undefined;

  // ═══════════════════════════════════════════════════════════════════════════
  // Viewport Registration (for modals)
  // ═══════════════════════════════════════════════════════════════════════════

  /** Register a viewport (modal) to receive entity updates */
  registerViewport: (
    entityId: string,
    callbacks: ViewportCallbacks
  ) => () => void;

  // ═══════════════════════════════════════════════════════════════════════════
  // Multi-Occurrence Sync
  // ═══════════════════════════════════════════════════════════════════════════

  /** Update a field across all occurrences of an entity */
  syncEntityField: (entityId: string, fieldPath: string, value: any) => void;
}

/**
 * Props for the ConflictDetectionProvider
 */
export interface ConflictDetectionProviderProps {
  children: React.ReactNode;
  config?: ConflictDetectionConfig;
}
