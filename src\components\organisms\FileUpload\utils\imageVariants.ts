/**
 * Utilities for handling public image variants and URLs
 */

import { listAll, ref } from 'firebase/storage';

import { UploadedFile } from '../types/fileUpload';
import { getStorageInstanceForFile } from './bucketManager';
import { generateFileUrl } from './urlGeneration';

/**
 * Check if a file should have image variants (public images with client-side processing)
 * Since we now always use folder structure for images, we check if it's an image type 'i'
 */
export const hasImageVariants = (file: UploadedFile): boolean => {
  return file.t === 'i';
};

/**
 * Get all available variants for a public image
 * For in-memory files, gets variants from inMemoryData
 * For storage files, lists the actual storage folder
 */
export const getImageVariants = async (
  file: UploadedFile
): Promise<string[]> => {
  if (!hasImageVariants(file)) {
    return [];
  }

  // For in-memory files, get variants from inMemoryData
  if (file.inMemoryData) {
    const variants: string[] = ['original']; // Always include original

    if (file.inMemoryData.variants) {
      // Add all available variants from memory
      variants.push(...Array.from(file.inMemoryData.variants.keys()));
    }

    return variants;
  }

  try {
    // Get the appropriate storage instance (temp or permanent)
    const storage = getStorageInstanceForFile(file.t, file.x || false);
    const folderPath = `i/${file.f}/`;
    const folderRef = ref(storage, folderPath);

    // List all files in the folder
    const listResult = await listAll(folderRef);

    // Extract variant names from file names
    const variants = listResult.items.map(itemRef => {
      const fileName = itemRef.name;
      // Remove file extension to get variant name
      return fileName.replace(/\.[^/.]+$/, '');
    });

    return variants;
  } catch (error) {
    // Fallback to common variants that might exist
    return ['original', 'thumbnail'];
  }
};

/**
 * Generate URL for a specific variant of a public image
 */
export const generateVariantUrl = async (
  file: UploadedFile,
  variant: string = 'thumbnail',
  format?: string
): Promise<string> => {
  if (!hasImageVariants(file)) {
    // Fallback to standard URL generation for non-variant images
    return generateFileUrl(file);
  }

  // Determine format based on variant
  let variantFormat = format;
  if (!variantFormat) {
    // Original keeps the original extension, others default to webp
    variantFormat = variant === 'original' ? file.e : 'webp';
  }

  // For public images (t: 'i'), keep f clean but indicate it's a variant path internally
  // The f field stays clean (just uniqueId), we handle folder structure in URL generation
  const variantFile: UploadedFile = {
    ...file,
    e: variantFormat,
    // f remains clean - no slashes or variant names
    // Remove url property to force regeneration with variant option
    url: undefined,
  };

  const url = await generateFileUrl(variantFile, {
    imageVariant: variant as any,
  });

  return url;
};

/**
 * Get the thumbnail URL for display purposes
 */
export const getThumbnailUrl = async (file: UploadedFile): Promise<string> => {
  if (hasImageVariants(file)) {
    return generateVariantUrl(file, 'thumbnail');
  }
  // For non-variant images, use the standard URL
  return generateFileUrl(file);
};

/**
 * Get display names for variants
 */
export const getVariantDisplayName = (variant: string): string => {
  const displayNames: Record<string, string> = {
    original: 'Original',
    thumbnail: 'Thumbnail (50×50)',
    square_s: 'Square Small (150×150)',
    square_m: 'Square Medium (300×300)',
    square_l: 'Square Large (600×600)',
    logo_receipt: 'Logo Receipt (450×150)',
    logo_ordernow_s: 'OrderNow Logo Small (150×40)',
    logo_ordernow_m: 'OrderNow Logo Medium (225×60)',
    logo_ordernow_l: 'OrderNow Logo Large (300×80)',
    mobile_intro_standard: 'Mobile Intro Standard (750×1334)',
    mobile_intro_wide: 'Mobile Intro Wide (828×1792)',
  };

  return displayNames[variant] || variant;
};

/**
 * Sort variants by importance for display
 * Priority: thumbnail first, variants by size (smallest to largest), original last
 */
export const sortVariantsByImportance = (variants: string[]): string[] => {
  // Define size/pixel count for each variant to enable size-based sorting
  const variantSizes: Record<string, number> = {
    thumbnail: 50 * 50, // 2,500 pixels
    square_s: 150 * 150, // 22,500 pixels
    square_m: 300 * 300, // 90,000 pixels
    logo_receipt: 450 * 150, // 67,500 pixels
    square_l: 600 * 600, // 360,000 pixels
    mobile_intro_standard: 750 * 1334, // 1,000,500 pixels
    mobile_intro_wide: 828 * 1792, // 1,483,776 pixels
    original: Number.MAX_SAFE_INTEGER, // Always last
  };

  return variants.sort((a, b) => {
    // Thumbnail always comes first
    if (a === 'thumbnail') return -1;
    if (b === 'thumbnail') return 1;

    // Original always comes last
    if (a === 'original') return 1;
    if (b === 'original') return -1;

    // For other variants, sort by size (pixels)
    const sizeA = variantSizes[a] || 0;
    const sizeB = variantSizes[b] || 0;

    if (sizeA !== sizeB) {
      return sizeA - sizeB; // Ascending order (smallest first)
    }

    // If sizes are equal or unknown, sort alphabetically
    return a.localeCompare(b);
  });
};
