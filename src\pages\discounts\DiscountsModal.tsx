import { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  Dialog,
  Divider,
  Grid,
  Input,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';

export interface DiscountDataType {
  name: string;
  value: number;
}

interface Props {
  isOpen: boolean;
  initialValue?: DiscountDataType;
  sellpointName: string;
  onClose: () => void;
  onSave: (data: DiscountDataType) => void;
  onDelete: () => void;
}

const textFieldProp = {
  sx: {
    '& .MuiInputBase-root': {
      height: 55,
    },
  },
};

export default function DiscountsModal({
  isOpen,
  initialValue,
  sellpointName,
  onClose,
  onSave,
  onDelete,
}: Props) {
  const { t } = useTranslation();
  const [values, setValues] = useState<DiscountDataType>(
    initialValue ?? {
      name: '',
      value: 0,
    }
  );

  useEffect(() => {
    setValues(
      initialValue ?? {
        name: '',
        value: 0,
      }
    );
  }, [initialValue]);

  const setValueH = (newVal: Partial<DiscountDataType>) => {
    setValues({ ...values, ...newVal });
  };

  const isEdit = !!initialValue;

  return (
    <Dialog
      {...getFullscreenModalProps()}
      open={isOpen}
      onClose={onClose}
      aria-labelledby="discounts-dialog"
      aria-describedby="discounts-dialog"
    >
      {/* header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          px: 2,
          py: 3,
        }}
      >
        <Button
          onClick={onClose}
          // @ts-ignore
          variant="close-btn"
          aria-label="close"
          sx={{
            '& span': { mr: 0 },
          }}
        >
          <CloseIcon fontSize="small" />
        </Button>
        <Box>
          {isEdit ? (
            //   @ts-ignore
            (<Button onClick={onDelete} variant="contained-light" color="error">
              {t('discountsPage.deleteDiscount')}
            </Button>)
          ) : (
            <></>
          )}
          <Button
            onClick={() => onSave(values)}
            variant="contained"
            disabled={
              JSON.stringify(initialValue) === JSON.stringify(values) ||
              !values.name.trim()
            }
            sx={{ ml: 2 }}
          >
            {isEdit ? t('shared.save') : t('discountsPage.createDiscount')}
          </Button>
        </Box>
      </Box>
      <Divider />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 5,
        }}
      >
        <Typography variant="h2">
          {isEdit ? t('shared.edit') : t('shared.create')} discount
        </Typography>
        <Grid container spacing={2}>
          <Grid size={12}>
            <TextField
              fullWidth
              value={values.name}
              onChange={event => setValueH({ name: event.target.value })}
              label={t('shared.name')}
              // error={!values.name.trim()}
              {...textFieldProp}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              sm: 6
            }}>
            <TextField
              fullWidth
              disabled
              value={t('discountsPage.amountType')}
              label={t('discountsPage.amountType')}
              {...textFieldProp}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              sm: 6
            }}>
            <TextField
              fullWidth
              value={values.value}
              type="number"
              onChange={event =>
                setValueH({ value: parseInt(event.target.value) })
              }
              label={t('discountsPage.amount')}
              error={!values.value.toString().trim()}
              {...textFieldProp}
            />
          </Grid>
          <Grid size={12}>
            <Typography variant="h5">{t('shared.location')}</Typography>
            <Typography color="custom.gray600">{sellpointName}</Typography>
          </Grid>
        </Grid>
      </Box>
    </Dialog>
  );
}
