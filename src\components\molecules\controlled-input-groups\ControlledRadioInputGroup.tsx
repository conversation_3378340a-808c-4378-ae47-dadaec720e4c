import {
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';

interface Props {
  title: string;
  choices: Array<{
    id: string;
    name: string;
    description?: string;
    disabled?: boolean;
  }>;
  disabled?: boolean;
  value: any;
  setValue: (value: any) => void;
}
export default function ControlledRadioInputGroup({
  title,
  choices,
  disabled,
  value,
  setValue,
}: Props) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const handleChange = (event: any) => {
    setValue(event.target.value);
  };

  return (
    <Box
      sx={{
        border: `1px solid`,
        borderColor: 'custom.gray400',
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        marginTop: '-1px',
        bgcolor: 'custom.fieldBg',
      }}
      onClick={event => {
        if (disabled) {
          event.preventDefault();
          event.stopPropagation();
        }
      }}
    >
      {title && (
        <Box
          sx={{
            width: isXSmall ? '100%' : '200px',
            minWidth: isXSmall ? '100%' : '200px',
            minHeight: isXSmall ? '35px' : '45px',
            // bgcolor: !!fieldState.error ? "error.light" : "background.tinted",
            bgcolor: 'background.tinted',
            display: 'flex',
            px: 2,
            py: 1,
          }}
        >
          {/* @ts-ignore */}
          <Typography
            variant="body2"
            sx={{ fontWeight: 500, mt: isXSmall ? 0 : 0.5 }}
          >
            {title}
          </Typography>
        </Box>
      )}
      <Box
        sx={{
          px: 3.5,
          pb: 3.5,
        }}
      >
        <FormControl>
          <RadioGroup
            name="radio-buttons-group"
            value={value ?? null}
            onChange={handleChange}
          >
            {choices.map(choice => (
              <FormControlLabel
                disabled={choice.disabled}
                key={choice.id}
                value={choice.id}
                label={
                  <Box sx={{ mt: '20px', ml: '5px' }}>
                    {/* @ts-ignore */}
                    <Typography variant="label" fontWeight={300}>
                      {choice.name}
                    </Typography>
                    {choice.description && (
                      <Typography variant="subtitle2">
                        {choice.description}
                      </Typography>
                    )}
                  </Box>
                }
                control={<Radio />}
              />
            ))}
            {/* <FormControlLabel
              value="female"
              control={<Radio />}
              label="Female"
            />
            <FormControlLabel value="male" control={<Radio />} label="Male" />
            <FormControlLabel value="other" control={<Radio />} label="Other" /> */}
          </RadioGroup>
        </FormControl>
      </Box>
    </Box>
  );
}
