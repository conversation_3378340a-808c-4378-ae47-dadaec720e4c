import { useEffect, useState } from 'react';
import { Box, Grid, Typography } from '@mui/material';
import { DateRange } from '@mui/x-date-pickers-pro';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import { groupReport } from '~/fake-provider/reports/groupReport';
import {
  OmitKeysWithTypeTransform,
  Report,
  ReportType,
} from '~/fake-provider/reports/types';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import {
  generatePaddedDaysArray,
  generatePaddedHoursArray,
} from '~/pages/reports/utils/generatePaddedReportData';
import { CurrencyType, formatNumber } from '../../../utils/formatNumber';
import Sparkline from '../../atoms/charts/Sparkline';
import PercentageChange from '../../atoms/PercentageChange';

const REPORT_TYPE = 'sales';

const containerStyle = {
  borderRadius: '6px',
  ':hover': {
    bgcolor: 'background.tinted',
    cursor: 'pointer',
  },
};

const getParsedData = (
  currentData: number[],
  lastData: number[],
  t: any,
  currency: CurrencyType = 'RON'
) => {
  const getIncreasePercentage = (newValue: number, oldValue: number) => {
    if (!newValue || !oldValue) return '';
    return (newValue - oldValue) / oldValue;
  };

  return {
    netSales: {
      value: formatNumber(currentData[0], currency),
      changePercentage: getIncreasePercentage(currentData[0], lastData[0]),
    },
    stats: [
      {
        label: `${t('shared.netSales')}`,
        value: formatNumber(currentData[1], currency),
        changePercentage: getIncreasePercentage(currentData[1], lastData[1]),
      },
      {
        label: t('dashboard.bills'),
        value: formatNumber(currentData[2]),
        changePercentage: getIncreasePercentage(currentData[2], lastData[2]),
      },
      {
        label: t('dashboard.averageSale'),
        value: formatNumber(currentData[3], currency),
        changePercentage: getIncreasePercentage(currentData[3], lastData[3]),
      },
      {
        label: t('dashboard.covers'),
        value: formatNumber(currentData[4]),
        changePercentage: getIncreasePercentage(currentData[4], lastData[4]),
      },
      {
        label: t('menu.itemSales'),
        value: formatNumber(currentData[5]),
        changePercentage: getIncreasePercentage(currentData[5], lastData[5]),
      },
      {
        label: t('dashboard.teamMembers'),
        value: formatNumber(currentData[6]),
        changePercentage: getIncreasePercentage(currentData[6], lastData[6]),
      },
    ],
  };
};

const getStatsArrayFromRawData = (
  rawData: OmitKeysWithTypeTransform<Report<'sales'>>[]
): number[] => {
  const grouped = groupReport(REPORT_TYPE, rawData, [], []);
  const members = groupReport(REPORT_TYPE, rawData, ['memberId'], []);
  const data = grouped[0]?.report[0] as ReportType[typeof REPORT_TYPE];

  if (!data) return [0, 0, 0, 0, 0, 0, 0];

  return [
    data.totalValue / 10000,
    data.netValue / 10000,
    data.bills ?? 0,
    data.totalValue / (data.bills ?? 1) / 10000,
    data.covers ?? 0,
    (data.itemsQty ?? 0) / 1000,
    members.length,
  ];
};

const getPriorPeriod = (
  dateRange: DateRange<dayjs.Dayjs>
): DateRange<dayjs.Dayjs> => {
  if (!dateRange[0] || !dateRange[1]) return dateRange;

  const start = dateRange[0];
  const end = dateRange[1];

  const diffInDays = end.diff(start, 'day');
  const diffInMonths = end.diff(start, 'month') + 1; // +1 to include both months if full months selected

  // Helper to check if range is exactly a full month
  const isFullMonth =
    start.date() === 1 &&
    end.date() === end.daysInMonth() &&
    start.month() === end.month() &&
    start.year() === end.year();

  // Helper to check if range is multiple full months
  const isMultipleFullMonths =
    start.date() === 1 &&
    end.date() === end.daysInMonth() &&
    (end.month() - start.month() + 1 === diffInMonths ||
      end.year() > start.year());

  if (diffInDays <= 7) {
    // Last week
    return [start.subtract(7, 'day'), end.subtract(7, 'day')];
  } else if (isFullMonth) {
    // Full previous month
    const prevMonth = start.subtract(1, 'month');
    const prevMonthStart = prevMonth.startOf('month');
    const prevMonthEnd = prevMonth.endOf('month');
    return [prevMonthStart, prevMonthEnd];
  } else if (isMultipleFullMonths && diffInMonths > 1) {
    // Same months last year
    return [start.subtract(1, 'year'), end.subtract(1, 'year')];
  } else {
    // Same period last month
    return [start.subtract(1, 'month'), end.subtract(1, 'month')];
  }
};

export default function TotalSales() {
  const { details: fbDetails } = useFirebase();
  const [currency, setCurrency] = useState<CurrencyType>();
  const [graphData, setGraphData] =
    useState<{ value: number; date: string }[]>();
  const [data, setData] = useState<{
    stats: any[];
    netSales: { value: string; changePercentage: number | string };
  }>();

  const { dateRange, sellPointId } = useGlobalResourceFilters();
  const [priorPeriod, setPriorPeriod] =
    useState<DateRange<dayjs.Dayjs>>(dateRange);
  const { t } = useTranslation('');

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;
      const isOneDay = dateRange[0]?.isSame(dateRange[1], 'day');

      try {
        const rawData = (await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          endDate: dateRange[1].format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        })) as OmitKeysWithTypeTransform<Report<'sales'>>[];

        const priorPeriod = getPriorPeriod(dateRange);
        setPriorPeriod(priorPeriod);

        const priorRawData =
          !priorPeriod[0] || !priorPeriod[1]
            ? []
            : ((await getReportDataHelper({
                database: fbDetails.rtdb!,
                startDate: priorPeriod[0].format('YYYY-MM-DD'),
                accountId: fbDetails.selectedAccount!,
                sellPointId: sellPointId,
                endDate: priorPeriod[1].format('YYYY-MM-DD'),
                reportType: REPORT_TYPE,
              })) as OmitKeysWithTypeTransform<Report<'sales'>>[]);

        const currentStats = getStatsArrayFromRawData(rawData);
        const lastStats = getStatsArrayFromRawData(priorRawData);

        const currency = (rawData[0]?.currency || 'RON') as CurrencyType;
        setCurrency(currency);

        const parsedData = getParsedData(currentStats, lastStats, t, currency);
        setData(parsedData);

        // If we selected one day, we show hours on chart. Else, we show days
        if (isOneDay && rawData.length) {
          const groupedByHour = groupReport(
            REPORT_TYPE,
            rawData,
            ['hourOfDay', 'whatDay'],
            []
          );

          const paddedHourData = generatePaddedHoursArray(
            8,
            24,
            groupedByHour,
            'totalValue'
          );
          setGraphData(
            paddedHourData.labels.map((el, idx) => ({
              value: paddedHourData.values[idx] / 10000,
              date: el,
            }))
          );
        } else if (!isOneDay && rawData.length) {
          const groupedByDay = groupReport(REPORT_TYPE, rawData, ['date'], []);

          const startDay = dateRange[0]!.format('YYYY-MM-DD');
          const endDay = dateRange[1]!.format('YYYY-MM-DD');

          const paddedDaysData = generatePaddedDaysArray(
            startDay,
            endDay,
            groupedByDay,
            'totalValue'
          );

          setGraphData(
            paddedDaysData.labels.map((el, idx) => ({
              value: paddedDaysData.values[idx] / 10000,
              date: el,
            }))
          );
        } else {
          setGraphData([]);
        }
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  return (
    <Grid sx={{ pt: 2, pb: 3 }} container>
      <Grid sx={containerStyle} mb={4} size={12}>
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            flexWrap: 'wrap',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Box p={2}>
            <Typography variant="subtitle2">{t('dashboard.sales')}</Typography>
            <Typography fontSize={25} fontWeight={500} mb={1}>
              {data?.netSales.value}
            </Typography>
            <PercentageChange
              value={data?.netSales.changePercentage}
              date={priorPeriod}
            />
          </Box>
          {!!graphData?.length && (
            <Sparkline
              chartData={graphData}
              formatData={data => formatNumber(data, currency)}
            />
          )}
        </Box>
      </Grid>
      {data?.stats.map(el => {
        return (
          <Grid
            key={el.label}
            sx={{
              ...containerStyle,
              ...{
                display: 'flex',
                p: 2,
                justifyContent: 'space-between',
                alignItems: 'center',
              },
            }}
            size={{
              xs: 12,
              sm: 6,
              lg: 4
            }}>
            <Box>
              <Typography variant="subtitle2">{el.label}</Typography>
              <Typography variant="subtitle1" fontWeight={500}>
                {el.value}
              </Typography>
            </Box>
            <PercentageChange value={el.changePercentage} date={priorPeriod} />
          </Grid>
        );
      })}
    </Grid>
  );
}
