import React, { useMemo, useState } from 'react';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import RefreshIcon from '@mui/icons-material/Refresh';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import { Box, CircularProgress, useTheme } from '@mui/material';

import { PlaceholderThumbnail } from '~/components/organisms/FileUpload/components/PlaceholderThumbnail';
import { shouldUsePlaceholder } from '~/components/organisms/FileUpload/utils/filePlaceholders';
import { useBlobUrlErrorRecovery } from '../hooks/useBlobUrlErrorRecovery';
import { useFileUrl } from '../hooks/useFileUrl';
import { ImageVariant, UploadedFile } from '../types/fileUpload';
import { isImageFile, isVideoFile } from '../utils/previewUtils';

export interface FilePreviewInlineProps {
  /**
   * Single file or array of files - when array is provided, first file is used
   */
  files: UploadedFile | UploadedFile[];

  /**
   * Image variant to display (for images only)
   * @default 'thumbnail'
   */
  imageVariant?: ImageVariant;

  /**
   * Size of the thumbnail in pixels (creates square: size x size)
   * Use this for backward compatibility, or use width/height for custom dimensions
   * @default 50
   */
  size?: number;

  /**
   * Width of the thumbnail in pixels
   * If specified, overrides the width from 'size' prop
   */
  width?: number;

  /**
   * Height of the thumbnail in pixels
   * If specified, overrides the height from 'size' prop
   */
  height?: number;

  /**
   * Click handler
   */
  onClick?: () => void;

  /**
   * Whether the thumbnail is disabled
   * @default false
   */
  disabled?: boolean;

  /**
   * Custom placeholder fallback URL
   * @default '/assets/placeholder-item.svg'
   * @deprecated Use placeholderFileType instead
   */
  placeholderUrl?: string;

  /**
   * File type to use for placeholder when no files are provided
   * @default 'image/jpeg' (will show image placeholder)
   */
  placeholderFileType?: string;

  /**
   * File extension to use for placeholder when no files are provided
   * @default 'jpg'
   */
  placeholderExtension?: string;

  /**
   * Border radius override
   */
  borderRadius?: number | string;

  /**
   * Custom styling
   */
  sx?: any;

  /**
   * File type for better placeholder icon selection
   */
  fileType?: 'images' | 'videos' | 'public' | 'private';
}

// Global cache to persist URLs across component re-renders during reordering
const globalUrlCache = new Map<string, string>();

// Cleanup function to prevent memory leaks (exported for external use if needed)
export const cleanupFilePreviewCache = () => {
  // Revoke all blob URLs before clearing the cache
  globalUrlCache.forEach(url => {
    if (url.startsWith('blob:')) {
      try {
        URL.revokeObjectURL(url);
      } catch (error) {
        // Ignore revoke errors
      }
    }
  });
  globalUrlCache.clear();
};

// Function to clear cache for a specific file when it transitions states
export const clearFilePreviewCacheForFile = (file: UploadedFile) => {
  // Clear cache entries for this file with different variants and states
  const baseKey = `${file.f}.${file.e}`;
  const keys = Array.from(globalUrlCache.keys()).filter(key =>
    key.startsWith(baseKey)
  );
  keys.forEach(key => {
    const cachedUrl = globalUrlCache.get(key);
    // If it's a blob URL, revoke it to free memory
    if (cachedUrl && cachedUrl.startsWith('blob:')) {
      try {
        URL.revokeObjectURL(cachedUrl);
      } catch (error) {
        // Ignore revoke errors
      }
    }
    globalUrlCache.delete(key);
  });
};

/**
 * File Preview Inline component that handles:
 * - Single files or arrays of files (uses first file from array)
 * - Multiple image variants (thumbnail, square_s, square_m, square_l, etc.)
 * - Fallback to placeholder for unsupported file types
 * - Video file indicators
 * - Loading and error states
 * - Custom styling and sizing
 */
export const FilePreviewInline: React.FC<FilePreviewInlineProps> = ({
  files,
  imageVariant = 'thumbnail',
  size = 50,
  width,
  height,
  onClick,
  disabled = false,
  placeholderUrl = '/assets/placeholder-item.svg',
  placeholderFileType = 'image/jpeg',
  placeholderExtension = 'jpg',
  borderRadius = 1.5,
  sx,
  fileType,
}) => {
  const theme = useTheme();

  // State to track if the image failed to load
  const [imageLoadFailed, setImageLoadFailed] = useState(false);

  // Blob URL error recovery
  const { createImageErrorHandler } = useBlobUrlErrorRecovery();

  // Calculate final dimensions - width/height props override size
  const finalWidth = width || size;
  const finalHeight = height || size;

  // Extract first file from array or use single file
  const file = useMemo(() => {
    return Array.isArray(files) ? files[0] : files;
  }, [files]);

  // Reset image load failed state when file changes
  const fileKey = file
    ? `${file.f}.${file.e}.${file.x ? 'temp' : 'perm'}`
    : null;
  const [lastFileKey, setLastFileKey] = useState<string | null>(null);

  if (fileKey !== lastFileKey) {
    setImageLoadFailed(false);
    setLastFileKey(fileKey);
  }

  // Create cache key - always create, even if no file
  const cacheKey = useMemo(() => {
    if (!file) return null;
    const baseKey = `${file.f}.${file.e}`;
    return `${baseKey}.${file.x ? 'temp' : 'perm'}.${imageVariant}`;
  }, [file?.f, file?.e, file?.x, imageVariant]);

  // Configure useFileUrl options - always call, even if no file
  const options = useMemo(() => {
    if (!file || !isImageFile(file)) return undefined;

    // Now that square variants are included in ImageVariant type, we can use them directly
    return { imageVariant: imageVariant as ImageVariant };
  }, [file?.t, file?.e, imageVariant]);

  // Always call useFileUrl hook - this fixes the hooks order issue
  const { url, loading, error, refresh } = useFileUrl(file, options);

  // Check cached URL
  const cachedUrl = cacheKey ? globalUrlCache.get(cacheKey) : null;

  // Cache URL when available (but only if it's not a blob URL for permanent files)
  if (url && cacheKey && !cachedUrl) {
    // Don't cache blob URLs for permanent files as they become invalid
    const isBlobUrl = url.startsWith('blob:');
    const isPermanentFile = file && !file.x;

    if (!isBlobUrl || !isPermanentFile) {
      globalUrlCache.set(cacheKey, url);
    }
  }

  // If no file is provided, show placeholder using PlaceholderThumbnail
  if (!file) {
    // Create a mock file object for the placeholder
    const mockFile = {
      rn: 'placeholder',
      f: 'placeholder',
      e: placeholderExtension,
      t: 'i' as const, // Default to image type
      type: placeholderFileType, // Add type property for PlaceholderThumbnail
    } as UploadedFile & { type: string };

    return (
      <PlaceholderThumbnail
        file={mockFile}
        width={finalWidth}
        height={finalHeight}
        onClick={onClick}
        disabled={disabled}
        borderRadius={borderRadius}
        sx={sx}
        // Don't pass fileType for "no file" placeholders - use default gray
      />
    );
  }

  // Check if we should use placeholder for this file type
  if (shouldUsePlaceholder(file)) {
    // For uploaded video files, we want to show a video-specific placeholder
    const isUploadedVideo = isVideoFile(file);
    return (
      <PlaceholderThumbnail
        file={file}
        width={finalWidth}
        height={finalHeight}
        onClick={onClick}
        disabled={disabled}
        borderRadius={borderRadius}
        sx={sx}
        fileType={isUploadedVideo ? 'videos' : fileType}
      />
    );
  }

  const displayUrl = cachedUrl || url;
  const isActuallyLoading = loading && !cachedUrl;
  const isImage = isImageFile(file);
  const isVideo = isVideoFile(file);

  // Don't use cached blob URLs for permanent files
  const shouldUseCachedUrl =
    cachedUrl && !(cachedUrl.startsWith('blob:') && !file?.x);
  const effectiveDisplayUrl = shouldUseCachedUrl ? cachedUrl : url;

  // Loading state
  if (isActuallyLoading) {
    return (
      <Box
        sx={{
          width: finalWidth,
          height: finalHeight,
          minWidth: finalWidth,
          minHeight: finalHeight,
          maxWidth: finalWidth,
          maxHeight: finalHeight,
          flexShrink: 0,
          flexGrow: 0,
          margin: 'auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${theme.palette.divider}`,
          borderRadius,
          backgroundColor: theme.palette.action.hover,
          ...sx,
        }}
      >
        <CircularProgress size={Math.min(finalWidth, finalHeight) * 0.6} />
      </Box>
    );
  }

  // Error state or no URL - use PlaceholderThumbnail for better consistency
  if (error || (!effectiveDisplayUrl && isImage) || imageLoadFailed) {
    return (
      <PlaceholderThumbnail
        file={file}
        width={finalWidth}
        height={finalHeight}
        onClick={onClick}
        disabled={disabled}
        borderRadius={borderRadius}
        sx={sx}
        fileType={fileType}
      />
    );
  }

  // Fallback to placeholder URL if no effectiveDisplayUrl
  const finalUrl = effectiveDisplayUrl || placeholderUrl;

  // Image display
  if (isImage && finalUrl) {
    return (
      <Box
        component="img"
        src={finalUrl}
        alt={file.f}
        onClick={onClick}
        onError={
          file?.inMemoryData
            ? createImageErrorHandler(file, imageVariant, newUrl => {
                // Update the cache with the new URL
                const cacheKey = `${file.f}.${file.e}_${imageVariant}_${file.x ? 'temp' : 'perm'}`;
                globalUrlCache.set(cacheKey, newUrl);
              })
            : e => {
                console.log(
                  'Image load failed for:',
                  file?.f,
                  'URL:',
                  finalUrl
                );
                setImageLoadFailed(true);
              }
        }
        sx={{
          width: finalWidth,
          height: finalHeight,
          minWidth: finalWidth,
          minHeight: finalHeight,
          maxWidth: finalWidth,
          maxHeight: finalHeight,
          flexShrink: 0,
          flexGrow: 0,
          margin: 'auto',
          borderRadius,
          objectFit: 'cover',
          border: `1px solid ${theme.palette.divider}`,
          cursor: onClick && !disabled ? 'pointer' : 'default',
          opacity: disabled ? 0.6 : 1,
          transition: 'filter 0.2s ease-in-out',
          display: 'block', // Ensure consistent display
          '&:hover':
            onClick && !disabled
              ? {
                  filter: 'brightness(0.9)',
                }
              : {},
          ...sx,
        }}
      />
    );
  }

  // Video display with play overlay
  if (isVideo) {
    // If fileType is 'videos', use PlaceholderThumbnail for consistent styling
    if (fileType === 'videos') {
      return (
        <PlaceholderThumbnail
          file={file}
          width={finalWidth}
          height={finalHeight}
          onClick={onClick}
          disabled={disabled}
          borderRadius={borderRadius}
          sx={sx}
          fileType={fileType}
        />
      );
    }

    // Fallback to custom video display for backward compatibility
    return (
      <Box
        sx={{
          position: 'relative',
          width: finalWidth,
          height: finalHeight,
          minWidth: finalWidth,
          minHeight: finalHeight,
          maxWidth: finalWidth,
          maxHeight: finalHeight,
          flexShrink: 0,
          flexGrow: 0,
          margin: 'auto',
          borderRadius,
          border: `1px solid ${theme.palette.divider}`,
          cursor: onClick && !disabled ? 'pointer' : 'default',
          opacity: disabled ? 0.6 : 1,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${theme.palette.primary.main}20 0%, ${theme.palette.secondary.main}20 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '&:hover':
            onClick && !disabled
              ? {
                  '& .video-overlay': {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  },
                  transform: 'scale(1.02)',
                }
              : {},
          transition: 'transform 0.2s ease-in-out',
          ...sx,
        }}
        onClick={onClick}
      >
        {/* Video file icon background */}
        <VideoFileIcon
          sx={{
            color: theme.palette.text.secondary,
            fontSize: Math.min(finalWidth, finalHeight) * 0.4,
            opacity: 0.3,
            position: 'absolute',
          }}
        />

        {/* Play button overlay */}
        <Box
          className="video-overlay"
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            transition: 'background-color 0.2s ease-in-out',
          }}
        >
          <PlayCircleOutlineIcon
            sx={{
              color: 'white',
              fontSize: Math.min(finalWidth, finalHeight) * 0.5,
              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.8))',
            }}
          />
        </Box>
      </Box>
    );
  }

  // Generic file display - use PlaceholderThumbnail for better consistency
  return (
    <PlaceholderThumbnail
      file={file}
      width={finalWidth}
      height={finalHeight}
      onClick={onClick}
      disabled={disabled}
      borderRadius={borderRadius}
      sx={sx}
      fileType={fileType}
    />
  );
};

export default FilePreviewInline;
