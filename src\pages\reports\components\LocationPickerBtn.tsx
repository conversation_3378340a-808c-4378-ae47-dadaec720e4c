import { useMemo, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import {
  Box,
  Button,
  InputAdornment,
  Popover,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { t } from 'i18next';

import { useTheme } from '~/contexts/ThemeContext';
import { useGetListLocationsLive } from '~/providers/resources';

import type { ChangeEvent, MouseEvent } from 'react';

interface LocationPickerBtnProps {
  sellpointId: string;
  globalFiltersVariant?: string;
  disabled?: boolean;
  setSellpointId: (sellpointId: string) => void;
}

export default function LocationPickerBtn({
  sellpointId,
  setSellpointId,
  disabled,
  globalFiltersVariant,
}: LocationPickerBtnProps) {
  const { data: sellpoints } = useGetListLocationsLive();
  const [filteredSellpoints, setFilteredSellpoints] = useState(sellpoints);
  const [searchQuery, setSearchQuery] = useState('');

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);
  const id = open ? 'location-popover' : undefined;

  const openDropdown = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    setSearchQuery('');
    setFilteredSellpoints(sellpoints);
  };

  const closeDropdown = () => {
    setAnchorEl(null);
  };

  const label = useMemo(() => {
    if (!sellpointId || !sellpoints) {
      return;
    }

    return sellpoints.find(el => el.id === sellpointId).name;
  }, [sellpointId, sellpoints]);

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setSearchQuery(value);
    setFilteredSellpoints(
      sellpoints?.filter(el =>
        el.name.toLowerCase().includes(value.toLowerCase())
      )
    );
  };
  const { theme } = useTheme();

  return (
    <>
      <Button
        //@ts-ignore
        variant={
          globalFiltersVariant ? globalFiltersVariant : 'contained-light'
        }
        disabled={disabled}
        aria-describedby={id}
        onClick={openDropdown}
        sx={isXSmall ? { height: '40px' } : {}}
      >
        {isXSmall && (
          <Typography variant="body2" sx={{ mr: 1 }} color="custom.gray600">
            {t('shared.location')}
          </Typography>
        )}

        <Typography
          // @ts-ignore
          variant="outlined"
          fontWeight={400}
          color={
            isXSmall
              ? theme.palette.mode === 'dark'
                ? 'white'
                : 'black'
              : 'custom.gray800'
          }
        >
          {label}
        </Typography>
        {/* {!disabled && <KeyboardArrowDownIcon color="disabled" />} */}
      </Button>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeDropdown}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box p={2} pt={1} width="250px">
          <TextField
            sx={{ mt: 0 }}
            variant="standard"
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          {filteredSellpoints?.map((sellpoint, idx) => (
            <Box
              key={sellpoint.id}
              onClick={() => {
                setSellpointId(sellpoint.id);
                closeDropdown();
              }}
              py={1.5}
              px={1}
              sx={{
                cursor: 'pointer',
                bgcolor: 'transparent',
                transition: 'all 0.1s ease',
                borderBottom:
                  idx < filteredSellpoints.length - 1 ? 'solid 1px' : 'none',
                borderColor: 'custom.gray200',
                '&:hover': {
                  bgcolor: 'primary.veryLight',
                  borderRadius: '6px',
                },
              }}
            >
              {sellpoint.name}
            </Box>
          ))}
        </Box>
      </Popover>
    </>
  );
}
