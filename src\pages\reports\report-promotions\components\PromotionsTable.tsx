import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { ReportType } from '~/fake-provider/reports/types';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import replaceNumberWithPercentage from '~/utils/replaceNumbersWithPercentage';

interface ExtraData {
  [key: string]: any;
}

interface TableRow {
  name: string;
  itemsQty: string;
  vat: number | undefined;
  itemsValue: string;
  giftCardsQty: string;
  modifiersQty: string;
  modifiersValue: string;
  giftCardsValue: string;
  totalValue: number;
  items?: TableRow[];
  subItems?: TableRow[];
  extraData?: ExtraData;
  extraChargesValue: string;
  extraChargesQty: string;
}

export default function PromotionsTable({
  tableData,
  fields,
  setFields,
  groupingItems,
  onChangeGrouping,
}: {
  tableData: ReportType[keyof ReportType][] | undefined;
  fields: FieldOption[];
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  groupingItems: string[];
  onChangeGrouping?: (items: any[]) => void;
}) {
  const { t } = useTranslation();
  const promotionsTableData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.name = 'Total';
      totalItemsData.vat = undefined;
      totalItemsData.subItems = [];

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }
    return [];
  }, [tableData]);

  const promotionsTablConfig: ColumnConfig<TableRow>[] = useMemo(
    () => [
      {
        id: 'name',
        textAlign: 'start',
        label: t('shared.name'),
        render: (row: TableRow) => {
          return <>{replaceNumberWithPercentage(row.name)}</>;
        },
      },
      {
        id: 'vat',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                //@ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
            </div>
          );
        },
        textAlign: 'end',
        label: t('shared.tva'),
      },
      {
        id: 'itemsQty',
        textAlign: 'end',
        label: t('reportsPage.itemsPromotionsApplied'),
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.itemsQty), true)}</>;
        },
      },
      {
        id: 'itemsValue',
        textAlign: 'end',
        label: t('reportsPage.itemsPromotionsAmount'),
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.itemsValue))}</>;
        },
      },
      {
        id: 'modifiersQty',
        textAlign: 'end',
        label: t('reportsPage.modifiersPromotionsApplied'),
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.modifiersQty), true)}</>;
        },
      },
      {
        id: 'modifiersValue',
        textAlign: 'end',
        label: t('reportsPage.modifiersPromotionsAmount'),
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.modifiersValue))}</>;
        },
      },
      {
        id: 'giftCardsQty',
        label: t('reportsPage.giftCardPromotionsApplied'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.giftCardsQty), true)}</>;
        },
      },
      {
        id: 'giftCardsValue',
        label: t('reportsPage.giftCardAmountDiscounted'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.giftCardsValue))}</>;
        },
      },
      {
        id: 'extraChargesQty',
        label: t('reportsPage.extraChargesPromotionsApplied'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.giftCardsQty), true)}</>;
        },
      },
      {
        id: 'extraChargesValue',
        textAlign: 'end',
        label: t('reportsPage.extraChargesPromotionsAmount'),
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.modifiersValue))}</>;
        },
      },
      {
        id: 'totalValue',
        label: t('reportsPage.totalAmount'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.totalValue))}</>;
        },
      },
    ],
    [t]
  );

  const columnsToFilter = useMemo(() => {
    const columns = ['itemsQty', 'giftCardsQty', 'extraChargesQty', 'vat'];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [
    { value: 'itemsQty', label: t('reportsPage.itemsPromotionsApplied') },
    {
      value: 'extraChargesQty',
      label: t('reportsPage.extraChargesPromotionsApplied'),
    },
    {
      value: 'giftCardsQty',
      label: t('reportsPage.giftCardPromotionsApplied'),
    },
    { value: 'vat', label: t('shared.tva') },
  ];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <GroupingTable
          config={promotionsTablConfig}
          data={promotionsTableData}
          fields={fields}
          separateFirstColumn={true}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          fixedFirstColumn={true}
        />
      </Box>
    </>
  );
}
