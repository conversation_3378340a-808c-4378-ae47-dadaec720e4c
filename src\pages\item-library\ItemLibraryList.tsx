import { Fragment, use, useCallback, useMemo } from 'react';
import ErrorIcon from '@mui/icons-material/Error';
import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import { visuallyHidden } from '@mui/utils';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import jsonExport from 'jsonexport/dist';
import {
  BulkDeleteWithConfirmButton,
  ColumnsButton,
  CreateButton,
  DatagridConfigurable,
  DataTable,
  downloadCSV,
  FetchRelatedRecords,
  FilterButton,
  FunctionField,
  InfiniteList,
  NumberField,
  ReferenceField,
  SelectArrayInput,
  TextField,
  TopToolbar,
  useGetList,
  useLocaleState,
} from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { CurrencyField } from '~/components/atoms/CurrencyField';
import { CurrencyRangeField } from '~/components/atoms/CurrencyRangeField';
import Pill from '~/components/atoms/Pill';
import { ValueFieldWithError } from '~/components/atoms/ValueFieldWithError';
import { CustomColumnsButton } from '~/components/molecules/CustomSelectColumnsButton';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { FilePreviewInline } from '~/components/organisms/FileUpload';
import {
  RESOURCES,
  resourcesInfo,
  useGetListHospitalityCatalogsLive,
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
  useGetListLocationsLive,
  useGetListMeasureUnits,
} from '~/providers/resources';
import { getValueFromRecord } from '~/utils/getValueFromRecord';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { useTheme } from '../../contexts';
import { ExportMenuButton } from './ExportMenuButton';

const PostListActions = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <TopToolbar>
      <CreateButton
        variant="contained"
        label="Add a item"
        {...(isXSmall ? {} : { icon: <></> })}
      />
    </TopToolbar>
  );
};

const AssetBulkActionButtons = (props: any) => (
  <Fragment>
    <BulkDeleteWithConfirmButton {...props} />
  </Fragment>
);

export const ItemLibraryList = ({ contentRef }: { contentRef: any }) => {
  const params = useParams<'id'>();
  const { theme } = useTheme();
  const { t } = useTranslation('');
  const [locale] = useLocaleState();

  const postRowStyle = (record: any) => {
    const id = (params as any)['*'].split('/')[0];

    if (id == record.id)
      return {
        background: theme.palette.primary.main,
        color: 'white',
      };
    return {};
  };

  const { data: measureUnits } = useGetListMeasureUnits();
  const measureUnitsIdToSymbol = useMemo(() => {
    if (!measureUnits) return {};
    return measureUnits.reduce((acc: any, measureUnit: any) => {
      acc[measureUnit.id] = measureUnit.symbol[locale];
      return acc;
    }, {});
  }, [measureUnits, locale]);

  const { data: items } = useGetListHospitalityItemsLive();

  const { data: categories } = useGetListHospitalityCategoriesLive({
    filter: { _d: false },
  });
  const categoryIdToName = useMemo(() => {
    if (!categories) return {};
    return categories.reduce((acc: any, category: any) => {
      acc[category.id] = category.name;
      return acc;
    }, {});
  }, [categories]);

  const { data: catalogs } = useGetListHospitalityCatalogsLive();
  const catalogsMap = useMemo(() => {
    if (!catalogs) return {};
    return catalogs.reduce((acc: any, catalog: any) => {
      acc[catalog.id] = {
        name: catalog.name,
        sellPointIds: catalog.sellPointIds,
      };
      return acc;
    }, {});
  }, [catalogs]);

  const { data: sellPoints } = useGetListLocationsLive();
  const sellPointsMap = useMemo(() => {
    if (!sellPoints) return {};
    return sellPoints.reduce((acc: any, sellPoint: any) => {
      acc[sellPoint.id] = sellPoint.name;
      return acc;
    }, {});
  }, [sellPoints]);

  const getLocations = (catalogSpecific: {
    [catalogId: string]: {
      catalogId: string;
      price: number;
      vat: number;
    };
  }): string[] => {
    if (!catalogSpecific) return [];
    const locations = Object.keys(catalogSpecific).map(catalogId => {
      const sellPointIds = catalogsMap[catalogId]?.sellPointIds || [];
      return sellPointIds.map(
        (sellPointId: string) => sellPointsMap[sellPointId]
      );
    });
    const flatLocations = locations.flat();
    // filter out undefined values
    const filteredLocations = flatLocations.filter(Boolean).sort();
    // return and array with unique values
    return [...new Set(filteredLocations)];
  };

  const getVats = (catalogSpecific: {
    [catalogId: string]: {
      catalogId: string;
      price: number;
      vat: number;
    };
  }): number[] => {
    if (!catalogSpecific) return [];
    // Extract the vat values from the catalogs object
    // and return them as an array with unique values
    const vats = Object.values(catalogSpecific)
      .map(catalog => catalog.vat)
      .sort((a, b) => a - b);
    return [...new Set(vats)];
  };

  const getPriceRange = (catalogSpecific: {
    [catalogId: string]: {
      catalogId: string;
      price: number;
      vat: number;
    };
  }): number[] => {
    if (!catalogSpecific) return [];
    const prices = Object.values(catalogSpecific).map(catalog => catalog.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    if (minPrice === maxPrice) return [minPrice, maxPrice];
    return [minPrice, maxPrice];
  };

  const filters = [
    <CustomSearchInput
      placeholder={t('dashboard.headerSearchPlaceholder')}
      key="search-input"
      source="q"
      alwaysOn
    />,
    <SelectArrayInput
      key="categories-select"
      source="groupId_eq_any"
      label={t('itemLibrary.categories')}
      choices={categories ?? []}
      alwaysOn
    />,
  ];

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('sm'));

  const ListActions = () => (
    <TopToolbar>
      {!isSmall && <CustomColumnsButton />}
      <FilterButton />
      <ExportMenuButton contentRef={contentRef} />
    </TopToolbar>
  );

  const exporter = async (
    itemsLibrary: any[],
    fetchRelatedRecords: FetchRelatedRecords
  ) => {
    // will call dataProvider.getMany('posts', { ids: records.map(record => record.post_id) }),
    // ignoring duplicate and empty post_id
    const categories = await fetchRelatedRecords(
      itemsLibrary,
      'groupId',
      RESOURCES.HOSPITALITY_CATEGORIES
    );
    const itemsLibraryWithPostTitle = itemsLibrary.map(item => ({
      name: item.name,
      measureUnit: item.measureUnit,
      category: categories[item.groupId].name,
      location: getLocations(item.catalogSpecific),
      price: getPriceRange(item.catalogSpecific),
      vat: getVats(item.catalogSpecific),
      sku: item.sku,
      gtin: item.gtin,
      ean: item.ean,
    }));
    interface ExportItem {
      name: string;
      measureUnit: string;
      category: string;
      location: string;
      price: number[];
      vat: number[];
      sku: string;
      gtin: string;
      ean: string;
    }

    interface JsonExportOptions {
      headers: string[];
    }

    // Create CSV with title row
    const title = 'Items Library Export';
    const emptyRow = '';
    const headers = [
      'name',
      'measureUnit',
      'category',
      'location',
      'price_min',
      'price_max',
      'vat_min',
      'vat_max',
      'sku',
      'gtin',
      'ean',
    ];
    const csvContent = [
      title, // Title row
      emptyRow,
      headers.join(','), // Headers row
      ...itemsLibraryWithPostTitle.map(item =>
        [
          item.name,
          item.measureUnit,
          item.category,
          item.location,
          item.price,
          item.vat,
          item.sku,
          item.gtin,
          item.ean,
        ].join(',')
      ),
    ].join('\n');

    downloadCSV(csvContent, 'itemsLibrary');
  };

  return (
    <InfiniteList
      resource={RESOURCES.HOSPITALITY_ITEMS}
      component="div"
      exporter={exporter}
      filters={filters}
      sort={resourcesInfo[RESOURCES.HOSPITALITY_ITEMS].defaultSort}
      filter={{ _d: false }}
      perPage={25}
      actions={<ListActions />}
      sx={{
        '& .RaFilterFormInput-spacer': {
          display: { xs: 'none', md: 'block' },
        },
      }}
    >
      {isSmall ? (
        <MobileGrid>
          <MobileCard>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('itemsLibrary.items-unit')}
              </Typography>
              <FunctionField
                label={t('itemsLibrary.items-unit')}
                textAlign="center"
                render={record => {
                  const measureUnit = getValueFromRecord(
                    measureUnitsIdToSymbol,
                    record.measureUnit
                  );
                  return (
                    <ValueFieldWithError
                      value={measureUnit}
                      errorMessage={t('itemsLibrary.missingUnit')}
                    />
                  );
                }}
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('shared.category_capitalize')}
              </Typography>
              <FunctionField
                label={t('shared.category_capitalize')}
                textAlign="right"
                render={record => {
                  const category = getValueFromRecord(
                    categoryIdToName,
                    record.groupId
                  );
                  return (
                    <ValueFieldWithError
                      value={category}
                      errorMessage={t('itemsLibrary.missingCategory')}
                    />
                  );
                }}
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('shared.location_few')}
              </Typography>
              <FunctionField
                render={record =>
                  getLocations(record.catalogSpecific).join(', ')
                }
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('shared.price')}
              </Typography>
              <FunctionField
                label={t('shared.price')}
                textAlign="right"
                render={record => {
                  const priceRange = getPriceRange(record.catalogSpecific);
                  let minPrice = undefined;
                  let maxPrice = undefined;
                  if (priceRange.length === 2) {
                    minPrice = priceRange[0];
                    maxPrice = priceRange[1];
                  }
                  return (
                    <CurrencyRangeField
                      min={minPrice}
                      max={maxPrice}
                      locales={'ro-RO'}
                      currency="RON"
                      isStoredInCents
                    />
                  );
                }}
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('shared.tva')}
              </Typography>
              <FunctionField
                label={t('shared.tva')}
                render={record =>
                  getVats(record.catalogSpecific)
                    .map(vat => `${vat}%`)
                    .join(', ')
                }
              />
            </Box>
          </MobileCard>
        </MobileGrid>
      ) : (
        <DataTable
          hiddenColumns={['sku', 'gtin', 'ean']}
          rowSx={postRowStyle}
          sx={{
            '& .RaBulkActionsToolbar-topToolbar': {
              backgroundColor: 'transparent',
            },
            marginTop: '10px',
            '& .RaDataTable-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
          }}
          bulkActionButtons={<AssetBulkActionButtons />}
        >
          <DataTable.Col source="images" label=" " align="center">
            <FunctionField
              textAlign="center"
              render={useCallback((record: any) => {
                // Create a stable key that changes when image data changes
                // This forces React to re-render and clear caches when images are updated
                const imageKey =
                  record.images && record.images.length > 0
                    ? `${record.id}-${record.images[0].f}-${record.images[0].e}-${record.lastUpdatedAt || Date.now()}`
                    : `${record.id}-no-image-${record.lastUpdatedAt || Date.now()}`;

                return (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '100%',
                      minHeight: 40,
                    }}
                  >
                    <FilePreviewInline
                      key={imageKey}
                      files={record.images || []}
                      borderRadius={1.5}
                      imageVariant="thumbnail"
                      fileType="images"
                    />
                  </Box>
                );
              }, [])}
            />
          </DataTable.Col>
          <DataTable.Col source="name" label={t('shared.name')} />
          <DataTable.Col label={t('itemsLibrary.items-unit')} align="right">
            <FunctionField
              textAlign="center"
              render={record => {
                const measureUnit = getValueFromRecord(
                  measureUnitsIdToSymbol,
                  record.measureUnit
                );
                return (
                  <ValueFieldWithError
                    value={measureUnit}
                    errorMessage={t('itemsLibrary.missingUnit')}
                  />
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('shared.category_capitalize')} align="right">
            <FunctionField
              textAlign="right"
              render={record => {
                const category = getValueFromRecord(
                  categoryIdToName,
                  record.groupId
                );
                return (
                  <ValueFieldWithError
                    value={category}
                    errorMessage={t('itemsLibrary.missingCategory')}
                  />
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('shared.location_few')} align="right">
            <FunctionField
              textAlign="right"
              render={record => {
                const locations = getLocations(record.catalogSpecific);
                if (!locations || locations.length === 0) return null;
                return (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 1,
                      flexWrap: 'wrap',
                    }}
                  >
                    {locations.map(location => (
                      <Pill key={`location-${record.id}-${location}`}>
                        <Typography variant="body2">{location}</Typography>
                      </Pill>
                    ))}
                  </Box>
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col source="sku" label="SKU" align="right" />
          <DataTable.Col source="gtin" label="GTIN" align="right" />
          <DataTable.Col source="ean" label="EAN" align="right" />
          <DataTable.Col label={t('shared.price')} align="right">
            <FunctionField
              textAlign="right"
              render={record => {
                const priceRange = getPriceRange(record.catalogSpecific);
                let minPrice = undefined;
                let maxPrice = undefined;
                if (priceRange.length === 2) {
                  minPrice = priceRange[0];
                  maxPrice = priceRange[1];
                }
                return (
                  <CurrencyRangeField
                    min={minPrice}
                    max={maxPrice}
                    locales={'ro-RO'}
                    currency="RON"
                    isStoredInCents
                  />
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('shared.tva')} align="right">
            <FunctionField
              textAlign="right"
              render={record => {
                const vats = getVats(record.catalogSpecific);
                // If no VATs, return null
                if (!vats || vats.length === 0) return null;
                // Return a fragment containing all the Pills with proper spacing
                return (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 0.5,
                      flexWrap: 'wrap',
                    }}
                  >
                    {vats.map(vat => (
                      <Pill key={`vat-${record.id}-${vat}`}>
                        <Typography variant="body2">{`${vat}%`}</Typography>
                      </Pill>
                    ))}
                  </Box>
                );
              }}
            />
          </DataTable.Col>
          <DataTable.Col label={t('prepStations.actions')} align="right">
            <ActionsField
              hasEdit={true}
              hasDelete={true}
              deleteMutationMode="optimistic"
            />
          </DataTable.Col>
        </DataTable>
      )}
      <ListLiveUpdate />
    </InfiniteList>
  );
};
