import { Box, Typography } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  SimpleForm,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { useAvailableCategories } from '~/hooks';
import { validateName } from '~/utils/validateName';

const PrepStationsCreateInner = () => {
  const resource = useResourceContext();
  const redirect = useRedirect();
  const { t } = useTranslation('');
  const { availableCategories, isLoading } = useAvailableCategories();

  const handleClose = () => {
    redirect('list', resource, undefined, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('prepStations.createPrepStation')}
      >
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <CustomInput
            source="name"
            type="text"
            ui="custom"
            sanitize="singleLine"
            label={t('shared.name')}
            validate={[required(), validateName]}
            roundedCorners="top"
          />
          {availableCategories.length > 0 ? (
            <CustomInput
              source="groupIds"
              choices={availableCategories}
              type="selectArray"
              ui="custom"
              label={t('prepStations.tags')}
              placeholder={t('shared.none', { context: 'female' })}
              optionText="name"
              optionValue="id"
              validate={[required()]}
              disabled={isLoading}
              roundedCorners="bottom"
            />
          ) : (
            <>
              <CustomInput
                source="groupIds"
                choices={[]}
                type="selectArray"
                ui="custom"
                label={t('prepStations.tags')}
                optionText="name"
                optionValue="id"
                validate={[required()]}
                disabled={true}
                roundedCorners="bottom"
              />
              {!isLoading && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: 'block' }}
                >
                  {t('prepStations.allCategoriesAssigned')}
                </Typography>
              )}
            </>
          )}
        </Box>
      </Box>
    </>
  );
};

export const PrepStationsCreate = () => {
  const transform = (data: any) => {
    return {
      ...data,
      groups: data.groupIds,
    };
  };

  return (
    <CreateDialog maxWidth="sm" fullWidth transform={transform}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <PrepStationsCreateInner />
      </SimpleForm>
    </CreateDialog>
  );
};
