import { Box } from '@mui/material';

import { useOptionalTrackedField } from '~/hooks/useConflictDetection';
import { PermissionsParts } from './index';
import PermissionCreateStep1 from './PermissionCreateStep1';
import PermissionCreateStep2 from './PermissionCreateStep2';

interface PermissionFormProps {
  step: number;
  setStep: (step: number) => void;
  edit?: boolean;
}
function PermissionsForm({ edit, step, setStep }: PermissionFormProps) {
  // Track fields for conflict detection (always mounted regardless of step)
  useOptionalTrackedField('permissions');
  useOptionalTrackedField('access');

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '800px',
        width: '90%',
        mx: 'auto',
        my: 1,
        gap: 6,
      }}
    >
      <PermissionsParts.Breadcrumbs step={step} setStep={setStep} />

      {step === 0 && <PermissionCreateStep1 edit={edit} />}
      {step === 1 && <PermissionCreateStep2 />}
    </Box>
  );
}

export { PermissionsForm };
