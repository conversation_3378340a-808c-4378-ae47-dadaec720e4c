import { memo, useCallback, useMemo, useState } from 'react';
import { Box, Button, Checkbox, Dialog, Typography } from '@mui/material';
import { SelectArrayInput } from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomSearchInput from '~/components/atoms/inputs/CustomSearchInput';
import {
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
} from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';
import { useTheme } from '../../contexts';

const ItemRow = memo(
  ({
    item,
    isSelected,
    onToggle,
  }: {
    item: any;
    isSelected: boolean;
    onToggle: (id: string) => void;
  }) => {
    const handleClick = useCallback(() => {
      onToggle(item.id);
    }, [item.id, onToggle]);

    return (
      <Box
        sx={{
          display: 'flex',
          borderBottom: '1px solid',
          borderColor: 'custom.gray400',
          backgroundColor: 'custom.fieldBg',
          width: '100%',
          height: '50px',
          alignItems: 'center',
          justifyContent: 'start',
        }}
      >
        <Checkbox checked={isSelected} onClick={handleClick} />
        <Typography variant="body1" fontWeight={300} fontSize={14}>
          {item.name}
        </Typography>
      </Box>
    );
  }
);

export const ItemsDialog = memo(
  ({
    open,
    selectedItems,
    onClose,
  }: {
    open: boolean;
    selectedItems: any;
    setSelectedItems: (selectedItems: any) => void;
    onClose: (selectedItems: any) => void;
  }) => {
    const { t } = useTranslation();
    const { theme } = useTheme();
    const { data: items } = useGetListHospitalityItemsLive({
      filter: { _d: false },
    });

    const [localSelectedItems, setLocalSelectedItems] =
      useState<string[]>(selectedItems);

    const [searchQuery, setSearchQuery] = useState<string>('');
    const [categoriesFilter, setCategoriesFilter] = useState<string[]>([]);

    const { data: categories } = useGetListHospitalityCategoriesLive({
      filter: { _d: false },
    });

    const handleToggle = useCallback((id: string) => {
      setLocalSelectedItems(prev => {
        if (prev.includes(id)) {
          return prev.filter(itemId => itemId !== id);
        } else {
          return [...prev, id];
        }
      });
    }, []);

    const filteredItems = useMemo(() => {
      return (
        items
          ?.filter((item: any) =>
            searchQuery.length > 2
              ? item.name.toLowerCase().includes(searchQuery.toLowerCase())
              : true
          )
          ?.filter((item: any) =>
            categoriesFilter.length > 0
              ? categoriesFilter.includes(item.groupId)
              : true
          ) || []
      );
    }, [items, searchQuery, categoriesFilter]);

    const handleSelectAll = useCallback(() => {
      const filteredItemIds = filteredItems.map((item: any) => item.id);
      const allFilteredSelected = filteredItemIds.every(id =>
        localSelectedItems.includes(id)
      );

      if (allFilteredSelected && filteredItemIds.length > 0) {
        setLocalSelectedItems(prev =>
          prev.filter(id => !filteredItemIds.includes(id))
        );
      } else {
        setLocalSelectedItems(prev => {
          const newSelection = [...prev];
          filteredItemIds.forEach(id => {
            if (!newSelection.includes(id)) {
              newSelection.push(id);
            }
          });
          return newSelection;
        });
      }
    }, [filteredItems, localSelectedItems]);

    return (
      <Dialog
        open={open}
        onClose={() => onClose(selectedItems)}
        maxWidth="md"
        fullWidth
      >
        <ModalHeader
          handleClose={() => onClose(selectedItems)}
          title={t('extraCharges.selectItems')}
        >
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              onClose(localSelectedItems);
            }}
          >
            {t('shared.done')}
          </Button>
        </ModalHeader>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            px: 2,
            py: { xs: 0, md: 2 },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column-reverse', md: 'row' },
              gap: { xs: 0, md: 2 },
            }}
          >
            <SelectArrayInput
              sx={{ maxWidth: { xs: '100%', md: '200px' } }}
              key="categories-select"
              source="__categoriesFilter"
              label={t('itemLibrary.categories')}
              choices={categories ?? []}
              alwaysOn
              onChange={e => setCategoriesFilter(e.target.value)}
            />
            <CustomSearchInput
              placeholder={t('dashboard.headerSearchPlaceholder')}
              key="search-input"
              source="__searchQuery"
              alwaysOn
              onChange={e => setSearchQuery(e.target.value)}
            />
          </Box>

          <Box
            sx={{
              display: 'flex',
              borderBottom: '1px solid',
              borderColor: 'custom.gray400',
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              width: '100%',
              height: '50px',
              alignItems: 'center',
              justifyContent: 'start',
              marginTop: { xs: 0, md: 2 },
            }}
          >
            <Checkbox
              checked={
                filteredItems.length > 0 &&
                filteredItems.every((item: any) =>
                  localSelectedItems.includes(item.id)
                )
              }
              indeterminate={
                filteredItems.some((item: any) =>
                  localSelectedItems.includes(item.id)
                ) &&
                !filteredItems.every((item: any) =>
                  localSelectedItems.includes(item.id)
                )
              }
              onClick={handleSelectAll}
            />
            <Typography variant="body1" fontWeight={700} fontSize={14}>
              {t('shared.items_capitalize')}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            {filteredItems.map((item: any) => (
              <ItemRow
                key={item.id}
                item={item}
                isSelected={localSelectedItems.includes(item.id)}
                onToggle={handleToggle}
              />
            ))}
          </Box>
        </Box>
      </Dialog>
    );
  }
);
