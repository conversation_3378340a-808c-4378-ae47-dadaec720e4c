import { useCallback, useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useGetList,
  useRedirect,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useUniqueCustom } from '~/hooks/useUniqueCustom';
import { RESOURCES, useGetListTeamMembersLive } from '~/providers/resources';
import { CustomInput } from '../../components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';
import FloorPlanTabSelector from '../../components/organisms/table-selection/FloorPlanTabSelector';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';

function TeamMemberWorkingAreaCreateInner({
  sellPointId,
}: {
  sellPointId: string;
}) {
  const { t } = useTranslation('');
  const redirect = useRedirect();
  const unique = useUniqueCustom({ meta: { sellPointId } });
  const { setValue, watch } = useFormContext();

  // Get all team members
  const { data: teamMembers } = useGetListTeamMembersLive();

  // Get existing working area assignments
  const { data: existingWorkingAreas } = useGetList(
    RESOURCES.TEAM_MEMBERS_WORKING_AREA,
    {
      filter: { sellPointId },
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
      meta: { sellPointId },
    }
  );

  // Filter out team members who already have working areas
  const availableTeamMembers = useMemo(() => {
    if (!teamMembers || !existingWorkingAreas) return [];

    const assignedMemberIds = new Set(existingWorkingAreas.map(wa => wa.id));
    return teamMembers.filter(member => !assignedMemberIds.has(member.id));
  }, [teamMembers, existingWorkingAreas]);

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.TEAM_MEMBERS_WORKING_AREA);
  }, [redirect]);

  const handleAreaItemsChange = useCallback(
    (areaItems: any[]) => {
      setValue('areaItems', areaItems, {
        shouldDirty: true,
        shouldValidate: true,
      });
    },
    [setValue]
  );

  const currentAreaItems = watch('areaItems') || [];

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('workingArea.createWorkingArea')}
      >
        <SaveButton
          type="button"
          icon={<></>}
          alwaysEnable
          label={t('shared.save')}
        />
      </ModalHeader>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '1200px',
          width: '95%',
          mx: 'auto',
          my: 4,
          gap: 4,
        }}
      >
        <CustomInput
          source="id"
          type="autocomplete"
          label={t('workingArea.member')}
          optionText="displayName"
          optionValue="id"
          validate={[required(), unique()]}
          choices={availableTeamMembers}
        />
        <FloorPlanTabSelector
          sellPointId={sellPointId}
          value={currentAreaItems}
          onChange={handleAreaItemsChange}
          excludeAssignedTables={true}
          teamMembers={teamMembers}
          existingWorkingAreas={existingWorkingAreas}
        />
      </Box>
    </>
  );
}

export const TeamMemberWorkingAreaCreate = (props: { sellPointId: string }) => {
  const { sellPointId } = props;

  return (
    <CreateDialog
      {...getFullscreenModalProps()}
      mutationOptions={{
        meta: {
          sellPointId: sellPointId,
        },
      }}
    >
      <SimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        defaultValues={{
          sellPointId,
          areaItems: [],
        }}
      >
        <TeamMemberWorkingAreaCreateInner sellPointId={sellPointId} />
      </SimpleForm>
    </CreateDialog>
  );
};
