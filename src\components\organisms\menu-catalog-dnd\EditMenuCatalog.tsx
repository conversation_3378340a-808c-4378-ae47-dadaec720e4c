import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import {
  required,
  SaveButton,
  SimpleForm,
  useNotify,
  useRedirect,
  useUpdate,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { RESOURCES, useGetListLocationsLive } from '~/providers/resources';

interface EditMenuDialogProps {
  menuId: number;
  onClose: () => void;
}

export default function EditMenuDialog({
  menuId,
  onClose,
}: EditMenuDialogProps) {
  const notify = useNotify();
  const redirect = useRedirect();
  const { data: sellPoints } = useGetListLocationsLive({
    filter: { _d: false },
  });
  const { t } = useTranslation();

  const [update] = useUpdate();

  const handleSubmit = (values: any) => {
    update(
      RESOURCES.HOSPITALITY_CATALOGS,
      { id: menuId, data: values, previousData: {} },
      {
        onSuccess: () => {
          notify('Menu updated', { type: 'success' });
          redirect('list', RESOURCES.HOSPITALITY_CATALOGS);
          onClose();
        },
        onError: error => {
          notify(`Error: ${error.message}`, { type: 'error' });
        },
      }
    );
  };

  return (
    <Dialog open onClose={onClose} maxWidth="sm" fullWidth>
      <ModalHeader handleClose={onClose} title={t('menu.editMenu')}>
        <SaveButton
          form="edit-menu-form"
          alwaysEnable
          icon={<></>}
          sx={{ width: '20%' }}
        />
      </ModalHeader>

      <DialogContent sx={{ padding: 0 }}>
        <SimpleForm
          id="edit-menu-form"
          toolbar={false}
          onSubmit={handleSubmit}
          defaultValues={{ id: menuId }}
        >
          <Box display="flex" flexDirection="column" sx={{ width: '100%' }}>
            <CustomInput
              source="name"
              label={t('menu.menuName')}
              validate={[required()]}
              type="text"
              ui="custom"
              sanitize="singleLine"
              roundedCorners="top"
            />
            <CustomInput
              source="sellPointIds"
              choices={sellPoints}
              type="selectArray"
              label={t('shared.location')}
              optionText="name"
              optionValue="id"
              placeholder={t('shared.none', { context: 'female' })}
              validate={[required()]}
              roundedCorners="bottom"
            />
          </Box>
        </SimpleForm>
      </DialogContent>
    </Dialog>
  );
}
