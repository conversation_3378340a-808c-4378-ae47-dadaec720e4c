/**
 * Client-side image processing utilities for generating thumbnails and variants
 * This replaces backend image processing with pure client-side processing
 */

import { StandardImageSize } from '~/configs/imageSize';

export interface ImageVariant {
  file: File;
  size: StandardImageSize;
  originalFile: File;
}

export interface ProcessedImageResult {
  original: File;
  variants: ImageVariant[];
  thumbnail: File; // Always generated for display purposes
}

/**
 * Convert a File to an Image element
 */
const fileToImage = (file: File): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Detect if an image has transparency by checking alpha channel
 */
const hasTransparency = (img: HTMLImageElement): boolean => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    return false;
  }

  canvas.width = img.width;
  canvas.height = img.height;

  // Draw the image on canvas
  ctx.drawImage(img, 0, 0);

  try {
    // Get image data to check alpha channel
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Check every 4th byte (alpha channel) in the image data
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] < 255) {
        // Found a pixel with transparency (alpha < 255)
        return true;
      }
    }

    return false; // No transparency found
  } catch (error) {
    // If we can't read image data (e.g., CORS issues), assume no transparency
    return false;
  }
};

/**
 * Create a canvas with the specified dimensions and fit the image while preserving aspect ratio
 * Centers the image in the canvas, leaving empty space if needed
 */
const createResizedCanvasWithAspectRatio = (
  img: HTMLImageElement,
  width: number,
  height: number,
  quality: number = 0.9
): HTMLCanvasElement => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Could not get canvas context');
  }

  canvas.width = width;
  canvas.height = height;

  // Fill with transparent background
  ctx.clearRect(0, 0, width, height);

  // Calculate the scale factor to fit the image within the canvas while preserving aspect ratio
  const imgAspectRatio = img.width / img.height;
  const canvasAspectRatio = width / height;

  let drawWidth = width;
  let drawHeight = height;
  let drawX = 0;
  let drawY = 0;

  if (imgAspectRatio > canvasAspectRatio) {
    // Image is wider than canvas - fit by width, center vertically
    drawHeight = width / imgAspectRatio;
    drawY = (height - drawHeight) / 2;
  } else {
    // Image is taller than canvas - fit by height, center horizontally
    drawWidth = height * imgAspectRatio;
    drawX = (width - drawWidth) / 2;
  }

  // Enable high-quality image scaling
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  // Draw the image centered and fitted
  ctx.drawImage(
    img,
    0,
    0,
    img.width,
    img.height,
    drawX,
    drawY,
    drawWidth,
    drawHeight
  );

  return canvas;
};

/**
 * Create a canvas with the specified dimensions and draw the image
 */
const createResizedCanvas = (
  img: HTMLImageElement,
  width: number,
  height: number,
  quality: number = 0.9
): HTMLCanvasElement => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Could not get canvas context');
  }

  canvas.width = width;
  canvas.height = height;

  // Calculate the best fit for the image while maintaining aspect ratio
  const imgAspectRatio = img.width / img.height;
  const targetAspectRatio = width / height;

  let sourceX = 0;
  let sourceY = 0;
  let sourceWidth = img.width;
  let sourceHeight = img.height;

  if (imgAspectRatio > targetAspectRatio) {
    // Image is wider than target - crop width
    sourceWidth = img.height * targetAspectRatio;
    sourceX = (img.width - sourceWidth) / 2;
  } else {
    // Image is taller than target - crop height
    sourceHeight = img.width / targetAspectRatio;
    sourceY = (img.height - sourceHeight) / 2;
  }

  // Enable high-quality image scaling
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  // Draw the image with center cropping
  ctx.drawImage(
    img,
    sourceX,
    sourceY,
    sourceWidth,
    sourceHeight,
    0,
    0,
    width,
    height
  );

  return canvas;
};

/**
 * Convert canvas to File with specified format and quality
 * For transparent images with WebP format, uses lossless compression to preserve transparency
 */
const canvasToFile = (
  canvas: HTMLCanvasElement,
  filename: string,
  format: 'jpeg' | 'png' | 'webp' = 'jpeg',
  quality: number = 0.9,
  hasTransparency: boolean = false
): Promise<File> => {
  return new Promise(resolve => {
    const mimeType =
      format === 'jpeg'
        ? 'image/jpeg'
        : format === 'png'
          ? 'image/png'
          : 'image/webp';

    // For WebP with transparency, use lossless compression (quality = 1.0)
    // to preserve the alpha channel
    const finalQuality = format === 'webp' && hasTransparency ? 1.0 : quality;

    canvas.toBlob(
      blob => {
        if (!blob) {
          throw new Error('Failed to create blob from canvas');
        }

        const file = new File([blob], filename, { type: mimeType });
        resolve(file);
      },
      mimeType,
      finalQuality
    );
  });
};

/**
 * Generate a thumbnail (always 50x50 for consistency)
 * Preserves aspect ratio and centers the image in the square canvas
 * Uses lossless WebP for images with transparency
 */
export const generateThumbnail = async (
  originalFile: File,
  quality: number = 0.8
): Promise<File> => {
  const img = await fileToImage(originalFile);

  // Check if the original image has transparency
  const imageHasTransparency = hasTransparency(img);

  const canvas = createResizedCanvasWithAspectRatio(img, 50, 50, quality);

  // Clean up the object URL
  URL.revokeObjectURL(img.src);

  const thumbnailFilename = originalFile.name.replace(
    /\.[^/.]+$/,
    '_thumbnail.webp'
  );
  return canvasToFile(
    canvas,
    thumbnailFilename,
    'webp',
    quality,
    imageHasTransparency
  );
};

/**
 * Generate a specific size variant of an image
 * Uses lossless WebP for images with transparency
 */
export const generateImageVariant = async (
  originalFile: File,
  targetSize: StandardImageSize,
  quality: number = 0.9
): Promise<File> => {
  const img = await fileToImage(originalFile);

  // Check if the original image has transparency
  const imageHasTransparency = hasTransparency(img);

  const canvas = createResizedCanvas(
    img,
    targetSize.width,
    targetSize.height,
    quality
  );

  // Clean up the object URL
  URL.revokeObjectURL(img.src);

  const variantFilename = originalFile.name.replace(
    /\.[^/.]+$/,
    `_${targetSize.key}.webp`
  );

  return canvasToFile(
    canvas,
    variantFilename,
    'webp',
    quality,
    imageHasTransparency
  );
};

/**
 * Process an image file to generate all required variants
 */
export const processImageWithVariants = async (
  originalFile: File,
  targetSizes: StandardImageSize[] = [],
  quality: number = 0.9
): Promise<ProcessedImageResult> => {
  // Always generate a thumbnail for display purposes
  const thumbnail = await generateThumbnail(originalFile, 0.8);

  // Generate all requested variants
  const variants: ImageVariant[] = [];

  for (const size of targetSizes) {
    try {
      const variantFile = await generateImageVariant(
        originalFile,
        size,
        quality
      );
      variants.push({
        file: variantFile,
        size,
        originalFile,
      });
    } catch (error) {
      // Failed to generate variant - continue with others
    }
  }

  return {
    original: originalFile,
    variants,
    thumbnail,
  };
};

/**
 * Batch process multiple images
 */
export const processMultipleImages = async (
  files: File[],
  targetSizes: StandardImageSize[] = [],
  quality: number = 0.9,
  onProgress?: (progress: number, current: number, total: number) => void
): Promise<ProcessedImageResult[]> => {
  const results: ProcessedImageResult[] = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    onProgress?.(((i + 1) / files.length) * 100, i + 1, files.length);

    try {
      const result = await processImageWithVariants(file, targetSizes, quality);
      results.push(result);
    } catch (error) {
      // Failed to process image - continue with next
    }
  }

  return results;
};

/**
 * Utility to get file extension from mime type
 */
export const getExtensionFromMimeType = (mimeType: string): string => {
  const map: Record<string, string> = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/webp': 'webp',
    'image/gif': 'gif',
  };

  return map[mimeType] || 'jpg';
};

/**
 * Create a filename for a variant
 */
export const createVariantFilename = (
  originalFilename: string,
  variantKey: string,
  extension: string = 'jpg'
): string => {
  const baseName = originalFilename.replace(/\.[^/.]+$/, '');
  return `${baseName}_${variantKey}.${extension}`;
};

/**
 * Check if an image file has transparency
 * This is a public utility function that can be used elsewhere
 */
export const checkImageTransparency = async (file: File): Promise<boolean> => {
  try {
    const img = await fileToImage(file);
    const result = hasTransparency(img);

    // Clean up the object URL
    URL.revokeObjectURL(img.src);

    return result;
  } catch (error) {
    // If we can't process the image, assume no transparency
    return false;
  }
};
