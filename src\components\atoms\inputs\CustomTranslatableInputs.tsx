import { useTheme } from '@mui/material/styles';
import { TranslatableInputs, TranslatableInputsProps } from 'react-admin';

type RoundedCorners = 'top' | 'bottom' | 'both' | 'none';

interface CustomTranslatableInputsProps extends TranslatableInputsProps {
  roundedCorners?: RoundedCorners;
}

/**
 * CustomTranslatableInputs - Minimal wrapper for TranslatableInputs
 *
 * Removes rounded corners and padding from the container.
 * Supports optional rounded corners via roundedCorners prop.
 *
 * @example
 * <CustomTranslatableInputs locales={['en', 'ro']} roundedCorners="both">
 *   <CustomInput type="text" source="publicName" ui="custom" />
 * </CustomTranslatableInputs>
 */
export const CustomTranslatableInputs = ({
  locales,
  children,
  sx,
  roundedCorners,
  ...props
}: CustomTranslatableInputsProps) => {
  const theme = useTheme();
  const custom = (theme.palette as any).custom;

  // Calculate border radius based on roundedCorners prop
  const getBorderRadius = () => {
    if (!roundedCorners) return {};
    const radius = '6px'; // Standard app-wide border radius

    switch (roundedCorners) {
      case 'top':
        return {
          borderTopLeftRadius: `${radius} !important`,
          borderTopRightRadius: `${radius} !important`,
        };
      case 'bottom':
        return {
          borderBottomLeftRadius: `${radius} !important`,
          borderBottomRightRadius: `${radius} !important`,
        };
      case 'both':
        return {
          borderRadius: `${radius} !important`,
        };
      case 'none':
      default:
        return {};
    }
  };
  return (
    <TranslatableInputs
      locales={locales}
      sx={{
        // Remove default rounded corners
        borderRadius: '0 !important',
        // Remove all padding
        p: '0 !important',
        overflow: 'hidden', // Ensure rounded corners clip content properly
        // Apply border radius to the main container
        ...getBorderRadius(),
        // Target the tabs container (the wrapper with tabs + content)
        '& .RaTranslatableInputsTabs-root': {
          borderRadius: '0 !important',
          overflow: 'hidden',
          border: `1px solid ${custom.gray400}`,
          borderBottom: 'none',
          ...getBorderRadius(),
        },
        // Target the MUI Tabs component (the tab buttons)
        '& .MuiTabs-root': {
          minHeight: '45px !important',
          border: 'none',
          backgroundColor: 'custom.modalHeader',
          ...(roundedCorners === 'top' || roundedCorners === 'both'
            ? {
                borderTopLeftRadius: '6px !important',
                borderTopRightRadius: '6px !important',
              }
            : {}),
        },
        // Target individual tab buttons
        '& .MuiTab-root': {
          minHeight: '45px !important',
        },
        // Target the first tab to round top-left corner
        '& .MuiTab-root:first-of-type': {
          ...(roundedCorners === 'top' || roundedCorners === 'both'
            ? {
                borderTopLeftRadius: '6px !important',
              }
            : {}),
        },
        // Target the tab content container specifically
        '& .RaTranslatableInputsTabContent-root': {
          p: '0 !important',
          paddingLeft: '0 !important',
          paddingRight: '0 !important',
          paddingTop: '0 !important',
          paddingBottom: '0 !important',
          border: 'none !important',
          borderRadius: '0 !important',
          borderBottomLeftRadius: '0 !important',
          borderBottomRightRadius: '0 !important',
          // Remove top margin from first input to merge borders
          '& > div:first-of-type': {
            marginTop: '-1px !important',
          },
        },
        ...sx,
      }}
      {...props}
    >
      {children}
    </TranslatableInputs>
  );
};
