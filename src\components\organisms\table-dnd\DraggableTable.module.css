.Draggable {
  position: absolute;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.Draggable > .innerDiv {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  appearance: none;
  outline: none;
  background-color: var(--bg-color);
  transform: translate3d(var(--translate-x, 0), var(--translate-y, 0), 0)
    scale(var(--scale, 1));
  border-radius: var(--border-radius);
  z-index: var(--z-index);
  cursor: grabbing;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px, var(--box-shadow, 0 0 0 0 transparent);
  overflow: hidden;
}

.Draggable label {
  text-align: center;
  font-size: 14px;
  font-weight: 300;
  color: white;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
  cursor: grabbing;
  pointer-events: none;
}
