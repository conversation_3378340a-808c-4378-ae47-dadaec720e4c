import { UploadedFile } from '../types/fileUpload';

// ==========================================
// FILE PREVIEW UTILITIES
// ==========================================

/**
 * Check if a file is an image based on its extension
 */
export const isImageFile = (file: UploadedFile): boolean => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
  return imageExtensions.includes(file.e.toLowerCase());
};

/**
 * Check if a file is a video based on its extension
 */
export const isVideoFile = (file: UploadedFile): boolean => {
  const videoExtensions = [
    'mp4',
    'webm',
    'ogg',
    'avi',
    'mov',
    'wmv',
    'flv',
    'm4v',
  ];
  return videoExtensions.includes(file.e.toLowerCase());
};

/**
 * Check if a file can be previewed (is an image or video)
 * Note: URL availability is now handled by the useFileUrl hook
 */
export const canPreviewFile = (file: UploadedFile): boolean => {
  return isImageFile(file) || isVideoFile(file);
};
