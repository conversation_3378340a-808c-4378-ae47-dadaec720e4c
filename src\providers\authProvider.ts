/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    // GoogleAuthProvider, // Commented: Now handled in auth.html
    // signInWithEmailAndPassword, // Commented: Now handled in auth.html
    // signInWithPopup, // Commented: Now handled in auth.html
    // signInWithRedirect, // Commented: Now handled in auth.html
    onAuthStateChanged,
    signOut,
} from 'firebase/auth';
import { AuthProvider, UserIdentity } from 'react-admin';

import { auth } from '../configs/firebaseConfig';
import { accountStorage } from './utils/accountStorage';
import cleanupActions from './utils/cleanupActions';

// ============================================================================
// AUTHENTICATION ARCHITECTURE: Pre-React Auth Check
// ============================================================================
//
// This app checks authentication before loading React to optimize bundle size
// and improve user experience:
//
// 1. index.html initializes Firebase and checks auth state using onAuthStateChanged
// 2. If no auth exists, redirects to /auth page (auth.html)
// 3. auth.html handles ALL authentication flows:
//    - Google Sign-In with signInWithPopup (localhost)
//    - Google Sign-In with signInWithRedirect (production)
//    - Handles getRedirectResult() for production OAuth flow
//    - Email/password authentication
// 4. After successful auth, user is redirected back to main app
// 5. This authProvider only manages existing auth state (logout, checkAuth, etc.)
//
// IMPORTANT: According to Firebase documentation, when using signInWithRedirect:
// - You must call getRedirectResult(auth) after the page redirects back
// - This is handled in auth.html, NOT in the React app
// - The modular SDK returns null if no redirect operation occurred
// - Production requires proper authDomain configuration for third-party cookie blocking
//
// ============================================================================

/* COMMENTED OUT: Old implementation that handled auth within React app
 * This approach had issues:
 * - Loaded 3.5MB React bundle before checking authentication
 * - signInWithRedirect from authProvider didn't handle getRedirectResult properly
 * - Required duplicate auth logic in both React app and index.html
 *
interface LoginParams {
  email?: string;
  password?: string;
  method: 'email' | 'google';
}

const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({ prompt: 'select_account' });

const login = async ({
  email,
  password,
  method,
}: LoginParams): Promise<{ redirectTo?: string | boolean } | void> => {
  console.log('login called with params:', { email, password, method });
  try {
    if (method === 'google') {
      // Hybrid: popup for localhost, redirect for production (fixes Safari PWA)
      const isLocalhost =
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1';

      if (isLocalhost) {
        await signInWithPopup(auth, googleProvider);
      } else {
        // ISSUE: After signInWithRedirect, page redirects away immediately
        // When Firebase redirects back, we MUST call getRedirectResult()
        // This wasn't being handled properly in the React app lifecycle
        await signInWithRedirect(auth, googleProvider);
      }
    } else if (method === 'email' && email && password) {
      await signInWithEmailAndPassword(auth, email, password);
    } else {
      throw new Error('Invalid login method or missing credentials');
    }
    return Promise.resolve();
  } catch (error: any) {
    throw new Error('Login failed: ' + error.message);
  }
};
*/

// Minimal login implementation - actual authentication happens in auth.html
// This redirects to /auth page where all authentication is handled
const login = async (): Promise<{ redirectTo?: string | boolean } | void> => {
    console.log('[AuthProvider] login called - redirecting to /auth');
    const currentPath =
        window.location.pathname + window.location.search + window.location.hash;
    const returnUrl =
        currentPath !== '/' && currentPath !== '/login'
            ? '?returnUrl=' + encodeURIComponent(currentPath)
            : '';
    window.location.href = '/auth' + returnUrl;
    return Promise.resolve({ redirectTo: false });
};
// After logout, react-admin redirects the user to the string returned by authProvider.logout() or to the /login url if the method returns nothing.
// You can customize the redirection url by returning a route string, or false to disable redirection after logout.
const logout = async (params: any): Promise<string | void | false> => {
    console.log('[AuthProvider] logout called');
    cleanupActions.runAll();
    if (auth.currentUser) await signOut(auth);
    // Redirect to auth page
    window.location.href = '/auth';
    return Promise.resolve(false); // Prevent react-admin's default redirect
};

// Fortunately, each time the user navigates to a list, edit, create or show page, react-admin calls the authProvider.checkAuth() method.
// If this method throws an error, react-admin calls authProvider.logout() and redirects the user to the login page.
// So it’s the ideal place to make sure the credentials are still valid.
// When checkAuth() throws an error, react-admin redirects to the /login page by default.
// You can override this path by throwing an error with a redirectTo property
// If both authProvider.checkAuth() and authProvider.logout() return a redirect URL, the one from authProvider.checkAuth() takes precedence.
// When checkAuth() throws an error, react-admin displays a notification to the end user. You can customize this message by throwing an error with a particular message
// You can also disable this notification completely by rejecting an error with a false message.
const checkAuth = async (): Promise<void> => {
    console.log('[AuthProvider] checkAuth called, currentUser:', auth.currentUser?.email || 'null');

    // Due to gateway pattern architecture, Firebase auth is initialized in index-init.js
    // before the React app loads. If we reach this point, the user has already been authenticated
    // at the gateway level. The npm Firebase SDK instance needs a brief moment to restore auth
    // state from browser persistence (localStorage/IndexedDB).
    if (!auth.currentUser) {
        console.log('[AuthProvider] checkAuth: No current user yet, waiting for Firebase to restore from persistence...');

        // Wait up to 1 second for Firebase to restore auth state
        let attempts = 0;
        const maxAttempts = 4; // 4 attempts * 250ms = 1 second total

        while (!auth.currentUser && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 250));
            attempts++;
        }

        if (!auth.currentUser) {
            console.log('[AuthProvider] checkAuth: No user after waiting, rejecting');
            return Promise.reject(new Error('User not authenticated'));
        }

        console.log('[AuthProvider] checkAuth: User authenticated after waiting');
    }

    console.log('[AuthProvider] checkAuth: User authenticated, resolving');
    return Promise.resolve();
};

// Fortunately, each time the dataProvider returns an error, react-admin calls authProvider.checkError() to check if the error is an authentication error.
// If this method throws an error itself, react-admin calls the authProvider.logout() method immediately, and redirects the user to the login page.
// When checkError() throws an error, react-admin redirects to the /login page, or to the error.redirectTo url. That means you can override the default redirection
// It’s possible to not log the user out, and to instead redirect them. You can do this by passing error.logoutUser = false along with an error.redirectTo url.
// When checkError() throws an error, react-admin displays a notification to the end user, unless the error.message is false. That means you can disable or customize the notification on error.
const checkError = async (error: {
    message?: string;
    status?: number;
}): Promise<void> => {
    console.log('checkError called with error:', error);
};

// React-admin delegates the storage of the connected user identity to the authProvider.
// If it exposes a getIdentity() method, react-admin will call it to read the user details.
// getIdentity should return an object with at least an id field.
// You can also return a fullName and an avatar field, or any other field you need in your app
const getIdentity = async (): Promise<UserIdentity> => {
    const user = auth.currentUser;
    if (!user) {
        return Promise.reject(new Error('User not authenticated'));
    }
    return Promise.resolve({
        id: user.uid,
        fullName: user.displayName || user.email || 'N/A',
        avatar: user.photoURL || undefined,
        email: user.email || undefined,
    });
};

// getPermissions() lets you return an arbitrary permissions object.
// This object can be used by React components to enable or disable UI elements based on the user’s role.
// The permissions can be in any format: a simple string (e.g. 'editor'),
// an array of strings (e.g. ['editor', 'admin']),
// or a complex object (e.g. { posts: 'editor', comments: 'moderator', users: 'admin' }).
// If getPermissions() throws an error, the error will be passed to checkError
const getPermissions = async (): Promise<any> => {
    const user = auth.currentUser;
    if (!user) {
        return Promise.reject(new Error('User not authenticated'));
    }
    const cachedSelectedAccount = accountStorage.getSelectedAccount();
    if (cachedSelectedAccount === null) {
        return Promise.resolve({});
    }
    const idToken = await user.getIdTokenResult(false);
    const claims = idToken.claims as { a?: { [key: string]: any } };
    const accountPermissions = claims.a?.[cachedSelectedAccount] ?? {};
    return Promise.resolve(accountPermissions);
};

export const authProvider: AuthProvider = {
    login,
    logout,
    checkAuth,
    checkError,
    getIdentity,
    getPermissions,
};
