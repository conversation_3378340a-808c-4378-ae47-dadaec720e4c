import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Box,
  CircularProgress,
  LinearProgress,
  useMediaQuery,
} from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { FieldOption } from '~/components/organisms/CustomTable/types/globals';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useReportRawData } from '~/hooks/useReportRawData';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import { useGetListLocationsLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import cleanStringArond from '~/utils/cleanStringArond';
import { CurrencyType } from '~/utils/formatNumber';
import replaceNumberWithPercentage from '~/utils/replaceNumbersWithPercentage';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import DiscountsGraph from './components/DiscountsGraph';
import DiscountsTable from './components/DiscountsTable';

const REPORT_TYPE = 'discounts';

const fieldsConstant = [
  { isChecked: false, value: 'itemsQty' },
  { isChecked: false, value: 'itemsValue' },
  { isChecked: false, value: 'modifiersQty' },
  { isChecked: false, value: 'modifiersValue' },
  { isChecked: false, value: 'giftCardsQty' },
  { isChecked: false, value: 'giftCardsValue' },
  { isChecked: true, value: 'name' },
  { isChecked: false, value: 'totalValue' },
  { isChecked: false, value: 'vat' },
];

export default function Discounts() {
  const { t } = useTranslation();
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  // Replace manual data fetching with the hook
  const { data, dataTimestamp, isLoading, error, isInitialStorageFetchDone } =
    useReportRawData(REPORT_TYPE);

  // Use the data from the hook
  const rawData = useMemo(() => data, [data]);

  const [isProcessingData, setIsProcessingData] = useState(false);
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [currency, setCurrency] = useState<CurrencyType>();
  const [tableFields, setTableFields] = useState<FieldOption[]>(fieldsConstant);
  const [groupingItems, setGroupingItems] = useState<string[]>([]);

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = useCallback((key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  }, []);

  const contentRef = useRef<HTMLDivElement>(null);

  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  // Set filters with default values on mount
  useEffect(() => {
    setFilters(defaultValues);
  }, [dateRange, sellPointId, timeRange]);

  // Process common fields from data
  useEffect(() => {
    if (!rawData || !Array.isArray(rawData) || rawData.length === 0) return;

    try {
      // Set currency from data
      if (rawData.length && rawData[0].currency) {
        setCurrency(rawData[0].currency as CurrencyType);
      }

      const commonFieldsValues = getReportCommonFieldsValues(
        REPORT_TYPE,
        rawData
      );

      // Process members
      const tmpMembers: OptionType[] = [];
      commonFieldsValues.member?.forEach((el, index) => {
        if (el) {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId?.[index] || el,
          });
        }
      });
      updateCommonField('member', tmpMembers);

      // Process floors
      const tmpFloors: OptionType[] = [];
      commonFieldsValues.section?.forEach(el => {
        if (el) {
          tmpFloors.push({
            label: el,
            value: el,
          });
        }
      });
      updateCommonField('floor', tmpFloors);

      // Process service types
      const tmpServiceType: OptionType[] = [];
      commonFieldsValues.serviceType?.forEach(el => {
        if (el) {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        }
      });
      updateCommonField('serviceType', tmpServiceType);

      // Process sources
      const tmpSources: OptionType[] = [];
      commonFieldsValues.source?.forEach(el => {
        if (el) {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        }
      });
      updateCommonField('sources', tmpSources);
    } catch (e) {
      console.error('Error processing common fields:', e);
    }
  }, [rawData, updateCommonField]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  // Updated useMemo to use setIsProcessingData
  const { tableData, graphData } = useMemo(() => {
    if (!rawData || !filters)
      return { tableData: [], graphData: { datasets: [], labels: [] } };

    setIsProcessingData(true);
    try {
      const composedFilters = composeFilters(filters, REPORT_TYPE);
      const rawDataFiltered = filterReport(
        REPORT_TYPE,
        rawData,
        composedFilters,
        []
      );

      const highestValueDiscountNames = (
        groupReport(REPORT_TYPE, rawDataFiltered, [], ['name'])[0]?.report ?? []
      )
        .sort((a, b) => b.totalValue ?? 0 - (a.totalValue ?? 0))
        .slice(0, 5)
        ?.map(el => el.name);

      const filteredByName = filterReport(
        REPORT_TYPE,
        rawData,
        composedFilters,
        [
          {
            field: 'name',
            operator: 'in',
            value: highestValueDiscountNames,
          },
        ]
      );

      const groupedByHour = groupReport(
        REPORT_TYPE,
        filteredByName,
        ['hourOfDay'],
        ['name']
      );

      const labels = groupedByHour?.map(el => el.hourOfDay.toString());
      const datasets: { label: string; data: number[] }[] =
        highestValueDiscountNames?.map(name => ({
          label: name,
          data: [],
        }));

      groupedByHour.forEach(({ report: items }) => {
        datasets.forEach(el => {
          const item = items.find(i => i.name == el.label);
          const totalValue = item?.totalValue || 0;
          const formattedValue = ((totalValue / 10000) * 10) / 10;

          el.data.push(formattedValue);
        });
      });
      const graphData = {
        datasets: datasets?.map(el => {
          return {
            ...el,
            label: replaceNumberWithPercentage(el.label),
          };
        }),
        labels,
      };

      const filteredFields = tableFields.filter((field: any) => {
        return (
          field.isChecked &&
          reportSpecificFields.discounts.some(
            discountField =>
              cleanStringArond(field.value) === cleanStringArond(discountField)
          )
        );
      });

      const groupedTableData = groupReport(
        REPORT_TYPE,
        rawDataFiltered,
        [],
        [...filteredFields].map((item: FieldOption) => item.value)
      );

      if (!groupedTableData) {
        setIsProcessingData(false);
        return { tableData: [], graphData };
      }

      const groupedByItemsTableData =
        groupGroupedReportBySpecificFieldsHierarchical(
          REPORT_TYPE,
          groupedTableData,
          groupingItems as []
        )[0]?.report.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

      const tableData = remapReports(
        sortData(groupedByItemsTableData) || [],
        'name'
      );

      setIsProcessingData(false);
      return { tableData, graphData };
    } catch (e) {
      console.error('Error processing report data:', e);
      setIsProcessingData(false);
      return { tableData: [], graphData: { datasets: [], labels: [] } };
    }
  }, [filters, rawData, groupingItems, tableFields]);

  const onChangeGrouping = useCallback((items: string[]) => {
    setGroupingItems(items);
  }, []);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  // Update the rendering conditions to always show existing data while loading

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    console.log(tableData);
    const title = 'Report discounts';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Category Name',
        'VAT',
        'Items Discount Applied',
        'Items Discount Amount',
        'Modifiers Discount Applied',
        'Modifiers Discount Amount',
        'Gift Cards Discount Applied',
        'Gift Cards Amount Discounted',
        'Total Amount',
      ].join(','),
      ...tableData.map(el =>
        [
          el.name,
          el.vat,
          el.itemsQty / 1000 || 0,
          el.itemsValue / 10000 || 0,
          el.modifiersQty / 1000 || 0,
          el.modifiersValue / 10000 || 0,
          el.giftCardsQty / 1000 || 0,
          el.giftCardsValue / 10000 || 0,
          el.totalValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        '',
        tableData.reduce((acc, el) => acc + (el.itemsQty || 0) / 1000, 0),
        tableData.reduce((acc, el) => acc + (el.itemsValue || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.modifiersQty || 0) / 1000, 0),
        tableData.reduce(
          (acc, el) => acc + (el.modifiersValue || 0) / 10000,
          0
        ),
        tableData.reduce((acc, el) => acc + (el.giftCardsQty || 0) / 1000, 0),
        tableData.reduce(
          (acc, el) => acc + (el.giftCardsValue || 0) / 10000,
          0
        ),
        tableData.reduce((acc, el) => acc + (el.totalValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'discounts');
  };

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('md'));

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('discounts.title')}
        description={
          <>
            {t('discounts.description')}
            with total impact on sales.{' '}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={
          <ExportMenuButton
            contentRef={contentRef}
            handleExport={handleExport}
          />
        }
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />
      {/* Show linear progress at the top when loading */}
      <Box
        sx={{
          width: isSmall ? 'calc(100% + 50px)' : '100%',
          ml: isSmall ? '-25px' : 0,
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          height: 4,
        }}
      >
        {(isLoading || isProcessingData) && <LinearProgress />}
      </Box>

      <ReportDateTitle />

      {/* Show error when applicable */}
      {error && !isLoading && !isProcessingData ? (
        <Box sx={{ color: 'error.main', textAlign: 'center', my: 2 }}>
          {typeof error === 'object' && error && 'message' in error
            ? (error as any).message
            : String(error)}
        </Box>
      ) : null}

      {/* Always render content when we have data, regardless of loading state */}
      {isInitialStorageFetchDone && (
        <>
          {isSmall ? null : (
            <DiscountsGraph data={graphData} currency={currency} />
          )}
          <DiscountsTable
            tableData={tableData || []}
            fields={tableFields}
            setFields={setTableFields}
            groupingItems={groupingItems}
            onChangeGrouping={onChangeGrouping}
          />
        </>
      )}
    </Box>
  );
}
