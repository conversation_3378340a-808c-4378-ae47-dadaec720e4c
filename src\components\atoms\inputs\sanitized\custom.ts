/**
 * Sanitized Custom Components
 *
 * Pre-wrapped custom input components with automatic text sanitization.
 *
 * @module sanitized/custom
 */

import { withSanitization } from '~/utils/withSanitization';
import CustomInput from '../CustomInput';
import MuiCustomInput from '../MuiCustomInput';
import PhoneNumberInput from '../PhoneNumberInput';
import CodeInput from '../CodeInput';

/**
 * Sanitized CustomInput component
 * This is the custom input component used throughout the app with React-Admin
 *
 * @example
 * ```tsx
 * import { SanitizedCustomInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedCustomInput
 *     source="name"
 *     label="Name"
 *     type="text"
 *   />
 * </SimpleForm>
 * ```
 */
export const SanitizedCustomInput = withSanitization(CustomInput, {
    handlers: ['onChange'],
});

/**
 * Sanitized MuiCustomInput component
 * This is the MUI-only version of CustomInput
 *
 * @example
 * ```tsx
 * import { SanitizedMuiCustomInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedMuiCustomInput
 *   label="Name"
 *   value={name}
 *   onChange={(e) => setName(e.target.value)}
 *   type="text"
 * />
 * ```
 */
export const SanitizedMuiCustomInput = withSanitization(MuiCustomInput, {
    handlers: ['onChange'],
});

/**
 * Sanitized PhoneNumberInput component
 *
 * @example
 * ```tsx
 * import { SanitizedPhoneNumberInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedPhoneNumberInput
 *     source="phone"
 *     label="Phone Number"
 *   />
 * </SimpleForm>
 * ```
 */
export const SanitizedPhoneNumberInput = withSanitization(PhoneNumberInput, {
    handlers: ['onChange'],
});

/**
 * Sanitized CodeInput component
 * Note: CodeInput is typically for numeric codes, but sanitization
 * is applied to remove any accidentally pasted invisible characters
 *
 * @example
 * ```tsx
 * import { SanitizedCodeInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedCodeInput
 *     source="verificationCode"
 *     label="Verification Code"
 *     digits={6}
 *   />
 * </SimpleForm>
 * ```
 */
export const SanitizedCodeInput = withSanitization(CodeInput, {
    handlers: ['onChange'],
});
