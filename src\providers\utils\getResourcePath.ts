import {
  getDefaultResourceInfo,
  ResourcesInfo,
  resourcesInfo,
} from '~/providers/resources';

export const getResourcePath = (
  resource: string,
  accountId: string,
  meta?: { [key: string]: any }
): string | null => {
  console.log(
    `getResourcePath: ${resource}`,
    `accountId: ${accountId}`,
    `meta: ${JSON.stringify(meta)}`
  );
  // Get the specific config or the default one
  const resourceInfo =
    resourcesInfo[resource as keyof ResourcesInfo] ??
    getDefaultResourceInfo(resource);

  let template = resourceInfo.pathTemplate;
  let placeholder = '{accountId}';
  if (template.includes(placeholder)) {
    if (!accountId) {
      // Throw error only if the placeholder exists AND accountId is missing
      throw new Error(
        `Account Id is required for resource '${resource}' path template "${template}" but was not provided.`
      );
    }
    template = template.replace(placeholder, accountId);
  }
  placeholder = '{sellPointId}';
  if (template.includes(placeholder)) {
    if (!meta?.sellPointId) {
      // Return null to signal that the path cannot be determined yet
      // This allows the data provider to skip/defer the request
      console.warn(
        `SellPoint Id is required for resource '${resource}' but was not provided. Request will be skipped.`
      );
      return null;
    }
    template = template.replace(placeholder, meta.sellPointId);
  }
  // No placeholder, return template as is (handles global resources like 'accounts')
  return template;
};
