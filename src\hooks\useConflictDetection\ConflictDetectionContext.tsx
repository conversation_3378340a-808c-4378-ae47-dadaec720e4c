/**
 * Conflict Detection Context
 *
 * React Context and Provider for resource-agnostic conflict detection.
 * Wraps forms to provide entity-aware change tracking and conflict resolution.
 */

import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useReducer,
  useRef,
} from 'react';
import { useFormContext } from 'react-hook-form';

import {
  applyAutoMerges,
  resolveConflict as doResolveConflict,
  processRemoteUpdate as processUpdate,
} from './conflictResolver';
import {
  buildEntityRegistry,
  deepClone,
  deepEqual,
  extractEntityOwnFields,
  findEntityById,
  getEffectiveIdField,
  getValueAtPath,
  isPlainObject,
  pathMatchesAnyPattern,
} from './entityRegistry';
import { buildFullPath, parsePathToEntity } from './pathUtils';
import {
  Conflict,
  ConflictDetectionConfig,
  ConflictDetectionContextValue,
  ConflictDetectionProviderProps,
  EntityRecord,
  LocalChange,
  ProcessUpdateResult,
  TrackerState,
  ViewportCallbacks,
} from './types';

// ═══════════════════════════════════════════════════════════════════════════
// Reducer
// ═══════════════════════════════════════════════════════════════════════════

type Action =
  | {
      type: 'INITIALIZE';
      payload: {
        record: any;
        idField: string;
        excludeFields: string[];
        arrayIdFields: Record<string, string>;
        referenceArrayPaths: string[];
      };
    }
  | { type: 'SET_CONFLICTS'; payload: Conflict[] }
  | {
      type: 'CLEAR_CONFLICT';
      payload: { conflict: Conflict; resolution: 'local' | 'remote' };
    }
  | { type: 'CLEAR_ALL_CONFLICTS' }
  | { type: 'TRACK_CHANGE'; payload: LocalChange }
  | { type: 'REMOVE_CHANGE'; payload: string }
  | { type: 'CLEAR_ALL_CHANGES' }
  | { type: 'UPDATE_SERVER_SNAPSHOT'; payload: any }
  | {
      type: 'UPDATE_ENTITIES';
      payload: {
        entities: TrackerState['entities'];
        noIdArrays: TrackerState['noIdArrays'];
      };
    }
  | { type: 'ADD_REMOTE_CHANGES'; payload: Map<string, any> };

const initialState: TrackerState = {
  entities: new Map(),
  noIdArrays: new Map(),
  localChanges: new Map(),
  remoteChanges: new Map(),
  conflicts: [],
  formOpenSnapshot: null,
  latestServerSnapshot: null,
  formOpenTime: 0,
  idField: 'id',
  arrayIdFields: {},
  referenceArrayPaths: [],
  excludeFields: [],
};

function reducer(state: TrackerState, action: Action): TrackerState {
  switch (action.type) {
    case 'INITIALIZE': {
      const {
        record,
        idField,
        excludeFields,
        arrayIdFields,
        referenceArrayPaths,
      } = action.payload;
      const { entities, noIdArrays } = buildEntityRegistry(
        record,
        idField,
        excludeFields,
        arrayIdFields,
        referenceArrayPaths
      );
      return {
        ...initialState,
        entities,
        noIdArrays,
        formOpenSnapshot: deepClone(record),
        latestServerSnapshot: deepClone(record),
        formOpenTime: Date.now(),
        idField,
        arrayIdFields,
        referenceArrayPaths,
        excludeFields,
      };
    }

    case 'SET_CONFLICTS':
      return { ...state, conflicts: action.payload };

    case 'CLEAR_CONFLICT': {
      const { conflict, resolution } = action.payload;
      const newRemoteChanges = new Map(state.remoteChanges);
      const newLocalChanges = new Map(state.localChanges);

      if (conflict.type === 'field_conflict') {
        const key = `${conflict.entityId}:${conflict.entityPath ?? ''}:${conflict.fieldPath}`;

        if (resolution === 'local') {
          // Keeping LOCAL changes - preserve remote changes so indicators remain visible
        } else {
          // Accepting REMOTE changes - clear local changes but keep remote changes (for blue indicator)
          newLocalChanges.delete(key);

          // Also clear any nested local changes within the conflicted field for this occurrence
          for (const [localKey, localChange] of newLocalChanges) {
            if (localChange.entityId !== conflict.entityId) continue;
            if ((localChange.entityPath ?? '') !== (conflict.entityPath ?? ''))
              continue;

            if (
              localChange.fieldPath === conflict.fieldPath ||
              localChange.fieldPath.startsWith(`${conflict.fieldPath}.`)
            ) {
              newLocalChanges.delete(localKey);
            }
          }

          // Also clear array-based local changes that include this entity occurrence
          const conflictEntity = state.entities.get(conflict.entityId);
          if (conflictEntity) {
            for (const [localKey, localChange] of newLocalChanges) {
              if (!Array.isArray(localChange.currentValue)) continue;

              const basePaths: string[] = [];
              if (localChange.entityId === 'ROOT') {
                basePaths.push(localChange.fieldPath);
              } else {
                const localEntity = state.entities.get(localChange.entityId);
                if (localEntity) {
                  for (const [, occurrence] of localEntity.occurrences) {
                    basePaths.push(
                      buildFullPath(occurrence.path, localChange.fieldPath)
                    );
                  }
                }
              }

              let shouldClear = false;
              for (const basePath of basePaths) {
                for (const [, occurrence] of conflictEntity.occurrences) {
                  if (!occurrence.path.startsWith(basePath)) continue;

                  let relPath = occurrence.path.slice(basePath.length);
                  if (relPath.startsWith('.')) relPath = relPath.slice(1);

                  const oldEntity = getValueAtPath(
                    localChange.originalValue,
                    relPath
                  );
                  const newEntity = getValueAtPath(
                    localChange.currentValue,
                    relPath
                  );

                  if (
                    (!oldEntity && newEntity) ||
                    (oldEntity && !newEntity) ||
                    (oldEntity && newEntity && !deepEqual(oldEntity, newEntity))
                  ) {
                    shouldClear = true;
                    break;
                  }
                }
                if (shouldClear) break;
              }

              if (shouldClear) {
                newLocalChanges.delete(localKey);
              }
            }
          }

          // If the field was an array, also clear local changes for entities inside it
          if (Array.isArray(conflict.remoteValue)) {
            const fullFieldPath = buildFullPath(
              conflict.entityPath ?? null,
              conflict.fieldPath
            );
            const isReferenceArray = pathMatchesAnyPattern(
              fullFieldPath,
              state.referenceArrayPaths
            );
            const effectiveIdField = getEffectiveIdField(
              fullFieldPath,
              state.idField,
              state.arrayIdFields
            );

            const isEntityArray =
              !isReferenceArray &&
              conflict.remoteValue.some(
                item =>
                  isPlainObject(item) && item[effectiveIdField] !== undefined
              );

            if (isEntityArray) {
              const findEntityIds = (
                arr: any[],
                ids: Set<string> = new Set()
              ): Set<string> => {
                for (const item of arr) {
                  if (
                    isPlainObject(item) &&
                    item[effectiveIdField] !== undefined
                  ) {
                    ids.add(String(item[effectiveIdField]));
                  }
                  if (Array.isArray(item)) {
                    findEntityIds(item, ids);
                  }
                  // Check all array properties recursively (not just 'items')
                  if (isPlainObject(item)) {
                    for (const value of Object.values(item)) {
                      if (Array.isArray(value)) {
                        findEntityIds(value, ids);
                      }
                    }
                  }
                }
                return ids;
              };

              const entityIds = findEntityIds(conflict.remoteValue);
              for (const [localKey, localChange] of newLocalChanges) {
                if (entityIds.has(localChange.entityId)) {
                  newLocalChanges.delete(localKey);
                }
              }
            }
          }
        }
      }

      return {
        ...state,
        conflicts: state.conflicts.filter(c => c !== conflict),
        localChanges: newLocalChanges,
        remoteChanges: newRemoteChanges,
      };
    }

    case 'CLEAR_ALL_CONFLICTS':
      return { ...state, conflicts: [] };

    case 'TRACK_CHANGE': {
      const newChanges = new Map(state.localChanges);
      const key = `${action.payload.entityId}:${action.payload.entityPath ?? ''}:${action.payload.fieldPath}`;
      newChanges.set(key, action.payload);
      return { ...state, localChanges: newChanges };
    }

    case 'REMOVE_CHANGE': {
      const newChanges = new Map(state.localChanges);
      newChanges.delete(action.payload);
      return { ...state, localChanges: newChanges };
    }

    case 'CLEAR_ALL_CHANGES':
      return { ...state, localChanges: new Map() };

    case 'UPDATE_SERVER_SNAPSHOT':
      return { ...state, latestServerSnapshot: deepClone(action.payload) };

    case 'UPDATE_ENTITIES':
      return {
        ...state,
        entities: action.payload.entities,
        noIdArrays: action.payload.noIdArrays,
      };

    case 'ADD_REMOTE_CHANGES': {
      const newRemoteChanges = new Map(state.remoteChanges);
      for (const [key, value] of action.payload) {
        newRemoteChanges.set(key, value);
      }
      return { ...state, remoteChanges: newRemoteChanges };
    }

    default:
      return state;
  }
}

// ═══════════════════════════════════════════════════════════════════════════
// Context
// ═══════════════════════════════════════════════════════════════════════════

const ConflictDetectionContext =
  createContext<ConflictDetectionContextValue | null>(null);

// ═══════════════════════════════════════════════════════════════════════════
// Provider
// ═══════════════════════════════════════════════════════════════════════════

export function ConflictDetectionProvider({
  children,
  config = {},
}: ConflictDetectionProviderProps) {
  const idField = config.idField || 'id';
  const arrayIdFields = config.arrayIdFields || {};
  const referenceArrayPaths = config.referenceArrayPaths || [];
  const excludeFields = config.excludeFields || [];
  const debug = config.debug || false;

  const [state, dispatch] = useReducer(reducer, initialState);
  const formContext = useFormContext();
  const isInitializedRef = useRef(false);

  // Flag to skip tracking when applying remote changes (auto-merge)
  const isApplyingRemoteChangesRef = useRef(false);

  // Store viewport registrations
  const viewportCallbacks = useRef<Map<string, Set<ViewportCallbacks>>>(
    new Map()
  );

  const log = useCallback(
    (...args: any[]) => {
      if (debug) {
        console.log('[ConflictDetection]', ...args);
      }
    },
    [debug]
  );

  const setApplyingRemoteChanges = useCallback((isApplying: boolean) => {
    isApplyingRemoteChangesRef.current = isApplying;
  }, []);

  // ═══════════════════════════════════════════════════════════════════════════
  // Initialization
  // ═══════════════════════════════════════════════════════════════════════════

  const initializeForRecord = useCallback(
    (record: any) => {
      log('Initializing for record:', record?.id);
      dispatch({
        type: 'INITIALIZE',
        payload: {
          record,
          idField,
          excludeFields,
          arrayIdFields,
          referenceArrayPaths,
        },
      });
      isInitializedRef.current = true;
    },
    [idField, excludeFields, arrayIdFields, referenceArrayPaths, log]
  );

  const reinitializeAfterSave = useCallback(
    (savedRecord: any) => {
      log('Reinitializing after save');
      dispatch({
        type: 'INITIALIZE',
        payload: {
          record: savedRecord,
          idField,
          excludeFields,
          arrayIdFields,
          referenceArrayPaths,
        },
      });
    },
    [idField, excludeFields, arrayIdFields, referenceArrayPaths, log]
  );

  const updateServerSnapshot = useCallback(
    (newData: any) => {
      log(
        'Updating server snapshot (preserving remoteChanges for visual indicators)'
      );
      dispatch({ type: 'UPDATE_SERVER_SNAPSHOT', payload: newData });
    },
    [log]
  );

  // Methods to control remote change application flag
  const startApplyingRemoteChanges = useCallback(() => {
    isApplyingRemoteChangesRef.current = true;
  }, []);

  const stopApplyingRemoteChanges = useCallback(() => {
    isApplyingRemoteChangesRef.current = false;
  }, []);

  // ═══════════════════════════════════════════════════════════════════════════
  // Change Tracking
  // ═══════════════════════════════════════════════════════════════════════════

  const trackChange = useCallback(
    (fullPath: string, newValue: any) => {
      log('trackChange called with path:', fullPath);

      // Skip tracking when we're applying remote changes (auto-merge)
      if (isApplyingRemoteChangesRef.current) {
        log('trackChange: skipping - applying remote changes');
        return;
      }

      if (!isInitializedRef.current || !state.latestServerSnapshot) {
        log('trackChange: skipping - not initialized or no snapshot', {
          isInitialized: isInitializedRef.current,
          hasSnapshot: !!state.latestServerSnapshot,
        });
        return;
      }

      const { entityId, fieldPath, entityPath } = parsePathToEntity(
        fullPath,
        state.latestServerSnapshot,
        idField,
        arrayIdFields,
        referenceArrayPaths
      );
      log('trackChange: parsed path', { entityId, fieldPath });

      if (!entityId) {
        log('trackChange: skipping - no entityId found for path:', fullPath);
        return;
      }

      // Get original value - handle nested paths properly
      let originalValue: any;
      if (entityId === 'ROOT') {
        // Root-level field - get from formOpenSnapshot using nested path
        originalValue = getValueAtPath(state.formOpenSnapshot, fieldPath);
      } else {
        const entity = state.entities.get(entityId);
        // For entities, fieldPath is relative to the entity
        // First check if it's a direct field, then try nested path
        if (entity?.ownFieldsAtFormOpen[fieldPath] !== undefined) {
          originalValue = entity.ownFieldsAtFormOpen[fieldPath];
        } else {
          // Handle nested paths within entity fields (e.g., "charge.fixed.value")
          // The entity's ownFields might have "charge" as a key with nested structure
          originalValue = getValueAtPath(
            entity?.ownFieldsAtFormOpen,
            fieldPath
          );
        }
      }

      const changeKey = `${entityId}:${entityPath ?? ''}:${fieldPath}`;

      if (deepEqual(newValue, originalValue)) {
        // Value reverted to original - remove change
        log('Change reverted to original:', changeKey);
        dispatch({ type: 'REMOVE_CHANGE', payload: changeKey });
      } else {
        log('Tracking change:', changeKey, { originalValue, newValue });
        dispatch({
          type: 'TRACK_CHANGE',
          payload: {
            entityId,
            entityPath: entityPath ?? undefined,
            fieldPath,
            originalValue,
            currentValue: newValue,
            timestamp: Date.now(),
          },
        });
      }
    },
    [
      state.latestServerSnapshot,
      state.formOpenSnapshot,
      state.entities,
      idField,
      arrayIdFields,
      referenceArrayPaths,
      log,
    ]
  );

  const hasLocalChanges = useCallback(() => {
    return state.localChanges.size > 0;
  }, [state.localChanges]);

  const clearAllLocalChanges = useCallback(() => {
    log('Clearing all local changes');
    dispatch({ type: 'CLEAR_ALL_CHANGES' });
  }, [log]);

  /**
   * Clear local changes only for the entities involved in the given conflicts.
   * This preserves local changes for entities where user previously chose "Keep My Changes".
   */
  const clearLocalChangesForConflicts = useCallback(
    (conflicts: Conflict[]) => {
      if (conflicts.length === 0) return;

      // Collect all entity IDs from the conflicts
      const conflictedEntityIds = new Set<string>();
      for (const conflict of conflicts) {
        if (
          conflict.type === 'field_conflict' ||
          conflict.type === 'entity_deleted'
        ) {
          conflictedEntityIds.add(conflict.entityId);
        }
      }

      log('Clearing local changes for conflicted entities:', [
        ...conflictedEntityIds,
      ]);

      // Find and remove local changes that affect the conflicted entities
      const keysToRemove: string[] = [];

      for (const [key, change] of state.localChanges) {
        // Direct match: local change is for one of the conflicted entities
        if (conflictedEntityIds.has(change.entityId)) {
          keysToRemove.push(key);
          continue;
        }

        // Array-level changes (e.g., ROOT:pages) - check if the array contains conflicted entities
        if (
          Array.isArray(change.currentValue) ||
          Array.isArray(change.originalValue)
        ) {
          const checkArrayForEntities = (arr: any[]): boolean => {
            if (!Array.isArray(arr)) return false;
            for (const item of arr) {
              if (item && typeof item === 'object') {
                // Check if this item is one of the conflicted entities
                const itemId = item[state.idField];
                if (
                  itemId !== undefined &&
                  conflictedEntityIds.has(String(itemId))
                ) {
                  return true;
                }
                // Check nested arrays (like pages which is array of arrays)
                if (Array.isArray(item)) {
                  if (checkArrayForEntities(item)) return true;
                }
                // Check all array properties recursively
                for (const value of Object.values(item)) {
                  if (Array.isArray(value) && checkArrayForEntities(value)) {
                    return true;
                  }
                }
              }
            }
            return false;
          };

          const currentHasEntity =
            Array.isArray(change.currentValue) &&
            checkArrayForEntities(change.currentValue);
          const originalHasEntity =
            Array.isArray(change.originalValue) &&
            checkArrayForEntities(change.originalValue);

          if (currentHasEntity || originalHasEntity) {
            keysToRemove.push(key);
          }
        }
      }

      // Remove the identified local changes
      for (const key of keysToRemove) {
        log('Removing local change:', key);
        dispatch({ type: 'REMOVE_CHANGE', payload: key });
      }
    },
    [state.localChanges, state.idField, log]
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // Remote Update Processing
  // ═══════════════════════════════════════════════════════════════════════════
  const processRemoteUpdate = useCallback(
    (newData: any, userId: string): ProcessUpdateResult => {
      log('Processing remote update from:', userId);

      const result = processUpdate(newData, userId, state);

      // Update server snapshot
      dispatch({ type: 'UPDATE_SERVER_SNAPSHOT', payload: newData });

      // Update entity registry (with arrayIdFields for path-specific ID fields)
      const { entities, noIdArrays } = buildEntityRegistry(
        newData,
        idField,
        excludeFields,
        arrayIdFields,
        referenceArrayPaths
      );
      dispatch({ type: 'UPDATE_ENTITIES', payload: { entities, noIdArrays } });

      // Set conflicts
      if (result.conflicts.length > 0) {
        log('Conflicts detected:', result.conflicts.length);
        dispatch({ type: 'SET_CONFLICTS', payload: result.conflicts });
      }

      // Store remote changes for visual indicators
      const remoteChangesMap = new Map();

      const locallyAffectedEntityIds = new Set<string>();
      for (const [, change] of state.localChanges) {
        locallyAffectedEntityIds.add(change.entityId);
      }
      for (const conflict of result.conflicts) {
        if (
          conflict.type === 'field_conflict' ||
          conflict.type === 'entity_deleted'
        ) {
          locallyAffectedEntityIds.add(conflict.entityId);
        }
      }

      const conflictedEntityIds = new Set<string>();
      for (const conflict of result.conflicts) {
        if (
          conflict.type === 'field_conflict' ||
          conflict.type === 'entity_deleted'
        ) {
          conflictedEntityIds.add(conflict.entityId);
        }
      }

      const getRelativePath = (
        basePath: string,
        fullPath: string
      ): string | null => {
        if (!basePath) return fullPath;
        if (fullPath === basePath) return '';
        if (fullPath.startsWith(basePath)) {
          let rel = fullPath.slice(basePath.length);
          if (rel.startsWith('.')) rel = rel.slice(1);
          return rel;
        }
        return null;
      };

      const isArrayChange = (value: any): boolean => Array.isArray(value);
      for (const change of result.remoteChanges) {
        const arrayChange =
          isArrayChange(change.oldValue) || isArrayChange(change.newValue);

        const fullFieldPath = buildFullPath(
          change.entityPath ?? null,
          change.fieldPath
        );
        const isReferenceArray = pathMatchesAnyPattern(
          fullFieldPath,
          referenceArrayPaths
        );

        if (!arrayChange) {
          if (
            locallyAffectedEntityIds.has(change.entityId) ||
            conflictedEntityIds.has(change.entityId)
          ) {
            remoteChangesMap.set(
              `${change.entityId}:${change.entityPath ?? ''}:${change.fieldPath}`,
              change
            );
          }
          continue;
        }

        if (isReferenceArray) {
          continue;
        }

        // For array changes, only keep remote indicators for entities that are in conflict
        // (remote-only changes inside arrays should auto-merge and not show indicators)
        for (const id of conflictedEntityIds) {
          const record = entities.get(id);
          if (!record) continue;

          const basePath = change.fieldPath;
          let isInArray = false;
          for (const [, occurrence] of record.occurrences) {
            const relPath = getRelativePath(basePath, occurrence.path);
            if (relPath !== null) {
              isInArray = true;
              break;
            }
          }

          if (!isInArray) continue;

          remoteChangesMap.set(
            `${id}:${change.entityPath ?? ''}:${change.fieldPath}`,
            {
              ...change,
              entityId: id,
            }
          );
        }
      }
      dispatch({ type: 'ADD_REMOTE_CHANGES', payload: remoteChangesMap });

      // NOTE: Auto-merges are NOT applied here - they are applied by ConflictAwareSimpleForm
      // only when there are no conflicts. This ensures the user can choose how to resolve
      // conflicts before any form values are modified.

      // Notify viewports of path changes
      notifyViewportsOfChanges(
        state.entities,
        entities,
        viewportCallbacks.current
      );

      return result;
    },
    [
      state,
      idField,
      excludeFields,
      arrayIdFields,
      referenceArrayPaths,
      formContext,
      log,
    ]
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // Conflict Resolution
  // ═══════════════════════════════════════════════════════════════════════════

  const resolveConflict = useCallback(
    (conflict: Conflict, resolution: 'local' | 'remote') => {
      log('Resolving conflict:', conflict.type, resolution);

      if (formContext) {
        doResolveConflict(
          conflict,
          resolution,
          state,
          (path, value, options) => formContext.setValue(path, value, options),
          () => formContext.getValues()
        );
      }

      dispatch({ type: 'CLEAR_CONFLICT', payload: { conflict, resolution } });
    },
    [state, formContext, log]
  );

  const resolveAllConflicts = useCallback(
    (resolution: 'local' | 'remote') => {
      log('Resolving all conflicts:', resolution);
      for (const conflict of state.conflicts) {
        resolveConflict(conflict, resolution);
      }
    },
    [state.conflicts, resolveConflict, log]
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // Queries
  // ═══════════════════════════════════════════════════════════════════════════

  const hasFieldConflict = useCallback(
    (entityId: string, fieldPath: string): boolean => {
      return state.conflicts.some(
        c =>
          c.type === 'field_conflict' &&
          c.entityId === entityId &&
          c.fieldPath === fieldPath
      );
    },
    [state.conflicts]
  );

  const wasFieldUpdatedRemotely = useCallback(
    (entityId: string, fieldPath: string): boolean => {
      for (const [, change] of state.remoteChanges) {
        if (change.entityId === entityId && change.fieldPath === fieldPath) {
          return true;
        }
      }
      return false;
    },
    [state.remoteChanges]
  );

  const wasEntityUpdatedRemotely = useCallback(
    (entityId: string): boolean => {
      const isAncestorOf = (ancestorId: string, childId: string): boolean => {
        let currentId: string | null = childId;
        const visited = new Set<string>();

        while (currentId && !visited.has(currentId)) {
          visited.add(currentId);
          const node = state.entities.get(currentId);
          if (!node) return false;

          // Check all occurrences (child can appear multiple times)
          for (const [, occurrence] of node.occurrences) {
            const parentId = occurrence.parentEntityId;
            if (!parentId) continue;
            if (parentId === ancestorId) return true;
            currentId = parentId;
          }

          // If there are no occurrences, stop
          if (node.occurrences.size === 0) return false;
        }

        return false;
      };

      // First check: direct match (entity's own fields changed)
      for (const key of state.remoteChanges.keys()) {
        if (key.startsWith(`${entityId}:`)) {
          return true;
        }
      }

      // Second check: this entity is an ancestor of a remotely changed child
      for (const [, change] of state.remoteChanges.entries()) {
        if (isAncestorOf(entityId, change.entityId)) {
          return true;
        }
      }

      // Third check: entity is inside a changed array
      // For each remote change that affects an array, check if this entity is inside it
      for (const [key, change] of state.remoteChanges.entries()) {
        const oldValue = change.oldValue;
        const newValue = change.newValue;

        const fullFieldPath = buildFullPath(
          change.entityPath ?? null,
          change.fieldPath
        );
        const isReferenceArray = pathMatchesAnyPattern(
          fullFieldPath,
          state.referenceArrayPaths
        );

        if (isReferenceArray) {
          continue;
        }

        // Check if old or new value is an array containing entities
        const checkArrayForEntity = (arr: any[]): boolean => {
          if (!Array.isArray(arr)) return false;
          for (const item of arr) {
            // Direct item match
            if (item && typeof item === 'object' && item.id === entityId) {
              return true;
            }
            // Nested array (like pages which is array of arrays)
            if (Array.isArray(item)) {
              if (checkArrayForEntity(item)) return true;
            }
            // Check all array properties recursively (not just 'items')
            if (item && typeof item === 'object') {
              for (const value of Object.values(item)) {
                if (Array.isArray(value) && checkArrayForEntity(value)) {
                  return true;
                }
              }
            }
          }
          return false;
        };

        // If this entity exists in either old or new array value, it may have been affected
        const inOld = Array.isArray(oldValue) && checkArrayForEntity(oldValue);
        const inNew = Array.isArray(newValue) && checkArrayForEntity(newValue);

        if (inOld || inNew) {
          const entity = state.entities.get(entityId);
          if (!entity) return true;

          const basePath = fullFieldPath;
          const getRelativePath = (fullPath: string): string | null => {
            if (!basePath) return fullPath;
            if (fullPath === basePath) return '';
            if (fullPath.startsWith(basePath)) {
              let rel = fullPath.slice(basePath.length);
              if (rel.startsWith('.')) rel = rel.slice(1);
              return rel;
            }
            return null;
          };

          for (const [, occurrence] of entity.occurrences) {
            const relPath = getRelativePath(occurrence.path);
            if (relPath === null) continue;

            const oldEntity = Array.isArray(oldValue)
              ? getValueAtPath(oldValue, relPath)
              : undefined;
            const newEntity = Array.isArray(newValue)
              ? getValueAtPath(newValue, relPath)
              : undefined;

            if ((!oldEntity && newEntity) || (oldEntity && !newEntity)) {
              return true;
            }
            if (oldEntity && newEntity && !deepEqual(oldEntity, newEntity)) {
              return true;
            }
          }
        }
      }

      return false;
    },
    [state.remoteChanges]
  );

  const getEntityRemoteInfo = useCallback(
    (entityId: string): { userId?: string; timestamp?: number } | null => {
      const isAncestorOf = (ancestorId: string, childId: string): boolean => {
        let currentId: string | null = childId;
        const visited = new Set<string>();

        while (currentId && !visited.has(currentId)) {
          visited.add(currentId);
          const node = state.entities.get(currentId);
          if (!node) return false;

          for (const [, occurrence] of node.occurrences) {
            const parentId = occurrence.parentEntityId;
            if (!parentId) continue;
            if (parentId === ancestorId) return true;
            currentId = parentId;
          }

          if (node.occurrences.size === 0) return false;
        }

        return false;
      };

      // First check: direct match
      for (const [, change] of state.remoteChanges.entries()) {
        if (change.entityId === entityId) {
          return { userId: change.userId, timestamp: change.timestamp };
        }
      }

      // Second check: this entity is an ancestor of a remotely changed child
      for (const [, change] of state.remoteChanges.entries()) {
        if (isAncestorOf(entityId, change.entityId)) {
          return { userId: change.userId, timestamp: change.timestamp };
        }
      }

      // Third check: entity is inside a changed array (path-aware)
      for (const [, change] of state.remoteChanges.entries()) {
        const oldValue = change.oldValue;
        const newValue = change.newValue;

        const fullFieldPath = buildFullPath(
          change.entityPath ?? null,
          change.fieldPath
        );
        const isReferenceArray = pathMatchesAnyPattern(
          fullFieldPath,
          state.referenceArrayPaths
        );

        if (isReferenceArray) continue;

        const entity = state.entities.get(entityId);
        if (!entity) continue;

        const basePath = fullFieldPath;
        const getRelativePath = (fullPath: string): string | null => {
          if (!basePath) return fullPath;
          if (fullPath === basePath) return '';
          if (fullPath.startsWith(basePath)) {
            let rel = fullPath.slice(basePath.length);
            if (rel.startsWith('.')) rel = rel.slice(1);
            return rel;
          }
          return null;
        };

        for (const [, occurrence] of entity.occurrences) {
          const relPath = getRelativePath(occurrence.path);
          if (relPath === null) continue;

          const oldEntity = Array.isArray(oldValue)
            ? getValueAtPath(oldValue, relPath)
            : undefined;
          const newEntity = Array.isArray(newValue)
            ? getValueAtPath(newValue, relPath)
            : undefined;

          if ((!oldEntity && newEntity) || (oldEntity && !newEntity)) {
            return { userId: change.userId, timestamp: change.timestamp };
          }
          if (oldEntity && newEntity && !deepEqual(oldEntity, newEntity)) {
            return { userId: change.userId, timestamp: change.timestamp };
          }
        }
      }

      return null;
    },
    [state.remoteChanges]
  );

  const getFieldRemoteValue = useCallback(
    (entityId: string, fieldPath: string): any => {
      for (const [, change] of state.remoteChanges) {
        if (change.entityId === entityId && change.fieldPath === fieldPath) {
          return change.newValue;
        }
      }
      return undefined;
    },
    [state.remoteChanges]
  );

  const getEntityPath = useCallback(
    (entityId: string, parentEntityId?: string): string | null => {
      const entity = state.entities.get(entityId);
      if (!entity) return null;

      if (parentEntityId) {
        for (const [, occurrence] of entity.occurrences) {
          if (occurrence.parentEntityId === parentEntityId) {
            return occurrence.path;
          }
        }
      }

      // Return first occurrence if no parent specified
      const firstOccurrence = entity.occurrences.values().next().value;
      return firstOccurrence?.path || null;
    },
    [state.entities]
  );

  const isFieldDirty = useCallback(
    (entityId: string, fieldPath: string): boolean => {
      for (const [, change] of state.localChanges) {
        if (change.entityId === entityId && change.fieldPath === fieldPath) {
          return true;
        }
      }
      return false;
    },
    [state.localChanges]
  );

  const isEntityDirty = useCallback(
    (entityId: string): boolean => {
      const isAncestorOf = (ancestorId: string, childId: string): boolean => {
        let currentId: string | null = childId;
        const visited = new Set<string>();

        while (currentId && !visited.has(currentId)) {
          visited.add(currentId);
          const node = state.entities.get(currentId);
          if (!node) return false;

          for (const [, occurrence] of node.occurrences) {
            const parentId = occurrence.parentEntityId;
            if (!parentId) continue;
            if (parentId === ancestorId) return true;
            currentId = parentId;
          }

          if (node.occurrences.size === 0) return false;
        }

        return false;
      };

      // First check: direct match (entity's own fields changed)
      for (const key of state.localChanges.keys()) {
        if (key.startsWith(`${entityId}:`)) {
          return true;
        }
      }

      // Second check: this entity is an ancestor of a locally changed child
      for (const [, change] of state.localChanges.entries()) {
        if (isAncestorOf(entityId, change.entityId)) {
          return true;
        }
      }

      // Third check: entity is inside a locally changed array (path-aware)
      // For each local change that affects an array, check if this entity is inside it
      for (const [, change] of state.localChanges.entries()) {
        const localValue = change.currentValue;
        const originalValue = change.originalValue;

        const fullFieldPath = buildFullPath(
          change.entityPath ?? null,
          change.fieldPath
        );
        const isReferenceArray = pathMatchesAnyPattern(
          fullFieldPath,
          state.referenceArrayPaths
        );

        if (isReferenceArray) {
          continue;
        }

        if (Array.isArray(localValue) || Array.isArray(originalValue)) {
          const entity = state.entities.get(entityId);
          if (!entity) return true;

          const basePath = fullFieldPath;
          const getRelativePath = (fullPath: string): string | null => {
            if (!basePath) return fullPath;
            if (fullPath === basePath) return '';
            if (fullPath.startsWith(basePath)) {
              let rel = fullPath.slice(basePath.length);
              if (rel.startsWith('.')) rel = rel.slice(1);
              return rel;
            }
            return null;
          };

          for (const [, occurrence] of entity.occurrences) {
            const relPath = getRelativePath(occurrence.path);
            if (relPath === null) continue;

            const oldEntity = Array.isArray(originalValue)
              ? getValueAtPath(originalValue, relPath)
              : undefined;
            const newEntity = Array.isArray(localValue)
              ? getValueAtPath(localValue, relPath)
              : undefined;

            if ((!oldEntity && newEntity) || (oldEntity && !newEntity)) {
              return true;
            }
            if (oldEntity && newEntity && !deepEqual(oldEntity, newEntity)) {
              return true;
            }
          }
        }
      }

      return false;
    },
    [state.localChanges]
  );

  const getEntity = useCallback(
    (entityId: string): EntityRecord | undefined => {
      return state.entities.get(entityId);
    },
    [state.entities]
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // Viewport Registration
  // ═══════════════════════════════════════════════════════════════════════════

  const registerViewport = useCallback(
    (entityId: string, callbacks: ViewportCallbacks) => {
      if (!viewportCallbacks.current.has(entityId)) {
        viewportCallbacks.current.set(entityId, new Set());
      }
      viewportCallbacks.current.get(entityId)!.add(callbacks);

      log('Registered viewport for entity:', entityId);

      // Return unregister function
      return () => {
        viewportCallbacks.current.get(entityId)?.delete(callbacks);
        log('Unregistered viewport for entity:', entityId);
      };
    },
    [log]
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // Multi-Occurrence Sync
  // ═══════════════════════════════════════════════════════════════════════════

  const syncEntityField = useCallback(
    (entityId: string, fieldPath: string, value: any) => {
      const entity = state.entities.get(entityId);
      if (!entity || !formContext) return;

      log('Syncing entity field across occurrences:', entityId, fieldPath);

      // Update all occurrences
      for (const [, occurrence] of entity.occurrences) {
        const fullPath = buildFullPath(occurrence.path, fieldPath);
        formContext.setValue(fullPath, value, { shouldDirty: true });
      }

      // Track the change
      const firstOccurrence = entity.occurrences.values().next().value;
      if (firstOccurrence) {
        trackChange(buildFullPath(firstOccurrence.path, fieldPath), value);
      }
    },
    [state.entities, formContext, trackChange, log]
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // Context Value
  // ═══════════════════════════════════════════════════════════════════════════

  const value: ConflictDetectionContextValue = useMemo(
    () => ({
      state,
      hasConflicts: state.conflicts.length > 0,
      conflicts: state.conflicts,
      isInitialized: isInitializedRef.current,
      initializeForRecord,
      reinitializeAfterSave,
      updateServerSnapshot,
      startApplyingRemoteChanges,
      stopApplyingRemoteChanges,
      processRemoteUpdate,
      trackChange,
      hasLocalChanges,
      clearAllLocalChanges,
      clearLocalChangesForConflicts,
      resolveConflict,
      resolveAllConflicts,
      hasFieldConflict,
      wasFieldUpdatedRemotely,
      wasEntityUpdatedRemotely,
      getEntityRemoteInfo,
      getFieldRemoteValue,
      getEntityPath,
      isFieldDirty,
      isEntityDirty,
      getEntity,
      registerViewport,
      syncEntityField,
    }),
    [
      state,
      initializeForRecord,
      reinitializeAfterSave,
      updateServerSnapshot,
      startApplyingRemoteChanges,
      stopApplyingRemoteChanges,
      processRemoteUpdate,
      trackChange,
      hasLocalChanges,
      clearAllLocalChanges,
      clearLocalChangesForConflicts,
      resolveConflict,
      resolveAllConflicts,
      hasFieldConflict,
      wasFieldUpdatedRemotely,
      wasEntityUpdatedRemotely,
      getEntityRemoteInfo,
      getFieldRemoteValue,
      getEntityPath,
      isFieldDirty,
      isEntityDirty,
      getEntityPath,
      isFieldDirty,
      getEntity,
      registerViewport,
      syncEntityField,
    ]
  );

  return (
    <ConflictDetectionContext.Provider value={value}>
      {children}
    </ConflictDetectionContext.Provider>
  );
}

// ═══════════════════════════════════════════════════════════════════════════
// Hook
// ═══════════════════════════════════════════════════════════════════════════

export function useConflictDetection(): ConflictDetectionContextValue {
  const context = useContext(ConflictDetectionContext);
  if (!context) {
    throw new Error(
      'useConflictDetection must be used within ConflictDetectionProvider'
    );
  }
  return context;
}

/**
 * Optional hook that returns null if not in a provider (for optional usage)
 */
export function useConflictDetectionOptional(): ConflictDetectionContextValue | null {
  return useContext(ConflictDetectionContext);
}

export { ConflictDetectionContext };

// ═══════════════════════════════════════════════════════════════════════════
// Helper Functions
// ═══════════════════════════════════════════════════════════════════════════

function notifyViewportsOfChanges(
  oldEntities: Map<string, EntityRecord>,
  newEntities: Map<string, EntityRecord>,
  viewportCallbacksMap: Map<string, Set<ViewportCallbacks>>
): void {
  for (const [entityId, oldRecord] of oldEntities) {
    const newRecord = newEntities.get(entityId);
    const callbacks = viewportCallbacksMap.get(entityId);

    if (!callbacks || callbacks.size === 0) continue;

    if (!newRecord) {
      // Entity was deleted
      for (const cb of callbacks) {
        cb.onEntityDeleted();
      }
    } else {
      // Check for path changes
      for (const cb of callbacks) {
        const parentId = cb.parentEntityId || 'ROOT';

        const oldOccurrence = Array.from(oldRecord.occurrences.values()).find(
          o => o.parentEntityId === parentId
        );
        const newOccurrence = Array.from(newRecord.occurrences.values()).find(
          o => o.parentEntityId === parentId
        );

        if (
          oldOccurrence &&
          newOccurrence &&
          oldOccurrence.path !== newOccurrence.path
        ) {
          cb.onPathChange(newOccurrence.path);
        } else if (oldOccurrence && !newOccurrence) {
          cb.onOccurrenceRemoved?.();
        }
      }
    }
  }
}
