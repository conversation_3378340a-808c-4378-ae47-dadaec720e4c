<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>Sign In - SELIO Manager</title>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Geologica:wght@100;200;300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700&display=swap" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <meta name="theme-color" content="#667eea" />

    <!-- Shared loader styles -->
    <link rel="stylesheet" href="/loader.css" />

    <!-- Cookie Consent styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@3.1.0/dist/cookieconsent.css" />
    <link rel="stylesheet" href="/cookieConsent.css" />

    <!-- Firebase config will be injected by Vite plugin -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            position: relative;
        }

        .container {
            width: 100%;
            max-width: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .card {
            width: 100%;
            text-align: center;
        }

        h1 {
            font-size: 1.25rem;
            font-weight: bold;
            color: #212121;
            text-shadow: 2px 2px 4px #b1b1b1;
            margin-bottom: 8px;
        }

        p {
            color: #616161;
            font-size: 1.25rem;
            margin-bottom: 40px;
        }

        .google-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 0 16px;
            background: white;
            border: 1px solid #dadce0;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.25rem;
            font-weight: 500;
            width: 100%;
            height: 62px;
            color: #3c4043;
            box-shadow: 0 1px 2px 0 rgba(60, 64, 67, .3), 0 1px 3px 1px rgba(60, 64, 67, .15);
            transition: all 0.2s;
        }

        .google-btn:hover {
            background: #f8f9fa;
            box-shadow: 0 1px 3px 0 rgba(60, 64, 67, .3), 0 2px 6px 2px rgba(60, 64, 67, .15);
        }

        .google-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .google-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .google-icon.spinning svg {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .error-container {
            min-height: 50px;
            margin-top: 16px;
        }

        .error {
            color: #d32f2f;
            font-size: 14px;
            display: none;
        }

        footer {
            text-align: center;
            color: #616161;
            padding: 20px 0;
            width: 100%;
            position: fixed;
            bottom: 0;
            left: 0;
            background: #fff;
        }

        footer .copyright {
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        footer .footer-logo {
            font-weight: bold;
            letter-spacing: 2px;
            font-size: 1.25rem;
            color: #212121;
        }
    </style>
</head>

<body>
    <div id="loading-state"
        style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: #f5f5f5; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 9999;">
        <div class="loader">
            <div>
                <img src="/assets/logo/SELIO_LOGO_BLACK.svg" width="52px" class="spinner-logo" />
            </div>
            <div class="square" id="sq1"></div>
            <div class="square" id="sq2"></div>
            <div class="square" id="sq3"></div>
            <div class="square" id="sq4"></div>
            <div class="square" id="sq5"></div>
            <div class="square" id="sq6"></div>
            <div class="square" id="sq7"></div>
            <div class="square" id="sq8"></div>
            <div class="square" id="sq9"></div>
        </div>
    </div>

    <div style="flex: 1; display: flex; align-items: center; justify-content: center; width: 100%;">
        <div id="login-card" class="container" style="display: none;">
            <div class="card">
                <h1>Welcome</h1>
                <p>Sign in to your account</p>
                <button id="google-signin-btn" class="google-btn">
                    <div class="google-icon">
                        <svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                            <path fill="#EA4335"
                                d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z" />
                            <path fill="#4285F4"
                                d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z" />
                            <path fill="#FBBC05"
                                d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z" />
                            <path fill="#34A853"
                                d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z" />
                            <path fill="none" d="M0 0h48v48H0z" />
                        </svg>
                    </div>
                    Sign in with Google
                </button>
                <div class="error-container">
                    <p id="error-message" class="error"></p>
                </div>
            </div>
        </div>
    </div>

    <footer id="auth-footer">
        <div class="copyright">© 2025 Selio Software</div>
        <div class="footer-logo">S Ξ L I O</div>
    </footer>

    <!-- Cookie Consent initialization -->
    <script src="/cookieConsent.js"></script>
    <script type="module">
        // Initialize Cookie Consent
        import * as CookieConsent from 'https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@3.1.0/dist/cookieconsent.esm.js';

        // Run cookie consent with shared config
        if (window.initCookieConsent) {
            CookieConsent.run(window.initCookieConsent());
        }
    </script>

    <script>
        window.APP_VERSION = '__APP_VERSION__';
    </script>

    <!-- Firebase Auth initialization - TypeScript module bundled by Vite -->
    <script type="module" src="/src/gateway/auth-init.ts"></script>
</body>

</html>