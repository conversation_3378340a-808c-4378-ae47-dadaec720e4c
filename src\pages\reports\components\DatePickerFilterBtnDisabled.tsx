import { forwardRef } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Button, Typography } from '@mui/material';
import { usePickerContext } from '@mui/x-date-pickers/hooks';

import type {
  FieldType,
  SingleInputDateRangeFieldProps,
} from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

interface DatePickerFilterBtnProps extends SingleInputDateRangeFieldProps {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

type DatePickerFilterBtnComponent = ((
  props: DatePickerFilterBtnProps & React.RefAttributes<HTMLDivElement>
) => React.JSX.Element) & { fieldType?: FieldType };

const DatePickerFilterBtnDisabled = forwardRef(
  (props: DatePickerFilterBtnProps, ref: React.Ref<HTMLElement>) => {
    const {
      setOpen,
      label,
      id,
      inputProps: { 'aria-label': ariaLabel } = {},
    } = props;

    const pickerContext = usePickerContext();

    return (
      <Button
        //@ts-ignore
        variant="contained-light"
        id={id}
        disabled={true}
        ref={pickerContext?.triggerRef}
        aria-label={ariaLabel}
        onClick={() => setOpen?.(prev => !prev)}
      >
        {/* @ts-ignore */}
        <Typography variant="label" fontWeight={500} color="custom.gray800">
          {label}
        </Typography>
      </Button>
    );
  }
) as DatePickerFilterBtnComponent;

DatePickerFilterBtnDisabled.fieldType = 'single-input';

export default DatePickerFilterBtnDisabled;
