import React, { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import {
  Box,
  Button,
  SxProps,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useTheme } from '~/contexts/ThemeContext';
import { ColumnConfig } from '../../../../../types/globals';
import SidePanelCard from '../components/SidePanelCard';
import TableInput from '../components/TableInput';
import styles from '../styles.module.css';
import { MainFilterSelect } from '../../MainFilterSelect';

type RowData = {
  [key: string]: any;
  items?: RowData[];
  subItems?: RowData[];
  extraData?: { [key: string]: any };
};

type CustomTableProps<T extends RowData> = {
  config: ColumnConfig<T>[];
  data: T[];
  scrollable?: boolean;
  hasFilters?: boolean;
  searchBar?: boolean;
  enableSidePanel?: boolean;
  renderSidePanel?: (rowData: T) => ReactNode;
  filter?: boolean;
  scrollHeight?: string;
  searchPlaceHolder?: string;
  searchMaxWidth?: number | string;
  separateFirstColumn?: boolean;
};

const SimpleTable = <T extends RowData>({
  config,
  data,
  scrollable,
  hasFilters = true,
  searchBar = true,
  enableSidePanel,
  renderSidePanel,
  scrollHeight,
  searchPlaceHolder,
  separateFirstColumn,
  searchMaxWidth,
}: CustomTableProps<T>) => {
  const { theme } = useTheme();
  const [selectedRowData, setSelectedRowData] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);
  const { t } = useTranslation();
  const filterHierarchy = (rows: RowData[], query: string): RowData[] => {
    return rows
      ?.map((row): RowData | null => {
        const filteredSubItems = row.subItems
          ? filterHierarchy(row.subItems, query)
          : [];

        const matchesRow = JSON.stringify(row).toLowerCase().includes(query);

        if (matchesRow || filteredSubItems.length > 0) {
          return {
            ...row,
            subItems: filteredSubItems,
          };
        }
        return null;
      })
      .filter((row): row is RowData => row !== null);
  };

  const filteredData = useMemo(() => {
    if (!searchQuery.trim().length) return data;

    const normalizedQuery = searchQuery.trim().toLowerCase();

    return filterHierarchy(data, normalizedQuery);
  }, [searchQuery, data]);

  const handleRowClick = (row: T, rowKey: string) => {
    if (enableSidePanel && row) {
      setSelectedRowData(row);
      setSelectedRowKey(rowKey);
      setIsModalOpen(true);
    }
  };

  const closeModal = () => {
    setSelectedRowKey(null);
    setIsModalOpen(false);
  };

  
  const OPTIONS = [
    'closedWith',
    'hasDiscounts',
    'hasTransfers',
    'hasBillsIssued',
  ];


  const renderRows = (rows: RowData[], parentKey = '') => {
    return rows?.map((row: any, rowIndex: number) => {
      const rowKey = `${parentKey}-${rowIndex}`;
      const isSelected = rowKey === selectedRowKey;

      return (
        <React.Fragment key={rowKey}>
          <TableRow
            onClick={() => {
              handleRowClick(row, rowKey);
            }}
            sx={{
              cursor: 'pointer',
              backgroundColor: isSelected ? '#0064F0' : 'transparent',
              '&:hover': {
                backgroundColor: isSelected
                  ? "'#0064F0'"
                  : theme.palette.mode == 'light'
                    ? '#F1F2F2 '
                    : '#222229 ',
              },

              '&:active': {
                backgroundColor: '#0064F0 ',
                outline: 'none ',
                boxShadow: 'none ',
                '-webkit-tap-highlight-color': 'transparent',
                transition: 'none ',
              },
              '&:active  td p': {
                color: '#FFFFFF !important',
              },
              '&:active  td span': {
                color: '#FFFFFF !important',
              },
              '&:active  td img': {
                filter:
                  'brightness(0) saturate(100%) invert(100%) sepia(7%) saturate(0%) hue-rotate(39deg) brightness(106%) contrast(109%) !important',
              },
              '&:focus': {
                backgroundColor: '#0064F0 !important',
                outline: 'none ',
                boxShadow: 'none ',
                '-webkit-tap-highlight-color': 'transparent',
                transition: 'none ',
              },
              '&:focus *': {
                color: '#FFFFFF !important',
                backgroundColor: '#0064F0 !important',
              },
            }}
          >
            {config?.map((column, colIndex) => (
              <TableCell key={`${rowKey}-${String(column.id)}`}>
                {column.render
                  ? column.render(row, rowIndex, colIndex, isSelected)
                  : row[column.id as keyof RowData] || ''}
              </TableCell>
            ))}
          </TableRow>
        </React.Fragment>
      );
    });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        justifyContent: 'space-between',
        width: '100%',
      }}
    >
      <Box
        sx={{
          width: '100%',
          backgroundColor:
            theme.palette.mode === 'dark' ? '#26262B' : '#F2F2F2',
          p: 2,
          borderTopLeftRadius: 10,
          borderTopRightRadius: 10,
        }}
      >
        {searchBar && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <TableInput
              searchMaxWidth={searchMaxWidth}
              searchPlaceHolder={searchPlaceHolder}
              onChange={setSearchQuery}
            />
            {hasFilters && <MainFilterSelect options={OPTIONS}/>}
          </Box>
        )}
      </Box>

      <TableContainer
        className={styles.table}
        sx={
          {
            overflowY: scrollable ? 'visible' : 'visible',
            overflowX: 'auto !important',
            width: '100%',
            maxWidth: { xs: 'calc(100vw - 20px)' },
            '@media print': {
              overflowX: 'visible !important',
              maxWidth: {
                xs: '100%!important',
              },
            },
            maxHeight:
              !searchQuery && scrollable
                ? {
                    sm: scrollHeight ? scrollHeight : 'calc(100vh - 300px)',
                    xs: 'calc(100vh - 370px)',
                  }
                : undefined,
          } as SxProps<Theme>
        }
      >
        <Table stickyHeader>
          <TableHead sx={{ display: 'none' }}>
            <TableRow>
              {config?.map((column, index) => {
                const isFirstColumn = index === 0;
                const isLastColumn = config.length - 1 === index;
                return (
                  <TableCell
                    key={column.id as string}
                    sx={{
                      display: 'none',
                      '@media print': {
                        backgroundColor: '#FFFFFF !important',
                        color: 'black !important',
                      },
                      textAlign: index === config.length - 1 ? 'right' : 'left',
                      width: !isLastColumn ? '10%' : 'auto',
                      borderRight:
                        isFirstColumn && separateFirstColumn
                          ? '1px #cecece dashed'
                          : '',
                      backgroundColor:
                        theme.palette.mode == 'light' ? '#F2F2F2' : '#26262B',
                      cursor: enableSidePanel ? 'pointer' : 'default',
                    }}
                  >
                    {column.label}
                  </TableCell>
                );
              })}
            </TableRow>
          </TableHead>

          {filteredData.length <= 0 ? (
            <TableRow>
              <TableCell colSpan={config.length} align="center">
                <Box sx={{ width: '100%', textAlign: 'center' }}>{t('reportsPage.noData')}</Box>
              </TableCell>
            </TableRow>
          ) : (
            <TableBody>{renderRows(filteredData)}</TableBody>
          )}
        </Table>
      </TableContainer>

      {enableSidePanel && selectedRowData && (
        <SidePanelCard
          open={isModalOpen}
          extraData={selectedRowData}
          onClose={closeModal}
        >
          {renderSidePanel?.(selectedRowData)}
        </SidePanelCard>
      )}
    </Box>
  );
};

export default SimpleTable;
