import { useMemo, useState } from 'react';
import { InfoOutlined } from '@mui/icons-material';
import { Box, Button, InputAdornment, Typography } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useRedirect,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import { MuiSwitchInput } from '~/components/atoms/inputs/SwitchInput';
import ControlledRadioInputGroup from '~/components/molecules/controlled-input-groups/ControlledRadioInputGroup';
import { RESOURCES, useGetListVatsLive } from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';
import { ItemsDialog } from './ItemsDialog';

function SectionCreateInner(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const { setValue, getValues, watch } = useFormContext();

  const [itemsDialogOpen, setItemsDialogOpen] = useState(false);
  setValue('conditions[0].operator', 'in');
  setValue('conditions[0].source', 'product');
  setValue('conditions[0].property', 'id');

  setValue('conditions[1].constraint', ['100']);
  setValue('conditions[1].operator', 'neq');
  setValue('conditions[1].source', 'bill');
  setValue('conditions[1].property', 'discount');

  /**
   * This function is called before submitting a form. It creates an array of table objects based
   * on the number of tables specified in the form.
   */
  const beforeHandleSubmit = (toGo: boolean, appliedTo: 'product' | 'bill') => {
    if (toGo) {
      setValue('conditions[2].constraint', ['togo']);
      setValue('conditions[2].operator', 'eq');
      setValue('conditions[2].source', 'bill');
      setValue('conditions[2].property', 'dinningOption');
    }

    appliedTo === 'product'
      ? setValue('appliesTo', 'product')
      : setValue('appliesTo', 'bill');
  };

  const handleClose = () => {
    redirect('list', RESOURCES.EXTRA_CHARGES);
  };

  const { t } = useTranslation();

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      label: `${vat.value}%`,
    }));
  }, [vats]);
  const [selectedItems, setSelectedItems] = useState<any>([]);
  const [toGo, setToGo] = useState<boolean>(false);
  const [appliedTo, setAppliedTo] = useState<'product' | 'bill'>('product');

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('extraCharges.createExtraCharge')}
      >
        <SaveButton
          onClick={() => beforeHandleSubmit(toGo, appliedTo)}
          type="button"
          label={t('shared.save')}
          alwaysEnable
          icon={<></>}
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: { xs: 2, md: 8 },
          gap: 0,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            border: '1px solid',
            backgroundColor: '#E6F0FF',
            borderColor: '#0064F0',
            p: 2,
            borderRadius: '8px',
            mb: 2,
          }}
        >
          <InfoOutlined sx={{ color: '#0064F0' }} />
          <Typography variant="body2">
            {t('extraCharges.sectionCreateInnerDescription')}
          </Typography>
        </Box>
        <ReferenceInput source="sellPointId" reference={RESOURCES.LOCATIONS}>
          <CustomInput
            type="select"
            source="sellPointId"
            ui="custom"
            label={t('shared.location')}
            placeholder={t('shared.none', { context: 'female' })}
            optionText="name"
            optionValue="id"
            defaultValue={sellPointId}
            readOnly
            roundedCorners="top"
          />
        </ReferenceInput>
        <CustomInput
          type="text"
          ui="custom"
          sanitize="singleLine"
          source="name"
          label={t('shared.name')}
          placeholder={t('extraCharges.sgrFeePlaceholder')}
          isRequired
          validate={required()}
        />
        <ControlledRadioInputGroup
          title={t('extraCharges.appliedTo')}
          value={appliedTo}
          setValue={val => {
            setAppliedTo(val);
          }}
          choices={[
            {
              id: 'product',
              name: t('extraCharges.item'),
              description: t('extraCharges.itemDescription'),
            },
            {
              id: 'bill',
              name: t('extraCharges.bill'),
              description: t('extraCharges.billDescription'),
              disabled: true,
            },
          ]}
        />
        <CustomInput
          source="charge.fixed.value"
          label={t('shared.amount')}
          type="number"
          // locale="ro-RO"
          format={v => (v ? v / 10000 : '')}
          parse={v => Math.floor(v * 10000)}
          // options={{
          //   style: 'currency',
          //   currency: 'RON',
          // }}
          slotProps={{
            input: {
              endAdornment: <InputAdornment position="end">Lei</InputAdornment>,
            },
          }}
          isRequired
          validate={required()}
        />
        <CustomInput
          source="charge.fixed.vat"
          type="autocomplete"
          ui="custom"
          label={t('shared.tva')}
          choices={vatsChoices}
          optionText="label"
          optionValue="id"
          selectOnFocus={false}
          validate={[required()]}
          roundedCorners="bottom"
        />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            pl: 2,
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography>{t('extraCharges.applyToItems')}</Typography>
            <Typography variant="subtitle2">
              {selectedItems.length > 0
                ? `${selectedItems.length > 1 ? t('shared.item_few', { count: selectedItems.length }) : t('shared.item_one', { count: selectedItems.length })}`
                : t('shared.none')}
            </Typography>
          </Box>
          <Button
            variant="text"
            color="primary"
            onClick={() => {
              setItemsDialogOpen(true);
            }}
          >
            {t('shared.select')}
          </Button>
        </Box>

        <CustomDivider />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            pl: 2,
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography>
              {t('extraCharges.applyAutomaticallyOnlyForTOGOOrders')}
            </Typography>
            <Typography variant="subtitle2">
              {t('extraCharges.applyAutomaticallyOnlyForTOGOOrdersDescription')}
            </Typography>
          </Box>
          <MuiSwitchInput
            checked={toGo}
            onChange={event => setToGo(event.target.checked)}
            sx={{
              marginRight: 0,
            }}
          />
        </Box>

        <CustomDivider />
      </Box>

      <ItemsDialog
        open={itemsDialogOpen}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        onClose={items => {
          setSelectedItems(items);
          setValue('conditions[0].constraint', items);
          setItemsDialogOpen(false);
        }}
      />
    </>
  );
}

export default function ExtraChargesCreate(props: { sellPointId: string }) {
  const { sellPointId } = props;
  if (!sellPointId) {
    return null;
  }
  return (
    <CreateDialog
      maxWidth="sm"
      fullWidth
      fullScreen
      mutationOptions={{ meta: { sellPointId: sellPointId } }}
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <SectionCreateInner sellPointId={sellPointId} />
      </SimpleForm>
    </CreateDialog>
  );
}
