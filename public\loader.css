/* Shared loading spinner styles */
body {
    margin: 0;
    background-color: #f5f5f5;
}

#initial-loader,
#loading-state,
.loading-state {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    gap: 24px;
}

#initial-loader.hidden {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease-out;
}

@keyframes loader_5191 {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.square {
    background: #999;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -5px;
    margin-left: -5px;
}

#sq1 {
    margin-top: -25px;
    margin-left: -25px;
    animation: loader_5191 675ms ease-in-out 0s infinite alternate;
}

#sq2 {
    margin-top: -25px;
    animation: loader_5191 675ms ease-in-out 75ms infinite alternate;
}

#sq3 {
    margin-top: -25px;
    margin-left: 15px;
    animation: loader_5191 675ms ease-in-out 150ms infinite;
}

#sq4 {
    margin-left: -25px;
    animation: loader_5191 675ms ease-in-out 225ms infinite;
}

#sq5 {
    animation: loader_5191 675ms ease-in-out 300ms infinite;
}

#sq6 {
    margin-left: 15px;
    animation: loader_5191 675ms ease-in-out 375ms infinite;
}

#sq7 {
    margin-top: 15px;
    margin-left: -25px;
    animation: loader_5191 675ms ease-in-out 450ms infinite;
}

#sq8 {
    margin-top: 15px;
    animation: loader_5191 675ms ease-in-out 525ms infinite;
}

#sq9 {
    margin-top: 15px;
    margin-left: 15px;
    animation: loader_5191 675ms ease-in-out 600ms infinite;
}

.spinner-logo {
    position: absolute;
    top: 119%;
    left: 50%;
    transform: translate(-50%, -50%);
    filter: brightness(0) saturate(100%) invert(50%) sepia(0%);
}

.loader {
    position: relative;
    width: 50px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}
