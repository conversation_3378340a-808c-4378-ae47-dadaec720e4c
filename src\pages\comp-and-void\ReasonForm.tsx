import { useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Divider, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import MuiCustomInput from '../../components/atoms/inputs/MuiCustomInput';

interface ReasonFormProps {
  isEdit?: boolean;
  initialValue?: string;
  reasonType?: 'comp' | 'void';
  onClose: () => void;
  onSave: (data: string) => void;
  onDelete: () => void;
}
export default function ReasonForm({
  isEdit,
  reasonType,
  initialValue = '',
  onClose,
  onSave,
  onDelete,
}: ReasonFormProps) {
  const { t } = useTranslation();
  const [value, setValue] = useState(initialValue);

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          position: 'relative',
          px: 2,
          py: 3,
        }}
      >
        <Button
          onClick={onClose}
          // @ts-ignore
          variant="close-btn"
          aria-label="close"
          sx={{
            '& span': { mr: 0 },
            position: 'absolute',
            top: '16px',
            left: '20px',
          }}
        >
          <CloseIcon fontSize="small" />
        </Button>
        <Typography variant="h2">
          {isEdit ? t('shared.edit') : t('shared.create')} {reasonType} reason
        </Typography>
      </Box>
      <Divider />
      <Box sx={{ p: 4 }}>
        <MuiCustomInput
          fullWidth
          value={value}
          onChange={event => setValue(event.target.value)}
          label={t('compAndVoidPage.reason')}
        />
      </Box>
      <Divider />
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {/* @ts-ignore */}
          <Button onClick={onClose} variant="contained-light">
            {t('shared.cancel')}
          </Button>
          {isEdit ? (
            //   @ts-ignore
            (<Button onClick={onDelete} variant="contained-light" color="error">
              {t('shared.delete')}
            </Button>)
          ) : (
            <></>
          )}
        </Box>
        <Button
          onClick={() => onSave(value)}
          variant="contained"
          disabled={initialValue === value || !value.trim()}
        >
          {isEdit ? t('shared.save') : t('shared.create')}
        </Button>
      </Box>
    </Box>
  );
}
