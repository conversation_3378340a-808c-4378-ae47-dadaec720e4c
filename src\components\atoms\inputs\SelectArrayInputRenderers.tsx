import { ReactNode } from 'react';
import { Box, Chip, ListItemText } from '@mui/material';
import { useRecordContext } from 'react-admin';

/**
 * Generic option renderer for SelectArrayInput dropdown items
 * Shows primary text and optional secondary/description text
 * Uses useRecordContext to access the choice data
 *
 * @example
 * // Basic usage with default 'name' and 'description' fields
 * <CustomInput
 *   type="selectArray"
 *   optionText={<SelectArrayOptionRenderer />}
 *   choices={[{ id: '1', name: 'Title', description: 'Description' }]}
 * />
 *
 * @example
 * // Custom field names
 * <CustomInput
 *   type="selectArray"
 *   optionText={<SelectArrayOptionRenderer primaryField="title" secondaryField="subtitle" />}
 *   choices={[{ id: '1', title: 'Title', subtitle: 'Subtitle' }]}
 * />
 */
export const SelectArrayInputOptionRenderer = ({
  primaryField = 'name',
  secondaryField = 'description',
}: {
  primaryField?: string;
  secondaryField?: string;
}) => {
  const record = useRecordContext();
  return (
    <ListItemText
      primary={record?.[primaryField]}
      secondary={record?.[secondaryField]}
    />
  );
};

/**
 * Creates a chip selection renderer that uses the choices array to look up labels
 * This is the recommended approach when choices contain all needed data
 *
 * @param choices - The choices array with id and name properties
 * @param labelField - The field to use for chip labels (default: 'name')
 * @returns A render function compatible with MUI Select's renderValue prop
 *
 * @example
 * const choices = dietaryPreferencesOptions.map(key => ({
 *   id: key,
 *   name: t(`add-item.dietary-preferences.${key}.title`),
 *   description: t(`add-item.dietary-preferences.${key}.description`),
 * }));
 *
 * <CustomInput
 *   type="selectArray"
 *   choices={choices}
 *   options={{ renderValue: createChipSelectionRendererFromChoices(choices) }}
 * />
 */
export const createChipSelectionRendererFromChoices = <
  T extends { id: string; [key: string]: any },
>(
  choices: T[],
  labelField: keyof T = 'name' as keyof T
) => {
  return (selected: unknown): ReactNode => (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
      {(selected as string[]).map(id => {
        const choice = choices.find(c => c.id === id);
        const label = choice ? String(choice[labelField]) : id;
        return <Chip key={id} label={label} size="small" />;
      })}
    </Box>
  );
};

/**
 * Creates a selection renderer function for SelectArrayInput
 * Renders selected items as chips using a custom label function
 *
 * @param getLabelFn - Function to get the label for each selected id
 * @returns A render function compatible with MUI Select's renderValue prop
 *
 * @example
 * // Basic usage with translation
 * const { t } = useTranslation();
 * const renderDietaryPreferences = createChipSelectionRenderer(
 *   (id) => t(`add-item.dietary-preferences.${id}.title`)
 * );
 *
 * <CustomInput
 *   type="selectArray"
 *   options={{ renderValue: renderDietaryPreferences }}
 * />
 */
export const createChipSelectionRenderer = (
  getLabelFn: (id: string) => string
) => {
  return (selected: unknown): ReactNode => (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
      {(selected as string[]).map(id => (
        <Chip key={id} label={getLabelFn(id)} size="small" />
      ))}
    </Box>
  );
};

/**
 * Creates a simple comma-separated text renderer for SelectArrayInput
 *
 * @param getLabelFn - Function to get the label for each selected id
 * @returns A render function compatible with MUI Select's renderValue prop
 *
 * @example
 * const renderAsText = createTextSelectionRenderer(
 *   (id) => t(`items.${id}.name`)
 * );
 *
 * <CustomInput
 *   type="selectArray"
 *   options={{ renderValue: renderAsText }}
 * />
 */
export const createTextSelectionRenderer = (
  getLabelFn: (id: string) => string
) => {
  return (selected: unknown): string =>
    (selected as string[]).map(getLabelFn).join(', ');
};
