import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Checkbox, Typography } from '@mui/material';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export default function AccessPointsSelect() {
  const { setValue } = useFormContext();
  const { t } = useTranslation();

  // Watch access for changes (including from auto-merge)
  const accessFormValue = useWatch({ name: 'access' }) ?? [];

  // Note: access field tracking is done in PermissionsForm (parent) to ensure
  // it's always mounted regardless of which step the user is on

  const accessPoints = useMemo(
    () => [
      {
        id: 'all',
        title: t('createPermissions.step2.all'),
        subtitle: t('createPermissions.step2.allDescription'),
      },
      {
        id: 'pos',
        title: t('createPermissions.step2.sharedPointsOfSale'),
        subtitle: t('createPermissions.step2.sharedPointsOfSaleDescription'),
      },
      {
        id: 'manager',
        title: t('createPermissions.step2.dashboard'),
        subtitle: t('createPermissions.step2.dashboardDescription'),
      },
    ],
    [t]
  );

  const [selectedAccessPoints, setSelectedAccessPoints] = useState<string[]>(
    () => {
      // Initialize from form value (which may have been auto-merged)
      if (!accessFormValue || accessFormValue.length === 0) {
        return ['all'];
      }
      return accessFormValue;
    }
  );

  // Track if we're making local changes to avoid reacting to our own updates
  const isLocalChange = useRef(false);

  // Sync local state when form value changes from external source (auto-merge)
  useEffect(() => {
    if (isLocalChange.current) {
      isLocalChange.current = false;
      return;
    }

    // Convert form value to UI state
    // Empty array in form means "all" is selected
    if (!accessFormValue || accessFormValue.length === 0) {
      setSelectedAccessPoints(['all']);
    } else {
      setSelectedAccessPoints(accessFormValue);
    }
  }, [accessFormValue]);

  // Update form value when local selection changes
  const updateFormValue = (newSelection: string[]) => {
    isLocalChange.current = true;
    setSelectedAccessPoints(newSelection);

    // If "all" is selected, set form value to empty array
    // Otherwise, filter out "all" and set the specific access points
    const formValue = newSelection.includes('all')
      ? []
      : newSelection.filter(point => point !== 'all');

    setValue('access', formValue, {
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  const toggleOption = (id: string) => {
    let newSelection: string[];
    if (id === 'all') {
      // If "all" is clicked, deselect everything else and select only "all"
      newSelection = ['all'];
    } else {
      // If a specific option is clicked
      if (selectedAccessPoints.includes(id)) {
        // Remove the specific access point
        const filtered = selectedAccessPoints.filter(p => p !== id);
        // If no specific options are left, select "all"
        newSelection =
          filtered.length === 0 || filtered.every(p => p === 'all')
            ? ['all']
            : filtered;
      } else {
        // Add the specific access point and remove "all" if present
        newSelection = [...selectedAccessPoints.filter(p => p !== 'all'), id];
      }
    }
    updateFormValue(newSelection);
  };

  return (
    <>
      {accessPoints.map(accessPoint => (
        <Box
          key={accessPoint.id}
          onClick={() => toggleOption(accessPoint.id)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '25px',
            border: '1px solid',
            borderColor: selectedAccessPoints?.includes(accessPoint.id)
              ? 'primary.main'
              : 'custom.gray400',
            borderRadius: '6px',
            px: 3,
            py: 2,
            cursor: 'pointer',
            ':hover': {
              bgcolor: 'primary.veryLight',
            },
          }}
        >
          <Box>
            <Typography
              variant="h6"
              color={
                selectedAccessPoints?.includes(accessPoint.id)
                  ? 'inherit'
                  : 'custom.gray600'
              }
            >
              {accessPoint.title}
            </Typography>
            <Typography variant="subtitle2">{accessPoint.subtitle}</Typography>
          </Box>
          <Checkbox
            checked={selectedAccessPoints?.includes(accessPoint.id)}
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </Box>
      ))}
    </>
  );
}
