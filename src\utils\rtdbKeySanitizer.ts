/* eslint-disable @typescript-eslint/no-explicit-any, no-prototype-builtins, valid-jsdoc, no-control-regex */

// Firebase RTDB forbidden characters: . $ # [ ] / and ASCII control characters 0-31 and 127
// eslint-disable-next-line no-control-regex
const FORBIDDEN_CHARS = /[.#$/[\]\x00-\x1F\x7F]/g;
const REPLACEMENT_MAP = new Map([
    // Special characters
    [".", "∘"], ["#", "♯"], ["$", "＄"],
    ["/", "∕"], ["[", "［"], ["]", "］"],
    // ASCII control characters 0-31
    ["\x00", "␀"], ["\x01", "␁"], ["\x02", "␂"], ["\x03", "␃"],
    ["\x04", "␄"], ["\x05", "␅"], ["\x06", "␆"], ["\x07", "␇"],
    ["\x08", "␈"], ["\x09", "␉"], ["\x0A", "␊"], ["\x0B", "␋"],
    ["\x0C", "␌"], ["\x0D", "␍"], ["\x0E", "␎"], ["\x0F", "␏"],
    ["\x10", "␐"], ["\x11", "␑"], ["\x12", "␒"], ["\x13", "␓"],
    ["\x14", "␔"], ["\x15", "␕"], ["\x16", "␖"], ["\x17", "␗"],
    ["\x18", "␘"], ["\x19", "␙"], ["\x1A", "␚"], ["\x1B", "␛"],
    ["\x1C", "␜"], ["\x1D", "␝"], ["\x1E", "␞"], ["\x1F", "␟"],
    // ASCII control character 127
    ["\x7F", "␡"],
]);

const REVERSE_MAP = new Map([
    // Special characters
    ["∘", "."], ["♯", "#"], ["＄", "$"],
    ["∕", "/"], ["［", "["], ["］", "]"],
    // ASCII control characters 0-31
    ["␀", "\x00"], ["␁", "\x01"], ["␂", "\x02"], ["␃", "\x03"],
    ["␄", "\x04"], ["␅", "\x05"], ["␆", "\x06"], ["␇", "\x07"],
    ["␈", "\x08"], ["␉", "\x09"], ["␊", "\x0A"], ["␋", "\x0B"],
    ["␌", "\x0C"], ["␍", "\x0D"], ["␎", "\x0E"], ["␏", "\x0F"],
    ["␐", "\x10"], ["␑", "\x11"], ["␒", "\x12"], ["␓", "\x13"],
    ["␔", "\x14"], ["␕", "\x15"], ["␖", "\x16"], ["␗", "\x17"],
    ["␘", "\x18"], ["␙", "\x19"], ["␚", "\x1A"], ["␛", "\x1B"],
    ["␜", "\x1C"], ["␝", "\x1D"], ["␞", "\x1E"], ["␟", "\x1F"],
    // ASCII control character 127
    ["␡", "\x7F"],
]);

// Maximum key length in bytes for Firebase RTDB
const MAX_KEY_BYTES = 768;

// Unique suffix separator for truncated keys - using a character that won't appear in normal text
const HASH_SUFFIX_SEPARATOR = "‖"; // U+2016 (double vertical line)

/**
 * Sanitizes a string to be safe for use as Firebase RTDB key
 * Handles forbidden characters and enforces 768-byte limit
 * @param {string} key - The key to sanitize
 * @return {string} The sanitized key
 */
export function sanitizeRtdbKey(key: string): string {
    if (typeof key !== "string") {
        throw new Error("Key must be a string");
    }

    // Replace forbidden characters
    let sanitized = key.replace(FORBIDDEN_CHARS, (char) => REPLACEMENT_MAP.get(char) || char);

    // Check byte length and truncate if necessary
    const encoder = new TextEncoder();
    const sanitizedBytes = encoder.encode(sanitized);

    if (sanitizedBytes.length > MAX_KEY_BYTES) {
        // Generate hash suffix first to calculate available space
        const hashSuffix = HASH_SUFFIX_SEPARATOR + hashString(sanitized).substring(0, 6);
        const suffixBytes = encoder.encode(hashSuffix);
        const availableSpace = MAX_KEY_BYTES - suffixBytes.length;

        // Ensure we have enough space for the suffix
        if (availableSpace <= 0) {
            throw new Error("Key is too long even after maximum truncation");
        }

        // Binary search to find the longest valid substring that fits with suffix
        let start = 0;
        let end = sanitized.length;

        while (start < end) {
            const mid = Math.floor((start + end + 1) / 2);
            const substring = sanitized.substring(0, mid);
            const testBytes = encoder.encode(substring);

            if (testBytes.length <= availableSpace) {
                start = mid;
            } else {
                end = mid - 1;
            }
        }

        const truncated = sanitized.substring(0, start);
        sanitized = truncated + hashSuffix;
    }

    return sanitized;
}

/**
 * Restores a sanitized Firebase RTDB key back to its original form
 * Removes hash suffix if present (for truncated keys)
 * @param {string} key - The key to restore
 * @return {string} The restored key
 */
export function unSanitizeRtdbKey(key: string): string {
    if (typeof key !== "string") {
        return key;
    }

    // Remove hash suffix if present (for truncated keys)
    let cleanKey = key;
    const suffixIndex = key.indexOf(HASH_SUFFIX_SEPARATOR);
    if (suffixIndex !== -1) {
        cleanKey = key.substring(0, suffixIndex);
    }

    // Restore forbidden characters
    return cleanKey.replace(/[∘♯＄∕［］␀-␟␡]/g, (char) => REVERSE_MAP.get(char) || char);
}

/**
 * Sanitizes a string value to remove ASCII control characters
 * Firebase RTDB doesn't allow control characters in values either
 * @param {string} value - The value to sanitize
 * @return {string} The sanitized value
 */
export function sanitizeRtdbValue(value: string): string {
    if (typeof value !== "string") {
        return value;
    }

    // eslint-disable-next-line no-control-regex
    return value.replace(/[\x00-\x1F\x7F]/g, (char) => REPLACEMENT_MAP.get(char) || "");
}

/**
 * Restores a sanitized value back to its original form
 * @param {string} value - The value to restore
 * @return {string} The restored value
 */
export function unSanitizeRtdbValue(value: string): string {
    if (typeof value !== "string") {
        return value;
    }

    return value.replace(/[␀-␟␡]/g, (char) => REVERSE_MAP.get(char) || char);
}

// Shared TextEncoder instance for performance
const ENCODER = new TextEncoder();

/**
 * Optimized version specifically for 95% clean / 5% dirty use case
 * Checks if sanitization is needed before processing
 * @param {T} obj - The object to sanitize
 * @return {T} The sanitized object
 */
export function sanitizeRtdbObjectKeys<T>(obj: T): T {
    // Fast path: if object is completely clean, return original unchanged
    if (!needsSanitization(obj)) {
        return obj;
    }

    // Fallback to in-place sanitization for the rare dirty cases
    return sanitizeRtdbObjectKeysInPlace(obj);
}

/**
 * Legacy implementation - kept for backwards compatibility
 * Creates new objects instead of modifying in-place
 * @param {T} obj - The object to sanitize
 * @return {T} The sanitized object
 */
export function sanitizeRtdbObjectKeysImmutable<T>(obj: T): T {
    if (obj === null || obj === undefined) {
        return obj;
    }

    if (typeof obj === "string") {
        return sanitizeRtdbValue(obj) as T;
    }

    if (typeof obj !== "object") {
        return obj;
    }

    if (Array.isArray(obj)) {
        return obj.map((item) => sanitizeRtdbObjectKeysImmutable(item)) as T;
    }

    const result = {} as T;
    for (const [key, value] of Object.entries(obj)) {
        const sanitizedKey = sanitizeRtdbKey(key);
        const sanitizedValue = sanitizeRtdbObjectKeysImmutable(value);
        (result as any)[sanitizedKey] = sanitizedValue;
    }

    return result;
}

/**
 * Streamlined in-place sanitization for dirty objects
 * Simplified two-pass approach for maximum performance
 */
function sanitizeRtdbObjectKeysInPlace<T>(obj: T): T {
    if (obj === null || obj === undefined) {
        return obj;
    }

    if (typeof obj === "string") {
        return sanitizeRtdbValue(obj) as T;
    }

    if (typeof obj !== "object") {
        return obj;
    }

    if (Array.isArray(obj)) {
        // Mutate array elements in-place
        for (let i = 0; i < obj.length; i++) {
            obj[i] = sanitizeRtdbObjectKeysInPlace(obj[i]);
        }
        return obj;
    }

    // Two-pass approach: rename keys and process values
    const keysToRename: Array<{ oldKey: string; newKey: string; value: any }> = [];

    // First pass: collect keys that need renaming and their values
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const sanitizedKey = sanitizeRtdbKey(key);
            const originalValue = (obj as any)[key];

            if (sanitizedKey !== key) {
                keysToRename.push({ oldKey: key, newKey: sanitizedKey, value: originalValue });
                delete (obj as any)[key];
            } else {
                // Process value in-place for keys that don't need renaming
                (obj as any)[key] = sanitizeRtdbObjectKeysInPlace(originalValue);
            }
        }
    }

    // Second pass: add renamed keys with sanitized values
    for (const { newKey, value } of keysToRename) {
        (obj as any)[newKey] = sanitizeRtdbObjectKeysInPlace(value);
    }

    return obj;
}

/**
 * Ultra-fast dirty check optimized for 95% clean objects
 * Single pass with minimal allocations
 */
function needsSanitization(obj: any): boolean {
    const stack = [obj];

    while (stack.length > 0) {
        const current = stack.pop();

        if (current === null || current === undefined) {
            continue;
        }

        if (typeof current === "string") {
            // Check for control characters in string values
            if (/[\x00-\x1F\x7F]/.test(current)) {
                return true;
            }
        } else if (typeof current === "object") {
            if (Array.isArray(current)) {
                // Add array elements to stack
                for (let i = 0; i < current.length; i++) {
                    stack.push(current[i]);
                }
            } else {
                // Check object keys and add values to stack
                for (const key in current) {
                    if (current.hasOwnProperty(key)) {
                        // Quick forbidden character check
                        if (FORBIDDEN_CHARS.test(key)) {
                            return true;
                        }

                        // Quick byte length check (using shared encoder)
                        if (ENCODER.encode(key).length > MAX_KEY_BYTES) {
                            return true;
                        }

                        stack.push(current[key]);
                    }
                }
            }
        }
    }

    return false;
}

/**
 * Optimized unsanitization with fast path for objects that don't need processing
 * Checks if object contains sanitized characters before processing
 */
export function unSanitizeRtdbObjectKeys<T>(obj: T): T {
    // Fast path: if object contains no sanitized characters, return original unchanged
    if (!needsUnsanitization(obj)) {
        return obj;
    }

    // Fallback to in-place unsanitization for objects with sanitized data
    return unSanitizeRtdbObjectKeysInPlace(obj);
}

/**
 * Legacy implementation - kept for backwards compatibility
 * Creates new objects instead of modifying in-place
 */
export function unSanitizeRtdbObjectKeysImmutable<T>(obj: T): T {
    if (obj === null || obj === undefined) {
        return obj;
    }

    if (typeof obj === "string") {
        return unSanitizeRtdbValue(obj) as T;
    }

    if (typeof obj !== "object") {
        return obj;
    }

    if (Array.isArray(obj)) {
        return obj.map((item) => unSanitizeRtdbObjectKeysImmutable(item)) as T;
    }

    const result = {} as T;
    for (const [key, value] of Object.entries(obj)) {
        const restoredKey = unSanitizeRtdbKey(key);
        const restoredValue = unSanitizeRtdbObjectKeysImmutable(value);
        (result as any)[restoredKey] = restoredValue;
    }

    return result;
}

/**
 * Streamlined in-place unsanitization for objects with sanitized data
 * Simplified two-pass approach for maximum performance
 */
function unSanitizeRtdbObjectKeysInPlace<T>(obj: T): T {
    if (obj === null || obj === undefined) {
        return obj;
    }

    if (typeof obj === "string") {
        return unSanitizeRtdbValue(obj) as T;
    }

    if (typeof obj !== "object") {
        return obj;
    }

    if (Array.isArray(obj)) {
        // Mutate array elements in-place
        for (let i = 0; i < obj.length; i++) {
            obj[i] = unSanitizeRtdbObjectKeysInPlace(obj[i]);
        }
        return obj;
    }

    // Two-pass approach: rename keys and process values
    const keysToRename: Array<{ oldKey: string; newKey: string; value: any }> = [];

    // First pass: collect keys that need renaming and their values
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const restoredKey = unSanitizeRtdbKey(key);
            const originalValue = (obj as any)[key];

            if (restoredKey !== key) {
                keysToRename.push({ oldKey: key, newKey: restoredKey, value: originalValue });
                delete (obj as any)[key];
            } else {
                // Process value in-place for keys that don't need renaming
                (obj as any)[key] = unSanitizeRtdbObjectKeysInPlace(originalValue);
            }
        }
    }

    // Second pass: add renamed keys with unsanitized values
    for (const { newKey, value } of keysToRename) {
        (obj as any)[newKey] = unSanitizeRtdbObjectKeysInPlace(value);
    }

    return obj;
}

/**
 * Ultra-fast check for sanitized characters optimized for clean objects
 * Single pass with minimal allocations
 */
function needsUnsanitization(obj: any): boolean {
    const stack = [obj];

    while (stack.length > 0) {
        const current = stack.pop();

        if (current === null || current === undefined) {
            continue;
        }

        if (typeof current === "string") {
            // Check for sanitized control characters in string values
            if (/[␀-␟␡]/.test(current)) {
                return true;
            }
        } else if (typeof current === "object") {
            if (Array.isArray(current)) {
                // Add array elements to stack
                for (let i = 0; i < current.length; i++) {
                    stack.push(current[i]);
                }
            } else {
                // Check object keys and add values to stack
                for (const key in current) {
                    if (current.hasOwnProperty(key)) {
                        // Quick check for sanitized characters in keys
                        if (/[∘♯＄∕［］␀-␟␡]/.test(key)) {
                            return true;
                        }

                        // Check for hash suffix separator (truncated keys)
                        if (key.includes(HASH_SUFFIX_SEPARATOR)) {
                            return true;
                        }

                        stack.push(current[key]);
                    }
                }
            }
        }
    }

    return false;
}

/**
 * Simple hash function for generating unique suffixes
 * @param {string} str - The string to hash
 * @return {string} The hash string
 */
function hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
}
