import { useMemo } from 'react';
import { Theme, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { useRecordContext } from 'react-admin';

import { ConflictAwareSimpleForm } from '~/components/conflict-detection';
import {
  MyIntegrationsOrderNowCreateEdit,
  validateOrderNowForm,
} from './MyIntegrationsOrderNowCreateEdit';
import { MyIntegrationsSagaCreateEdit } from './MyIntegrationsSagaCreateEdit';
import { MyIntegrationsWinMentorCreateEdit } from './MyIntegrationsWinMentorCreateEdit';

const MyIntegrationsEditInner = () => {
  const record = useRecordContext();

  const validate = useMemo(() => {
    if (record?.id === 'orderNow') {
      return (values: any) => validateOrderNowForm(values);
    }
    return undefined;
  }, [record?.id]);

  return (
    <ConflictAwareSimpleForm
      toolbar={<></>}
      sx={{ p: 0 }}
      translationNamespace="shared"
      validate={validate}
    >
      {record?.id === 'winMentor' ? (
        <MyIntegrationsWinMentorCreateEdit mode="edit" />
      ) : record?.id === 'orderNow' ? (
        <MyIntegrationsOrderNowCreateEdit mode="edit" />
      ) : record?.id === 'saga' ? (
        <MyIntegrationsSagaCreateEdit mode="edit" />
      ) : (
        <></>
      )}
    </ConflictAwareSimpleForm>
  );
};

export const MyIntegrationsEdit = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <EditDialog fullScreen={true} mutationMode="pessimistic">
      <MyIntegrationsEditInner />
    </EditDialog>
  );
};
