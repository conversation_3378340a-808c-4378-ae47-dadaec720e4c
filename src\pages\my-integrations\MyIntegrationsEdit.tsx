import { Theme, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { SimpleForm, useRecordContext } from 'react-admin';

import { MyIntegrationsOrderNowCreateEdit } from './MyIntegrationsOrderNowCreateEdit';
import { MyIntegrationsSagaCreateEdit } from './MyIntegrationsSagaCreateEdit';
import { MyIntegrationsWinMentorCreateEdit } from './MyIntegrationsWinMentorCreateEdit';

const MyIntegrationsEditInner = () => {
  const record = useRecordContext();
  return (
    <>
      {record?.id === 'winMentor' ? (
        <MyIntegrationsWinMentorCreateEdit mode="edit" />
      ) : record?.id === 'orderNow' ? (
        <MyIntegrationsOrderNowCreateEdit mode="edit" />
      ) : record?.id === 'saga' ? (
        <MyIntegrationsSagaCreateEdit mode="edit" />
      ) : (
        <></>
      )}
    </>
  );
};

export const MyIntegrationsEdit = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  return (
    <EditDialog fullScreen={true} mutationMode="pessimistic">
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <MyIntegrationsEditInner />
      </SimpleForm>
    </EditDialog>
  );
};
