import { useEffect, useMemo, useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  Dialog,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  inputBaseClasses,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  SimpleForm,
  TextInput,
  TranslatableInputs,
  useCreate,
  useGetList,
  useLocaleState,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { MuiSwitchInput } from '~/components/atoms/inputs/SwitchInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import {
  FilePreviewInline,
  FileUploadComponent,
} from '~/components/organisms/FileUpload';
import { UploadedFile } from '~/components/organisms/FileUpload/types/fileUpload';
import {
  createStandardImageConfig,
  resolveImageEditorConfig,
} from '~/components/organisms/FileUpload/utils/standardImageEditor';
import { menuColors } from '~/data/menu-colors';
import { getValueByPath } from '~/pages/menus/utils';
import {
  RESOURCES,
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
  useGetListMeasureUnits,
  useGetListVatsLive,
} from '~/providers/resources';
import changeItemInNestedObject from '~/utils/changeItemInNestedObject';
import { generateFirestoreId } from '~/utils/generateFirestoreId';
import getFullscreenModalProps from '~/utils/getFullscreenModalProps';
import AgeRestrictionModal from './add-item-modal/AgeRestrictionModal';
import ColorPicker from './add-item-modal/ColorPicker';
import ItemPrepTimeModal from './add-item-modal/ItemPrepTimeModal';
import NewFeatureTag from './add-item-modal/NewFeatureTag';
import NutritionalValuesModal, {
  NutritionalValues,
} from './add-item-modal/NutritionalValuesModal';
import { MuiTranslatableInputs } from './MuiTranslatableInputs';
// import SearchEngineModal from './add-item-modal/SearchEngineModal';
// import SocialMediaModal from './add-item-modal/SocialMediaModal';
import {
  Coordinates,
  ItemPrepValues,
  ItemType,
  MenuItem as MenuItemI,
  // SocialMediaValues,
} from './types';

interface AddItemModalProps {
  recordType: string;
  path: string;
  type?: string;
  position?: Coordinates;
  onClose: () => void;
  initialValues?: MenuItemI;
  displayGroupColor?: string;
}

const dietaryPreferences = [
  'dairy-free',
  'gluten-free',
  'halal',
  'kosher',
  'nut-free',
  'vegan',
  'vegetarian',
  'low-sugar',
  'low-carb',
  'low-sodium',
];

const allergens = [
  'celery',
  'crustaceans',
  'eggs',
  'fish',
  'gluten',
  'lupin',
  'milk',
  'molluscs',
  'mustard',
  'peanuts',
  'sesame',
  'soy',
  'sulphites',
  'tree-nuts',
];

const PROPERTIES_TO_SYNC = [
  'displayName',
  'group',
  'price',
  'measureUnit',
  'vat',
  'sku',
  'gtin',
  'ean',
  'images',
];

const defaultItemValues: ItemType = {
  color: menuColors[0],
  displayName: '',
  group: '',
  kitchenName: '',
  age: undefined,
  description: '',
  calories: undefined,
  dietaryPreferences: [],
  allergens: [],
  forceModify: false,
  forceQuantity: false,
  itemPrep: {
    itemPrepTime: 30,
  },
  measureUnit: undefined,
  images: [],
  sku: '',
  gtin: '',
  ean: '',
};

const isPriceValid = (value: any) => {
  return (
    (typeof value === 'string' && value.trim() !== '') ||
    (typeof value === 'number' && !isNaN(value))
  );
};

const isFormValid = (values: ItemType) => {
  return (
    !!values.displayName.trim() &&
    isPriceValid(values.price) &&
    !!values.measureUnit &&
    values.vat !== undefined &&
    !!values.group
  );
};

export default function AddItemModal({
  recordType = 'pos',
  type,
  path,
  position,
  onClose,
  initialValues,
  displayGroupColor,
}: AddItemModalProps) {
  const [locale] = useLocaleState();

  // Normalize initial values to match state structure
  const normalizedInitialValues = useMemo(() => {
    if (!initialValues) return null;
    return {
      ...initialValues,
      images: Array.isArray(initialValues.images) ? initialValues.images : [],
      sku: initialValues.sku || '',
      gtin: initialValues.gtin || '',
      ean: initialValues.ean || '',
      kitchenName: initialValues.kitchenName || '',
    };
  }, [initialValues]);

  const [state, setState] = useState<ItemType>(() => {
    const initialState = initialValues || defaultItemValues;
    return {
      ...initialState,
      images: Array.isArray(initialState.images) ? initialState.images : [],
      sku: initialState.sku || '',
      gtin: initialState.gtin || '',
      ean: initialState.ean || '',
      kitchenName: initialState.kitchenName || '',
    };
  });
  const [checkingValidation, setCheckingValidation] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isOpenAgeModal, setIsOpenAgeModal] = useState(false);
  const [isOpenNutritionalValuesModal, setIsOpenNutritionalValuesModal] =
    useState(false);
  const [isOpenPrepTimeModal, setIsOpenPrepTimeModal] = useState(false);
  // const [isSEOModalOpen, setIsSEOModalOpen] = useState(false);
  // const [isSocialMediaModalOpen, setIsSocialMediaModalOpen] = useState(false);
  const [isAddingExistingItem, setIsAddingExistingItem] = useState(false);
  const [shouldAddGroup, setShouldAddGroup] = useState(false);

  const { setValue, getValues } = useFormContext();
  const { t } = useTranslation();
  const { data: allGroups } = useGetListHospitalityCategoriesLive({
    filter: { _d: false },
  });
  const { data: allItems } = useGetListHospitalityItemsLive({
    filter: { _d: false },
  });

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      label: `${vat.value}%`,
    }));
  }, [vats]);

  const { data: measureUnits } = useGetListMeasureUnits();
  const measureUnitsOptions = useMemo(() => {
    if (!measureUnits) return [];
    return measureUnits.map((measureUnit: any) => ({
      id: measureUnit.id,
      label:
        measureUnit.name[locale] +
        `${measureUnit.symbol[locale] ? ` (${measureUnit.symbol[locale]})` : ''}`,
    }));
  }, [measureUnits, locale]);

  const [createGroup, { data: groupData }] = useCreate(
    RESOURCES.HOSPITALITY_CATEGORIES,
    { data: { name: state.group } },
    {}
  );

  const isInDisplayGroup = path.includes('items');
  const isEdit = !!initialValues;

  // Deep equality check function
  const deepEqual = (obj1: any, obj2: any): boolean => {
    if (obj1 === obj2) return true;

    if (obj1 == null || obj2 == null) return obj1 === obj2;

    if (typeof obj1 !== typeof obj2) return false;

    if (typeof obj1 !== 'object') return obj1 === obj2;

    if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

    if (Array.isArray(obj1)) {
      if (obj1.length !== obj2.length) return false;
      for (let i = 0; i < obj1.length; i++) {
        if (!deepEqual(obj1[i], obj2[i])) return false;
      }
      return true;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!deepEqual(obj1[key], obj2[key])) return false;
    }

    return true;
  };

  const updateValue = (field: string, value: unknown) => {
    setCheckingValidation(false);
    setState(prev => {
      const newState = { ...prev, ...{ [field]: value } };

      // Check for changes if in edit mode
      if (isEdit && normalizedInitialValues) {
        const hasChanged = !deepEqual(newState, normalizedInitialValues);
        setHasChanges(hasChanged);
      }

      return newState;
    });
  };

  useEffect(() => {
    if (groupData?.id) {
      createTile(groupData?.id as string);
    }
  }, [groupData]);

  // Initialize hasChanges to false when in edit mode
  useEffect(() => {
    if (isEdit && normalizedInitialValues) {
      const initialHasChanges = !deepEqual(state, normalizedInitialValues);
      setHasChanges(initialHasChanges);
    } else {
      setHasChanges(false);
    }
  }, [isEdit, normalizedInitialValues, state]);

  const handleBtnAction = () => {
    if (!isFormValid(state)) {
      setCheckingValidation(true);
      return;
    }

    if (shouldAddGroup && !isAddingExistingItem) {
      createGroup();
      return;
    }

    createTile();
  };

  const createTile = (newGroupId?: string) => {
    const newState = { ...state };
    if (!!newGroupId) {
      newState.group = newGroupId;
    }

    if (isInDisplayGroup) {
      delete newState.color;
    }

    if ((isAddingExistingItem || isEdit) && newState.id) {
      const { pages } = getValues();
      // We need to look for items with same id and update fields
      const updatedPages = changeItemInNestedObject(
        pages,
        newState.id,
        newState,
        PROPERTIES_TO_SYNC
      );

      if (updatedPages) {
        setValue('pages', updatedPages, {
          shouldDirty: true,
        });
      }
    }

    if (isEdit) {
      setValue(path, newState);
    } else {
      const record = getValues();
      const updatedData = getValueByPath(record, path);
      let tmp = [...updatedData];

      tmp.push({
        ...newState,
        type,
        ...(type === 'displayGroup' ? { items: [] } : {}),
        ...(isInDisplayGroup ? {} : { position }),
        // if we are adding an existing item, we don't need to generate an id
        // exisiting item id is already set in state
        ...(!isAddingExistingItem ? { id: generateFirestoreId() } : {}),
      });

      // if we are inside display group, we need to arrange items
      // first by type and then by display name
      if (isInDisplayGroup) {
        tmp.sort((a, b) => {
          const typeA = a.type ?? 'product';
          const typeB = b.type ?? 'product';

          if (typeA === 'displayGroup' && typeB !== 'displayGroup') return -1;
          if (typeA !== 'displayGroup' && typeB === 'displayGroup') return 1;

          return a.displayName.localeCompare(b.displayName);
        });

        // Remove position if we're inside display group (exista pozitie pe element
        // pentru ca o adaugam artificial ca sa stim unde le afisam in tabel)
        tmp = tmp.map(item => {
          const { position, ...rest } = item;
          return rest;
        });
      }

      setValue(path, tmp);
    }

    setState(defaultItemValues);
    onClose();
  };

  const saveAgeRestriction = (age: number) => {
    updateValue('age', age);
    setIsOpenAgeModal(false);
  };

  const saveNutritionalValues = (values: NutritionalValues) => {
    updateValue('nutritionalValues', values);
    setIsOpenNutritionalValuesModal(false);
  };

  const saveItemPrepTime = (values: ItemPrepValues) => {
    updateValue('itemPrep', values);
    setIsOpenPrepTimeModal(false);
  };

  const handleMultiSeletChange = (
    event: any,
    name: 'dietaryPreferences' | 'allergens'
  ) => {
    const {
      target: { value },
    } = event;
    updateValue(name, typeof value === 'string' ? value.split(',') : value);
  };

  // const handleSEOSave = (values: any) => {
  //   updateValue('seo', values);
  //   setIsSEOModalOpen(false);
  // };

  // const handleSocialMediaSave = (values: SocialMediaValues) => {
  //   updateValue('socialMedia', values);
  //   setIsSocialMediaModalOpen(false);
  // };

  return (
    <Dialog open onClose={onClose} {...getFullscreenModalProps()}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={onClose}
          title={isEdit ? t('menu.editItem') : t('menu.addItem')}
        >
          <Button
            variant="contained"
            onClick={handleBtnAction}
            disabled={isEdit && !hasChanges}
          >
            {isEdit ? t('shared.save') : t('shared.create')}
          </Button>
        </ModalHeader>
        <Grid
          p={3}
          pl={0}
          container
          spacing={3}
          sx={{ width: '100%', maxWidth: '800px', margin: 'auto' }}
        >
          <Grid item xs={12}>
            <Typography variant="h5">{t('menu.details')}</Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            {isEdit ? (
              <TextField
                label={t('shared.name')}
                value={state.displayName}
                onChange={e => updateValue('displayName', e.target.value)}
                error={checkingValidation && !state.displayName.trim()}
                required
                sx={{ mb: 3 }}
                fullWidth
              />
            ) : (
              <Autocomplete
                freeSolo
                options={allItems || []}
                getOptionLabel={option => {
                  if (typeof option === 'string') return option;
                  return option.name || '';
                }}
                filterOptions={(options, { inputValue }) => {
                  return options
                    .filter(option =>
                      option.name
                        .toLowerCase()
                        .includes(inputValue.toLowerCase())
                    )
                    .slice(0, 50);
                }}
                value={state.displayName}
                onChange={(_, newValue) => {
                  if (newValue === null && isAddingExistingItem) {
                    setIsAddingExistingItem(false);
                  }

                  if (typeof newValue === 'string') {
                    updateValue('displayName', newValue);
                    return;
                  }

                  if (newValue) {
                    let catalogPrice = null;
                    let catalogVat = null;
                    let sku = null;
                    let gtin = null;
                    let ean = null;
                    let images = null;
                    console.log(newValue);
                    if (
                      newValue.catalogSpecific &&
                      Object.keys(newValue.catalogSpecific).length > 0
                    ) {
                      const firstCatalogKey = Object.keys(
                        newValue.catalogSpecific
                      )[0];
                      catalogPrice =
                        newValue.catalogSpecific[firstCatalogKey]?.price;
                      catalogVat =
                        newValue.catalogSpecific[firstCatalogKey]?.vat;
                    }

                    setState(prev => {
                      const newState = {
                        ...prev,
                        displayName: newValue.name || '',
                        price:
                          catalogPrice ||
                          parseInt(newValue.price) ||
                          prev.price,
                        vat: catalogVat || newValue.vat,
                        measureUnit: newValue.measureUnit,
                        group: newValue.groupId,
                        sku: sku || newValue.sku,
                        gtin: gtin || newValue.gtin,
                        ean: ean || newValue.ean,
                        images: images || newValue.images,
                        id: newValue.id,
                      };

                      // Check for changes if in edit mode
                      if (isEdit && normalizedInitialValues) {
                        const hasChanged = !deepEqual(
                          newState,
                          normalizedInitialValues
                        );
                        setHasChanges(hasChanged);
                      }

                      return newState;
                    });
                    setIsAddingExistingItem(true);
                  }
                }}
                onInputChange={(_, newInputValue) => {
                  updateValue('displayName', newInputValue);
                }}
                renderOption={(props, option) => (
                  <li {...props} key={option.id}>
                    {option.name}
                  </li>
                )}
                renderInput={params => (
                  <TextField
                    {...params}
                    label={t('shared.name')}
                    error={checkingValidation && !state.displayName.trim()}
                    required
                    sx={{ mb: 3 }}
                  />
                )}
              />
            )}
            <Autocomplete
              freeSolo
              sx={{ mb: 3 }}
              options={allGroups || []}
              disabled={isAddingExistingItem}
              getOptionLabel={option => {
                if (typeof option === 'string') return option;
                return option.name || '';
              }}
              value={
                shouldAddGroup && !isAddingExistingItem
                  ? state.group
                  : allGroups?.find(group => group.id === state.group) || null
              }
              onChange={(_, newValue) => {
                if (typeof newValue === 'string') {
                  updateValue('group', newValue);
                  setShouldAddGroup(true);
                  return;
                }

                setShouldAddGroup(false);
                updateValue('group', newValue?.id || '');
              }}
              onInputChange={(_, newInputValue, reason) => {
                // only when user is typing
                if (reason === 'input' && !isAddingExistingItem) {
                  setShouldAddGroup(true);
                  updateValue('group', newInputValue);
                }
              }}
              renderOption={(props, option) => (
                <li {...props} key={option.id}>
                  {option.name}
                </li>
              )}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t('categoryLibrary.title')}
                  fullWidth
                  error={checkingValidation && !state.group}
                  required
                />
              )}
            />
            <TextField
              fullWidth
              type="number"
              value={state.price ? state.price / 10000 : ''}
              onChange={event => {
                const value = parseFloat(event.target.value);
                updateValue('price', Math.round(value * 10000));
              }}
              label={t('shared.price')}
              error={checkingValidation && !isPriceValid(state.price)}
              required
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment
                      position="end"
                      sx={{
                        opacity: 0,
                        pointerEvents: 'none',
                        [`[data-shrink=true] ~ .${inputBaseClasses.root} > &`]:
                          {
                            opacity: 1,
                          },
                      }}
                    >
                      Lei
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Grid>
          {recordType === 'pos' ? (
            <Grid item xs={12} sm={4}>
              <ColorPicker
                color={displayGroupColor || state.color}
                setColor={color => updateValue('color', color)}
                disabledColorPicker={!!displayGroupColor || isInDisplayGroup}
              />
            </Grid>
          ) : (
            <Grid item xs={12} sm={4}>
              <FilePreviewInline
                files={state.images || []}
                width={200}
                height={200}
                borderRadius={1.5}
                imageVariant="square_s"
                fileType="images"
                sx={{
                  objectFit: 'cover',
                  width: '100%', // Override to fill the container?
                }}
              />
            </Grid>
          )}

          <Grid item xs={12} sm={6}>
            <Autocomplete
              options={vatsChoices}
              getOptionLabel={option => option.label}
              value={vatsChoices.find(option => option.id == state.vat) || null}
              onChange={(_, newValue) => {
                updateValue('vat', newValue?.id || 0);
              }}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t('menu.tvaTaxes')}
                  fullWidth
                  error={checkingValidation && state.vat === undefined}
                  required
                />
              )}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Autocomplete
              options={measureUnitsOptions}
              getOptionLabel={option => option.label}
              value={
                measureUnitsOptions.find(
                  option => option.id === state.measureUnit
                ) || null
              }
              onChange={(_, newValue) => {
                updateValue('measureUnit', newValue?.id || '');
              }}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t('measure-units.title')}
                  fullWidth
                  error={checkingValidation && !state.measureUnit}
                  required
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <CustomDivider />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                paddingY: 2,
              }}
            >
              <Typography variant="h5">
                {t('itemLibrary.identifications')}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextField
                  value={state.sku || ''}
                  onChange={event => updateValue('sku', event.target.value)}
                  label="SKU"
                />
                <TextField
                  value={state.gtin || ''}
                  onChange={event => updateValue('gtin', event.target.value)}
                  label="GTIN"
                />
                <TextField
                  value={state.ean || ''}
                  onChange={event => updateValue('ean', event.target.value)}
                  label="EAN"
                />
              </Box>
            </Box>
            <CustomDivider />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5">{t('menu.media')}</Typography>
          </Grid>
          <Grid item xs={12}>
            <FileUploadComponent
              value={Array.isArray(state.images) ? state.images : []}
              onChange={(files: UploadedFile[]) => updateValue('images', files)}
              config={{
                fileType: 'images',
                multiple: false,
                maxFiles: 1,
                maxSize: 2 * 1024 * 1024, // 2MB - reasonable for menu items
                acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
                imageConfig: (() => {
                  const resolved = resolveImageEditorConfig(
                    createStandardImageConfig.squares()
                  );
                  return {
                    targetSizes: resolved.targetSizes,
                    autoGenerateThumbnail: true,
                    quality: 0.8,
                    editorConfig: resolved, // Pass the full resolved config as editorConfig
                  };
                })(),
                ui: {
                  disabled: false,
                  readOnly: false,
                },
              }}
            />
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5">{t('menu.ordering')}</Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: {
                  xs: 'flex-start',
                  md: 'center',
                },
                justifyContent: 'space-between',
                mt: 3,
              }}
            >
              <Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: { xs: 1, md: 4 },
                  }}
                >
                  <Typography fontWeight={500}>
                    {t('menu.forceModify')}
                  </Typography>
                  <NewFeatureTag />
                </Box>
                <Typography variant="body2" fontWeight={100} mt={0.5}>
                  {t('menu.forceModifyTooltip')}
                </Typography>
                <Typography variant="body2" fontWeight={100}>
                  {t('menu.forceModifyTooltip2')}
                </Typography>
              </Box>
              <MuiSwitchInput
                checked={state.forceModify}
                onChange={event =>
                  updateValue('forceModify', event.target.checked)
                }
                sx={{
                  marginRight: 0,
                }}
              />
            </Box>

            <Box
              sx={{
                display: 'flex',
                alignItems: {
                  xs: 'flex-start',
                  md: 'center',
                },
                justifyContent: 'space-between',
                mt: 3,
              }}
            >
              <Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: { xs: 1, md: 4 },
                  }}
                >
                  <Typography fontWeight={500}>
                    {t('menu.forceQuantity')}
                  </Typography>
                  <NewFeatureTag />
                </Box>
                <Typography variant="body2" fontWeight={100} mt={0.5}>
                  {t('menu.forceQuantityTooltip')}
                </Typography>
                <Typography variant="body2" fontWeight={100}>
                  {t('menu.forceQuantityTooltip2')}
                </Typography>
              </Box>
              <MuiSwitchInput
                checked={state.forceQuantity}
                onChange={event =>
                  updateValue('forceQuantity', event.target.checked)
                }
              />
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: {
                  xs: 'flex-start',
                  md: 'center',
                },
                justifyContent: 'space-between',
                mt: 3,
              }}
            >
              <Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: { xs: 1, md: 4 },
                  }}
                >
                  <Typography fontWeight={500}>
                    {t('menu.excludedFromDiscount')}
                  </Typography>
                  <NewFeatureTag />
                </Box>
                <Typography
                  variant="body2"
                  fontWeight={100}
                  mt={0.5}
                  maxWidth={600}
                >
                  {t('menu.excludedFromDiscountTooltip')}
                </Typography>
              </Box>
              <MuiSwitchInput
                checked={state.excludedFromDiscount}
                onChange={event =>
                  updateValue('excludedFromDiscount', event.target.checked)
                }
              />
            </Box>
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5">Restrictions</Typography>
          </Grid>
          <Grid item xs={12}>
            <Box
              onClick={() => setIsOpenAgeModal(true)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                border: 'solid 1px',
                borderColor: 'custom.gray400',
                borderRadius: 2,
                height: '56px',
                paddingX: 2,
                ':hover': {
                  cursor: 'pointer',
                  borderColor: 'custom.gray800',
                },
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                <Typography color="textSecondary" fontSize="14px">
                  {t('menu.ageRestriction')}
                </Typography>
                <Tooltip title={t('menu.ageRestrictionTooltip')}>
                  <IconButton>
                    <InfoOutlinedIcon color="disabled" />
                  </IconButton>
                </Tooltip>
              </Box>
              {state.age ? (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <Box>
                    <Typography
                      color="textSecondary"
                      fontSize="15px"
                      textAlign="left"
                      fontWeight={'bold'}
                    >
                      {state.age === 18
                        ? t('menu.ageRestriction18')
                        : `${t('menu.ageRestriction')} ${state.age}`}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography color="primary" fontSize="14px">
                  {t('shared.set')}
                </Typography>
              )}
              {state.age ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {/* <KeyboardArrowRightIcon color="disabled" />
                    {state.age} */}
                  <img src="/assets/icons/check.svg" alt="check-icon" />
                </Box>
              ) : (
                ''
              )}
            </Box>
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5">
              Name and Description Variations
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              value={state.kitchenName || ''}
              onChange={event => updateValue('kitchenName', event.target.value)}
              label={t('menu.kitchenName')}
              slotProps={{
                input: {
                  endAdornment: (
                    <Tooltip title={t('menu.kitchenNameTooltip')}>
                      <IconButton>
                        <InfoOutlinedIcon color="disabled" />
                      </IconButton>
                    </Tooltip>
                  ),
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <MuiTranslatableInputs
              state={state}
              updateValue={updateValue}
              locales={['ro', 'en']}
            />
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          {/* <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={state.description}
              onChange={event => updateValue('description', event.target.value)}
              label={t('measure-units.table-desc')}
            />
          </Grid> */}

          <Grid item xs={12}>
            <Typography variant="h5">
              {t('menu.nutritionalInformation')}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              type="number"
              value={state.calories || ''}
              onChange={event => updateValue('calories', event.target.value)}
              label={t('menu.calorieCount')}
              sx={{ mb: 3 }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment
                      position="end"
                      sx={{
                        opacity: 0,
                        pointerEvents: 'none',
                        [`[data-shrink=true] ~ .${inputBaseClasses.root} > &`]:
                          {
                            opacity: 1,
                          },
                      }}
                    >
                      {t('menu.calories')}
                    </InputAdornment>
                  ),
                },
              }}
            />

            <FormControl fullWidth>
              <InputLabel id="dietary-preferences-label">
                {t('menu.dietaryPreferences')}
              </InputLabel>
              <Select
                fullWidth
                labelId="dietary-preferences-label"
                id="dietary-preferences-checkbox"
                multiple
                value={state.dietaryPreferences ?? []}
                onChange={event =>
                  handleMultiSeletChange(event, 'dietaryPreferences')
                }
                input={<OutlinedInput label="Dietary preferences" />}
                renderValue={selected =>
                  selected
                    .map(el => t(`add-item.dietary-preferences.${el}.title`))
                    .join(', ')
                }
                sx={{ mb: 3 }}
              >
                {dietaryPreferences.map(key => (
                  <MenuItem key={key} value={key}>
                    <Checkbox
                      checked={state.dietaryPreferences?.includes(key)}
                    />
                    <ListItemText
                      primary={t(`add-item.dietary-preferences.${key}.title`)}
                      secondary={t(
                        `add-item.dietary-preferences.${key}.description`
                      )}
                    />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel id="allergens-label">
                {t('menu.allergens')}
              </InputLabel>
              <Select
                fullWidth
                labelId="allergens-label"
                id="allergens-checkbox"
                multiple
                value={state.allergens ?? []}
                onChange={event => handleMultiSeletChange(event, 'allergens')}
                input={<OutlinedInput label={t('menu.allergens')} />}
                renderValue={selected =>
                  selected
                    .map(el => t(`add-item.allergens.${el}.title`))
                    .join(', ')
                }
                sx={{ mb: 3 }}
              >
                {allergens.map(key => (
                  <MenuItem key={key} value={key}>
                    <Checkbox checked={state.allergens?.includes(key)} />
                    <ListItemText
                      primary={t(`add-item.allergens.${key}.title`)}
                      secondary={t(`add-item.allergens.${key}.description`)}
                    />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box
              onClick={() => setIsOpenNutritionalValuesModal(true)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                border: 'solid 1px',
                borderColor: 'custom.gray400',
                borderRadius: 2,
                height: '56px',
                paddingX: 2,
                ':hover': {
                  cursor: 'pointer',
                  borderColor: 'custom.gray800',
                },
              }}
            >
              <Typography color="textSecondary" fontSize="14px">
                {t('menu.nutritionalValues')}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          <Grid item xs={12}>
            <Tooltip title={t('menu.modifiersTooltipUnavailable')}>
              <Box sx={{ opacity: 0.5 }}>
                <Typography variant="h5">{t('menu.modifiers')}</Typography>
                <Typography variant="body1" mt={3}>
                  {t('menu.modifiersTooltip')}
                </Typography>
              </Box>
            </Tooltip>
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5">{t('menu.fulfillment')}</Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mt: 3,
              }}
            >
              <Box>
                <Typography fontWeight={500}>
                  {t('menu.itemPrepTime')}
                </Typography>
                <Typography variant="body2" fontWeight={100} mt={0.5}>
                  {state.itemPrep?.itemPrepTime} {t('shared.minutes')}
                </Typography>
              </Box>
              <Button
                variant="text"
                color="primary"
                onClick={() => setIsOpenPrepTimeModal(true)}
              >
                {t('shared.edit')}
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid>

          {/* <Grid item xs={12}>
            <Typography variant='h5'>Search</Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mt: 3,
              }}
            >
              <Box>
                <Typography fontWeight={300}>
                  SEO (Search Engine Optimization)
                </Typography>
                <Typography variant='body2' fontWeight={100} mt={0.5}>
                  Customize this item's title and description for Google and
                  other search engines.
                </Typography>
              </Box>
              <Button
                variant='text'
                color='primary'
                onClick={() => setIsSEOModalOpen(true)}
              >
                Edit
              </Button>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mt: 3,
              }}
            >
              <Box>
                <Typography fontWeight={300}>Social Media Preview</Typography>
                <Typography variant='body2' fontWeight={100} mt={0.5}>
                  Control how this item appears when shared on social platforms.
                </Typography>
              </Box>
              <Button
                variant='text'
                color='primary'
                onClick={() => setIsSocialMediaModalOpen(true)}
              >
                Edit
              </Button>
            </Box>
          </Grid> */}

          {/* <Grid item xs={12} my={3}>
            <CustomDivider />
          </Grid> */}
        </Grid>

        {isOpenAgeModal && (
          <AgeRestrictionModal
            age={state.age}
            onClose={e => {
              e?.stopPropagation();
              setIsOpenAgeModal(false);
            }}
            onSave={saveAgeRestriction}
          />
        )}

        {isOpenNutritionalValuesModal && (
          <NutritionalValuesModal
            initialValue={state.nutritionalValues}
            onClose={e => {
              e?.stopPropagation();
              setIsOpenNutritionalValuesModal(false);
            }}
            onSave={saveNutritionalValues}
          />
        )}

        {isOpenPrepTimeModal && (
          <ItemPrepTimeModal
            initialValue={state.itemPrep}
            onClose={() => {
              setIsOpenPrepTimeModal(false);
            }}
            onSave={saveItemPrepTime}
          />
        )}

        {/* {isSEOModalOpen && (
          <SearchEngineModal
            initialValue={state.seo}
            onClose={() => setIsSEOModalOpen(false)}
            onSave={handleSEOSave}
          />
        )}

        {isSocialMediaModalOpen && (
          <SocialMediaModal
            initialValue={state.socialMedia}
            onClose={() => setIsSocialMediaModalOpen(false)}
            onSave={handleSocialMediaSave}
          />
        )} */}
      </SimpleForm>
    </Dialog>
  );
}
