export type GetReportsDataFromStorage = (
  storageBucket: unknown,
  files: Array<string>
) => Promise<Array<unknown>>;

export type GetReportsDataFromRealtime = (
  database: unknown,
  path: string
) => Promise<Record<string, unknown>>;

type ReportFieldItems = {
  itemsQty?: number;
  itemsValue?: number;
};
type ReportFieldModifiers = {
  modifiersQty?: number;
  modifiersValue?: number;
};
type ReportFieldGiftCards = {
  giftCardsQty?: number;
  giftCardsValue?: number;
};
type ReportFieldExtraChargesQty = {
  extraChargesQty?: number;
};
type ReportFieldExtraChargesValue = {
  extraChargesValue?: number;
};
type ReportFieldExtraCharges = ReportFieldExtraChargesQty &
  ReportFieldExtraChargesValue;
type ReportFieldDiscountsValue = {
  discountsValue?: number;
};
type ReportFieldCouponsValue = {
  couponsValue?: number;
};
type ReportFieldPromotionsValue = {
  promotionsValue?: number;
};
type ReportFieldTips = {
  tipsCashValue?: number;
  tipsNonCashValue?: number;
};
type ReportFieldQuantity = {
  quantity: number;
};
type ReportFieldValue = {
  value: number;
};
type ReportFieldBills = {
  bills?: number;
};
type ReportFieldBillPercentage = {
  billPercentage?: number;
};
type ReportFieldDummy = Omit<{ dummy: string }, 'dummy'>;

type ReportSpecificFieldVat = {
  vat: number;
  'vat@number'?: string;
};
type ReportSpecificFieldGroupId = {
  groupId: string;
};
type ReportSpecificFieldPrepStation = {
  prepStation: string;
  prepStationId: string;
};
type ReportSpecificFieldId = {
  id: string;
};
type ReportSpecificFieldMeasureUnit = {
  measureUnit: string;
};
type ReportSpecificFieldVariant = {
  variant: string;
};
type ReportSpecificFieldPrice = {
  price: number;
  'price@number'?: string;
};
type ReportSpecificFieldDiscountName = {
  discountName: string;
};
type ReportSpecificFieldPromotionName = {
  promotionName: string;
};
type ReportSpecificFieldReason = {
  reason: string;
};
type ReportSpecificFieldType = {
  type: string;
};
type ReportSpecificFieldName = {
  name: string;
};
type ReportCalculatedFieldNetValue = {
  netValue: number;
};
type ReportCalculatedFieldTipsValue = {
  tipsValue: number;
};
type ReportCalculatedFieldTotalValue = {
  totalValue: number;
};
type ReportCalculatedFieldScoreValue = {
  scoreValue: number;
};
type ReportCalculatedFieldNormalizedBills = {
  normalizedBills: number;
};
type ReportCalculatedFieldNormalizedBillPercentage = {
  normalizedBillPercentage: number;
};
type ReportCalculatedFieldNormalizedQuantity = {
  normalizedQuantity: number;
};
type ReportCalculatedFieldNormalizedValue = {
  normalizedValue: number;
};
type ReportCalculatedFieldNormalizedNetValue = {
  normalizedNetValue: number;
};
type ReportCalculatedFieldReductionValue = {
  reductionValue: number;
};
type ReportCalculatedFieldSubTotalValue = {
  subTotalValue: number;
};

type ReportSalesSpecificFields = ReportFieldDummy;
type ReportSalesSummableFields = ReportFieldItems &
  ReportFieldModifiers &
  ReportFieldGiftCards &
  ReportFieldExtraCharges &
  ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldTips &
  ReportFieldBills & {
    payments?: {
      [paymentType: string]: number;
    };
    prepStations?: {
      [prepStationName: string]: {
        [prepStationId: string]: {
          itemsQty: number;
          value: number;
        };
      };
    };
    covers?: number;
    voidValue?: number;
    compValue?: number;
    compedBills?: number;
    pmsBills?: number;
  };
type ReportSalesCalculatedSummableFields = ReportFieldValue &
  ReportCalculatedFieldReductionValue &
  ReportCalculatedFieldNetValue &
  ReportCalculatedFieldTipsValue &
  ReportCalculatedFieldTotalValue & {
    'value@eval:$itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue'?: string;
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
    'netValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    'tipsValue@eval:$tipsCashValue+$tipsNonCashValue'?: string;
    'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+($tipsCashValue+$tipsNonCashValue)'?: string;
  };
type ReportSales = ReportSalesSpecificFields &
  ReportSalesSummableFields &
  ReportSalesCalculatedSummableFields;

type ReportTeamRevenueSpecificFields = ReportFieldDummy;
type ReportTeamRevenueSummableFields = {
  payments?: {
    [paymentType: string]: number;
  };
  comps?: {
    [compReason: string]: number;
  };
  netSalesValue?: number;
};
type ReportTeamRevenueCalculatedSummableFields = ReportFieldDummy;
type ReportTeamRevenue = ReportTeamRevenueSpecificFields &
  ReportTeamRevenueSummableFields &
  ReportTeamRevenueCalculatedSummableFields;

type ReportPaymentsSpecificFields = ReportSpecificFieldType & {
  field1: string;
  field2: string;
};
type ReportPaymentsSummableFields = ReportFieldQuantity & ReportFieldValue;
type ReportPaymentsCalculatedSummableFields = ReportFieldDummy;
type ReportPayments = ReportPaymentsSpecificFields &
  ReportPaymentsSummableFields &
  ReportPaymentsCalculatedSummableFields;

type ReportGroupsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldId;
type ReportGroupsSummableFields = ReportFieldItems &
  ReportFieldModifiers &
  ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue;
type ReportGroupsCalculatedSummableFields = ReportFieldValue &
  ReportCalculatedFieldReductionValue &
  ReportCalculatedFieldNetValue & {
    'value@eval:$itemsValue+$modifiersValue'?: string;
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
    'netValue@eval:($itemsValue+$modifiersValue)-($discountsValue+$couponsValue+$promotionsValue)'?: string;
  };
type ReportGroups = ReportGroupsSpecificFields &
  ReportGroupsSummableFields &
  ReportGroupsCalculatedSummableFields;

type ReportItemsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldGroupId &
  ReportSpecificFieldPrepStation &
  ReportSpecificFieldId &
  ReportSpecificFieldMeasureUnit &
  ReportSpecificFieldVariant &
  ReportSpecificFieldPrice &
  ReportSpecificFieldDiscountName &
  ReportSpecificFieldPromotionName;
type ReportItemsSummableFields = ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldQuantity &
  ReportFieldValue;
type ReportItemsCalculatedSummableFields = ReportCalculatedFieldReductionValue &
  ReportCalculatedFieldNetValue & {
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
  };
type ReportItems = ReportItemsSpecificFields &
  ReportItemsSummableFields &
  ReportItemsCalculatedSummableFields;

type ReportModifiersSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldGroupId &
  ReportSpecificFieldPrepStation &
  ReportSpecificFieldId &
  ReportSpecificFieldMeasureUnit &
  ReportSpecificFieldVariant &
  ReportSpecificFieldPrice &
  ReportSpecificFieldDiscountName &
  ReportSpecificFieldPromotionName;
type ReportModifiersSummableFields = ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldQuantity &
  ReportFieldValue;
type ReportModifiersCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    };
type ReportModifiers = ReportModifiersSpecificFields &
  ReportModifiersSummableFields &
  ReportModifiersCalculatedSummableFields;

type ReportGiftCardsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldType &
  ReportSpecificFieldPrice &
  ReportSpecificFieldDiscountName &
  ReportSpecificFieldPromotionName;
type ReportGiftCardsSummableFields = ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldQuantity &
  ReportFieldValue;
type ReportGiftCardsCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    };
type ReportGiftCards = ReportGiftCardsSpecificFields &
  ReportGiftCardsSummableFields &
  ReportGiftCardsCalculatedSummableFields;

type ReportExtraChargesSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldName &
  ReportSpecificFieldId &
  ReportSpecificFieldPrice &
  ReportSpecificFieldPromotionName;
type ReportExtraChargesSummableFields = ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldPromotionsValue;
type ReportExtraChargesCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$promotionsValue'?: string;
      'netValue@eval:$value-$promotionsValue'?: string;
    };
type ReportExtraCharges = ReportExtraChargesSpecificFields &
  ReportExtraChargesSummableFields &
  ReportExtraChargesCalculatedSummableFields;

type ReportDiscountsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldName;
type ReportDiscountsSummableFields = ReportFieldItems &
  ReportFieldModifiers &
  ReportFieldGiftCards;
type ReportDiscountsCalculatedSummableFields =
  ReportCalculatedFieldTotalValue & {
    'totalValue@eval:$itemsValue+$modifiersValue+$giftCardsValue'?: string;
  };
type ReportDiscounts = ReportDiscountsSpecificFields &
  ReportDiscountsSummableFields &
  ReportDiscountsCalculatedSummableFields;

type ReportCouponsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldName;
type ReportCouponsSummableFields = ReportFieldItems &
  ReportFieldModifiers &
  ReportFieldGiftCards;
type ReportCouponsCalculatedSummableFields = ReportCalculatedFieldTotalValue & {
  'totalValue@eval:$itemsValue+$modifiersValue+$giftCardsValue'?: string;
};
type ReportCoupons = ReportCouponsSpecificFields &
  ReportCouponsSummableFields &
  ReportCouponsCalculatedSummableFields;

type ReportPromotionsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldName;
type ReportPromotionsSummableFields = ReportFieldItems &
  ReportFieldModifiers &
  ReportFieldGiftCards &
  ReportFieldExtraCharges;
type ReportPromotionsCalculatedSummableFields =
  ReportCalculatedFieldTotalValue & {
    'totalValue@eval:$itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue'?: string;
  };
type ReportPromotions = ReportPromotionsSpecificFields &
  ReportPromotionsSummableFields &
  ReportPromotionsCalculatedSummableFields;

type ReportTipsSpecificFields = ReportSpecificFieldVat &
  ReportSpecificFieldPrice;
type ReportTipsSummableFields = ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldTips;
type ReportTipsCalculatedSummableFields = ReportFieldDummy;
type ReportTips = ReportTipsSpecificFields &
  ReportTipsSummableFields &
  ReportTipsCalculatedSummableFields;

type ReportCompedItemsSpecificFields = ReportSpecificFieldReason &
  ReportSpecificFieldVat &
  ReportSpecificFieldGroupId &
  ReportSpecificFieldPrepStation &
  ReportSpecificFieldId &
  ReportSpecificFieldMeasureUnit &
  ReportSpecificFieldVariant &
  ReportSpecificFieldPrice &
  ReportSpecificFieldDiscountName &
  ReportSpecificFieldPromotionName;
type ReportCompedItemsSummableFields = ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldQuantity &
  ReportFieldValue;
type ReportCompedItemsCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    };
type ReportCompedItems = ReportCompedItemsSpecificFields &
  ReportCompedItemsSummableFields &
  ReportCompedItemsCalculatedSummableFields;

type ReportCompedModifiersSpecificFields = ReportSpecificFieldReason &
  ReportSpecificFieldVat &
  ReportSpecificFieldGroupId &
  ReportSpecificFieldPrepStation &
  ReportSpecificFieldId &
  ReportSpecificFieldMeasureUnit &
  ReportSpecificFieldVariant &
  ReportSpecificFieldPrice &
  ReportSpecificFieldDiscountName &
  ReportSpecificFieldPromotionName;
type ReportCompedModifiersSummableFields = ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldQuantity &
  ReportFieldValue;
type ReportCompedModifiersCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    };
type ReportCompedModifiers = ReportCompedModifiersSpecificFields &
  ReportCompedModifiersSummableFields &
  ReportCompedModifiersCalculatedSummableFields;

type ReportCompedGiftCardsSpecificFields = ReportSpecificFieldReason &
  ReportSpecificFieldVat &
  ReportSpecificFieldType &
  ReportSpecificFieldPrice &
  ReportSpecificFieldDiscountName &
  ReportSpecificFieldPromotionName;
type ReportCompedGiftCardsSummableFields = ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue &
  ReportFieldQuantity &
  ReportFieldValue;
type ReportCompedGiftCardsCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    };
type ReportCompedGiftCards = ReportCompedGiftCardsSpecificFields &
  ReportCompedGiftCardsSummableFields &
  ReportCompedGiftCardsCalculatedSummableFields;

type ReportCompedExtraChargesSpecificFields = ReportSpecificFieldReason &
  ReportSpecificFieldVat &
  ReportSpecificFieldName &
  ReportSpecificFieldId &
  ReportSpecificFieldPrice &
  ReportSpecificFieldPromotionName;
type ReportCompedExtraChargesSummableFields = ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldPromotionsValue;
type ReportCompedExtraChargesCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue & {
      'reductionValue@eval:$promotionsValue'?: string;
      'netValue@eval:$value-$promotionsValue'?: string;
    };
type ReportCompedExtraCharges = ReportCompedExtraChargesSpecificFields &
  ReportCompedExtraChargesSummableFields &
  ReportCompedExtraChargesCalculatedSummableFields;

type ReportCompedTipsSpecificFields = ReportSpecificFieldReason &
  ReportSpecificFieldVat &
  ReportSpecificFieldPrice;
type ReportCompedTipsSummableFields = ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldTips;
type ReportCompedTipsCalculatedSummableFields = ReportFieldDummy;
type ReportCompedTips = ReportCompedTipsSpecificFields &
  ReportCompedTipsSummableFields &
  ReportCompedTipsCalculatedSummableFields;

type ReportVoidsSpecificFields = ReportSpecificFieldReason &
  ReportSpecificFieldType &
  ReportSpecificFieldVat &
  ReportSpecificFieldGroupId &
  ReportSpecificFieldPrepStation &
  ReportSpecificFieldId &
  ReportSpecificFieldMeasureUnit &
  ReportSpecificFieldVariant &
  ReportSpecificFieldPrice;
type ReportVoidsSummableFields = ReportFieldQuantity & ReportFieldValue;
type ReportVoidsCalculatedSummableFields = ReportFieldDummy;
type ReportVoids = ReportVoidsSpecificFields &
  ReportVoidsSummableFields &
  ReportVoidsCalculatedSummableFields;

type ReportVatSpecificFields = ReportSpecificFieldVat;
type ReportVatSummableFields = Omit<ReportFieldItems, 'itemsQty'> &
  Omit<ReportFieldModifiers, 'modifiersQty'> &
  Omit<ReportFieldGiftCards, 'giftCardsQty'> &
  Omit<ReportFieldExtraCharges, 'extraChargesQty'> &
  ReportFieldCouponsValue &
  ReportFieldDiscountsValue &
  ReportFieldPromotionsValue & {
    tipsValue?: number;
    baseValue?: number;
    finalValue: number;
    nettoValue: number;
    vatValue: number;
  };
type ReportVatCalculatedSummableFields = ReportFieldValue &
  ReportCalculatedFieldNetValue &
  ReportCalculatedFieldTotalValue & {
    'value@eval:$itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue'?: string;
    'netValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)'?: string;
    'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+$tipsValue'?: string;
  };
type ReportVat = ReportVatSpecificFields &
  ReportVatSummableFields &
  ReportVatCalculatedSummableFields;

type ReportTopGroupsSpecificFields = ReportSpecificFieldId;
type ReportTopGroupsSummableFields = ReportFieldBills &
  ReportFieldBillPercentage &
  ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue;
type ReportTopGroupsCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue &
    ReportCalculatedFieldNormalizedBills &
    ReportCalculatedFieldNormalizedBillPercentage &
    ReportCalculatedFieldNormalizedQuantity &
    ReportCalculatedFieldNormalizedValue &
    ReportCalculatedFieldNormalizedNetValue &
    ReportCalculatedFieldScoreValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
      'normalizedBills@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)'?: string;
      'normalizedBillPercentage@eval:(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)'?: string;
      'normalizedQuantity@eval:(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)'?: string;
      'normalizedValue@eval:(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)'?: string;
      'normalizedNetValue@eval:(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)'?: string;
      'scoreValue@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)*(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)*(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)*(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)*(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)': string; // ((($norm)+0.01)**0.25)
    };
type ReportTopGroups = ReportTopGroupsSpecificFields &
  ReportTopGroupsSummableFields &
  ReportTopGroupsCalculatedSummableFields;

type ReportTopItemsSpecificFields = ReportSpecificFieldId;
type ReportTopItemsSummableFields = ReportFieldBills &
  ReportFieldBillPercentage &
  ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue;
type ReportTopItemsCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue &
    ReportCalculatedFieldNormalizedBills &
    ReportCalculatedFieldNormalizedBillPercentage &
    ReportCalculatedFieldNormalizedQuantity &
    ReportCalculatedFieldNormalizedValue &
    ReportCalculatedFieldNormalizedNetValue &
    ReportCalculatedFieldScoreValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
      'normalizedBills@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)'?: string;
      'normalizedBillPercentage@eval:(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)'?: string;
      'normalizedQuantity@eval:(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)'?: string;
      'normalizedValue@eval:(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)'?: string;
      'normalizedNetValue@eval:(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)'?: string;
      'scoreValue@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)*(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)*(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)*(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)*(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)': string; // ((($norm)+0.01)**0.25)
    };
type ReportTopItems = ReportTopItemsSpecificFields &
  ReportTopItemsSummableFields &
  ReportTopItemsCalculatedSummableFields;

type ReportTopModifiersSpecificFields = ReportSpecificFieldId;
type ReportTopModifiersSummableFields = ReportFieldBills &
  ReportFieldBillPercentage &
  ReportFieldQuantity &
  ReportFieldValue &
  ReportFieldDiscountsValue &
  ReportFieldCouponsValue &
  ReportFieldPromotionsValue;
type ReportTopModifiersCalculatedSummableFields =
  ReportCalculatedFieldReductionValue &
    ReportCalculatedFieldNetValue &
    ReportCalculatedFieldNormalizedBills &
    ReportCalculatedFieldNormalizedBillPercentage &
    ReportCalculatedFieldNormalizedQuantity &
    ReportCalculatedFieldNormalizedValue &
    ReportCalculatedFieldNormalizedNetValue &
    ReportCalculatedFieldScoreValue & {
      'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue'?: string;
      'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)'?: string;
      'normalizedBills@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)'?: string;
      'normalizedBillPercentage@eval:(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)'?: string;
      'normalizedQuantity@eval:(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)'?: string;
      'normalizedValue@eval:(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)'?: string;
      'normalizedNetValue@eval:(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)'?: string;
      'scoreValue@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)*(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)*(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)*(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)*(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)': string; // ((($norm)+0.01)**0.25)
    };
type ReportTopModifiers = ReportTopModifiersSpecificFields &
  ReportTopModifiersSummableFields &
  ReportTopModifiersCalculatedSummableFields;

type ReportTransactionsSpecificFields = ReportSpecificFieldId;
type ReportTransactionsSummableFields = ReportFieldDummy;
export type ReportTransactionsNonSummableFieldItem = {
  name: string;
  value: number;
  modifiers?: {
    [modifierIndex: string]: {
      prefix?: string;
      name: string;
      value: number;
    };
  };
};
export type ReportTransactionsNonSummableFieldItems = {
  [itemIndex: string]: {
    type: string;
    quantity: number;
    color?: string;
    voidedAt?: number;
    voidedBy?: string;
    voidReason?: string;
    compReason?: string;
    discount?: number;
    discountName?: string;
    discountValue?: number;
    note?: string;
    selections?: {
      [selectionIndex: string]: ReportTransactionsNonSummableFieldItem;
    };
    moves?: string;
    source?: string;
  } & ReportTransactionsNonSummableFieldItem;
};
export type ReportTransactionsTransferDetails = {
  addedAt: number;
  addedBy: string;
  type: 'assigned' | 'moved';
  value: number;
  assignedToMember?: string;
  movedFromBillName?: string;
  movedItems?: {
    [itemIndex: string]: {
      name?: string;
      quantity: number;
      price: number;
    };
  };
  movedFromMember?: string;
  movedFromSection?: string;
};
export type ReportTransactionsNonSummableFields = {
  id: string;
  billName: string;
  closedAt: number;
  closedBy: string;
  closedFrom: string;
  closedWith: string;
  closedWithCompReason?: string;
  coupons?: {
    [couponIndex: string]: {
      name: string;
      type: string;
      value: number;
    };
  };
  couponsValue?: number;
  covers?: number;
  customerId?: string;
  customerName?: string;
  customerPhoneNumber?: string;
  dinningOption?: string;
  diningOption?: string;
  discountsValue?: number;
  extraCharges?: {
    [extraChargeIndex: string]: {
      name: string;
      quantity: number;
      value: number;
    };
  };
  extraChargesValue?: number;
  fiscalNumber?: number;
  giftCards?: {
    [giftCardIndex: string]: {
      name: string;
      type: string;
      value: number;
    };
  };
  giftCardsValue?: number;
  items?: ReportTransactionsNonSummableFieldItems;
  itemsValue?: number;
  modifiersValue?: number;
  note?: string;
  openedAt: number;
  openedBy: string;
  orderDiscount?: number;
  orderDiscountName?: string;
  orderDiscountValue?: number;
  orderNowDetails?: {
    itemsCount?: number;
    itemsValue?: number;
  };
  owner: string;
  payments?: {
    [paymentIndex: string]: {
      addedAt: number;
      addedBy: string;
      type: string;
      value: number;
      details?: string;
    };
  };
  paymentsValue?: number;
  printedBills?: {
    [billIndex: string]: {
      addedAt: number;
      addedBy: string;
      type: string;
      value: number;
      url?: string;
    };
  };
  promotionsValue?: number;
  section: string;
  source: string;
  tipsPercentage?: number;
  tipsValue?: number;
  transfers?: {
    [transferIndex: string]: ReportTransactionsTransferDetails;
  };
  vatNoOnReceipt?: string;
};
type ReportTransactionsCalculatedSummableFields =
  ReportCalculatedFieldSubTotalValue &
    ReportCalculatedFieldTotalValue & {
      'subTotalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue)-($discountsValue-$orderDiscountValue)'?: string;
      'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+$tipsValue'?: string;
    };
type ReportTransactions = ReportTransactionsSpecificFields &
  ReportTransactionsSummableFields &
  ReportTransactionsCalculatedSummableFields &
  ReportTransactionsNonSummableFields;

type ReportOrderNowOrdersSpecificFields = ReportSpecificFieldId;
type ReportOrderNowOrdersSummableFields = ReportFieldDummy;
export type ReportOrderNowOrdersNonSummableFieldItem = {
  catalog: string;
  id: string;
  measureUnit: string;
  name: string;
  price: number;
  vat: number;
  addedAt: number;
  note?: string;
  quantity: number;
  type: 'product' | 'productCombo' | 'service';
};
export type ReportOrderNowOrdersNonSummableFields = {
  id: string;
  bill: string;
  createdAt: number; // Timestamp in milliseconds when the order was created
  items: Record<string, ReportOrderNowOrdersNonSummableFieldItem>;
  owner: string;
  section: string;
  itemsCount: number;
  itemsValue?: number;
  modifiersValue?: number;
  giftCardsValue?: number;
  discountsValue?: number;
  orderDiscountValue?: number;
  extraChargesValue?: number;
  couponsValue?: number;
  promotionsValue?: number;
  tipsValue?: number;
};
type ReportOrderNowOrdersCalculatedSummableFields =
  ReportCalculatedFieldSubTotalValue &
    ReportCalculatedFieldTotalValue & {
      'subTotalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue)-($discountsValue-$orderDiscountValue)'?: string;
      'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+$tipsValue'?: string;
    };
type ReportOrderNowOrders = ReportOrderNowOrdersSpecificFields &
  ReportOrderNowOrdersSummableFields &
  ReportOrderNowOrdersCalculatedSummableFields &
  ReportOrderNowOrdersNonSummableFields;

export type ReportType = {
  sales: ReportSales;
  payments: ReportPayments;
  groups: ReportGroups;
  items: ReportItems;
  modifiers: ReportModifiers;
  giftCards: ReportGiftCards;
  extraCharges: ReportExtraCharges;
  discounts: ReportDiscounts;
  coupons: ReportCoupons;
  promotions: ReportPromotions;
  tips: ReportTips;
  compedItems: ReportCompedItems;
  compedModifiers: ReportCompedModifiers;
  compedGiftCards: ReportCompedGiftCards;
  compedExtraCharges: ReportCompedExtraCharges;
  compedTips: ReportCompedTips;
  voids: ReportVoids;
  vat: ReportVat;
  topGroups: ReportTopGroups;
  topItems: ReportTopItems;
  topModifiers: ReportTopModifiers;
  transactions: ReportTransactions;
  invoices: ReportTransactions;
  pmsItems: ReportItems;
  pmsModifiers: ReportModifiers;
  pmsGiftCards: ReportGiftCards;
  pmsExtraCharges: ReportExtraCharges;
  pmsTips: ReportTips;
  teamRevenue: ReportTeamRevenue;
  orderNowOrders: ReportOrderNowOrders;
};

export type ReportTypeSpecificFields = {
  sales: ReportSalesSpecificFields;
  payments: ReportPaymentsSpecificFields;
  groups: ReportGroupsSpecificFields;
  items: ReportItemsSpecificFields;
  modifiers: ReportModifiersSpecificFields;
  giftCards: ReportGiftCardsSpecificFields;
  extraCharges: ReportExtraChargesSpecificFields;
  discounts: ReportDiscountsSpecificFields;
  coupons: ReportCouponsSpecificFields;
  promotions: ReportPromotionsSpecificFields;
  tips: ReportTipsSpecificFields;
  compedItems: ReportCompedItemsSpecificFields;
  compedModifiers: ReportCompedModifiersSpecificFields;
  compedGiftCards: ReportCompedGiftCardsSpecificFields;
  compedExtraCharges: ReportCompedExtraChargesSpecificFields;
  compedTips: ReportCompedTipsSpecificFields;
  voids: ReportVoidsSpecificFields;
  vat: ReportVatSpecificFields;
  topGroups: ReportTopGroupsSpecificFields;
  topItems: ReportTopItemsSpecificFields;
  topModifiers: ReportTopModifiersSpecificFields;
  transactions: ReportTransactionsSpecificFields;
  invoices: ReportTransactionsSpecificFields;
  pmsItems: ReportItemsSpecificFields;
  pmsModifiers: ReportModifiersSpecificFields;
  pmsGiftCards: ReportGiftCardsSpecificFields;
  pmsExtraCharges: ReportExtraChargesSpecificFields;
  pmsTips: ReportTipsSpecificFields;
  teamRevenue: ReportTeamRevenueSpecificFields;
  orderNowOrders: ReportOrderNowOrdersSpecificFields;
};

export type ReportTypeSummableFields = {
  sales: ReportSalesSummableFields;
  payments: ReportPaymentsSummableFields;
  groups: ReportGroupsSummableFields;
  items: ReportItemsSummableFields;
  modifiers: ReportModifiersSummableFields;
  giftCards: ReportGiftCardsSummableFields;
  extraCharges: ReportExtraChargesSummableFields;
  discounts: ReportDiscountsSummableFields;
  coupons: ReportCouponsSummableFields;
  promotions: ReportPromotionsSummableFields;
  tips: ReportTipsSummableFields;
  compedItems: ReportCompedItemsSummableFields;
  compedModifiers: ReportCompedModifiersSummableFields;
  compedGiftCards: ReportCompedGiftCardsSummableFields;
  compedExtraCharges: ReportCompedExtraChargesSummableFields;
  compedTips: ReportCompedTipsSummableFields;
  voids: ReportVoidsSummableFields;
  vat: ReportVatSummableFields;
  topGroups: ReportTopGroupsSummableFields;
  topItems: ReportTopItemsSummableFields;
  topModifiers: ReportTopModifiersSummableFields;
  transactions: ReportTransactionsSummableFields;
  invoices: ReportTransactionsSummableFields;
  pmsItems: ReportItemsSummableFields;
  pmsModifiers: ReportModifiersSummableFields;
  pmsGiftCards: ReportGiftCardsSummableFields;
  pmsExtraCharges: ReportExtraChargesSummableFields;
  pmsTips: ReportTipsSummableFields;
  teamRevenue: ReportTeamRevenueSummableFields;
  orderNowOrders: ReportOrderNowOrdersSummableFields;
};

export type ReportTypeCalculatedSummableFields = {
  sales: ReportSalesCalculatedSummableFields;
  payments: ReportPaymentsCalculatedSummableFields;
  groups: ReportGroupsCalculatedSummableFields;
  items: ReportItemsCalculatedSummableFields;
  modifiers: ReportModifiersCalculatedSummableFields;
  giftCards: ReportGiftCardsCalculatedSummableFields;
  extraCharges: ReportExtraChargesCalculatedSummableFields;
  discounts: ReportDiscountsCalculatedSummableFields;
  coupons: ReportCouponsCalculatedSummableFields;
  promotions: ReportPromotionsCalculatedSummableFields;
  tips: ReportTipsCalculatedSummableFields;
  compedItems: ReportCompedItemsCalculatedSummableFields;
  compedModifiers: ReportCompedModifiersCalculatedSummableFields;
  compedGiftCards: ReportCompedGiftCardsCalculatedSummableFields;
  compedExtraCharges: ReportCompedExtraChargesCalculatedSummableFields;
  compedTips: ReportCompedTipsCalculatedSummableFields;
  voids: ReportVoidsCalculatedSummableFields;
  vat: ReportVatCalculatedSummableFields;
  topGroups: ReportTopGroupsCalculatedSummableFields;
  topItems: ReportTopItemsCalculatedSummableFields;
  topModifiers: ReportTopModifiersCalculatedSummableFields;
  transactions: ReportTransactionsCalculatedSummableFields;
  invoices: ReportTransactionsCalculatedSummableFields;
  pmsItems: ReportItemsCalculatedSummableFields;
  pmsModifiers: ReportModifiersCalculatedSummableFields;
  pmsGiftCards: ReportGiftCardsCalculatedSummableFields;
  pmsExtraCharges: ReportExtraChargesCalculatedSummableFields;
  pmsTips: ReportTipsCalculatedSummableFields;
  teamRevenue: ReportTeamRevenueCalculatedSummableFields;
  orderNowOrders: ReportOrderNowOrdersCalculatedSummableFields;
};

export type ReportTypeNonSummableFields = {
  sales: ReportFieldDummy;
  payments: ReportFieldDummy;
  groups: ReportFieldDummy;
  items: ReportFieldDummy;
  modifiers: ReportFieldDummy;
  giftCards: ReportFieldDummy;
  extraCharges: ReportFieldDummy;
  discounts: ReportFieldDummy;
  coupons: ReportFieldDummy;
  promotions: ReportFieldDummy;
  tips: ReportFieldDummy;
  compedItems: ReportFieldDummy;
  compedModifiers: ReportFieldDummy;
  compedGiftCards: ReportFieldDummy;
  compedExtraCharges: ReportFieldDummy;
  compedTips: ReportFieldDummy;
  voids: ReportFieldDummy;
  vat: ReportFieldDummy;
  topGroups: ReportFieldDummy;
  topItems: ReportFieldDummy;
  topModifiers: ReportFieldDummy;
  transactions: ReportTransactionsNonSummableFields;
  invoices: ReportTransactionsNonSummableFields;
  pmsItems: ReportFieldDummy;
  pmsModifiers: ReportFieldDummy;
  pmsGiftCards: ReportFieldDummy;
  pmsExtraCharges: ReportFieldDummy;
  pmsTips: ReportFieldDummy;
  teamRevenue: ReportFieldDummy;
  orderNowOrders: ReportOrderNowOrdersNonSummableFields;
};

export type ReportCommonFields<K extends keyof ReportType> = {
  reportType: K;
  date: string;
  dayOfWeek: number;
  'dayOfWeek@number'?: string;
  year: number;
  'year@number'?: string;
  quarter: number;
  'quarter@number'?: string;
  month: number;
  'month@number'?: string;
  week: number;
  'week@number'?: string;
  weekYear: number;
  'weekYear@number'?: string;
  day: number;
  'day@number'?: string;
  currency: string;
  hourOfDay: number;
  'hourOfDay@number'?: string;
  whatDay: string;
  member: string;
  memberId: string;
  section: string;
  serviceType: string;
  source: string;
};

export type Report<K extends keyof ReportType> = ReportCommonFields<K> & {
  report: Array<ReportType[K]>;
};

export type OmitKeysWithTypeTransform<T> = {
  [K in keyof T as K extends `${string}@${string}` ? never : K]: T[K];
};

export type ReportFilterOperator =
  | '=='
  | '!='
  | '>'
  | '<'
  | '>='
  | '<='
  | 'in'
  | 'notIn'
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith';

type ReportFilterCriterion = {
  operator: ReportFilterOperator;
  value: string | number | Array<string | number>;
};

export type ReportFilterCommonFieldsCriterion<K extends keyof ReportType> =
  ReportFilterCriterion & {
    field: keyof OmitKeysWithTypeTransform<ReportCommonFields<K>>;
  };

export type ReportFilterSpecificFieldsCriterion<K extends keyof ReportType> =
  ReportFilterCriterion & {
    field: keyof OmitKeysWithTypeTransform<ReportTypeSpecificFields[K]>;
  };

export type ReportDateInfo = {
  day: string;
  timestamp: string;
  week: string;
  weekYear: string;
  month: string;
  quarter: string;
  year: string;
};
