// Optimized QR Code configuration for physical materials
// These settings ensure maximum scanning reliability on paper, plastic, wood, metal
export const QR_CONFIG = {
    // High error correction (Level H = ~30% recovery) for damaged/worn codes
    errorCorrectionLevel: 'H' as const,

    // Generous margins (quiet zones) for better scanner detection
    // Especially important for engraved/embossed materials
    margin: 4, // 4 modules on each side (standard recommends 4+ for reliability)

    // High contrast colors for optimal scanning
    color: {
        dark: '#000000', // Pure black for maximum contrast
        light: '#FFFFFF', // Pure white background
    },

    // High resolution for printing (minimum 300 DPI equivalent)
    printWidth: 2000, // 2000px for crisp printing at various sizes
    displayWidth: 1000, // 1000px for screen display/clipboard

    // Canvas settings for high-quality output
    canvasSize: 2400, // Large canvas for 300+ DPI printing
    padding: 0.15, // 15% padding around QR code for mounting/borders
} as const;
