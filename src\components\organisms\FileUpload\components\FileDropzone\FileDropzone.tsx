import React, { useCallback, useRef, useState } from 'react';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import {
  formatFileSize,
  getFileExtensions,
} from '../../utils/fileUploadHelpers';

/**
 * File dropzone component for drag and drop functionality
 */
interface FileDropzoneProps {
  onFilesSelected: (files: File[]) => void;
  disabled?: boolean;
  multiple?: boolean;
  acceptedTypes?: string[];
  maxFiles?: number;
  maxSize?: number; // Maximum file size in bytes
  placeholder?: string;
  error?: boolean;
  uploading?: boolean;
  children?: React.ReactNode;
  variant?: 'default' | 'compact';
}

export const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFilesSelected,
  disabled = false,
  multiple = false,
  acceptedTypes = [],
  maxFiles,
  maxSize,
  placeholder,
  error = false,
  uploading = false,
  children,
  variant = 'default',
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [dragOver, setDragOver] = useState(false);
  const [inputKey, setInputKey] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate unique ID for input
  const uniqueId = useRef(
    `file-dropzone-${Math.random().toString(36).substring(2, 11)}`
  );

  // Handle drag events
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      if (!disabled && !uploading) {
        setDragOver(true);
      }
    },
    [disabled, uploading]
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragOver(false);

      if (disabled || uploading) return;

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        onFilesSelected(files);
      }
    },
    [disabled, uploading, onFilesSelected]
  );

  // Handle file input change
  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        onFilesSelected(Array.from(files));
      }
      // Reset input to allow selecting the same file again
      setInputKey(prev => prev + 1);
    },
    [onFilesSelected]
  );

  // Handle click to open file dialog
  const handleClick = useCallback(() => {
    if (!disabled && !uploading && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled, uploading]);

  // Get comprehensive description message
  const getDescriptionText = useCallback(() => {
    if (placeholder) return placeholder;

    if (uploading) return t('fileUpload.uploading', 'Uploading...');

    // Generate dynamic description
    const fileWord = multiple
      ? maxFiles && maxFiles > 1
        ? t('fileUpload.files', 'files')
        : t('fileUpload.files', 'files')
      : t('fileUpload.file', 'file');

    const extensions = getFileExtensions(acceptedTypes);
    const extensionsText =
      extensions.length > 0
        ? extensions.join(', ').toUpperCase()
        : acceptedTypes.length > 0
          ? acceptedTypes
              .map(type => type.split('/')[1]?.toUpperCase() || type)
              .join(', ')
          : '';

    const sizeText = maxSize
      ? ` ${t('fileUpload.maxSize', 'max size')} ${formatFileSize(maxSize)}`
      : '';
    const countText =
      maxFiles && multiple
        ? ` (${t('fileUpload.upTo', 'up to')} ${maxFiles})`
        : '';

    if (extensionsText && sizeText) {
      return t(
        'fileUpload.uploadWithExtensionsAndSize',
        'Upload {{fileWord}}{{countText}} with extensions: {{extensions}} and {{sizeText}}',
        { fileWord, countText, extensions: extensionsText, sizeText }
      );
    } else if (extensionsText) {
      return t(
        'fileUpload.uploadWithExtensions',
        'Upload {{fileWord}}{{countText}} with extensions: {{extensions}}',
        { fileWord, countText, extensions: extensionsText }
      );
    } else if (sizeText) {
      return t(
        'fileUpload.uploadWithSize',
        'Upload {{fileWord}}{{countText}} with {{sizeText}}',
        { fileWord, countText, sizeText }
      );
    }

    // Fallback to simple message
    return multiple
      ? t('fileUpload.dropMultiple', 'Drop files here or click to browse')
      : t('fileUpload.dropSingle', 'Drop a file here or click to browse');
  }, [placeholder, uploading, multiple, maxFiles, maxSize, acceptedTypes, t]);

  // Get placeholder text (simpler version for basic cases)
  const getPlaceholderText = useCallback(() => {
    if (placeholder) return placeholder;

    if (uploading) return t('fileUpload.uploading', 'Uploading...');

    if (multiple) {
      return maxFiles
        ? t(
            'fileUpload.dropMultipleWithLimit',
            `Drop files here or click to browse (max {{maxFiles}})`,
            { maxFiles }
          )
        : t('fileUpload.dropMultiple', 'Drop files here or click to browse');
    }

    return t('fileUpload.dropSingle', 'Drop a file here or click to browse');
  }, [placeholder, uploading, multiple, maxFiles, t]);

  // Convert MIME types to file extensions for consistent cross-browser behavior
  const getAcceptAttribute = useCallback(() => {
    if (acceptedTypes.length === 0) return undefined;

    // Use the shared function to get extensions for consistency
    const extensions = getFileExtensions(acceptedTypes);

    // If we have extensions, use them; otherwise fall back to MIME types
    const result =
      extensions.length > 0 ? extensions.join(',') : acceptedTypes.join(',');
    // console.log(
    //   '🔍 [FileDropzone] Accept attribute:',
    //   result,
    //   'from types:',
    //   acceptedTypes
    // );
    return result;
  }, [acceptedTypes]);

  // Get accepted types text
  const getAcceptedTypesText = useCallback(() => {
    if (acceptedTypes.length === 0) return '';

    const types = acceptedTypes.map(type => {
      if (type.startsWith('image/')) return 'Images';
      if (type.startsWith('video/')) return 'Videos';
      if (type.includes('pdf')) return 'PDF';
      if (type.includes('word')) return 'Word';
      if (type.includes('excel') || type.includes('spreadsheet'))
        return 'Excel';
      return type.split('/')[1]?.toUpperCase() || type;
    });

    const uniqueTypes = [...new Set(types)];
    return uniqueTypes.join(', ');
  }, [acceptedTypes]);

  if (variant === 'compact') {
    return (
      <Box sx={{ display: 'inline-flex', alignItems: 'center' }}>
        <input
          key={inputKey}
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={getAcceptAttribute()}
          onChange={handleFileInputChange}
          disabled={disabled || uploading}
          style={{ display: 'none' }}
          id={uniqueId.current}
        />
        <label
          htmlFor={uniqueId.current}
          style={{
            cursor: disabled || uploading ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {children}
        </label>
      </Box>
    );
  }

  return (
    <Paper
      variant="outlined"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
      sx={{
        p: 3,
        textAlign: 'center',
        cursor: disabled || uploading ? 'not-allowed' : 'pointer',
        border: error
          ? `2px dashed ${theme.palette.error.main}`
          : dragOver
            ? `2px dashed ${theme.palette.primary.main}`
            : `2px dashed ${theme.palette.divider}`,
        backgroundColor: error
          ? theme.palette.error.light
          : dragOver
            ? theme.palette.primary.light
            : 'transparent',
        transition: 'border-color 0.2s, background-color 0.2s',
        opacity: disabled ? 0.6 : 1,
        '&:hover':
          disabled || uploading
            ? {}
            : {
                borderColor: error
                  ? theme.palette.error.main
                  : theme.palette.primary.main,
                backgroundColor: error
                  ? theme.palette.error.light
                  : theme.palette.primary.light,
              },
      }}
    >
      <input
        key={inputKey}
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={getAcceptAttribute()}
        onChange={handleFileInputChange}
        disabled={disabled || uploading}
        style={{ display: 'none' }}
        id={uniqueId.current}
      />

      <CloudUploadIcon
        sx={{
          fontSize: 48,
          color: error
            ? theme.palette.error.main
            : dragOver
              ? theme.palette.primary.main
              : theme.palette.text.secondary,
          mb: 2,
        }}
      />

      <Typography
        variant="body1"
        sx={{
          mb: 1,
          color: error ? theme.palette.error.main : theme.palette.text.primary,
        }}
      >
        {getDescriptionText()}
      </Typography>

      {children}
    </Paper>
  );
};
