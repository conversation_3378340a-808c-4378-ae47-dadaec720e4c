import { Box } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { SaveButton, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import {
  ConflictAwareInput,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import { RESOURCES } from '~/providers/resources';
import { CustomInput } from '../../components/atoms/inputs/CustomInput/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';

export default function TipsEdit(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const { t } = useTranslation();

  const handleClose = () => {
    redirect('list', RESOURCES.TIPS);
  };

  return (
    <EditDialog
      fullWidth={true}
      maxWidth={'sm'}
      mutationMode="pessimistic"
      mutationOptions={{ meta: { sellPointId: sellPointId } }}
      queryOptions={{ meta: { sellPointId: sellPointId } }}
    >
      <ConflictAwareSimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        defaultValues={{ _isJustAValue: true }}
        translationNamespace="shared"
        queryMeta={{ sellPointId: sellPointId }}
      >
        <ModalHeader
          handleClose={handleClose}
          title={t('tipsPage.editTipPercentage')}
          titleSize={17}
        >
          <SaveButton type="button" icon={<></>} label={t('shared.save')} />
        </ModalHeader>

        {/* Body */}
        <Box p={2} width="100%">
          <ConflictAwareInput>
            <CustomInput
              source="value"
              type="number"
              label={t('tipsPage.percentage')}
              locale="ro-RO"
              format={v => v / 100}
              parse={v => Math.floor(v * 100)}
              options={{
                style: 'percent',
                maximumFractionDigits: 2,
              }}
              slotProps={{
                input: {
                  endAdornment: '%',
                },
              }}
              roundedCorners="both"
            />
          </ConflictAwareInput>
        </Box>
      </ConflictAwareSimpleForm>
    </EditDialog>
  );
}
