import { Validator } from 'react-admin';

// Import ImageVariant type
export type ImageVariant =
    | 'thumbnail'
    | 'square_xs'
    | 'square_s'
    | 'square_m'
    | 'square_l'
    | 'logo_receipt'
    | 'mobile_intro_standard'
    | 'mobile_intro_wide'
    | 'original';

export interface UploadedFile {
    rn?: string; // realName (without extension) - only for temporary files
    f: string; // fileName (unique name without extension)
    e: string; // extension without dot (e.g., 'jpg', 'png')
    // s: removed - size now calculated at runtime to save Firestore space
    t: 'i' | 'v' | 's' | 'p'; // i=images, v=videos, s=public, p=private
    x?: boolean; // isTemporary (now means in-memory instead of temp bucket)
    url?: string; // cached download URL (optional for performance)
    inMemoryData?: InMemoryFileData; // NEW: in-memory file storage

    // File size metadata - for permanent files
    sizes?: {
        original: number; // Original file size in bytes
        variants?: number; // Total size of generated variants in bytes
    };
}

/**
 * In-memory file data for temporary files
 * Replaces temp bucket storage with client-side blob storage
 */
export interface InMemoryFileData {
    original: Blob; // Original file as blob
    variants?: Map<string, Blob>; // Variant name -> blob (e.g., "thumbnail", "card", "detail")
    metadata: InMemoryFileMetadata; // Metadata that would normally be stored in Firebase
}

/**
 * Metadata for in-memory files
 * Preserves all metadata that was previously stored in Firebase Storage
 */
export interface InMemoryFileMetadata {
    originalFileName: string; // Original file name with extension
    uploadedBy: string; // User ID who uploaded the file
    accountId: string; // Account ID for the upload
    variantCount?: number; // Number of variants (including original)
    uploadTimestamp: number; // When the file was uploaded to memory
    fileSize: number; // Size of original file in bytes
    mimeType: string; // MIME type of original file
    // Additional metadata for images with variants
    imageMetadata?: {
        originalWidth?: number;
        originalHeight?: number;
        variants?: Array<{
            key: string;
            width: number;
            height: number;
            size: number; // Size in bytes
        }>;
    };
} // Context for private file organization
export interface PrivateFileContext {
    accountId: string;
    sellpointId?: string;
    customPath?: string;
}

// Enhanced URL generation options
export interface UrlGenerationOptions {
    context?: PrivateFileContext;
    forceRefresh?: boolean;
    // Option for specific image variant within a folder
    imageVariant?: ImageVariant; // Image variant types
}

// Target size configuration for multi-size generation
export interface TargetSize {
    width: number;
    height: number;
    name?: string; // Optional identifier like 'thumbnail', 'medium', 'large'
}

// Aspect ratio group for multi-ratio cropping
export interface AspectRatioGroup {
    aspectRatio: number; // width/height ratio (e.g., 1 for square, 3 for 3:1 banner)
    targetSizes: TargetSize[];
    editingSize: { width: number; height: number }; // Largest size for optimal editing quality
    name?: string; // Optional group name like 'square', 'banner', 'portrait'
}

// Image editor configuration for pre-upload editing
export interface ImageEditorConfig {
    // Multi-aspect-ratio support (takes highest precedence)
    aspectRatioGroups?: AspectRatioGroup[];

    // Multiple target sizes (takes precedence if provided, will be auto-grouped by aspect ratio)
    targetSizes?: TargetSize[];

    // Dimension constraints (backward compatibility - used if targetSizes not provided)
    targetWidth?: number; // Exact target width
    targetHeight?: number; // Exact target height
    aspectRatio?: number; // width/height ratio (e.g., 1 for square)
    minWidth?: number;
    minHeight?: number;
    maxWidth?: number;
    maxHeight?: number;

    // Editor features
    allowZoom?: boolean; // Default: true
    allowRotate?: boolean; // Default: false
    allowFlip?: boolean; // Default: false

    // Output settings
    outputFormat?: 'jpeg' | 'png' | 'webp'; // Default: preserve original
    outputQuality?: number; // 0-1 for JPEG quality, default: 0.9

    // UI customization
    cropShape?: 'rect' | 'circle'; // Default: 'rect'
    gridLines?: boolean; // Default: true
    zoomStep?: number; // Default: 0.1

    // Behavior settings
    enforceAspectRatio?: boolean; // Default: true when aspectRatio is set
    allowFreeformCrop?: boolean; // Default: false when target dimensions are set
}

export interface FileUploadInputProps {
    source: string;
    multiple?: boolean;
    maxFiles?: number;
    maxSize?: number; // in bytes (will be converted to KB internally)
    acceptedTypes?: string[]; // MIME types
    helperText?: string;
    validate?: Validator | Validator[];
    label?: string;
    disabled?: boolean;
    readOnly?: boolean;
    fileType?: 'images' | 'videos' | 'public' | 'private'; // defaults to 'images'

    // Enhanced validation props (optional)
    showValidationErrors?: boolean;
    validationDisplay?: 'inline' | 'toast' | 'both';
    allowInvalidFiles?: boolean;
    minFileCount?: number;
    minFileSize?: number; // in bytes
    requiredFileTypes?: string[]; // MIME types that are required (stricter than acceptedTypes)
    customValidation?: (file: File) => string | null;

    // Private file context (required for private files)
    privateFileContext?: PrivateFileContext;

    // Image editor props (optional)
    enableImageEditor?: boolean;
    imageEditorConfig?: ImageEditorConfig;
    onImageEdit?: (originalFile: File, editedFiles: File[]) => void; // Updated to handle multiple files
    onImageEditCancel?: (file: File) => void;
    onImageEditStart?: (file: File) => void;
}

// Type-specific file configurations
export const FILE_TYPE_CONFIGS = {
    images: {
        acceptedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
        maxSize: 10 * 1024 * 1024, // 10MB for images
        maxFiles: 10,
    },
    videos: {
        acceptedTypes: ['video/mp4', 'video/webm', 'video/mov', 'video/avi'],
        maxSize: 100 * 1024 * 1024, // 100MB for videos
        maxFiles: 5,
    },
    public: {
        acceptedTypes: [
            'application/pdf',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        maxSize: 50 * 1024 * 1024, // 50MB for public files
        maxFiles: 20,
    },
    private: {
        acceptedTypes: [
            'application/pdf',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
        ],
        maxSize: 50 * 1024 * 1024, // 50MB for private files
        maxFiles: 50,
    },
} as const;

export const DEFAULT_FILE_UPLOAD_CONFIG = {
    maxFiles: 5,
    maxSize: 5 * 1024 * 1024, // 5MB
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    multiple: false,
    fileType: 'images' as const,
};

/**
 * Configuration for in-memory file management
 */
export const IN_MEMORY_FILE_CONFIG = {
    // Memory limits
    maxMemoryUsage: 500 * 1024 * 1024, // 500MB total memory limit
    maxFileSize: 100 * 1024 * 1024, // 100MB per file limit
    maxConcurrentFiles: 50, // Maximum number of files in memory

    // Cleanup settings
    maxBlobUrls: 200, // Maximum number of blob URLs to track
    cleanupInterval: 5 * 60 * 1000, // 5 minutes cleanup interval
} as const;

/**
 * Utility type for in-memory file operations
 */
export interface InMemoryFileOperations {
    createInMemoryFile: (
        file: File,
        fileType: 'i' | 'v' | 's' | 'p'
    ) => Promise<UploadedFile>;
    createInMemoryImageWithVariants: (
        file: File,
        targetSizes: any[],
        quality: number
    ) => Promise<UploadedFile>;
    createInMemoryImageWithCroppedVariants: (
        originalFile: File,
        croppedFiles: File[]
    ) => Promise<UploadedFile>;
    getInMemoryFileUrl: (file: UploadedFile, variant?: string) => Promise<string>;
    cleanupInMemoryFile: (file: UploadedFile) => void;
    getMemoryUsage: () => {
        totalSize: number;
        fileCount: number;
        blobUrlCount: number;
    };
}

export const getRecommendedConfigForFileType = (
    fileType: 'images' | 'videos' | 'public' | 'private'
) => {
    return FILE_TYPE_CONFIGS[fileType];
};

// Helper to determine if we should check for variants in folder structure
export const shouldCheckForVariants = (file: UploadedFile): boolean => {
    return file.t === 'i'; // All images can have variants (both temporary and permanent)
};
