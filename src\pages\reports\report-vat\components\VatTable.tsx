import { useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { groupReport } from '~/fake-provider/reports/groupReport';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';

type TableRow = {
  vat: number | string | undefined;
  extraChargesValue: number;
  discountsValue: number;
  couponsValue: number;
  tipsValue: number;
  itemsValue: number;
  value: number;
  giftCardsValue: number;
  modifiersValue: number;
  totalValue: number;
  netValue: number;
  vatValue: number;
  promotionsValue: number;
};

const fieldsConstant = [
  { isChecked: true, value: 'vat' },
  { isChecked: false, value: 'itemsValue' },
  { isChecked: false, value: 'modifiersValue' },
  { isChecked: false, value: 'giftCardsValue' },
  { isChecked: false, value: 'value' },
  { isChecked: false, value: 'extraChargesValue' },
  { isChecked: false, value: 'discountsValue' },
  { isChecked: false, value: 'couponsValue' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'tipsValue' },
  { isChecked: false, value: 'totalValue' },
  { isChecked: false, value: 'netValue' },
  { isChecked: true, value: 'vatValue' },
];

export default function VatTable({
  tableData,
}: {
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
}) {
  const [fields, setFields] = useState<FieldOption[]>(fieldsConstant);
  const { t } = useTranslation();

  const vatData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData.map((data: any) => {
        return {
          vat: data.vat,
          extraChargesValue: data.extraChargesValue,
          itemsValue: data.itemsValue,
          modifiersValue: data.modifiersValue,
          giftCardsValue: data.giftCardsValue,
          discountsValue: data.discountsValue,
          couponsValue: data.couponsValue,
          tipsValue: data.tipsValue,
          value: data.value,
          totalValue: data.totalValue,
          netValue: data.netValue,
          vatValue: data.vatValue,
          promotionsValue: data.promotionsValue,
        };
      });

      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;

      totalItemsData.vat = '';

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }
    return [];
  }, [tableData]);

  const vatConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'vat',
      label: t('shared.tva'),
      textAlign: 'start',
      render: (row: TableRow, rowIndex: number) => (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {rowIndex === vatData.length - 1
            ? 'Total'
            : row.vat !== undefined && row.vat + '%'}
        </div>
      ),
    },
    {
      id: 'itemsValue',
      label: t('reportsPage.itemsSales'),
      textAlign: 'end',
      render: (row: TableRow) => <>{formatAndDivideNumber(row.itemsValue)}</>,
    },
    {
      id: 'modifiersValue',
      label: t('reportsPage.modifiersSales'),
      textAlign: 'end',
      render: (row: TableRow) => (
        <>{formatAndDivideNumber(row.modifiersValue)}</>
      ),
    },
    {
      id: 'giftCardsValue',
      label: t('reportsPage.giftCardsSales'),
      textAlign: 'end',
      render: (row: TableRow) => (
        <>{formatAndDivideNumber(row.giftCardsValue)}</>
      ),
    },
    {
      id: 'extraChargesValue',
      label: t('reportsPage.extraChargesSales'),
      textAlign: 'end',
      render: (row: TableRow) => (
        <>{formatAndDivideNumber(row.extraChargesValue)}</>
      ),
    },
    {
      id: 'value',
      label: t('reportsPage.grossSales'),
      textAlign: 'end',
      render: (row: TableRow) => <>{formatAndDivideNumber(row?.value)}</>,
    },
    {
      id: 'discountsValue',
      label: t('reportsPage.discounts'),
      textAlign: 'end',
      render: (row: TableRow) => (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {row?.discountsValue
            ? '- ' + formatAndDivideNumber(row?.discountsValue)
            : ''}
        </div>
      ),
    },
    {
      id: 'couponsValue',
      label: t('reportsPage.coupons'),
      textAlign: 'end',
      render: (row: TableRow) => (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {row?.couponsValue
            ? '- ' + formatAndDivideNumber(row?.couponsValue)
            : ''}
        </div>
      ),
    },
    {
      id: 'promotionsValue',
      label: t('reportsPage.promotions'),
      textAlign: 'end',
      render: (row: TableRow) => (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {row?.promotionsValue
            ? '- ' + formatAndDivideNumber(row?.promotionsValue)
            : ''}
        </div>
      ),
    },
    {
      id: 'netValue',
      label: t('shared.netSales'),
      textAlign: 'end',
      render: (row: TableRow) => <>{formatAndDivideNumber(row?.netValue)}</>,
    },
    {
      id: 'tipsValue',
      label: t('reportsPage.tipsAmount'),
      textAlign: 'end',
      render: (row: TableRow) => <>{formatAndDivideNumber(row.tipsValue)}</>,
    },
    {
      id: 'totalValue',
      label: t('reportsPage.totalCollected2'),
      textAlign: 'end',
      render: (row: TableRow) => <>{formatAndDivideNumber(row.totalValue)}</>,
    },
    {
      id: 'vatValue',
      label: t('vat.vatAmount'),
      textAlign: 'end',
      render: (row: TableRow) => <>{formatAndDivideNumber(row.vatValue)}</>,
    },
  ];

  const columnsToFilter = [
    'extraChargesValue',
    'itemsValue',
    'modifiersValue',
    'giftCardsValue',
    'discountsValue',
    'couponsValue',
    'tipsValue',
    'value',
    'totalValue',
    'netValue',
    'promotionsValue',
  ];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <CustomTable
          greyLastRow={true}
          fields={fields}
          setFields={setFields}
          filter={true}
          fixedFirstColumn={true}
          maxWidthFirstColumn="150px"
          columnsToFilter={columnsToFilter}
          config={vatConfig}
          data={vatData}
          fixLastRow={true}
          alignLastColumnRight={false}
        />
      </Box>
    </>
  );
}
