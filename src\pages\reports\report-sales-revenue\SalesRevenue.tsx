import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, useMediaQuery } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import jsonexport from 'jsonexport/dist';
import { Button, downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupReport } from '~/fake-provider/reports/groupReport';
import {
  OmitKeysWithTypeTransform,
  Report,
} from '~/fake-provider/reports/types';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import { useGetListLocationsLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import { CurrencyType } from '~/utils/formatNumber';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  SourceOption,
} from '../components/ReportFilters';
import {
  generatePaddedDaysArray,
  generatePaddedHoursArray,
} from '../utils/generatePaddedReportData';
import { getStartEndHours } from '../utils/getStartEndHours';
import SalesRevenueGraphs, {
  DatasetsProp,
} from './components/SalesRevenueGraphs';
import SalesRevenueTables from './components/SalesRevenueTables';

import type { ReportFiltersState } from '../components/ReportFilters';

export enum SourceTypes {
  'all' = 'All Sources',
  '@pos' = 'Direct Selio Pos',
  'ariva' = 'Ariva Food',
  'glovo' = 'Glovo',
  'bolt' = 'Bolt Food',
}

const REPORT_TYPE = 'sales';

export default function SalesRevenue() {
  const { details: fbDetails } = useFirebase();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [currency, setCurrency] = useState<CurrencyType>();
  const [rawData, setRawData] =
    useState<OmitKeysWithTypeTransform<Report<'sales'>>[]>();
  const [lastWeekRawData, setLastWeekRawData] =
    useState<OmitKeysWithTypeTransform<Report<'sales'>>[]>();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});
  const { t } = useTranslation();
  const contentRef = useRef<HTMLDivElement>(null);
  const { data: sellPoints } = useGetListLocationsLive();
  const { data: members } = useGetListLive('members');
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  const updateCommonField = (key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  const defaultValues: ReportFiltersState = useMemo(
    () => ({
      dateRange,
      sellpointId: sellPointId,
      timeRange: {
        allDay: !timeRange,
        start: timeRange?.[0],
        end: timeRange?.[1],
      },
      diningOption: DiningOption.ALL,
      source: SourceOption.ALL,
    }),
    [dateRange, sellPointId, timeRange]
  );

  const { tableData, graphData } = useMemo(() => {
    if (!rawData || !filters)
      return { tableData: undefined, graphData: undefined };

    if (rawData.length) {
      setCurrency(rawData[0].currency as CurrencyType);
    }

    const isOneDay = dateRange[0]?.isSame(dateRange[1], 'day');
    const sellpoint = sellPoints?.find(el => el.id === sellPointId);

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const newFilteredData = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const groupedByHour = groupReport(
      REPORT_TYPE,
      newFilteredData,
      ['hourOfDay', 'whatDay'],
      []
    );

    const groupedByDay = groupReport(
      REPORT_TYPE,
      newFilteredData,
      ['date'],
      []
    );

    const groupedByDayOfWeek = groupReport(
      REPORT_TYPE,
      newFilteredData,
      ['dayOfWeek'],
      []
    );

    const { startHour, endHour } = getStartEndHours(filters, sellpoint);

    const paddedHourData = generatePaddedHoursArray(
      startHour,
      endHour,
      groupedByHour,
      'totalValue'
    );

    const startDay = dateRange[0]!.format('YYYY-MM-DD');
    const endDay = dateRange[1]!.format('YYYY-MM-DD');

    const paddedDaysData = generatePaddedDaysArray(
      startDay,
      endDay,
      groupedByDay,
      'totalValue'
    );

    const weekLabels = [
      t('sellpoints.mondayShort'),
      t('sellpoints.tuesdayShort'),
      t('sellpoints.wednesdayShort'),
      t('sellpoints.thursdayShort'),
      t('sellpoints.fridayShort'),
      t('sellpoints.saturdayShort'),
      t('sellpoints.sundayShort'),
    ];

    const graphData: DatasetsProp = {
      groupedByHour: {
        label: '',
        data: paddedHourData.values
          ? paddedHourData.values.map(el => {
              return el / 10000;
            })
          : [],
        tableLabels: paddedHourData.labels,
      },
      groupedByDay: {
        label: '',
        data: paddedDaysData.values
          ? paddedDaysData.values.map(el => {
              return el / 10000;
            })
          : [],
        tableLabels: paddedDaysData.labels,
      },
      groupedByDayInWeek: {
        label: '',
        data: weekLabels.map((_, index) => {
          const dayOfWeek = index === 6 ? 0 : index + 1;
          const dayData = groupedByDayOfWeek.find(
            el => el?.dayOfWeek === dayOfWeek
          );
          return (dayData?.report[0]?.totalValue ?? 0) / 10000;
        }),
        tableLabels: weekLabels,
      },
    };

    if (isOneDay && lastWeekRawData) {
      const total = groupReport(REPORT_TYPE, newFilteredData, [], []);
      const lastWeekTotal = groupReport(REPORT_TYPE, lastWeekRawData, [], []);
      graphData.oneDayComparison = {
        collectedToday: total[0]?.report[0]?.totalValue / 10000,
        lastWeekDifference:
          (total[0]?.report[0]?.totalValue -
            lastWeekTotal[0]?.report[0]?.totalValue) /
          10000,
      };
    }

    const groupedTableData = groupReport(
      REPORT_TYPE,
      newFilteredData,
      [],
      []
    )[0]?.report[0];

    const tableData = groupedTableData || {};

    return { tableData, graphData };
  }, [filters, rawData, lastWeekRawData]);

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data as OmitKeysWithTypeTransform<Report<'sales'>>[]);

        const isOneDay = dateRange[0]?.isSame(dateRange[1], 'day');
        if (isOneDay) {
          const lastWeekData = await getReportDataHelper({
            database: fbDetails.rtdb!,
            startDate: dateRange[0].subtract(7, 'days').format('YYYY-MM-DD'),
            accountId: fbDetails.selectedAccount!,
            sellPointId: sellPointId,
            endDate: dateRange[1]
              .subtract(7, 'days')
              .add(1, 'seconds')
              .format('YYYY-MM-DD'),
            reportType: REPORT_TYPE,
          });
          setLastWeekRawData(
            lastWeekData as OmitKeysWithTypeTransform<Report<'sales'>>[]
          );
        }

        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });

        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });

        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        });

        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });

        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const handleExport = () => {
    const title = 'Report sales revenue';
    const filtersCsv = `${sellPoints?.find(el => el.id === sellPointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption ? (filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types') : 'All service types'} | ${filters?.source ? (filters?.source !== 'all' ? filters?.source : 'All sources') : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      ['Gross Sales', tableData.value / 10000 || 0].join(','),
      ['Items Value', tableData.itemsValue / 10000 || 0].join(','),
      ['Modifiers', tableData.modifiersValue / 10000 || 0].join(','),
      ['Gift Cards', tableData.giftCardsValue / 10000 || 0].join(','),
      ['Extra Charges', tableData.extraChargesValue / 10000 || 0].join(','),
      ['Discounts', -tableData.discountsValue / 10000 || 0].join(','),
      ['Coupons', -tableData.couponsValue / 10000 || 0].join(','),
      ['Promotions', tableData.promotionsValue / 10000 || 0].join(','),
      ['Net Sales', tableData.netValue / 10000 || 0].join(','),
      ['Tips', tableData.tipsValue / 10000 || 0].join(','),
      ['Cash Tips', tableData.tipsCashValue / 10000 || 0].join(','),
      ['Non-Cash Tips', tableData.tipsNonCashValue / 10000 || 0].join(','),
      ['Total Sales', tableData.totalValue / 10000 || 0].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'salesRevenue');
  };

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('md'));

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('reports.salesRevenue')}
        description={
          <>
            {t('reports.salesRevenueDescription')}
            <a href="https://selio.io/support-center" target="_blank">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={
          <ExportMenuButton
            contentRef={contentRef}
            handleExport={handleExport}
          />
        }
      />

      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />
      <ReportDateTitle />
      {isSmall ? null : (
        <SalesRevenueGraphs
          currency={currency}
          dateRange={dateRange}
          datasets={graphData || {}}
        />
      )}
      <SalesRevenueTables tableData={tableData || []} />
    </Box>
  );
}
