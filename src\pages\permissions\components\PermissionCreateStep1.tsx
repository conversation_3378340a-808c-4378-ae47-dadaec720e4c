import { useEffect, useRef, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import { Grid, InputAdornment, TextField } from '@mui/material';
import { required, usePrevious } from 'react-admin';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { PermissionsParts } from '.';
import { CustomInput } from '../../../components/atoms/inputs/CustomInput/CustomInput';
import { ConflictAwareInput } from '../../../components/conflict-detection';
import { MuiSwitchInputGroup } from '../../../components/molecules/input-groups/SwitchInputGroup';
import Subsection from '../../../components/molecules/Subsection';
import { PermissionPages, permissionsTree } from './constants';

export default function PermissionCreateStep1({ edit }: { edit?: boolean }) {
  const { setValue } = useFormContext();

  // Watch permissions for changes (including from auto-merge)
  // Note: Tracking for conflict detection is done in parent PermissionsForm
  const permissions = useWatch({ name: 'permissions' }) ?? [];

  const [searchQuery, setSearchQuery] = useState('');
  // is full access btn checked
  const [fullAccessCheck, setFullAccessCheck] = useState(
    permissions.length === 1 && permissions[0] === 1
  );
  // and id array of allowed/checked permissions
  const [activePermissions, setActivePermissions] =
    useState<number[]>(permissions);
  const previousPermissions = usePrevious(activePermissions);
  // permissions pages that have active state and are editable
  const [activePages, setActivePages] = useState<PermissionPages[]>([]);
  const prevActivePages = usePrevious(activePages);
  // permission page that is currently in view
  const [currentPage, setCurrentPage] = useState<PermissionPages>(
    PermissionPages.ORDERS
  );

  // Track if we're making local changes to avoid reacting to our own updates
  const isLocalChange = useRef(false);

  // Sync local state when form value changes from external source (auto-merge)
  useEffect(() => {
    if (isLocalChange.current) {
      isLocalChange.current = false;
      return;
    }

    // Update local state to match form value
    setActivePermissions(permissions);

    const isFullAccess = permissions.length === 1 && permissions[0] === 1;
    setFullAccessCheck(isFullAccess);

    if (isFullAccess) {
      setActivePages([
        PermissionPages.ORDERS,
        PermissionPages.CUSTOMERS,
        PermissionPages.GIFT_CARDS,
        PermissionPages.SETTINGS,
      ]);
    } else {
      // Recalculate active pages based on permissions
      const pagesWithActivePermissions: PermissionPages[] = [];
      Object.keys(permissionsTree).forEach(pageKey => {
        const pageGroups = permissionsTree[pageKey as PermissionPages];
        const hasActivePermissions = pageGroups.some(group =>
          group.possiblePermissions.some(perm => permissions.includes(perm))
        );
        if (hasActivePermissions) {
          pagesWithActivePermissions.push(pageKey as PermissionPages);
        }
      });
      setActivePages(pagesWithActivePermissions);
    }
  }, [permissions]);

  const toggleFullAccess = (value: boolean) => {
    isLocalChange.current = true;
    setFullAccessCheck(value);
    if (value) {
      setValue('permissions', [1], { shouldDirty: true });
      setActivePermissions([1]);
      setActivePages([
        PermissionPages.ORDERS,
        PermissionPages.CUSTOMERS,
        PermissionPages.GIFT_CARDS,
        PermissionPages.SETTINGS,
      ]);
    } else {
      const newPermissions =
        previousPermissions?.[0] === 1 ? [] : (previousPermissions ?? []);
      setValue('permissions', newPermissions, { shouldDirty: true });
      setActivePermissions(newPermissions);
      setActivePages(prevActivePages ?? []);
    }
  };

  const handleActivePermissionChange = (permissionId: number) => {
    isLocalChange.current = true;
    let newVals = [];
    if (activePermissions.includes(permissionId)) {
      newVals = activePermissions.filter(id => id !== permissionId);
    } else {
      newVals = [...activePermissions, permissionId];
    }

    setValue('permissions', newVals, { shouldDirty: true });
    setActivePermissions(newVals);
  };

  const handleActivePageChange = (page: PermissionPages) => {
    if (activePages.includes(page)) {
      setActivePages(activePages.filter(p => p !== page));

      // remove associated permissions
      const permissionsToRemove =
        permissionsTree[page]?.flatMap(group => group.possiblePermissions) ||
        [];
      setActivePermissions(prevPermissions =>
        prevPermissions.filter(perm => !permissionsToRemove.includes(perm))
      );
    } else {
      setActivePages([...activePages, page]);
    }
  };

  const { t } = useTranslation();

  return (
    <>
      <ConflictAwareInput source="name">
        <CustomInput
          type="text"
          sanitize="singleLine"
          ui="custom"
          isRequired
          readOnly={!!edit}
          source="name"
          label={t('createPermissions.permissionSetName')}
          validate={[
            required(),
            (value: string) => {
              if (value && /[.#$\/\[\]]/.test(value)) {
                return t('permissions.nameValidationError');
              }
              return undefined;
            },
          ]}
        />
      </ConflictAwareInput>
      <Subsection
        title={t('createPermissions.customize')}
        subtitle={t('createPermissions.customizeSubtitle')}
      >
        <MuiSwitchInputGroup
          checked={fullAccessCheck}
          onChange={e => {
            toggleFullAccess(e.target.checked);
          }}
          label={t('createPermissions.fullAccess')}
          description={t('createPermissions.fullAccessDescription')}
        />
        <TextField
          sx={{ mt: 4, mb: 2 }}
          placeholder={t('createPermissions.searchPermissions')}
          value={searchQuery}
          onChange={e => {
            setSearchQuery(e.target.value);
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        <PermissionsParts.SearchResults
          query={searchQuery}
          setCurrentPage={setCurrentPage}
          resetQuery={() => setSearchQuery('')}
        />

        {!searchQuery && (
          <Grid
            container
            sx={{
              minHeight: { xs: 0, md: '1200px' },
            }}
          >
            <Grid
              size={{
                xs: 12,
                md: 4
              }}>
              <PermissionsParts.SideMenu
                activePages={activePages}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            </Grid>
            <Grid
              sx={{
                display: 'flex',
                height: 'fit-content',
                py: 2,
                px: 4,
                border: '1px #d9d9d9 solid',
                borderRadius: '6px',
              }}
              size={{
                xs: 12,
                md: 8
              }}>
              {/* merge facut un map */}
              {currentPage === PermissionPages.ORDERS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.ORDERS}
                  isPageActive={activePages.includes(PermissionPages.ORDERS)}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.orders}
                />
              )}

              {currentPage === PermissionPages.CUSTOMERS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.CUSTOMERS}
                  isPageActive={activePages.includes(PermissionPages.CUSTOMERS)}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.customers}
                />
              )}

              {currentPage === PermissionPages.GIFT_CARDS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.GIFT_CARDS}
                  isPageActive={activePages.includes(
                    PermissionPages.GIFT_CARDS
                  )}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.giftCards}
                />
              )}

              {currentPage === PermissionPages.SETTINGS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.SETTINGS}
                  isPageActive={activePages.includes(PermissionPages.SETTINGS)}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.settings}
                />
              )}
            </Grid>
          </Grid>
        )}
      </Subsection>
    </>
  );
}
