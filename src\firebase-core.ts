/**
 * Firebase Core Module
 *
 * Single source of truth for Firebase initialization.
 * This module is shared by BOTH the gateway (auth check) AND the main React app.
 *
 * Vite will automatically create a shared chunk for this module,
 * allowing the gateway to load Firebase and the main app to reuse the same instance.
 */

import { FirebaseApp, getApps, initializeApp } from 'firebase/app';
import {
  AppCheck,
  initializeAppCheck,
  ReCaptchaV3Provider,
} from 'firebase/app-check';
import { Auth, getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error(
    'Firebase configuration environment variables are missing! Ensure VITE_FIREBASE_* variables are set in your .env file.'
  );
}

// Single Firebase app instance - shared by gateway and React app
// Check if app already exists to prevent double initialization
export const app: FirebaseApp =
  getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

export const auth: Auth = getAuth(app);

// App Check - production only, configured once
// This is shared by both gateway and main app
export const appCheck: AppCheck | null =
  import.meta.env.VITE_NODE_ENV === 'prod' &&
  import.meta.env.VITE_FIREBASE_APP_CHECK_SITE_KEY
    ? initializeAppCheck(app, {
        provider: new ReCaptchaV3Provider(
          import.meta.env.VITE_FIREBASE_APP_CHECK_SITE_KEY
        ),
        isTokenAutoRefreshEnabled: true,
      })
    : null;

if (appCheck) {
  console.log('[Firebase Core] App Check initialized for production');
}

console.log('[Firebase Core] Initialized:', {
  projectId: firebaseConfig.projectId,
});
