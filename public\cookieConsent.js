/**
 * Shared Cookie Consent Configuration
 * This file can be used both in standalone HTML pages and React apps
 * Works with vanilla-cookieconsent library
 */

// Cookie consent configuration factory
// Pass translations object if available, otherwise uses English defaults
window.initCookieConsent = function (translations) {
    // Default English translations
    const defaultTranslations = {
        title: 'Cookie preferences',
        description: 'When you interact with our mobile applications or online services, we obtain certain information by using automated technologies, such as cookies, web beacons, and other technologies described in our Cookie Policy. This information might be about you, your preferences, or your device. You may opt out of certain categories of cookies, other than those that are strictly necessary to provide you with our Services. Expand the different categories for more information and to make your choices. To effectuate your right to opt-out of the sharing of your personal information for purposes of targeted advertising, please toggle off "retargeting or advertising technology" cookies below.',
        acceptAllBtn: 'Accept all',
        acceptNecessaryBtn: 'Reject all',
        savePreferencesBtn: 'Save preferences',
        closeIconLabel: 'Close modal',
        serviceCounterLabel: 'Service|Services',
        description2: 'To effectuate your right to opt-out of the sharing of your personal information for purposes of targeted advertising, please toggle off "retargeting or advertising technology" cookies below.',
        moreInformation: 'More information',
        privacyNotice: 'Privacy Notice',
        necessary: 'Strictly Necessary Technology',
        necessaryDescription: 'These technologies are necessary for us to provide you with the Services.',
        alwaysEnabled: 'Always Enabled',
        analytics: 'Performance and analytical technology',
        analyticsDescription: 'This information is used to make sure our Services can cope with the volume of users, to help us correct errors in the Services and to measure use across our Services. These technologies help us understand if you have used our Services before so we can identify the number of unique users we receive. They also help us understand how long you spend using our Services and from where you have accessed the Services, so that we can improve the Services and learn about the most popular aspects of the Services.',
        functionality: 'Functionality technology',
        functionalityDescription: 'These technologies enable us to remember you have used our Services before, preferences you may have indicated, and information you have provided to us to give you a customized experience. For example, this would include ensuring the continuity of your registration process.',
        retargeting: 'Retargeting or advertising technology',
        retargetingDescription: 'We use third parties, for example, Google Analytics, to analyze statistical information from users of the Site. We might be able to associate such information with other information which we collect from you once we receive it from a third party. For example, these technologies may collect information about whether you clicked an ad to download our application.'
    };

    // Use provided translations or defaults
    const t = translations || defaultTranslations;

    return {
        guiOptions: {
            consentModal: {
                layout: 'box inline',
                position: 'bottom left',
                equalWeightButtons: true,
                flipButtons: false,
            },
            preferencesModal: {
                layout: 'box',
                position: 'right',
                equalWeightButtons: true,
                flipButtons: false,
            },
        },
        categories: {
            necessary: {
                enabled: true,
                readOnly: true,
            },
            analytics: {},
            functionality: {},
            retargeting: {},
        },
        language: {
            default: 'en',
            autoDetect: 'browser',
            translations: {
                en: {
                    consentModal: {
                        title: 'Manage cookies',
                        description:
                            'Selio uses cookies (and similar technologies) for many purposes, including maintaining security, improving and customizing our services, and enabling relevant marketing. Please review our use of cookies to accept or reject non-essential cookies.',
                        acceptAllBtn: 'Accept all cookies',
                        acceptNecessaryBtn: 'Reject all',
                        showPreferencesBtn: 'Customize cookie preferences',
                        footer:
                            '<a href="https://selio.io/cookie-preferences/">Cookie Preferences</a>\n<a href="https://selio.io/terms-of-service/">Terms of Service</a>',
                    },
                    preferencesModal: {
                        title: t.title,
                        acceptAllBtn: t.acceptAllBtn,
                        acceptNecessaryBtn: t.acceptNecessaryBtn,
                        savePreferencesBtn: t.savePreferencesBtn,
                        closeIconLabel: t.closeIconLabel,
                        serviceCounterLabel: t.serviceCounterLabel,
                        sections: [
                            {
                                description: t.description,
                            },
                            {
                                title: '',
                                description:
                                    `<div>${t.description2} cookies below.</div><br/><br/><div><a class="cc__link" target="_blank" href="https://selio.io/cookie-preferences/">${t.moreInformation}</a><span>	&nbsp;	&nbsp;</span><a class="cc__link" target="_blank" href="https://selio.io/privacy-policy/">${t.privacyNotice}</a></div>`,
                            },
                            {
                                title: `${t.necessary} <span class="pm__badge">${t.alwaysEnabled}</span>`,
                                description: t.necessaryDescription,
                                linkedCategory: 'necessary',
                            },
                            {
                                title: t.analytics,
                                description: t.analyticsDescription,
                                linkedCategory: 'analytics',
                            },
                            {
                                title: t.functionality,
                                description: t.functionalityDescription,
                                linkedCategory: 'functionality',
                            },
                            {
                                title: t.retargeting,
                                description: t.retargetingDescription,
                                linkedCategory: 'retargeting',
                            },
                        ],
                    },
                },
            },
        },
    };
};
