import { useEffect, useReducer } from 'react';
import PrintIcon from '@mui/icons-material/Print';
import { Box, Divider, IconButton, Theme, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';

import DateRangePickerCustom from '~/components/molecules/DateRangePickerCustom';
import { useTheme } from '~/contexts/ThemeContext';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import DatePickerFilterBtn from './DatePickerFilterBtn';
import DatePickerFilterBtnOutlined from './DatePickerFilterBtnOutlined';
import FilterItem, { OptionType } from './FilterItem';
import LocationPickerBtn from './LocationPickerBtn';
import {
  ReportFiltersActionType,
  reportFiltersReducer,
} from './reportFiltersReducer';
import TimeRangePickerBtn from './TimeRangePickerBtn';

import type { DateRange } from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

export enum DiningOption {
  ALL = 'all',
  HERE = 'here',
  TOGO = 'togo',
}

export enum SourceOption {
  ALL = 'all',
  DIRECT_POS = '@pos',
  ARIVA = 'ariva',
  GLOVO = 'glovo',
  BOLT = 'bolt',
  PHONE = 'phone',
}

export enum ServiceOption {
  'forhere' = 'forhere',
  'togo' = 'togo',
  'here' = 'here',
  'pick-up' = 'pick-up',
  'delivery' = 'delivery',
}

export interface ReportFiltersState {
  dateRange: DateRange<Dayjs>;
  sellpointId: string;
  timeRange: {
    allDay?: boolean;
    start?: Dayjs | null;
    end?: Dayjs | null;
  };
  member?: string;
  floor?: string;
  diningOption: DiningOption;
  source: SourceOption;
  idToPrint?: string;
}

interface ReportFiltersProps {
  defaultValues: ReportFiltersState;
  onFiltersChange: (filters: ReportFiltersState) => void;
  globalFiltersVariant?: string;
  commonFields: {
    [key: string]: OptionType[];
  };
}

export default function ReportFilters({
  defaultValues,
  onFiltersChange,
  commonFields,
  globalFiltersVariant,
}: ReportFiltersProps) {
  const { sellPointId, setSellPointId, setDateRange, setTimeRange } =
    useGlobalResourceFilters();
  const { t } = useTranslation();

  const [filters, dispatch] = useReducer(reportFiltersReducer, defaultValues);

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  useEffect(() => {
    onFiltersChange(filters);
  }, [filters]);

  const handleSellPointChange = (newSellPointId: string) => {
    setSellPointId(newSellPointId);
    dispatch({
      type: ReportFiltersActionType.SET_SELLPOINT_ID,
      payload: newSellPointId,
    });
    // Reset member to default when sellpoint changes
    dispatch({
      type: ReportFiltersActionType.SET_MEMBER,
      payload: 'all',
    });
    // Reset floor to default when sellpoint changes
    dispatch({
      type: ReportFiltersActionType.SET_FLOOR,
      payload: 'all',
    });
    // Reset service type to default when sellpoint changes
    dispatch({
      type: ReportFiltersActionType.SET_DINING_OPTION,
      payload: DiningOption.ALL,
    });
    // Reset source to default when sellpoint changes
    dispatch({
      type: ReportFiltersActionType.SET_SOURCE,
      payload: SourceOption.ALL,
    });
  };

  const handleDateRangeChange = (newDateRange: DateRange<Dayjs>) => {
    setDateRange(newDateRange);
    dispatch({
      type: ReportFiltersActionType.SET_DATE_RANGE,
      payload: newDateRange,
    });
    // Reset all filters when date range changes
    dispatch({
      type: ReportFiltersActionType.SET_MEMBER,
      payload: 'all',
    });
    dispatch({
      type: ReportFiltersActionType.SET_FLOOR,
      payload: 'all',
    });
    dispatch({
      type: ReportFiltersActionType.SET_DINING_OPTION,
      payload: DiningOption.ALL,
    });
    dispatch({
      type: ReportFiltersActionType.SET_SOURCE,
      payload: SourceOption.ALL,
    });
  };

  const handleTimeRangeChange = (newTimeRange: DateRange<Dayjs> | null) => {
    // Check if time range actually changed
    const oldTimeRange = filters.timeRange;
    const hasChanged =
      (!newTimeRange && !oldTimeRange.allDay) || // Changed from time range to all day
      (newTimeRange && oldTimeRange.allDay) || // Changed from all day to time range
      (newTimeRange &&
        !oldTimeRange.allDay &&
        (newTimeRange[0]?.format('HH:mm') !==
          oldTimeRange.start?.format('HH:mm') ||
          newTimeRange[1]?.format('HH:mm') !==
            oldTimeRange.end?.format('HH:mm'))); // Time values changed

    setTimeRange(newTimeRange);
    dispatch({
      type: ReportFiltersActionType.SET_TIME_RANGE,
      payload: {
        allDay: !newTimeRange,
        start: newTimeRange?.[0],
        end: newTimeRange?.[1],
      },
    });

    // Only reset filters if time range actually changed
    if (hasChanged) {
      dispatch({
        type: ReportFiltersActionType.SET_MEMBER,
        payload: 'all',
      });
      dispatch({
        type: ReportFiltersActionType.SET_FLOOR,
        payload: 'all',
      });
      dispatch({
        type: ReportFiltersActionType.SET_DINING_OPTION,
        payload: DiningOption.ALL,
      });
      dispatch({
        type: ReportFiltersActionType.SET_SOURCE,
        payload: SourceOption.ALL,
      });
    }
  };
  const { theme } = useTheme();

  return (
    <Box
      sx={{
        position: { xs: 'sticky', sm: 'static' },
        top: 60,
        zIndex: 1000,
        bgcolor: theme.palette.mode === 'dark' ? '#13131A' : 'white',
      }}
    >
      <Box
        sx={{
          position: 'relative',
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          flexWrap: 'wrap',
          gap: 1,
          py: 1,
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: { xs: '-25px', sm: 0 },
            width: { xs: '100vw', sm: '100%' },
            height: '4px',
            boxShadow: {
              xs: '0 2px 4px rgba(0,0,0,.1)',
              sm: 'none',
            },
          }}
        />
        <LocationPickerBtn
          globalFiltersVariant={isXSmall ? 'outlined-2' : globalFiltersVariant}
          sellpointId={sellPointId}
          setSellpointId={handleSellPointChange}
        />
        <DateRangePickerCustom
          dateRange={filters.dateRange}
          setDateRange={handleDateRangeChange}
          ButtonComponent={
            globalFiltersVariant === 'outlined-2' || isXSmall
              ? DatePickerFilterBtnOutlined
              : DatePickerFilterBtn
          }
        />
        {!isXSmall && (
          <>
            <TimeRangePickerBtn
              defaultValues={filters.timeRange}
              setTimeRange={handleTimeRangeChange}
            />
            <FilterItem
              defaultValue={filters.member || 'all'}
              options={[
                { label: t('reportFilters.allMembers'), value: 'all' },
                ...(commonFields?.member || ''),
              ]}
              setSelectedOption={value => {
                dispatch({
                  type: ReportFiltersActionType.SET_MEMBER,
                  payload: value,
                });
              }}
            />
            <FilterItem
              defaultValue={filters.floor || 'all'}
              options={[
                { label: t('reportFilters.allFloors'), value: 'all' },
                ...(commonFields?.floor || ''),
              ]}
              setSelectedOption={value => {
                dispatch({
                  type: ReportFiltersActionType.SET_FLOOR,
                  payload: value,
                });
              }}
            />
            <FilterItem<DiningOption>
              defaultValue={filters.diningOption}
              options={[
                {
                  label: t('reportFilters.allServiceTypes'),
                  value: DiningOption.ALL,
                },
                ...(commonFields?.serviceType?.map(item => {
                  const translationKey = 'reportFilters.' + item.value;
                  console.log('[ReportFilters] Service type mapping:', {
                    originalValue: item.value,
                    translationKey,
                    translated: t(translationKey),
                  });
                  return {
                    label: t(translationKey),
                    value: item.value,
                  };
                }) || ''),
              ]}
              setSelectedOption={value => {
                dispatch({
                  type: ReportFiltersActionType.SET_DINING_OPTION,
                  payload: value,
                });
              }}
            />
            <FilterItem<SourceOption>
              defaultValue={filters.source}
              options={[
                {
                  label: t('reportFilters.allSources'),
                  value: SourceOption.ALL,
                },
                ...(commonFields?.sources?.map(item => {
                  const translationKey = 'reportFilters.' + item.value;
                  console.log('[ReportFilters] Source mapping:', {
                    originalValue: item.value,
                    translationKey,
                    translated: t(translationKey),
                  });
                  return {
                    label: t(translationKey),
                    value: item.value,
                  };
                }) || ''),
              ]}
              setSelectedOption={value => {
                dispatch({
                  type: ReportFiltersActionType.SET_SOURCE,
                  payload: value,
                });
              }}
            />
          </>
        )}
      </Box>
    </Box>
  );
}
