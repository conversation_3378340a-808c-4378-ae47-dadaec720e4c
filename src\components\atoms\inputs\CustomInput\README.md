# CustomInput Component

Universal React-Admin input wrapper with optional custom UI and text sanitization.

## Features

- ✅ **19 Input Types**: Supports all standard React-Admin inputs plus custom ones
- ✅ **Dual UI Modes**: Switch between custom horizontal layout or standard React-Admin UI
- ✅ **Text Sanitization**: Built-in sanitization with 3 presets to handle copy-paste artifacts
- ✅ **Full RA Compatibility**: Works in SimpleForm, ArrayInput, TranslatableInputs, ReferenceInput
- ✅ **Type Safe**: TypeScript discriminated unions for all input types
- ✅ **Zero Dependencies**: Uses existing React-Admin and MUI components

## Installation

The component is located at:

```
src/components/atoms/inputs/CustomInput/
```

Import it:

```tsx
import { CustomInput } from '~/components/atoms/inputs/CustomInput';
```

## Basic Usage

### Custom UI (Default)

```tsx
<CustomInput
  type="text"
  source="name"
  label="Product Name"
  sanitize="singleLine"
/>
```

### Original React-Admin UI

```tsx
<CustomInput
  type="text"
  source="name"
  label="Product Name"
  sanitize="singleLine"
  ui="original"
/>
```

## Props

### Core Props

| Prop       | Type                                                   | Required | Default    | Description                            |
| ---------- | ------------------------------------------------------ | -------- | ---------- | -------------------------------------- |
| `type`     | `string`                                               | Yes      | -          | Input type (see supported types below) |
| `source`   | `string`                                               | Yes      | -          | Form field name                        |
| `ui`       | `'original' \| 'custom'`                               | No       | `'custom'` | UI mode                                |
| `sanitize` | `'singleLine' \| 'multiLine' \| 'identifier' \| false` | No       | `false`    | Sanitization preset                    |

### Additional Props

All other props are passed through to the underlying React-Admin component. This includes:

- `label` - Field label
- `validate` - Validation rules
- `helperText` - Helper text (shown in tooltip for custom UI)
- `placeholder` - Placeholder text
- `disabled` - Disable input
- `readOnly` - Make input read-only
- `defaultValue` - Default value
- All type-specific props (e.g., `choices` for SelectInput)

## Supported Input Types

### Text Inputs

- `text` - TextInput
- `email` - TextInput with email type
- `url` - TextInput with url type
- `password` - PasswordInput
- `textArray` - TextArrayInput

### Number & Date Inputs

- `number` - NumberInput
- `date` - DateInput
- `datetime` - DateTimeInput
- `time` - TimeInput

### Choice Inputs

- `select` - SelectInput
- `autocomplete` - AutocompleteInput
- `radio` - RadioButtonGroupInput
- `checkboxGroup` - CheckboxGroupInput

### Boolean Inputs

- `boolean` - BooleanInput (checkbox)
- `nullableBoolean` - NullableBooleanInput (3-state)

### Custom Inputs

- `phone` - PhoneNumberInput
- `code` - CodeInput

## Sanitization Presets

### `singleLine`

Removes invisible characters and line breaks, normalizes spaces

- **Use for**: Single-line text fields (names, titles, codes)
- **Example**: `"  Hello\nWorld  "` → `"Hello World"`

### `multiLine`

Preserves line breaks, removes invisible characters, normalizes spaces, trims each line

- **Use for**: Descriptions, comments, multi-line text
- **Example**: `"Line 1  \n  Line 2"` → `"Line 1\nLine 2"`

### `identifier`

Converts to uppercase, alphanumeric only

- **Use for**: SKUs, codes, identifiers
- **Example**: `"  prod-123  "` → `"PROD-123"`

## UI Modes

### Custom UI (`ui="custom"`)

Horizontal layout with:

- Label section on left (200px box with background)
- Input section on right (flex:1)
- Error/focus state styling
- Helper text in tooltip
- Custom placeholder positioning
- Responsive (stacks vertically on mobile)

**When to use:**

- New forms
- Consistent horizontal layout desired
- Custom styling requirements

### Original UI (`ui="original"`)

Standard React-Admin inline layout

- Label above input
- Helper text below input
- Default RA styling

**When to use:**

- Migration from existing forms
- Complex inputs (RichTextInput)
- Standard RA look preferred

## Examples

### Simple Form

```tsx
<SimpleForm>
  <CustomInput
    type="text"
    source="name"
    label="Name"
    sanitize="singleLine"
    validate={required()}
  />

  <CustomInput
    type="email"
    source="email"
    label="Email"
    sanitize="singleLine"
  />

  <CustomInput
    type="select"
    source="category"
    label="Category"
    choices={categories}
  />
</SimpleForm>
```

### ArrayInput

```tsx
<ArrayInput source="items">
  <SimpleFormIterator>
    <CustomInput
      type="text"
      source="name"
      label="Item Name"
      sanitize="singleLine"
    />
    <CustomInput type="number" source="quantity" label="Quantity" />
  </SimpleFormIterator>
</ArrayInput>
```

### Mixed UI Modes

```tsx
<SimpleForm>
  {/* Custom UI for simple text */}
  <CustomInput type="text" source="title" label="Title" sanitize="singleLine" />

  {/* Original UI for complex input */}
  <CustomInput
    type="richText"
    source="description"
    label="Description"
    ui="original"
  />
</SimpleForm>
```

### With TranslatableInputs

```tsx
<TranslatableInputs locales={['en', 'ro']}>
  <CustomInput type="text" source="name" label="Name" sanitize="singleLine" />
  <CustomInput
    type="text"
    source="description"
    label="Description"
    sanitize="multiLine"
    multiline
    rows={3}
  />
</TranslatableInputs>
```

### With ReferenceInput

```tsx
<ReferenceInput source="categoryId" reference="categories">
  <CustomInput type="select" optionText="name" label="Category" />
</ReferenceInput>
```

## Migration from Old CustomInput

### Step 1: Update Import

```tsx
// Before
// After
import CustomInput, {
  CustomInput,
} from '~/components/atoms/inputs/CustomInput';
```

### Step 2: Keep Original UI During Transition

```tsx
// Add ui="original" to maintain exact same appearance
<CustomInput type="text" source="name" ui="original" />
```

### Step 3: Switch to Custom UI When Ready

```tsx
// Remove ui prop (defaults to custom)
<CustomInput type="text" source="name" sanitize="singleLine" />
```

### Step 4: Add Sanitization

```tsx
// Add sanitize prop to enable sanitization
<CustomInput type="text" source="name" sanitize="singleLine" />
```

## Architecture

### File Structure

```
CustomInput/
├── CustomInput.tsx       # Main component
├── types.ts             # TypeScript interfaces
├── helpers.ts           # Utility functions
└── index.ts             # Exports
```

### How It Works

#### Original UI Mode

1. Wraps parse prop to add sanitization
2. Renders React-Admin component directly
3. No custom layout, no form state access

#### Custom UI Mode

1. Uses `useFormContext` + `useWatch` to access form state
2. Gets error state without registering field (avoids double registration)
3. Renders custom Box layout with label and input sections
4. Wraps parse prop to add sanitization
5. Passes all props to underlying RA component

### Key Design Decisions

- **No `useInput` call**: Avoids double registration (underlying RA component calls it)
- **No `useChoicesContext` call**: Let SelectInput/AutocompleteInput handle it internally
- **Parse prop wrapper**: Adds sanitization without affecting RA functionality
- **Type-safe**: Discriminated unions ensure correct props for each type

## Testing

Run the test page to verify all functionality:

```tsx
// Add to your router
import { CustomInputTest } from '~/pages/custom-input-test';

// Add route
<Route path="/custom-input-test" element={<CustomInputTest />} />;
```

Test checklist:

- ✅ All 19 input types render correctly
- ✅ Custom UI and original UI both work
- ✅ Sanitization works (paste text with extra spaces/newlines)
- ✅ Validation works (required fields)
- ✅ ArrayInput works with CustomInput
- ✅ Error states display correctly
- ✅ Focus states work
- ✅ Disabled/readonly states work
- ✅ Responsive layout works on mobile

## License

Internal use only - part of Selio Manager application.
