import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Autocomplete,
  Box,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  SaveButton,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';
import {
  useGetListLocationsLive,
  useGetListPrepStationsLive,
} from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';

// Types
interface PrepStation {
  [key: string]: string;
}

interface SagaLocationData {
  locationNumberPrefix: string;
  groupAllCompsInSameDocument: boolean;
  prepStations: PrepStation;
}

interface SellPoints {
  [locationId: string]: SagaLocationData;
}

interface GiftCardConfig {
  code: string;
  symbol: string;
}

interface GiftCards {
  '@physical': GiftCardConfig;
  '@digital': GiftCardConfig;
}

interface TipsConfig {
  code: string;
  symbol: string;
}

interface SagaFormValues {
  itemsCodeField: string;
  modifiersCodeField: string;
  tips: TipsConfig;
  giftCards: GiftCards;
  sellPoints: SellPoints;
}

type CodeFieldType = 'sku' | 'gtin' | 'ean';

interface CodeFieldOption {
  value: CodeFieldType;
  label: string;
}

// Constants
const CODE_FIELD_OPTIONS: CodeFieldOption[] = [
  { value: 'sku', label: 'SKU' },
  { value: 'gtin', label: 'GTIN' },
  { value: 'ean', label: 'EAN' },
] as const;

// Custom hook for SAGA form management
const useSagaForm = () => {
  const { setValue, getValues, watch } = useFormContext<SagaFormValues>();

  const updateLocationField = useCallback(
    (
      locationId: string,
      field: keyof SagaLocationData,
      value: string | boolean
    ) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId] || {
        locationNumberPrefix: '',
        groupAllCompsInSameDocument: false,
        prepStations: {},
      };

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          [field]: value,
        },
      });
    },
    [setValue, getValues]
  );

  const addPrepStationToLocation = useCallback(
    (locationId: string, prepStationName: string): boolean => {
      if (!prepStationName?.trim()) {
        console.warn('Prep station name cannot be empty');
        return false;
      }

      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId] || {
        locationNumberPrefix: '',
        groupAllCompsInSameDocument: false,
        prepStations: {},
      };

      const currentPrepStations = currentLocation.prepStations || {};

      // Check for duplicates
      if (currentPrepStations[prepStationName]) {
        console.warn(
          `Prep station ${prepStationName} already exists for location ${locationId}`
        );
        return false;
      }

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          prepStations: {
            ...currentPrepStations,
            [prepStationName]: '',
          },
        },
      });

      return true;
    },
    [setValue, getValues]
  );

  const removePrepStationFromLocation = useCallback(
    (locationId: string, prepStationName: string) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return;
      }

      const currentPrepStations = { ...currentLocation.prepStations };
      delete currentPrepStations[prepStationName];

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          prepStations: currentPrepStations,
        },
      });
    },
    [setValue, getValues]
  );

  const updatePrepStationSymbol = useCallback(
    (locationId: string, prepStationName: string, symbol: string) => {
      const currentLocations = getValues('sellPoints') || {};
      const currentLocation = currentLocations[locationId];

      if (!currentLocation) {
        console.error(`Location ${locationId} not found`);
        return;
      }

      setValue('sellPoints', {
        ...currentLocations,
        [locationId]: {
          ...currentLocation,
          prepStations: {
            ...currentLocation.prepStations,
            [prepStationName]: symbol,
          },
        },
      });
    },
    [setValue, getValues]
  );

  const updateTips = useCallback(
    (field: keyof TipsConfig, value: string) => {
      const currentTips = getValues('tips') || { code: '', symbol: '' };
      setValue('tips', {
        ...currentTips,
        [field]: value,
      });
    },
    [setValue, getValues]
  );

  const updateGiftCard = useCallback(
    (
      key: '@physical' | '@digital',
      field: keyof GiftCardConfig,
      value: string
    ) => {
      const currentGiftCards = getValues('giftCards') || {
        '@physical': { code: '', symbol: '' },
        '@digital': { code: '', symbol: '' },
      };

      setValue('giftCards', {
        ...currentGiftCards,
        [key]: {
          ...currentGiftCards[key],
          [field]: value,
        },
      });
    },
    [setValue, getValues]
  );

  return {
    updateLocationField,
    addPrepStationToLocation,
    removePrepStationFromLocation,
    updatePrepStationSymbol,
    updateTips,
    updateGiftCard,
    watch,
    setValue,
  };
};

// Component for items and modifiers codes
const ItemsAndModifiersSection = () => {
  const { setValue, watch } = useSagaForm();
  const { t } = useTranslation('');

  const itemsCodeField = watch('itemsCodeField') || 'sku';
  const modifiersCodeField = watch('modifiersCodeField') || 'sku';

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('myIntegrations.itemsAndModifiers')}
        <Tooltip title={t('myIntegrations.itemsCodeTooltip')}>
          <IconButton size="small">
            <InfoOutlinedIcon color="disabled" sx={{ fontSize: '18px' }} />
          </IconButton>
        </Tooltip>
      </Typography>
      <Grid container spacing={2}>
        <Grid size={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="items-code-field-label">
              {t('myIntegrations.itemsCode')}
            </InputLabel>
            <Select
              labelId="items-code-field-label"
              value={itemsCodeField}
              label={t('myIntegrations.itemsCode')}
              onChange={e =>
                setValue('itemsCodeField', e.target.value as CodeFieldType)
              }
              required
            >
              {CODE_FIELD_OPTIONS.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid size={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="modifiers-code-field-label">
              {t('myIntegrations.modifiersCode')}
            </InputLabel>
            <Select
              labelId="modifiers-code-field-label"
              value={modifiersCodeField}
              label={t('myIntegrations.modifiersCode')}
              onChange={e =>
                setValue('modifiersCodeField', e.target.value as CodeFieldType)
              }
              required
            >
              {CODE_FIELD_OPTIONS.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

// Component for tips configuration
const TipsSection = () => {
  const { updateTips, watch } = useSagaForm();
  const { t } = useTranslation('');

  const tips = watch('tips') || { code: '', symbol: '' };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('myIntegrations.tips')}
      </Typography>
      <Grid container spacing={2}>
        <Grid size={6}>
          <TextField
            label={`${t('myIntegrations.code')} *`}
            value={tips.code}
            onChange={e => updateTips('code', e.target.value)}
            required
            fullWidth
            size="small"
          />
        </Grid>
        <Grid size={6}>
          <TextField
            label={t('myIntegrations.symbol')}
            value={tips.symbol}
            onChange={e => updateTips('symbol', e.target.value)}
            fullWidth
            size="small"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

// Component for gift cards configuration
const GiftCardsSection = () => {
  const { updateGiftCard, watch } = useSagaForm();
  const { t } = useTranslation('');
  const { theme } = useTheme();
  const giftCards = watch('giftCards') || {
    '@physical': { code: '', symbol: '' },
    '@digital': { code: '', symbol: '' },
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('myIntegrations.giftCards')}
      </Typography>
      <Grid container spacing={0}>
        {/* Physical Gift Cards */}
        <Grid
          size={{
            xs: 12,
            md: 6
          }}>
          <Box
            sx={{
              p: { xs: 0, md: 2 },
              pl: { xs: 0, md: 0 },
              pt: { xs: 2, md: 2 },
              pb: { xs: 1, md: 2 },
              borderRight: {
                xs: 'none',
                md: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
              },
              borderBottom: {
                xs: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                md: 'none',
              },
            }}
          >
            <Grid container spacing={2}>
              <Grid size={12}>
                <Typography
                  variant="body1"
                  sx={{ fontWeight: 'medium', mb: 1 }}
                >
                  {t('myIntegrations.physical')}
                </Typography>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label={`${t('myIntegrations.code')} *`}
                  value={giftCards['@physical']?.code || ''}
                  onChange={e =>
                    updateGiftCard('@physical', 'code', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label={`${t('myIntegrations.symbol')} *`}
                  value={giftCards['@physical']?.symbol || ''}
                  onChange={e =>
                    updateGiftCard('@physical', 'symbol', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        </Grid>

        {/* Digital Gift Cards */}
        <Grid
          size={{
            xs: 12,
            md: 6
          }}>
          <Box
            sx={{
              p: { xs: 0, md: 2 },
              pr: { xs: 0, md: 0 },
              pt: { xs: 1, md: 2 },
              pb: { xs: 2, md: 2 },
            }}
          >
            <Grid container spacing={2}>
              <Grid size={12}>
                <Typography
                  variant="body1"
                  sx={{ fontWeight: 'medium', mb: 1 }}
                >
                  {t('myIntegrations.digital')}
                </Typography>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label={`${t('myIntegrations.code')} *`}
                  value={giftCards['@digital']?.code || ''}
                  onChange={e =>
                    updateGiftCard('@digital', 'code', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label={`${t('myIntegrations.symbol')} *`}
                  value={giftCards['@digital']?.symbol || ''}
                  onChange={e =>
                    updateGiftCard('@digital', 'symbol', e.target.value)
                  }
                  required
                  fullWidth
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

// Component for managing locations with prep stations
const LocationsSection = () => {
  const {
    updateLocationField,
    addPrepStationToLocation,
    removePrepStationFromLocation,
    updatePrepStationSymbol,
    watch,
    setValue,
  } = useSagaForm();

  const {
    data: locationsList,
    error: locationsError,
    isLoading: locationsLoading,
  } = useGetListLocationsLive({
    filter: { _d: false },
  });
  const { data: prepStationsList } = useGetListPrepStationsLive();
  const { t } = useTranslation('');
  const locations = watch('sellPoints') || {};
  const { theme } = useTheme();

  // State for prep station input values
  const [prepStationToAdd, setPrepStationToAdd] = useState<
    Record<string, string>
  >({});

  // Initialize locations from available locations list
  const initializeLocations = useCallback(() => {
    if (locationsList && Object.keys(locations).length === 0) {
      const initialLocations: Record<string, SagaLocationData> = {};
      locationsList.forEach((location: any) => {
        initialLocations[location.id] = {
          locationNumberPrefix: '',
          groupAllCompsInSameDocument: false,
          prepStations: {},
        };
      });
      setValue('sellPoints', initialLocations);
    }
  }, [locationsList, locations, setValue]);

  // Initialize locations when component mounts or locationsList changes
  useEffect(() => {
    initializeLocations();
  }, [initializeLocations]);

  // Enhanced prep station addition with proper state management
  const handleAddPrepStation = useCallback(
    (locationId: string, prepStationName: string) => {
      const trimmedName = prepStationName?.trim();
      if (!trimmedName) return;

      const success = addPrepStationToLocation(locationId, trimmedName);
      if (success) {
        setPrepStationToAdd(prev => ({ ...prev, [locationId]: '' }));
      }
    },
    [addPrepStationToLocation]
  );

  // Get prep station names for autocomplete, excluding already added ones for this location
  const getPrepStationOptions = useCallback(
    (locationId: string) => {
      const allPrepStations =
        prepStationsList?.map((station: any) => station.name) || [];
      const locationData = locations[locationId] || {};
      const addedPrepStations = Object.keys(locationData.prepStations || {});

      return [...new Set(allPrepStations)]
        .filter(name => !addedPrepStations.includes(name))
        .sort();
    },
    [prepStationsList, locations]
  );

  // Memoize location names to prevent unnecessary re-renders
  const locationNamesMap = useMemo(() => {
    if (!locationsList) return {};
    return locationsList.reduce(
      (acc: Record<string, string>, location: any) => {
        acc[location.id] = location.name;
        return acc;
      },
      {}
    );
  }, [locationsList]);

  if (locationsLoading) {
    return <Typography>Loading locations...</Typography>;
  }

  if (locationsError) {
    return <Typography color="error">Error loading locations</Typography>;
  }

  if (!locationsList || locationsList.length === 0) {
    return <Typography>{t('myIntegrations.noLocationsAvailable')}</Typography>;
  }

  // Group locations into rows for responsive layout (2 per row on md, 3 per row on xl)
  const locationRows = [];
  for (let i = 0; i < locationsList.length; i += 3) {
    locationRows.push(locationsList.slice(i, i + 3));
  }

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('myIntegrations.locations')}
      </Typography>
      {locationRows.map((row, rowIndex) => (
        <Box key={rowIndex}>
          <Grid
            container
            sx={{
              backgroundColor: 'transparent',
              borderRadius: 1,
              minHeight: { md: 350 },
            }}
          >
            {row.map((location: any, colIndex: number) => {
              const locationData = locations[location.id] || {
                locationNumberPrefix: '',
                groupAllCompsInSameDocument: false,
                prepStations: {},
              };
              const isFirstInRow = colIndex === 0;
              const isLastInRow = colIndex === row.length - 1;

              return (
                <Grid
                  key={location.id}
                  sx={{
                    backgroundColor: 'transparent',
                    pl: { xs: 1, md: isFirstInRow ? 1 : 2 },
                    pr: { xs: 1, md: isLastInRow ? 1 : 1 },
                    pt: { xs: 1, md: 1 },
                    pb: { xs: colIndex < row.length - 1 ? 1 : 1, md: 1 },
                    borderRight: {
                      xs: 'none',
                      md: !isLastInRow
                        ? `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`
                        : 'none',
                    },
                    borderBottom: {
                      xs:
                        colIndex < row.length - 1
                          ? `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`
                          : 'none',
                      md: 'none',
                    },
                    height: { md: '100%' },
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                  size={{
                    xs: 12,
                    md: row.length === 1 ? 12 : 6,
                    xl: 4
                  }}>
                  {/* Location Header */}
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      mb: { xs: 1, md: 2 },
                    }}
                  >
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {locationNamesMap[location.id] || location.name}
                    </Typography>
                  </Box>
                  {/* Rest of the location content */}
                  <Box
                    sx={{
                      flexGrow: 1,
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    {/* Location Configuration Section */}
                    <Typography
                      variant="body1"
                      sx={{ mb: 1, fontWeight: 'medium' }}
                    >
                      Location Configuration
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 1,
                        mb: { xs: 1, md: 2 }, // Reduced margin on mobile
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: { xs: 'column', md: 'row' }, // Stack on mobile, side by side on desktop
                          gap: 1,
                        }}
                      >
                        <TextField
                          label={t('myIntegrations.saga.locationNumberPrefix')}
                          value={locationData?.locationNumberPrefix || ''}
                          onChange={e =>
                            updateLocationField(
                              location.id,
                              'locationNumberPrefix',
                              e.target.value
                            )
                          }
                          required
                          fullWidth
                          size="small"
                          slotProps={{
                            input: {
                              endAdornment: (
                                <Tooltip
                                  title={t(
                                    'myIntegrations.saga.locationNumberPrefixTooltip'
                                  )}
                                >
                                  <IconButton size="small">
                                    <InfoOutlinedIcon
                                      color="disabled"
                                      sx={{ fontSize: '18px' }}
                                    />
                                  </IconButton>
                                </Tooltip>
                              ),
                            },
                          }}
                        />
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mt: 1,
                        }}
                      >
                        <Typography variant="body2" sx={{ flexGrow: 1 }}>
                          {t('myIntegrations.saga.groupAllCompsInSameDocument')}
                        </Typography>
                        <Switch
                          checked={
                            locationData?.groupAllCompsInSameDocument || false
                          }
                          onChange={e => {
                            updateLocationField(
                              location.id,
                              'groupAllCompsInSameDocument',
                              e.target.checked
                            );
                          }}
                          size="small"
                        />
                      </Box>
                    </Box>

                    {/* Prep Stations Section - flex-grow to push to bottom */}
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 1, fontWeight: 'medium' }}
                      >
                        {t('myIntegrations.prepStations')}
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1,
                        }}
                      >
                        {Object.entries(locationData?.prepStations || {}).map(
                          ([prepStationName, symbol]: [string, any]) => (
                            <Box
                              key={prepStationName}
                              sx={{
                                // Remove the background color
                                borderRadius: 1,
                              }}
                            >
                              <Grid
                                container
                                spacing={1}
                                alignItems="center"
                                sx={{ p: 0 }}
                              >
                                <Grid size={6}>
                                  <Typography
                                    variant="body2"
                                    sx={{ fontWeight: 'medium' }}
                                  >
                                    {prepStationName}
                                  </Typography>
                                </Grid>
                                <Grid size={5}>
                                  <TextField
                                    label={t('myIntegrations.symbol')}
                                    value={symbol || ''}
                                    onChange={e =>
                                      updatePrepStationSymbol(
                                        location.id,
                                        prepStationName,
                                        e.target.value
                                      )
                                    }
                                    fullWidth
                                    size="small"
                                    sx={{
                                      backgroundColor:
                                        theme.palette.mode === 'dark'
                                          ? '#343439'
                                          : 'white',
                                    }}
                                  />
                                </Grid>
                                <Grid
                                  sx={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                  }}
                                  size={1}>
                                  <IconButton
                                    onClick={() =>
                                      removePrepStationFromLocation(
                                        location.id,
                                        prepStationName
                                      )
                                    }
                                    color="error"
                                    size="small"
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Grid>
                              </Grid>
                            </Box>
                          )
                        )}

                        {/* Add new prep station */}
                        <Grid container spacing={1} alignItems="center">
                          <Grid size={11}>
                            <Autocomplete
                              options={getPrepStationOptions(location.id)}
                              freeSolo
                              size="small"
                              value={prepStationToAdd[location.id] || ''}
                              inputValue={prepStationToAdd[location.id] || ''}
                              onInputChange={(event, newInputValue) => {
                                setPrepStationToAdd({
                                  ...prepStationToAdd,
                                  [location.id]: newInputValue,
                                });
                              }}
                              onChange={(event, value) => {
                                if (value && typeof value === 'string') {
                                  setPrepStationToAdd({
                                    ...prepStationToAdd,
                                    [location.id]: value,
                                  });
                                }
                              }}
                              renderInput={params => (
                                <TextField
                                  {...params}
                                  label={t('myIntegrations.addPrepStation')}
                                  placeholder={t(
                                    'myIntegrations.typePrepStationName'
                                  )}
                                  onKeyDown={e => {
                                    if (e.key === 'Enter') {
                                      e.preventDefault();
                                      const prepStationName =
                                        prepStationToAdd[location.id]?.trim();
                                      if (prepStationName) {
                                        handleAddPrepStation(
                                          location.id,
                                          prepStationName
                                        );
                                      }
                                    }
                                  }}
                                />
                              )}
                            />
                          </Grid>
                          <Grid sx={{ display: 'flex', justifyContent: 'center' }} size={1}>
                            <IconButton
                              onClick={() =>
                                handleAddPrepStation(
                                  location.id,
                                  prepStationToAdd[location.id] || ''
                                )
                              }
                              color="primary"
                              size="small"
                            >
                              <AddIcon />
                            </IconButton>
                          </Grid>
                        </Grid>
                      </Box>
                    </Box>
                  </Box>
                </Grid>
              );
            })}
          </Grid>

          {/* Horizontal divider between rows */}
          {rowIndex < locationRows.length - 1 && (
            <Box
              sx={{
                borderBottom: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                my: 2,
              }}
            />
          )}
        </Box>
      ))}
    </Box>
  );
};

// Main component
interface MyIntegrationsSagaCreateEditProps {
  setOpen?: (open: boolean) => void;
  partnerExtraData?: any;
  mode?: 'create' | 'edit';
}

export const MyIntegrationsSagaCreateEdit = ({
  setOpen,
  partnerExtraData,
  mode = 'create',
}: MyIntegrationsSagaCreateEditProps) => {
  const { t } = useTranslation('');
  const resource = useResourceContext();
  const record = useRecordContext();
  const redirect = useRedirect();
  const { setValue, watch } = useFormContext<SagaFormValues>();
  const { theme } = useTheme();

  const handleClose = () => {
    if (mode === 'create' && setOpen) {
      setOpen(false);
    } else {
      redirect('list', resource, record?.id, undefined, {
        _scrollToTop: false,
      });
    }
  };

  const updateBasicField = useCallback(
    (field: keyof SagaFormValues, value: string) => {
      setValue(field, value);
    },
    [setValue]
  );

  const itemsCodeField = watch('itemsCodeField');
  const modifiersCodeField = watch('modifiersCodeField');

  // Determine title based on mode
  const getTitle = () => {
    if (mode === 'create') {
      return `${t('myIntegrations.createIntegration')} ${partnerExtraData?.name || ''}`;
    }
    return `${t('shared.edit')} Integration - ${record?.name || ''}`;
  };

  return (
    <>
      <ModalHeader handleClose={handleClose} title={getTitle()}>
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          p: 3,
          width: '100%',
        }}
      >
        {/* First Row: Items & Modifiers + Tips */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {/* Items and Modifiers - Column 1 */}
          <Grid
            size={{
              xs: 12,
              md: 6
            }}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <ItemsAndModifiersSection />
            </Box>
          </Grid>

          {/* Tips - Column 2 */}
          <Grid
            size={{
              xs: 12,
              md: 6
            }}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <TipsSection />
            </Box>
          </Grid>
        </Grid>

        {/* Second Row: Gift Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid size={12}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <GiftCardsSection />
            </Box>
          </Grid>
        </Grid>

        {/* Third Row: Locations */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid size={12}>
            <Box
              sx={{
                border: `1px solid ${theme.palette.mode === 'dark' ? '#424242' : '#e0e0e0'}`,
                borderRadius: 2,
                p: 2,
              }}
            >
              <LocationsSection />
            </Box>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default MyIntegrationsSagaCreateEdit;
