import { useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  Divider,
  Grid,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';

import { useTheme } from '../../../contexts';

export default function ShareSelioBanner() {
  const [show, setShow] = useState(true);
  const { theme } = useTheme();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <>
      {show && (
        <Grid
          container
          mt={3}
          sx={{
            width: '100$',
            borderRadius: '6px',
            px: isXSmall ? 3 : 5,
            py: 4,
            bgcolor:
              theme.palette.mode == 'light'
                ? 'transparent'
                : 'background.tinted',
            boxShadow: '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)',
            marginBottom: '2px',
            position: 'relative',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              cursor: 'pointer',
            }}
            onClick={() => setShow(false)}
          >
            <CloseIcon fontSize="small" />
          </Box>
          <Grid
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            size={{
              xs: 12,
              md: 4.5
            }}>
            <img
              style={{
                filter: theme.palette.mode === 'light' ? '' : 'invert(1)',
              }}
              src="/assets/money-bag.svg"
              alt="money-bag-icon"
            />
            <Typography variant="h3" sx={{ ml: 2 }}>
              Share Selio, get up to 1.000 €.
            </Typography>
          </Grid>
          <Grid
            sx={{
              display: 'flex',
              justifyContent: 'center',
            }}
            size={{
              xs: 0,
              md: 0.5
            }}>
            <Divider orientation="vertical" />
          </Grid>
          <Grid
            mt={isXSmall ? 1 : 0}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            size={{
              xs: 12,
              md: 4.5
            }}>
            <Typography variant="caption">
              Refer a restaurant, café, or any other business to Selio and you
              may be eligible to receive up to 1.000 € in rewards for each
              business referred.
            </Typography>
          </Grid>
          <Grid
            mt={isXSmall ? 2 : 0}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}
            size={{
              xs: 12,
              md: 2.5
            }}>
            <Button
              variant="contained"
              sx={{ width: isXSmall ? '100%' : 'auto' }}
            >
              Refer a business
            </Button>
          </Grid>
        </Grid>
      )}
    </>
  );
}
