import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react';
import {
  <PERSON>ert,
  <PERSON>ert<PERSON><PERSON>le,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
} from '@mui/material';
import { useSubscribeToRecord } from '@react-admin/ra-realtime';
import get from 'lodash/get';
import {
  useDataProvider,
  useEditContext,
  useGetOne,
  useNotify,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '../../providers/resources';
import { GranularChangeTracker } from '../../utils/granularChangeTracker';

interface RealtimeEditConflictDetectorProps {
  /**
   * Translation namespace for conflict messages.
   * The component will look for these keys:
   * - {namespace}.itemUpdatedByOtherUser
   * - {namespace}.itemRefreshedWithLatestChanges
   * - {namespace}.itemDeletedByOtherUser
   * - {namespace}.formRefreshedWithLatestData
   * - {namespace}.conflictDialogTitle
   * - {namespace}.conflictDialogDescription
   * - {namespace}.modifiedBy
   * - {namespace}.conflictRiskTitle
   * - {namespace}.conflictRiskDescription
   * - {namespace}.granularMergeTitle
   * - {namespace}.granularMergeDescription
   * - {namespace}.lastUpdatedAt
   * - {namespace}.keepMyChanges
   * - {namespace}.keepMyChangesAndMerge
   * - {namespace}.getTheirChanges
   * - {namespace}.changesMergedAutomatically
   * - {namespace}.conflictsDetected
   * - {namespace}.changesMergedMyChangesKept
   */
  translationNamespace?: string;

  /**
   * Custom callback to execute after the user chooses to keep their changes
   */
  onKeepMyChanges?: () => void;

  /**
   * Custom callback to execute after the user chooses to keep their changes and merge
   * Receives the remote data as parameter so callers can track the true remote baseline
   */
  onKeepMyChangesAndMerge?: (remoteData: any) => void;

  /**
   * Custom callback to execute after the user chooses to get the latest changes
   */
  onGetTheirChanges?: () => void;

  /**
   * Custom callback to execute when the record is deleted by another user
   */
  onRecordDeleted?: () => void;

  /**
   * Whether to auto-refresh when the form is not dirty
   * @default true
   */
  autoRefreshWhenClean?: boolean;

  /**
   * Whether to show notifications for updates
   * @default true
   */
  showNotifications?: boolean;

  /**
   * Custom resource name (overrides context)
   */
  resource?: string;

  /**
   * Custom record ID (overrides context)
   */
  recordId?: string;

  /**
   * Instance of GranularChangeTracker for path-based conflict detection
   * If provided, enables granular conflict detection instead of simple isDirty check
   */
  changeTracker?: GranularChangeTracker;
}

/**
 * RealtimeEditConflictDetector
 *
 * A reusable component that detects when a record being edited has been modified
 * or deleted by another user. It automatically subscribes to realtime updates and
 * handles conflicts intelligently:
 *
 * - When form is NOT dirty: Auto-refreshes with latest data (non-intrusive)
 * - When form IS dirty: Shows conflict dialog for user to decide (protective)
 * - When record is deleted: Closes edit and redirects to list
 *
 * @example
 * ```tsx
 * const MyEdit = () => (
 *   <Edit>
 *     <SimpleForm>
 *       <TextInput source="name" />
 *       <RealtimeEditConflictDetector translationNamespace="myResource" />
 *     </SimpleForm>
 *   </Edit>
 * );
 * ```
 */
export const RealtimeEditConflictDetector = ({
  translationNamespace = 'shared',
  onKeepMyChanges,
  onKeepMyChangesAndMerge,
  onGetTheirChanges,
  onRecordDeleted,
  autoRefreshWhenClean = true,
  showNotifications = true,
  resource: customResource,
  recordId: customRecordId,
  changeTracker,
}: RealtimeEditConflictDetectorProps) => {
  const { t } = useTranslation('');
  const contextResource = useResourceContext();
  const contextRecord = useRecordContext();
  const redirect = useRedirect();
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const { refetch } = useEditContext();
  const {
    formState: { isDirty },
    reset,
    getValues,
    setValue,
    watch,
  } = useFormContext();

  const [conflictDialogOpen, setConflictDialogOpen] = useState(false);
  const [conflictTimestamp, setConflictTimestamp] = useState<number | null>(
    null
  );
  const [modifiedByUserId, setModifiedByUserId] = useState<string | null>(null);
  const [remoteData, setRemoteData] = useState<any>(null);
  const [hasConflicts, setHasConflicts] = useState(false);

  // Store user's form values continuously as they edit
  const userFormValuesRef = useRef<any>(null);

  // Use custom resource/recordId or fall back to context
  const resource = customResource || contextResource;
  const recordId = customRecordId || contextRecord?.id;

  const useGranular = !!changeTracker;

  // Continuously capture the user's current form values as they edit
  // This ensures we always have the latest user edits before any cache update
  useEffect(() => {
    if (isDirty) {
      userFormValuesRef.current = getValues();
    }
  }, [isDirty, getValues]);

  // Also update ref whenever form values change (for auto-merge scenarios)
  // This ensures we capture the state even after reset makes form clean
  useEffect(() => {
    const subscription = watch(value => {
      userFormValuesRef.current = value;
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  // Fetch team member details for the user who made the modification
  const shouldFetchUser = useMemo(() => {
    if (!modifiedByUserId) return false;
    if (modifiedByUserId === '@na' || modifiedByUserId === '@system')
      return false;
    return true;
  }, [modifiedByUserId]);

  const { data: teamMember } = useGetOne(
    RESOURCES.TEAM_MEMBERS,
    { id: modifiedByUserId || '' },
    { enabled: shouldFetchUser }
  );

  // Resolve the display name for the modifier
  const modifiedByUserName = useMemo(() => {
    if (!modifiedByUserId) return null;
    if (modifiedByUserId === '@system') return 'SYSTEM';
    if (modifiedByUserId === '@na') return 'N/A';
    if (teamMember?.displayName) return teamMember.displayName;
    if (shouldFetchUser) return 'UNKNOWN';
    return null;
  }, [modifiedByUserId, teamMember, shouldFetchUser]);

  // Default handler for keeping changes
  const handleKeepMyChanges = useCallback(() => {
    if (useGranular && hasConflicts && remoteData && changeTracker) {
      // Merge non-conflicting changes from remote
      const { mergedData, conflicts } =
        changeTracker.mergeWithRemote(remoteData);
      const conflictPaths = conflicts.map(c => c.path);

      // Keep my changes for conflict paths
      const finalData = changeTracker.keepMyChanges(conflictPaths, mergedData);

      // Update the form with merged data and mark it as dirty
      // We need to use setValue with shouldDirty: true to properly mark the form as changed
      reset(finalData, { keepDefaultValues: true });

      // Force the form to be dirty by touching fields that had conflicts
      setTimeout(() => {
        // Get the paths that had conflicts and set them again to mark as dirty
        conflicts.forEach(conflict => {
          const value = conflict.path
            .split('.')
            .reduce((obj, key) => obj?.[key], finalData);
          if (value !== undefined) {
            setValue(conflict.path, value, {
              shouldDirty: true,
              shouldTouch: true,
            });
          }
        });
      }, 0);

      if (showNotifications) {
        notify(
          t(`${translationNamespace}.changesMergedMyChangesKept`) ||
            'Non-conflicting changes merged, your edits preserved',
          {
            type: 'success',
            autoHideDuration: 3000,
          }
        );
      }
    }

    setConflictDialogOpen(false);
    setRemoteData(null);
    setHasConflicts(false);

    // Update snapshot to remote data while preserving user's tracked changes
    if (useGranular && changeTracker && hasConflicts && remoteData) {
      const currentFormData = getValues();
      changeTracker.updateSnapshotKeepingChanges(remoteData, currentFormData);

      console.log(
        '[RealtimeConflictDetector] After manual merge, still tracking:',
        changeTracker.getChangeSummary()
      );
    }

    onKeepMyChanges?.();
    // Call the merge callback with remote data so modals can track the true remote baseline
    if (useGranular && hasConflicts && remoteData) {
      onKeepMyChangesAndMerge?.(remoteData);
    }
  }, [
    useGranular,
    hasConflicts,
    remoteData,
    changeTracker,
    reset,
    setValue,
    notify,
    t,
    translationNamespace,
    showNotifications,
    onKeepMyChanges,
    onKeepMyChangesAndMerge,
  ]);

  // Default handler for getting their changes
  const handleGetTheirChanges = useCallback(() => {
    if (useGranular && remoteData && changeTracker) {
      // Accept all remote changes - force reset form state
      reset(remoteData, {
        keepDirty: false,
        keepDirtyValues: false,
      });
      changeTracker.updateSnapshot(remoteData);
      changeTracker.clearChanges();

      // Mark form as pristine by resetting it again with the same data
      // This ensures isDirty becomes false
      setTimeout(() => {
        reset(remoteData, {
          keepDirty: false,
          keepDirtyValues: false,
        });
      }, 0);

      if (showNotifications) {
        notify(
          t(`${translationNamespace}.formRefreshedWithLatestData`) ||
            'Form updated with latest changes',
          {
            type: 'success',
            autoHideDuration: 2000,
          }
        );
      }
    } else {
      // Simple mode - just reset with remote data
      reset(remoteData || {}, {
        keepDirty: false,
        keepDirtyValues: false,
      });

      if (showNotifications) {
        notify(
          t(`${translationNamespace}.formRefreshedWithLatestData`) ||
            'Form updated with latest changes',
          {
            type: 'success',
            autoHideDuration: 2000,
          }
        );
      }
    }

    setConflictDialogOpen(false);
    setRemoteData(null);
    setHasConflicts(false);
    onGetTheirChanges?.();
  }, [
    useGranular,
    remoteData,
    changeTracker,
    reset,
    notify,
    t,
    translationNamespace,
    showNotifications,
    onGetTheirChanges,
  ]);

  // Handle closing edit when record is deleted
  const handleRecordDeleted = useCallback(() => {
    if (showNotifications) {
      notify(
        t(`${translationNamespace}.itemDeletedByOtherUser`) ||
          'This item has been deleted by another user',
        {
          type: 'error',
        }
      );
    }

    // Redirect to list view
    redirect('list', resource);

    onRecordDeleted?.();
  }, [
    notify,
    t,
    translationNamespace,
    redirect,
    resource,
    showNotifications,
    onRecordDeleted,
  ]);

  // Realtime event handler
  const handleRealtimeEvent = useCallback(
    (event: any) => {
      console.log(
        `[RealtimeConflictDetector] Event received for ${resource}:`,
        event
      );

      // Extract the 'by' field from event payload
      const modifiedBy = event.payload?.by || null;

      if (event.type === 'updated') {
        // Store who modified the record
        if (modifiedBy) {
          setModifiedByUserId(modifiedBy);
        }

        // User's form values are already captured in the useEffect hook above
        // No need to capture here as it might already be stale

        // Fetch fresh data directly from data provider WITHOUT updating the form
        // This prevents the flash of remote data before user makes a decision
        if (!resource || !recordId) {
          console.warn(
            '[RealtimeConflictDetector] Missing resource or recordId'
          );
          return;
        }

        dataProvider.getOne(resource, { id: recordId }).then((result: any) => {
          const freshRemoteData = result?.data;

          if (!freshRemoteData) {
            console.warn(
              '[RealtimeConflictDetector] No data in refetch result'
            );
            return;
          }

          if (useGranular && changeTracker) {
            const hasTrackedChanges = changeTracker.hasChanges();

            if (hasTrackedChanges || isDirty) {
              // Check for conflicts using granular tracking
              const conflicts = changeTracker.detectConflicts(freshRemoteData);

              console.log('[RealtimeConflictDetector] Checking conflicts:', {
                hasTrackedChanges,
                isDirty,
                conflictsFound: conflicts.length,
                trackedPaths: changeTracker.getChangeSummary(),
              });

              if (conflicts.length === 0 && hasTrackedChanges) {
                // No conflicts but we have tracked changes, auto-merge
                const { mergedData } =
                  changeTracker.mergeWithRemote(freshRemoteData);
                reset(mergedData);

                // Update snapshot to remote data while preserving tracked user changes
                // This ensures future remote edits to user-modified paths trigger conflicts
                changeTracker.updateSnapshotKeepingChanges(
                  freshRemoteData,
                  mergedData
                );

                // Update userFormValuesRef with merged data so future conflicts restore correctly
                userFormValuesRef.current = mergedData;

                console.log(
                  '[RealtimeConflictDetector] Changes auto-merged successfully, still tracking:',
                  changeTracker.getChangeSummary()
                );

                if (showNotifications) {
                  notify(
                    t(`${translationNamespace}.changesMergedAutomatically`) ||
                      'Changes merged automatically (no conflicts)',
                    {
                      type: 'info',
                      autoHideDuration: 2000,
                    }
                  );
                }
              } else if (conflicts.length > 0) {
                // Conflicts detected - restore user's form values that we captured earlier
                console.log(
                  `[RealtimeConflictDetector] ${conflicts.length} conflict(s) detected, restoring user's form state`
                );

                // Restore user's values (undo the refetch update)
                if (userFormValuesRef.current) {
                  reset(userFormValuesRef.current, {
                    keepDirty: true,
                    keepDirtyValues: true,
                  });
                }

                // Store the remote data for later use
                setRemoteData(freshRemoteData);
                setHasConflicts(true);
                setConflictDialogOpen(true);
                setConflictTimestamp(Date.now());

                if (showNotifications) {
                  notify(
                    t(`${translationNamespace}.conflictsDetected`) ||
                      "Conflicts detected with another user's changes",
                    {
                      type: 'warning',
                      autoHideDuration: 4000,
                    }
                  );
                }
              } else if (isDirty && !hasTrackedChanges) {
                // Form is dirty but no tracked changes - show simple conflict
                console.log(
                  "[RealtimeConflictDetector] Form is dirty, restoring user's form state"
                );

                // Restore user's values (undo the refetch update)
                if (userFormValuesRef.current) {
                  reset(userFormValuesRef.current, {
                    keepDirty: true,
                    keepDirtyValues: true,
                  });
                }

                setRemoteData(freshRemoteData);
                setHasConflicts(false);
                setConflictDialogOpen(true);
                setConflictTimestamp(Date.now());

                if (showNotifications) {
                  notify(
                    t(`${translationNamespace}.itemUpdatedByOtherUser`) ||
                      'This item has been updated by another user',
                    {
                      type: 'warning',
                      autoHideDuration: 4000,
                    }
                  );
                }
              }
            } else if (autoRefreshWhenClean) {
              // No unsaved changes - safe to update form with fresh data
              console.log(
                '[RealtimeConflictDetector] Auto-refreshing (form not dirty)'
              );
              reset(freshRemoteData);
              if (changeTracker) {
                changeTracker.updateSnapshot(freshRemoteData);
              }

              if (showNotifications) {
                notify(
                  t(`${translationNamespace}.itemRefreshedWithLatestChanges`) ||
                    'Item updated with latest changes',
                  {
                    type: 'info',
                    autoHideDuration: 2000,
                  }
                );
              }
            }
          } else if (isDirty) {
            // Fallback to simple conflict detection
            console.log(
              "[RealtimeConflictDetector] Using simple conflict detection, restoring user's form state"
            );

            // Restore user's values (undo the refetch update)
            if (userFormValuesRef.current) {
              reset(userFormValuesRef.current, {
                keepDirty: true,
                keepDirtyValues: true,
              });
            }

            setRemoteData(freshRemoteData);
            setHasConflicts(false);
            setConflictDialogOpen(true);
            setConflictTimestamp(Date.now());

            if (showNotifications) {
              notify(
                t(`${translationNamespace}.itemUpdatedByOtherUser`) ||
                  'This item has been updated by another user',
                {
                  type: 'warning',
                  autoHideDuration: 4000,
                }
              );
            }
          } else if (autoRefreshWhenClean) {
            // No unsaved changes - safe to update form with fresh data
            console.log(
              '[RealtimeConflictDetector] Auto-refreshing (form clean)'
            );
            reset(freshRemoteData);

            if (showNotifications) {
              notify(
                t(`${translationNamespace}.itemRefreshedWithLatestChanges`) ||
                  'Item updated with latest changes',
                {
                  type: 'info',
                  autoHideDuration: 2000,
                }
              );
            }
          }
        });
      }

      if (event.type === 'deleted') {
        // Store who deleted the record
        if (modifiedBy) {
          setModifiedByUserId(modifiedBy);
        }
        // Item deleted by another user
        handleRecordDeleted();
      }
    },
    [
      isDirty,
      autoRefreshWhenClean,
      refetch,
      reset,
      getValues,
      notify,
      t,
      translationNamespace,
      showNotifications,
      handleRecordDeleted,
      resource,
      useGranular,
      changeTracker,
    ]
  );

  // Subscribe to realtime updates
  useSubscribeToRecord(handleRealtimeEvent, resource, recordId);

  return (
    <Dialog
      open={conflictDialogOpen}
      onClose={(event, reason) => {
        // Prevent closing by clicking overlay or pressing Escape
        if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
          return;
        }
      }}
      disableEscapeKeyDown
      aria-labelledby="conflict-dialog-title"
      aria-describedby="conflict-dialog-description"
    >
      <DialogTitle id="conflict-dialog-title">
        {t(`${translationNamespace}.conflictDialogTitle`) ||
          'Item Updated by Another User'}
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="conflict-dialog-description">
          {t(`${translationNamespace}.conflictDialogDescription`) ||
            'This item has been modified by another user. Your changes and their changes may conflict. What do you want to do?'}
        </DialogContentText>

        {modifiedByUserName && (
          <Typography
            variant="body2"
            sx={{ mt: 1, color: 'text.secondary', fontWeight: 500 }}
          >
            {t(`${translationNamespace}.modifiedBy`) || 'Modified by'}:{' '}
            {modifiedByUserName}
          </Typography>
        )}

        <Alert
          severity={useGranular && hasConflicts ? 'info' : 'warning'}
          sx={{ mt: 2 }}
        >
          <AlertTitle>
            {useGranular && hasConflicts
              ? t(`${translationNamespace}.granularMergeTitle`) ||
                'Smart Merge Available'
              : t(`${translationNamespace}.conflictRiskTitle`) ||
                'Risk of Data Loss'}
          </AlertTitle>
          {useGranular && hasConflicts
            ? t(`${translationNamespace}.granularMergeDescription`) ||
              'Only your conflicting changes will be kept. All other changes will be merged automatically.'
            : t(`${translationNamespace}.conflictRiskDescription`) ||
              'If you keep your changes, you may overwrite their modifications when you save.'}
        </Alert>

        {conflictTimestamp && (
          <Typography
            variant="caption"
            display="block"
            sx={{ mt: 2, color: 'text.secondary' }}
          >
            {t(`${translationNamespace}.lastUpdatedAt`) || 'Last updated at'}:{' '}
            {new Date(conflictTimestamp).toLocaleTimeString()}
          </Typography>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 2, gap: 1 }}>
        <Button
          onClick={handleKeepMyChanges}
          variant="contained"
          sx={{
            bgcolor: 'warning.main',
            color: 'warning.contrastText',
            '&:hover': {
              bgcolor: 'warning.dark',
            },
          }}
        >
          {useGranular && hasConflicts
            ? t(`${translationNamespace}.keepMyChangesAndMerge`) ||
              'Keep My Changes & Merge Others'
            : t(`${translationNamespace}.keepMyChanges`) ||
              'Keep My Changes (Risk Overwrite)'}
        </Button>
        <Button
          onClick={handleGetTheirChanges}
          variant="contained"
          color="primary"
        >
          {t(`${translationNamespace}.getTheirChanges`) ||
            'Get Their Changes (Lose Mine)'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
