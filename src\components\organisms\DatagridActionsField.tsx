import { useState } from 'react';
import { MoreHorizRounded } from '@mui/icons-material';
import { IconButton, Menu, MenuItem } from '@mui/material';
import { UseMutationOptions } from '@tanstack/react-query';
import {
  DeleteParams,
  MutationMode,
  RaRecord,
  UseDeleteOptions,
  useRecordContext,
  useRedirect,
  useResourceContext,
  WrapperField,
  WrapperFieldProps,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomDeleteWithConfirmButton from '../molecules/CustomDeleteWithConfirmButton';

interface ActionsFieldProps<
  RecordType extends RaRecord = any,
  MutationOptionsError = unknown,
> extends Omit<WrapperFieldProps, 'children'> {
  hasEdit: boolean;
  hasDelete: boolean;
  deleteMutationMode?: MutationMode;
  deleteMutationOptions?: UseDeleteOptions<RecordType, MutationOptionsError>;
  recordNameField?: string;
}

export const ActionsField = (props: ActionsFieldProps) => {
  const redirect = useRedirect();
  const resource = useResourceContext(props);
  const record = useRecordContext(props);
  const { t } = useTranslation();
  // Generate a unique ID when component mounts
  const [uniqueId] = useState(() =>
    Math.random().toString(36).substring(2, 15)
  );
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleClose();
    const deleteButton = document.querySelector(
      `#dwcb-${uniqueId} .ra-delete-button`
    ) as HTMLElement;
    if (deleteButton) {
      deleteButton.click();
    }
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleClose();
    redirect('edit', resource, record!.id, record, {
      _scrollToTop: false,
    });
  };

  const {
    label,
    deleteMutationMode,
    deleteMutationOptions,
    recordNameField = 'name',
    textAlign,
    hasEdit = true,
    hasDelete = true,
    ...rest
  } = props;
  return (
    <WrapperField label={label} textAlign={textAlign} {...rest}>
      <IconButton onClick={handleClick} sx={{ color: '#0064F0' }}>
        <MoreHorizRounded />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        onClick={e => e.stopPropagation()}
      >
        {hasEdit && (
          <MenuItem onClick={handleEditClick} sx={{ paddingX: 4 }}>
            {t('shared.edit')}
          </MenuItem>
        )}
        {hasDelete && (
          <MenuItem
            onClick={handleDeleteClick}
            sx={{ color: '#FF0000', paddingX: 4 }}
          >
            {t('shared.delete')}
          </MenuItem>
        )}
      </Menu>
      <div id={`dwcb-${uniqueId}`} style={{ display: 'none' }}>
        <CustomDeleteWithConfirmButton
          field={recordNameField}
          mutationMode={deleteMutationMode}
          mutationOptions={deleteMutationOptions}
          {...rest}
        />
      </div>
    </WrapperField>
  );
};
