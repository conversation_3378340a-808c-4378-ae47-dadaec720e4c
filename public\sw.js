/**
 * Minimal Service Worker for PWA "Add to Home Screen" functionality
 *
 * This service worker does NOT cache anything.
 * It exists solely to satisfy PWA installability requirements.
 */

const SW_VERSION = '__APP_VERSION__';

self.addEventListener('install', () => {
    console.log(`[SW] v${SW_VERSION} installing`);
    self.skipWaiting();
});

self.addEventListener('activate', (event) => {
    console.log(`[SW] v${SW_VERSION} activating`);
    // Clean up any old caches from previous versions
    event.waitUntil(
        caches.keys()
            .then(names => Promise.all(names.map(n => caches.delete(n))))
            .then(() => self.clients.claim())
    );
});

// Fetch handler required for PWA installability
// We don't cache anything - just pass through to network
self.addEventListener('fetch', (event) => {
    // Let the browser handle all requests normally (no caching)
    return;
});
