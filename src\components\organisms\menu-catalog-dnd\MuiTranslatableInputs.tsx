import { useState } from 'react';
import { Box, Tab, Tabs, TextField, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { SanitizedTextField } from '~/components/atoms/inputs/sanitized';

export const MuiTranslatableInputs = ({
  state,
  updateValue,
  locales,
  title
}: {
  state: any;
  updateValue: (key: string, value: any) => void;
  locales: string[];
  title: boolean;
}) => {
  const [locale, setLocale] = useState(locales[0]);
  const { t } = useTranslation('');
  console.log(state);
  return (
    <>
      {title && <Typography variant="h6" sx={{ mb: 2 }}>{t('itemsLibrary.nameAndDescriptionVariations')}</Typography>}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          border: '1px solid #E0E0E0',
          borderRadius: 2,
        }}
      >
        <Tabs
          sx={{ backgroundColor: '#F5F5F5', borderBottom: '1px solid #E0E0E0' }}
          value={locale}
          onChange={(e, value) => setLocale(value)}
        >
          {locales.map(locale => (
            <Tab
              sx={{ minWidth: '50px' }}
              key={locale}
              label={locale}
              value={locale}
            />
          ))}
        </Tabs>
        <Box sx={{ p: 2 }}>
          <SanitizedTextField
            label={t('itemsLibrary.publicName')}
            value={state.publicName?.[locale] || ''}
            onChange={e =>
              updateValue(`publicName`, {
                ...state.publicName,
                [locale]: e.target.value,
              })
            }
          />
          <SanitizedTextField
            label={t('itemsLibrary.publicDescription')}
            value={state.description?.[locale] || ''}
            onChange={e =>
              updateValue(`description`, {
                ...state.description,
                [locale]: e.target.value,
              })
            }
          />
          <SanitizedTextField
            label={t('itemsLibrary.ingredients')}
            value={state.ingredients?.[locale] || ''}
            onChange={e =>
              updateValue(`ingredients`, {
                ...state.ingredients,
                [locale]: e.target.value,
              })
            }
          />
        </Box>
      </Box>
    </>
  );
};
