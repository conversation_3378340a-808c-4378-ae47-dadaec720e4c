import { ChangeEvent, useCallback, useEffect, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import {
  Box,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  TextField,
  Typography,
} from '@mui/material';
import debounce from 'lodash/debounce';

import { formatNumber } from '../../utils/formatNumber';

export default function CheckboxList({
  type,
  items,
  selectedItems,
  updateSelectedItems,
}: any) {
  const [shownExistingItems, setShownExistingItems] = useState<any>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  useEffect(() => {
    if (items && items.length)
      setShownExistingItems(
        items.sort((a: any, b: any) =>
          a.displayName.localeCompare(b.displayName)
        )
      );
  }, [items]);

  const filterBySearch = (searchKey: string) => {
    const tmp = (items || []).filter((el: any) =>
      el.displayName.toLowerCase().includes(searchKey.toLowerCase())
    );

    setShownExistingItems(tmp);
  };

  const handleSearchChange = useCallback(
    debounce((searchKey: string) => {
      filterBySearch(searchKey);
    }, 200),
    [items]
  );

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    item: any
  ) => {
    const { checked } = event.target;
    const updatedItems = [...selectedItems];

    if (checked) {
      updatedItems.push({ id: item.id, type: item.type });
    } else {
      const index = updatedItems.findIndex((el: any) => el.id === item.id);
      if (index !== -1) {
        updatedItems.splice(index, 1);
      }
    }

    updateSelectedItems(updatedItems);
  };

  return (
    <>
      <TextField
        value={searchValue}
        onChange={(e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
          setSearchValue(e.target.value);
          handleSearchChange(e.target.value);
        }}
        placeholder="Search"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          p: 2,
          borderBottom: 'solid 1px',
          borderColor: 'custom.gray600',
        }}
      >
        <Typography>{type === 'product' ? 'Item' : 'Menu Group'}</Typography>
        {type === 'product' && <Typography>Price</Typography>}
      </Box>
      {shownExistingItems?.map((item: any) => {
        return (
          <Box
            key={item.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              borderBottom: 'solid 1px',
              borderColor: 'custom.gray400',
              p: 1,
              ':hover': {
                bgcolor: 'background.tinted',
              },
            }}
          >
            <FormControlLabel
              sx={{
                flex: 1,
                m: 0,
              }}
              control={
                <Checkbox
                  checked={!!selectedItems.find((el: any) => el.id === item.id)}
                  onChange={e => handleChange(e, item)}
                  inputProps={{ 'aria-label': 'controlled' }}
                />
              }
              label={
                <Typography variant="body2" fontWeight={200}>
                  {item.displayName}
                </Typography>
              }
            />
            {type === 'product' && (
              <Typography variant="body2" fontWeight={200}>
                {formatNumber(item.price / 10000, 'currency')}
              </Typography>
            )}
          </Box>
        );
      })}
    </>
  );
}
