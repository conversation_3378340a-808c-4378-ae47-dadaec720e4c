import { useEffect, useMemo, useRef } from 'react';
import {
  Box,
  Button,
  Dialog,
  Grid,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { required } from 'react-admin';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import { CustomTranslatableInputs } from '~/components/atoms/inputs/CustomTranslatableInputs';
import { useConflictDetectionOptional } from '~/hooks/useConflictDetection/ConflictDetectionContext';
import getFullscreenModalProps from '~/utils/getFullscreenModalProps';
import ModalHeader from '../../../molecules/ModalHeader';
import {
  createStandardImageConfig,
  FilePreviewInline,
  resolveImageEditorConfig,
} from '../../FileUpload';
import { Coordinates } from '../types';
import {
  cleanupTempFields,
  handleCreateDisplayGroup,
  initializeTempFields,
} from './helpers';

interface ListModeModalProps {
  mode: 'create' | 'edit';
  path: string;
  data?: any[]; // Required for create mode
  position?: Coordinates; // Required for create mode
  onClose: () => void;
}

/**
 * Component to properly handle image URL display for uploaded files
 */
const ItemImageDisplay = ({ images }: { images: any[] }) => {
  // Memoize the first image to ensure we get the correct one when the array changes
  const firstImage = useMemo(() => {
    return images && images.length > 0 ? images[0] : null;
  }, [images]);

  // Create a stable key to force re-render when the image changes to avoid stale blob URLs
  const imageKey = useMemo(() => {
    if (!firstImage) return 'no-image';
    // Create a key that includes both filename and last modified info to handle cache issues
    return `${firstImage.f}-${firstImage.e}-${firstImage.x ? 'temp' : 'perm'}`;
  }, [firstImage]);

  // Show the first image or placeholder
  return (
    <FilePreviewInline
      key={imageKey} // Force re-render with fresh blob URLs when image changes
      files={firstImage ? [firstImage] : []} // Pass only the first image or empty array for placeholder
      width={90}
      height={90}
      borderRadius={1.5}
      imageVariant="square_xs"
      fileType="images"
    />
  );
};

/**
 * Display Group Modal for List Mode (recordType='list')
 *
 * Features:
 * - Create mode: Uses temp fields, generates ID, adds to data array
 * - Edit mode: Directly edits fields at path
 * - Fields: displayName, images, publicName, publicDescription
 * - No color picker or page navigation
 */
export default function ListModeModal({
  mode,
  path,
  data = [],
  position,
  onClose,
}: ListModeModalProps) {
  const { t } = useTranslation('');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const {
    setValue,
    getValues,
    trigger,
    clearErrors,
    resetField,
    formState: { dirtyFields },
  } = useFormContext();
  const conflictCtx = useConflictDetectionOptional();

  const tempPrefix = '__modal_displayGroup_list_temp';
  const isCreateMode = mode === 'create';
  // Always use temp fields to avoid marking parent form as dirty
  const fieldPath = tempPrefix;

  // Watch fields for live updates
  const watchedData = useWatch({ name: fieldPath });
  const images = useWatch({ name: `${fieldPath}.images` }) || [];

  // Store original values in edit mode to restore on cancel
  const originalValuesRef = useRef<any>(null);

  // Store original values on mount (edit mode only)
  useEffect(() => {
    if (!isCreateMode && !originalValuesRef.current) {
      originalValuesRef.current = JSON.parse(JSON.stringify(getValues(path)));
    }
  }, [isCreateMode, path, getValues]);

  // Initialize temp fields on mount
  useEffect(() => {
    if (isCreateMode) {
      // Create mode: empty fields
      initializeTempFields(setValue, clearErrors, tempPrefix, {
        displayName: '',
        images: [],
      });
    } else {
      // Edit mode: copy existing values to temp fields
      const existingValues = getValues(path);
      initializeTempFields(
        setValue,
        clearErrors,
        tempPrefix,
        JSON.parse(JSON.stringify(existingValues))
      );
    }

    // Cleanup on unmount
    return () => {
      cleanupTempFields(resetField, tempPrefix);
    };
  }, [isCreateMode, setValue, clearErrors, resetField, path, getValues]);

  // Check if any fields have changed (compare temp fields with original)
  const hasChanges = useMemo(() => {
    if (isCreateMode) return true; // Always enabled in create mode

    if (!originalValuesRef.current || !watchedData) return false;

    const original = originalValuesRef.current;
    const current = watchedData;

    // Compare displayName
    if (original.displayName !== current.displayName) return true;

    // Compare images (check length and content)
    const originalImages = original.images || [];
    const currentImages = current.images || [];
    if (originalImages.length !== currentImages.length) return true;
    if (
      JSON.stringify(originalImages.map((i: any) => i.url).sort()) !==
      JSON.stringify(currentImages.map((i: any) => i.url).sort())
    ) {
      return true;
    }

    // Compare translatable fields
    if (
      JSON.stringify(original.publicName || {}) !==
      JSON.stringify(current.publicName || {})
    ) {
      return true;
    }

    if (
      JSON.stringify(original.publicDescription || {}) !==
      JSON.stringify(current.publicDescription || {})
    ) {
      return true;
    }

    return false;
  }, [isCreateMode, originalValuesRef.current, watchedData]);

  const handleSave = async () => {
    if (isCreateMode) {
      // Create mode: validate, create item, add to data
      const success = await handleCreateDisplayGroup(
        tempPrefix,
        path,
        data,
        position,
        trigger,
        getValues,
        setValue,
        conflictCtx
      );

      if (success) {
        onClose();
      }
    } else {
      // Edit mode: validate temp fields, then copy to actual path
      const isValid = await trigger(`${tempPrefix}.displayName`);
      if (isValid) {
        // Copy temp values to actual path
        const tempValues = getValues(tempPrefix);
        setValue(path, tempValues, { shouldDirty: true });
        // Track change for conflict detection
        conflictCtx?.trackChange(path, tempValues);
        onClose();
      }
    }
  };

  const handleClose = () => {
    // No need to restore values - temp fields are discarded automatically
    onClose();
  };

  return (
    <Dialog
      open
      onClose={handleClose}
      fullWidth={true}
      maxWidth="sm"
      transitionDuration={0}
      fullScreen={isMobile}
      {...(isMobile
        ? { TransitionComponent: getFullscreenModalProps().TransitionComponent }
        : {})}
    >
      <ModalHeader
        handleClose={handleClose}
        title={
          isCreateMode
            ? t('menu.addMenuGroup')
            : `${t('shared.edit')} ${t('menu.menuGroup')}`
        }
      >
        <Button variant="contained" onClick={handleSave} disabled={!hasChanges}>
          {isCreateMode ? t('shared.create') : t('shared.save')}
        </Button>
      </ModalHeader>

      <Box sx={{ p: 3 }}>
        {/* Details Section */}
        <Typography variant="h5" sx={{ mb: 2 }}>
          {t('menu.details')}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: 2,
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3,
          }}
        >
          <Box sx={{ flex: 1 }}>
            <CustomInput
              type="text"
              source={`${fieldPath}.displayName`}
              label={t('shared.name')}
              validate={required()}
              sanitize="singleLine"
              ui="custom"
              roundedCorners="top"
            />
            <CustomInput
              type="fileUpload"
              source={`${fieldPath}.images`}
              label={t('menu.media')}
              fileType="images"
              multiple={false}
              maxFiles={1}
              maxSize={20 * 1024 * 1024}
              acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
              variant="compact"
              imageConfig={(() => {
                const resolved = resolveImageEditorConfig(
                  createStandardImageConfig.custom([], {
                    standardSizes: ['square_xs'],
                    allowZoom: true,
                    outputFormat: 'webp',
                    outputQuality: 0.8,
                  })
                );
                return {
                  targetSizes: resolved.targetSizes,
                  autoGenerateThumbnail: true,
                  quality: 0.8,
                  editorConfig: resolved,
                };
              })()}
              ui="custom"
              roundedCorners="bottom"
            />
          </Box>
          <Box
            sx={{
              flexShrink: 0,
              display: { xs: 'none', md: 'block' },
              ml: 0,
            }}
          >
            <ItemImageDisplay images={images} />
          </Box>
        </Box>

        {/* Translatable Fields */}
        <CustomDivider />
        <Box sx={{ mt: 3 }}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {t('itemsLibrary.nameAndDescriptionVariations')}
          </Typography>
          <CustomTranslatableInputs locales={['en', 'ro']} roundedCorners="top">
            <CustomInput
              type="text"
              source={`${fieldPath}.publicName`}
              label={t('itemsLibrary.publicName')}
              sanitize="singleLine"
              ui="custom"
              roundedCorners="bottom"
            />
          </CustomTranslatableInputs>
        </Box>
      </Box>
    </Dialog>
  );
}
