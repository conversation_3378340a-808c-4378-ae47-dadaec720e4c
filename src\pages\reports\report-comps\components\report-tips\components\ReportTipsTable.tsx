import { useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListLocationsLive } from '~/providers/resources';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';

type TableRow = {
  vat: number;
  price: number;
  quantity: number;
  value: number;
  tipsCashValue: number;
  reason: string;
  tipsNonCashValue: number;
};

const fieldsConstant = [
  { isChecked: true, value: 'vat' },
  { isChecked: true, value: 'value' },
  { isChecked: true, value: 'tipsCashValue' },
  { isChecked: true, value: 'tipsNonCashValue' },
  { isChecked: true, value: 'reason' },
];

export default function ReportTipsTable({
  tableData,
  updateCompsData,
  filters,
}: {
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
  updateCompsData?: any;
  filters: any;
}) {
  const [fields, setFields] = useState<FieldOption[]>(fieldsConstant);
  const { t } = useTranslation();
  const reportTipsData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let totalItemsData = mergeAndSumObjects(tableData);
      totalItemsData.vat = '';
      totalItemsData.reason = '';
      totalItemsData.subItems = [];

      updateCompsData('totalTips', totalItemsData);
      return [...tableData, totalItemsData];
    }

    updateCompsData('totalTips', {});
    return [];
  }, [tableData]);

  const reportTipsConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'vat',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {rowIndex === reportTipsData.length - 1
              ? 'Total'
              : row.vat !== undefined && row.vat + '%'}
          </div>
        );
      },
      label: t('shared.tva'),
      textAlign: 'start',
    },
    {
      id: 'reason',
      label: t('reportsPage.reason'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{row.reason === '@na' ? 'N/A' : row.reason}</>;
      },
    },
    {
      id: 'value',
      label: 'Total',
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.value)}</>;
      },
    },
  ];

  const columnsToFilter = ['vat', 'value', 'tipsCashValue', 'reason'];

  const { dateRange, timeRange } = useGlobalResourceFilters();
  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report comped tips';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      ['VAT', 'Reason', 'Total'].join(','),
      ...reportTipsData
        ?.slice(0, reportTipsData.length - 1)
        .map((el: any) =>
          [`${el.vat}%`, el.reason, el.value / 10000].join(',')
        ),
      [
        'Total',
        '',
        Number(reportTipsData[reportTipsData.length - 1].value) / 10000 || 0,
      ].join(','),
    ].join('\n');

    downloadCSV(csvContent, 'comped-tips');
  };

  return (
    <>
      <Box sx={{ py: 2 }}>
        <CustomTable
          fields={fields}
          setFields={setFields}
          fixedFirstColumn={true}
          maxWidthFirstColumn="150px"
          exportCSV={true}
          handleExport={handleExport}
          columnsToFilter={columnsToFilter}
          config={reportTipsConfig}
          data={reportTipsData}
          alignLastColumnRight={false}
        />
      </Box>
    </>
  );
}
