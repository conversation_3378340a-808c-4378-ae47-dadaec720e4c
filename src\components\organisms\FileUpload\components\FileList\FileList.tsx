import React, { useCallback, useMemo } from 'react';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { PrivateFileContext, UploadedFile } from '../../types/fileUpload';
import { FileItem } from './FileItem';

/**
 * File list component for displaying uploaded files
 */
interface FileListProps {
  files: UploadedFile[];
  onRemoveFile: (index: number) => void;
  onRemoveFileByIdentifier: (file: UploadedFile) => void;
  onReorderFiles?: (fromIndex: number, toIndex: number) => void;
  onFilePreview?: (file: UploadedFile) => void;
  disabled?: boolean;
  readOnly?: boolean;
  multiple?: boolean;
  variant?: 'default' | 'compact';
  privateFileContext?: PrivateFileContext;
  showSummary?: boolean;
  fileType?: 'images' | 'videos' | 'public' | 'private';
}

export const FileList: React.FC<FileListProps> = ({
  files,
  onRemoveFile,
  onRemoveFileByIdentifier,
  onReorderFiles,
  onFilePreview,
  disabled = false,
  readOnly = false,
  multiple = false,
  variant = 'default',
  privateFileContext,
  showSummary = true,
  fileType,
}) => {
  const { t } = useTranslation();

  // Memoize stable IDs for the files to prevent flicker during reordering
  const stableIds = useMemo(
    () =>
      files.map((file, index) => {
        // Use a stable identifier based on file content, not index
        // For permanent files, use filename; for temp files, use URL if available
        const isTemporary = file.x === true;
        const baseId = isTemporary
          ? file.url || `${file.f}.${file.e}` || `file-${index}`
          : `${file.f}.${file.e}`; // For permanent files, always use filename

        // Don't include index to keep IDs stable during reordering
        // Add file type and temp status for uniqueness
        const uniqueSuffix = `-${file.t}${file.x ? '-temp' : '-perm'}`;
        return `${isTemporary ? 'temp' : 'perm'}-${baseId}${uniqueSuffix}`;
      }),
    [
      files
        .map(
          f => `${f.x ? 'temp' : 'perm'}-${f.f}.${f.e}-${f.url || ''}-${f.t}`
        )
        .join(','),
    ] // Content-based dependency
  );

  // Set up sensors for @dnd-kit - optimized for mobile
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200, // Longer delay to avoid conflicts with scrolling
        tolerance: 5, // Small tolerance for more precise touch
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end for reordering
  const handleDragEnd = useCallback(
    (event: any) => {
      const { active, over } = event;

      if (active.id !== over?.id && onReorderFiles) {
        // Find the indices by matching the stable IDs
        const oldIndex = stableIds.findIndex(id => id === active.id);
        const newIndex = stableIds.findIndex(id => id === over.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          onReorderFiles(oldIndex, newIndex);
        }
      }
    },
    [onReorderFiles, stableIds]
  );

  if (files.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: disabled || readOnly ? 0 : 2 }}>
      {/* Show summary only if enabled and not in disabled/readOnly mode */}
      {showSummary && !disabled && !readOnly && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {multiple
              ? t('fileUpload.filesUploaded', '{{count}} file(s) uploaded:', {
                  count: files.length,
                })
              : t('fileUpload.fileUploaded', 'Uploaded file:')}
          </Typography>
        </Box>
      )}

      {multiple && onReorderFiles ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={disabled || readOnly ? () => {} : handleDragEnd}
        >
          <SortableContext items={stableIds}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
              }}
            >
              {files.map((file, index) => (
                <FileItem
                  key={stableIds[index]}
                  file={file}
                  index={index}
                  onRemove={onRemoveFile}
                  onRemoveByIdentifier={onRemoveFileByIdentifier}
                  onPreview={onFilePreview}
                  disabled={disabled}
                  readOnly={readOnly}
                  variant={variant}
                  sortable={true}
                  sortableId={stableIds[index]}
                  fileType={fileType}
                />
              ))}
            </Box>
          </SortableContext>
        </DndContext>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: variant === 'compact' ? 'row' : 'column',
            gap: 1,
            flexWrap: variant === 'compact' ? 'wrap' : 'nowrap',
          }}
        >
          {files.map((file, index) => (
            <FileItem
              key={stableIds[index]}
              file={file}
              index={index}
              onRemove={onRemoveFile}
              onRemoveByIdentifier={onRemoveFileByIdentifier}
              onPreview={onFilePreview}
              disabled={disabled}
              readOnly={readOnly}
              variant={variant}
              sortable={false}
              fileType={fileType}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};
