import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
} from '@mui/icons-material';
import {
  Box,
  Chip,
  IconButton,
  Paper,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useDisplayFileName } from '../../hooks/useRealFileName';
import { PrivateFileContext, UploadedFile } from '../../types/fileUpload';
import { shouldUsePlaceholder } from '../../utils/filePlaceholders';
import {
  fetchAndCacheFileSizeInfo,
  formatFileSize,
  getFileSizes,
  hasAnyFileSizeInfo,
  hasFileSizeInfo,
} from '../../utils/fileSizeUtils';
import { canPreviewFile } from '../../utils/previewUtils';
import { FilePreviewInline } from '../FilePreviewInline';
import { PlaceholderThumbnail } from '../PlaceholderThumbnail';

/**
 * Individual file item component
 */
interface FileItemProps {
  file: UploadedFile;
  index: number;
  onRemove: (index: number) => void;
  onRemoveByIdentifier: (file: UploadedFile) => void;
  onPreview?: (file: UploadedFile) => void;
  disabled?: boolean;
  readOnly?: boolean;
  variant?: 'default' | 'compact';
  privateFileContext?: PrivateFileContext;
  sortable?: boolean;
  sortableId?: string;
  uploading?: boolean;
  fileType?: 'images' | 'videos' | 'public' | 'private';
}

const FileItemComponent: React.FC<FileItemProps> = ({
  file,
  index,
  onRemove,
  onRemoveByIdentifier,
  onPreview,
  disabled = false,
  readOnly = false,
  variant = 'default',
  privateFileContext,
  sortable = false,
  sortableId,
  uploading = false,
  fileType,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  // State for dynamically loaded file sizes
  const [dynamicSizes, setDynamicSizes] = useState<{
    original: number | null;
    variants: number | null;
  } | null>(null);
  const [sizesLoading, setSizesLoading] = useState(false);

  // Get display filename (with real name resolution for permanent files)
  const displayFileName = useDisplayFileName(file);

  // Effect to load file sizes for permanent files without immediate size data
  useEffect(() => {
    const loadFileSizes = async () => {
      // Only load for permanent files that don't have immediate size info
      if (hasFileSizeInfo(file)) {
        return; // Already has immediate data
      }

      // Check if we have cached data or need to fetch
      if (!hasAnyFileSizeInfo(file)) {
        setSizesLoading(true);
        try {
          const sizeInfo = await fetchAndCacheFileSizeInfo(
            file,
            privateFileContext
          );
          if (sizeInfo) {
            setDynamicSizes(sizeInfo);
          }
        } catch (error) {
          console.warn('Failed to load file sizes:', error);
        } finally {
          setSizesLoading(false);
        }
      }
    };

    loadFileSizes();
  }, [file.f, file.e, file.t, privateFileContext, file]);

  // Get combined file sizes (immediate or dynamically loaded)
  const getFinalSizes = useCallback(() => {
    // First try immediate sizes
    const immediateSizes = getFileSizes(file);
    if (immediateSizes.original !== null) {
      return immediateSizes;
    }

    // Fall back to dynamically loaded sizes
    return dynamicSizes || { original: null, variants: null };
  }, [file, dynamicSizes]);

  // Sortable functionality
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id:
      sortableId || `${file.x ? 'temp' : 'perm'}-${file.f}.${file.e}-${index}`,
    disabled: !sortable || disabled || readOnly,
    strategy: undefined, // Use default strategy
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Get file display name with proper truncation logic for compact variant
  const getDisplayName = useCallback(() => {
    if (variant === 'compact') {
      const extension = file.e ? `.${file.e}` : '';

      // Function to truncate from middle if needed (for dropdown context)
      const getTruncatedLabel = (name: string, ext: string) => {
        const fullName = `${name}${ext}`;
        // If the full name is short enough, return as is
        if (fullName.length <= 25) {
          // Slightly longer for dropdown
          return fullName;
        }

        // For longer names, show first part + ... + extension
        const maxStartLength = Math.max(10, 25 - ext.length - 3); // 3 for "..."
        const truncatedName =
          name.length > maxStartLength
            ? `${name.substring(0, maxStartLength)}...${ext}`
            : fullName;

        return truncatedName;
      };

      return getTruncatedLabel(displayFileName, extension);
    }

    // Default variant truncation
    const lastDotIndex = displayFileName.lastIndexOf('.');
    if (lastDotIndex === -1) return displayFileName;

    const name = displayFileName.substring(0, lastDotIndex);
    const extension = displayFileName.substring(lastDotIndex);

    // Truncate long filenames
    const maxLength = 40;
    if (name.length > maxLength) {
      return `${name.substring(0, maxLength - 3)}...${extension}`;
    }

    return displayFileName;
  }, [file, variant, displayFileName]);

  // Handle chip click for preview
  const handleChipClick = useCallback(() => {
    if (canPreviewFile(file) && !disabled && onPreview) {
      onPreview(file);
    }
  }, [file, disabled, onPreview]);

  // Handle remove file using file identifier (more robust than index)
  const handleRemove = useCallback(() => {
    if (!disabled && !readOnly) {
      onRemoveByIdentifier(file);
    }
  }, [disabled, readOnly, onRemoveByIdentifier, file]);

  // Render compact variant as chip (matching old SortableFileChip)
  if (variant === 'compact') {
    const extension = file.e ? `.${file.e}` : '';
    const fullName = `${displayFileName}${extension}`;

    return (
      <Chip
        ref={setNodeRef}
        style={style}
        label={getDisplayName()}
        title={fullName} // Show full name on hover
        icon={
          sortable && !disabled && !readOnly ? (
            <Box
              {...attributes}
              {...listeners}
              data-dnd-kit-draggable-drag-handle="true"
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'grab',
                minWidth: '32px', // Larger touch target for mobile
                minHeight: '32px',
                touchAction: 'none', // Prevent default touch behaviors
                userSelect: 'none', // Prevent text selection during drag
                '&:active': {
                  cursor: 'grabbing',
                },
                // Mobile-specific optimizations
                '@media (pointer: coarse)': {
                  minWidth: '44px', // Larger touch target for mobile
                  minHeight: '44px',
                },
              }}
              onPointerDown={e => {
                // Prevent event bubbling to chip click handler
                e.stopPropagation();
              }}
              onTouchStart={e => {
                // Prevent event bubbling to chip click handler
                e.stopPropagation();
              }}
            >
              <DragIndicatorIcon fontSize="small" />
            </Box>
          ) : undefined
        }
        onDelete={!disabled && !readOnly ? handleRemove : undefined}
        onClick={
          canPreviewFile(file) && !disabled
            ? e => {
                // Only handle click if not touching the drag handle
                const target = e.target as HTMLElement;
                const dragHandle = target.closest(
                  '[data-dnd-kit-draggable-drag-handle]'
                );
                if (!dragHandle) {
                  handleChipClick();
                }
              }
            : undefined
        }
        size="small"
        variant="outlined"
        sx={{
          opacity: isDragging ? 0.8 : 1,
          cursor: canPreviewFile(file) && !disabled ? 'pointer' : 'default',
          backgroundColor: 'background.paper',
          border: '1px solid',
          borderColor: 'divider',
          width: '100%',
          justifyContent: 'flex-start',
          height: '36px',
          minHeight: '36px', // Ensure minimum touch target
          position: 'relative',
          '& .MuiChip-icon': {
            cursor: 'grab',
            marginLeft: '4px',
            '&:active': {
              cursor: 'grabbing',
            },
          },
          '& .MuiChip-label': {
            fontWeight: 500,
            fontSize: '0.875rem',
            textAlign: 'left',
            paddingLeft: '8px',
            paddingRight: '8px',
            flex: 1,
            justifyContent: 'flex-start',
          },
          '&:hover': !disabled
            ? {
                backgroundColor: 'action.hover',
                borderColor: 'primary.main',
              }
            : {},
          // Mobile-specific touch optimizations
          '@media (pointer: coarse)': {
            height: '40px',
            minHeight: '40px',
            '&:hover': {
              backgroundColor: 'background.paper', // Remove hover on touch devices
              borderColor: 'divider',
            },
          },
        }}
      />
    );
  }

  // Render default variant as Paper (matching old MuiFileUploadInput design)
  return (
    <Paper
      ref={setNodeRef}
      style={style}
      variant="outlined"
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        opacity: isDragging ? 0.8 : 1,
        backgroundColor:
          disabled || readOnly
            ? theme.palette.action.hover
            : isDragging
              ? theme.palette.action.hover
              : 'transparent',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          flex: 1,
          minWidth: 0,
        }}
      >
        {/* Drag handle for sortable items */}
        {sortable && !disabled && !readOnly && (
          <Box
            {...attributes}
            {...listeners}
            data-dnd-kit-draggable-drag-handle="true"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'grab',
              color: theme.palette.text.secondary,
              minWidth: '36px', // Reduced from 44px
              minHeight: '36px', // Reduced from 44px
              width: '36px', // Fixed width
              height: '36px', // Fixed height
              borderRadius: 1,
              mr: 1,
              touchAction: 'none', // Prevent all default touch behaviors for reliable dragging
              userSelect: 'none',
              WebkitUserSelect: 'none', // Safari specific
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
              '&:active': {
                cursor: 'grabbing',
                backgroundColor: theme.palette.action.selected,
              },
              // Mobile-specific optimizations - keep functionality but reduce visual prominence
              '@media (pointer: coarse)': {
                minWidth: '44px', // Keep larger touch target on mobile
                minHeight: '44px',
                width: '44px',
                height: '44px',
              },
            }}
          >
            <DragIndicatorIcon
              fontSize="large" // Changed from "medium" to "large"
              sx={{
                pointerEvents: 'none',
                '@media (pointer: coarse)': {
                  fontSize: '1.3rem', // Slightly larger for mobile
                },
              }}
            />
          </Box>
        )}

        {/* Smart thumbnail - real thumbnail or placeholder */}
        {shouldUsePlaceholder(file) ? (
          <PlaceholderThumbnail
            key={`placeholder-${file.f}.${file.e}`}
            file={file}
            size={50}
            onClick={() => !disabled && onPreview?.(file)}
            disabled={disabled}
            fileType={fileType}
          />
        ) : (
          <FilePreviewInline
            key={`preview-${file.f}.${file.e}-${file.url || 'no-url'}`}
            files={file}
            size={50}
            onClick={() => !disabled && onPreview?.(file)}
            disabled={disabled}
            fileType={fileType}
          />
        )}

        {/* File info */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {displayFileName}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {file.e.toUpperCase()} file
            {uploading &&
              file.x &&
              ` • ${t('fileUpload.uploading', 'Uploading...')}`}
          </Typography>
        </Box>

        {/* File size info - Desktop only */}
        {(() => {
          const sizes = getFinalSizes();
          const hasAnySize = sizes.original !== null || dynamicSizes !== null;

          return (
            hasAnySize && (
              <Box
                sx={{
                  display: { xs: 'none', md: 'flex' }, // Hidden on mobile, visible on desktop
                  flexDirection: 'column',
                  alignItems: 'flex-end',
                  minWidth: 0,
                  mr: 1,
                }}
              >
                {sizesLoading ? (
                  <Typography
                    variant="caption"
                    color="textSecondary"
                    sx={{
                      fontSize: '0.75rem',
                      lineHeight: 1.2,
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {t('fileUpload.loading', 'loading...')}
                  </Typography>
                ) : (
                  <>
                    {sizes.original !== null && (
                      <Typography
                        variant="caption"
                        color="textSecondary"
                        sx={{
                          fontSize: '0.75rem',
                          lineHeight: 1.2,
                          whiteSpace: 'nowrap',
                        }}
                      >
                        original: {formatFileSize(sizes.original)}
                      </Typography>
                    )}
                    {sizes.variants && sizes.variants > 0 && (
                      <Typography
                        variant="caption"
                        color="textSecondary"
                        sx={{
                          fontSize: '0.75rem',
                          lineHeight: 1.2,
                          whiteSpace: 'nowrap',
                        }}
                      >
                        generated: {formatFileSize(sizes.variants)}
                      </Typography>
                    )}
                  </>
                )}
              </Box>
            )
          );
        })()}
      </Box>

      {/* Delete button - Hidden in disabled and readOnly modes */}
      {!disabled && !readOnly && (
        <IconButton
          size="small"
          onClick={handleRemove}
          disabled={uploading}
          color="error"
          sx={{ ml: 1 }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      )}
    </Paper>
  );
};

// Optimize with React.memo to prevent unnecessary re-renders during drag operations
export const FileItem = React.memo(
  FileItemComponent,
  (prevProps, nextProps) => {
    // Only re-render if essential file content or UI props change
    // Don't check index since it changes during reordering but doesn't affect rendering
    const prevSizes = getFileSizes(prevProps.file);
    const nextSizes = getFileSizes(nextProps.file);

    return (
      prevProps.file.url === nextProps.file.url &&
      prevProps.file.f === nextProps.file.f &&
      prevProps.file.e === nextProps.file.e &&
      prevProps.file.x === nextProps.file.x && // Include temporary/permanent status
      prevProps.file.t === nextProps.file.t && // Include file type
      prevSizes.original === nextSizes.original &&
      prevSizes.variants === nextSizes.variants &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.readOnly === nextProps.readOnly &&
      prevProps.variant === nextProps.variant &&
      prevProps.sortable === nextProps.sortable &&
      prevProps.uploading === nextProps.uploading &&
      prevProps.sortableId === nextProps.sortableId
    );
  }
);
