import type {
    AutocompleteArrayInputProps,
    AutocompleteInputProps,
    BooleanInputProps,
    CheckboxGroupInputProps,
    DateInputProps,
    DateTimeInputProps,
    FileInputProps,
    ImageInputProps,
    NullableBooleanInputProps,
    NumberInputProps,
    PasswordInputProps,
    RadioButtonGroupInputProps,
    SelectArrayInputProps,
    SelectInputProps,
    TextArrayInputProps,
    TextInputProps,
    TimeInputProps,
} from 'react-admin';
import type { RaFileUploadComponentProps } from '../../../organisms/FileUpload/types';

// UI mode types
export type UIMode = 'original' | 'custom';

// Sanitization preset types
export type SanitizePreset = 'singleLine' | 'multiLine' | 'identifier' | false;

// Rounded corners types
export type RoundedCorners = 'top' | 'bottom' | 'both' | 'none';

// Base props shared by all CustomInput variants
export interface CustomInputBaseProps {
    /**
     * The field name in the record. Optional when used inside ReferenceInput/ReferenceArrayInput
     * as the source is inherited from the parent component.
     */
    source?: string;
    ui?: UIMode;
    sanitize?: SanitizePreset;
    placeholder?: string;
    uppercase?: boolean;
    /** Locale for date/time/number formatting (e.g., 'en-US', 'fr-FR', 'ro-RO') */
    locale?: string;
    /** Apply rounded corners to the input container (custom UI mode only) */
    roundedCorners?: RoundedCorners;
    /**
     * Options prop - behavior varies by input type:
     * - BooleanInput: MUI Switch props (e.g., { checkedIcon: <Icon /> })
     * - TextArrayInput: Array of autocomplete suggestions (string[])
     * - SelectArrayInput: MUI Select props (e.g., { defaultOpen: true })
     * - Other types: May be ignored or passed to underlying MUI component
     */
    options?: Record<string, any> | string[];
}

// Discriminated union for all 20 supported input types
export type CustomInputProps =
    | (CustomInputBaseProps & { type: 'text' } & Omit<TextInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'number' } & Omit<
        NumberInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'date' } & Omit<DateInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'datetime' } & Omit<
        DateTimeInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'time' } & Omit<TimeInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'boolean' } & Omit<
        BooleanInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'nullableBoolean' } & Omit<
        NullableBooleanInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'select' } & Omit<
        SelectInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'autocomplete' } & Omit<
        AutocompleteInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'radio' } & Omit<
        RadioButtonGroupInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'checkboxGroup' } & Omit<
        CheckboxGroupInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'file' } & Omit<FileInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'image' } & Omit<ImageInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'password' } & Omit<
        PasswordInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'email' } & Omit<TextInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'url' } & Omit<TextInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'textArray' } & Omit<
        TextArrayInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'selectArray' } & Omit<
        SelectArrayInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'autocompleteArray' } & Omit<
        AutocompleteArrayInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'phone' } & Omit<TextInputProps, 'source'>)
    | (CustomInputBaseProps & { type: 'code'; digits?: number } & Omit<
        TextInputProps,
        'source'
    >)
    | (CustomInputBaseProps & { type: 'fileUpload' } & Omit<
        RaFileUploadComponentProps,
        'source'
    >);

// Helper type to extract props for specific input type
export type InputTypeProps<T extends CustomInputProps['type']> = Extract<
    CustomInputProps,
    { type: T }
>;
