import { useMemo, useState } from 'react';
import { Box, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import BreadCrumbs from '~/components/molecules/BreadCrumbs';
import ExtraDataCard from '~/components/molecules/ExtraDataCard';
import CustomTable from '~/components/organisms/CustomTable';
import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupReport } from '~/fake-provider/reports/groupReport';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import replaceNumberWithPercentage from '~/utils/replaceNumbersWithPercentage';
import FormattedFilters from './FormattedFilters';

type TableRow = {
  id: string;
  quantity: string;
  measureUnit: string;
  value: string | number;
  price: number;
  name: string;
  discountsValue: number;
  promotionsValue: number;
  promotionName: string;
  discountName: string;
  couponsValue: number;
  parentFields: string[];
  netValue: number;
  variant: string;
  items?: TableRow[];
  subItems?: TableRow[];
};

export default function ExtraDataItemsModal({
  extraData,
}: {
  extraData?: { [key: string]: any };
}) {
  const { t } = useTranslation();
  const { itemsData, totalItemsData } = useMemo(() => {
    if (!extraData?.rawData || !extraData?.filters)
      return { itemsData: [], totalItemsData: [] };
    const fields = [
      {
        field: 'id',
        operator: '==',
        value: extraData.rowData.id,
      },
      {
        field: 'groupId',
        operator: '==',
        value: extraData.rowData.groupId,
      },
      {
        field: 'measureUnit',
        operator: '==',
        value: extraData.rowData.measureUnit,
      },
      {
        field: 'prepStation',
        operator: '==',
        value: extraData.rowData.prepStation,
      },
    ];

    const filteredFields = fields.filter(item => {
      return item.value;
    });

    const rawDataFiltered = filterReport(
      extraData.reportType,
      extraData.rawData,
      extraData.composedFilters,
      //@ts-ignore
      filteredFields
    );

    const groupedTableData = groupReport(
      extraData?.reportType,
      rawDataFiltered,
      [],
      [
        'groupId',
        'id',
        'measureUnit',
        'variant',
        'prepStation',
        'prepStationId',
        'price',
        'vat',
        'discountName',
        'promotionName',
      ]
    );

    let itemsData = groupedTableData[0]?.report || [];

    itemsData = itemsData.filter(item => {
      const variant = item.variant?.toLowerCase() || '';
      return (!/\bno\b/.test(variant) && !/\ballergy\b/.test(variant) && item.price);
    });

    const totalItemsDataObject = mergeAndSumObjects(itemsData);
    totalItemsDataObject.name = extraData.rowData.name;
    totalItemsDataObject.price = '';
    totalItemsDataObject.variant = '';
    totalItemsDataObject.promotionName = '';
    totalItemsDataObject.discountName = '';
    const totalItemsData = [totalItemsDataObject, ...itemsData];

    if (!itemsData) return { itemsData: [], totalItemsData: [] };
    return { itemsData, totalItemsData };
  }, [extraData]);

  const extraDataItemSalesConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'id',
      label: t('reportsPage.itemName'),
      textAlign: 'start',
      render: (row: TableRow, rowIndex: number) => {
        const isCombo = row.variant !== '@none';
        return (
          <>
            <Typography
              fontWeight={rowIndex === 0 ? 500 : 300}
              sx={{
                py: 0.5,
                fontWeight: 500,
                fontSize: { xs: '11px', sm: '16px' },
                whiteSpace: 'wrap',
                minWidth: { xs: '100px', sm: '320px' },
              }}
              py={rowIndex === 0 ? 0 : 3}
            >
              {rowIndex === 0
                ? capitalize(row.name?.toLowerCase())
                : isCombo
                  ? 'Combo'
                  : `Regular`}{' '}
              {rowIndex !== 0 && (
                <span>@{formatAndDivideNumber(row.price)}</span>
              )}
            </Typography>
            {rowIndex !== 0 && isCombo && (
              <Typography
                fontWeight={rowIndex === 0 ? 500 : 300}
                sx={{
                  py: 0.2,
                  fontSize: { xs: '11px', sm: '16px' },
                  whiteSpace: 'wrap',
                  minWidth: { xs: '100px', sm: '320px' },
                }}
                py={rowIndex === 0 ? 0 : 3}
              >
                {capitalize(row.variant)}
              </Typography>
            )}
            {rowIndex !== 0 &&
              row?.discountName &&
              row?.discountName !== '@none' && (
                <Typography sx={{ fontSize: '12px', color: 'gray' }}>
                  Discount: {replaceNumberWithPercentage(row?.discountName)}
                </Typography>
              )}
            {rowIndex !== 0 &&
              row?.promotionName &&
              row?.promotionName !== '@none' && (
                <Typography sx={{ fontSize: '12px', color: 'gray' }}>
                  Discount: {replaceNumberWithPercentage(row?.promotionName)}
                </Typography>
              )}
            {rowIndex === 0 && extraData?.rowData?.parentFields && (
              <BreadCrumbs parentFields={extraData.rowData.parentFields} />
            )}
          </>
        );
      },
    },
    {
      id: 'quantity',
      label: t('reportsPage.itemsSold'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {formatNumberIntl(Number(row.quantity) / 1000)}
          </Typography>
        );
      },
    },
    // {
    //   id: 'price',
    //   label: 'Price',
    //   textAlign: 'end',
    //   render: (row: TableRow, rowIndex: number) => {
    //     return (
    //       <Typography
    //         sx={{ fontSize: { xs: '11px', sm: '16px' } }}
    //         fontWeight={rowIndex === 0 ? 500 : 300}
    //       >
    //         {formatAndDivideNumber(Number(row.price))}
    //       </Typography>
    //     );
    //   },
    // },
    {
      id: 'measureUnit',
      label: t('reportsPage.unit'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row.measureUnit && row.measureUnit === 'undefined'
              ? ''
              : row.measureUnit}
          </Typography>
        );
      },
    },
    {
      id: 'value',
      label: t('reportsPage.grossSales'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {formatAndDivideNumber(Number(row.value))}
          </Typography>
        );
      },
    },
    {
      id: 'discountsValue',
      label: t('reportsPage.discounts'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row?.discountsValue
              ? '- ' + formatAndDivideNumber(row?.discountsValue)
              : ''}
          </Typography>
        );
      },
    },
    {
      id: 'couponsValue',
      label: t('reportsPage.coupons'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row?.couponsValue
              ? '- ' + formatAndDivideNumber(row?.couponsValue)
              : ''}
          </Typography>
        );
      },
    },
    {
      id: 'promotionsValue',
      label: t('reportsPage.promotions'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </Typography>
        );
      },
    },
    {
      id: 'netValue',
      label: t('shared.netSales'),
      textAlign: 'end',
      render: (row: TableRow, rowIndex: number) => {
        return (
          <Typography
            sx={{ fontSize: { xs: '11px', sm: '16px' } }}
            fontWeight={rowIndex === 0 ? 500 : 300}
          >
            {formatAndDivideNumber(row?.netValue)}
          </Typography>
        );
      },
    },
  ];

  const soldIn = itemsData.length;

  const [fields, setFields] = useState<FieldOption[]>(
    extraDataItemSalesConfig.map(col => ({
      value: col.id as string,
      isChecked: true,
    }))
  );

  const calculateCardValues = (itemsData: TableRow[]) => {
    const totalSold = itemsData.reduce(
      (sum, item) => sum + Number(item.quantity || 0),
      0
    );

    const regularCount = itemsData
      .filter(item => item.variant === '@none')
      .reduce((sum, item) => sum + Number(item.quantity), 0);
    const comboCount = itemsData
      .filter(item => item.variant !== '@none')
      .reduce((sum, item) => sum + Number(item.quantity), 0);
    const discountCount = itemsData
      .filter(item => Number(item.discountsValue) > 0)
      .reduce((sum, item) => sum + Number(item.quantity), 0);
    const couponCount = itemsData
      .filter(item => Number(item.couponsValue) > 0)
      .reduce((sum, item) => sum + Number(item.quantity), 0);
    const promotionCount = itemsData
      .filter(item => Number(item.promotionsValue) > 0)
      .reduce((sum, item) => sum + Number(item.quantity), 0);

    return {
      totalSold: formatNumberIntl(totalSold, true),
      regular: regularCount / 1000,
      combos: comboCount / 1000,
      withDiscounts: discountCount / 1000,
      withCoupons: couponCount / 1000,
      withPromotions: promotionCount / 1000,
    };
  };

  const cardValues = calculateCardValues(itemsData);

  const cardsConfig: { title: string; value: string | number }[] = [
    { title: 'Total Sold', value: cardValues.totalSold },
    { title: 'Regular', value: cardValues.regular },
    { title: 'Combos', value: cardValues.combos },
    { title: 'w/ Discounts', value: cardValues.withDiscounts },
    { title: 'w/ Promotions', value: cardValues.withPromotions },
  ];

  return (
    <Box sx={{ width: '100%', maxWidth: '1500px', mx: 'auto' }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          maxWidth: '1000px',
          width: '100%',
          mx: 'auto',
          gap: 1,
        }}
      >
        <Typography sx={{ fontSize: 18, fontWeight: 300, textAlign: 'center' }}>
          {t('reportsPage.all')}
          <span style={{ fontWeight: 700 }}>
            {' '}
            {capitalize(extraData?.rowData.name?.toLowerCase()) ||
              extraData?.rowData.id}{' '}
          </span>{' '}
          {t('reportsPage.salesFrom')}
        </Typography>
        <FormattedFilters formattedFilters={extraData?.formattedFilters} />
        <Typography
          sx={{ fontSize: 17, mt: 0.5, textAlign: 'center', fontWeight: 300 }}
        >
          {t('reportsPage.thisItemWasSoldIn')}{' '}
          <span style={{ fontWeight: 700 }}>{soldIn}</span>{' '}
          {soldIn === 1
            ? t('reportsPage.combination')
            : t('reportsPage.combinations')}
          .
        </Typography>
      </Box>
      <Box
        sx={{
          width: '100%',
          display: { xs: 'grid', sm: 'flex' },
          gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'unset' },
          gap: 8,
          mt: 4,
          alignItems: 'center',
          justifyContent: { xs: 'center', sm: 'center' },
          pb: 2,
          borderBottom: '2px solid #F2F2F2',
          '@media print': {
            borderBottom: '2px solid black',
          },
        }}
      >
        {cardsConfig.map(
          (item: { title: string; value: string | number }, index: number) => {
            const isLastItem =
              extraData?.attributes &&
              index === extraData.attributes.length - 1;

            return (
              <Box
                key={index}
                sx={{
                  gridColumn: isLastItem ? '1 / -1' : 'auto',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ExtraDataCard item={item} />
              </Box>
            );
          }
        )}
      </Box>
      <CustomTable
        fields={fields}
        setFields={setFields}
        extraDataFirstRow={true}
        filter={false}
        searchBar={false}
        fixedFirstColumn={true}
        config={extraDataItemSalesConfig}
        data={totalItemsData || []}
        alignLastColumnRight={false}
        fixLastRow={true}
      />
    </Box>
  );
}
