import React from 'react';
import {
  Archive,
  AudioFile,
  Code,
  Description,
  Image,
  InsertDriveFile,
  Lock,
  PictureAsPdf,
  PlayCircleOutline,
  Public,
  Slideshow,
  TableChart,
  TextSnippet,
  VideoFile,
} from '@mui/icons-material';
import { Box, Typography, useTheme } from '@mui/material';

import { UploadedFile } from '../types/fileUpload';
import {
  getFilePlaceholder,
  shouldUsePlaceholder,
} from '../utils/filePlaceholders';

interface PlaceholderThumbnailProps {
  file: UploadedFile | File;
  size?: number;
  width?: number;
  height?: number;
  onClick?: () => void;
  disabled?: boolean;
  borderRadius?: number | string;
  sx?: any;
  fileType?: 'images' | 'videos' | 'public' | 'private';
}

// Icon mapping for Material-UI icons
const IconMap = {
  Image,
  VideoFile,
  AudioFile,
  PictureAsPdf,
  Description,
  TableChart,
  Slideshow,
  TextSnippet,
  Archive,
  Code,
  InsertDriveFile,
  PlayCircleOutline,
  Public,
  Lock,
} as const;

// FileType-based icon mapping
const FileTypeIcons = {
  images: {
    icon: Image,
    color: '#4caf50', // Green
    bgColor: '#e8f5e8',
    label: 'Images',
  },
  videos: {
    icon: PlayCircleOutline,
    color: '#f44336', // Red
    bgColor: '#ffebee',
    label: 'Videos',
  },
  public: {
    icon: Public,
    color: '#2196f3', // Blue
    bgColor: '#e3f2fd',
    label: 'Public',
  },
  private: {
    icon: Lock,
    color: '#ff9800', // Orange
    bgColor: '#fff3e0',
    label: 'Private',
  },
} as const;

/**
 * Simple, clean placeholder thumbnail component using Material-UI icons
 */
export const PlaceholderThumbnail: React.FC<PlaceholderThumbnailProps> = ({
  file,
  size = 50,
  width,
  height,
  onClick,
  disabled = false,
  borderRadius: customBorderRadius,
  sx,
  fileType,
}) => {
  const theme = useTheme();

  // Calculate final dimensions - width/height props override size
  const finalWidth = width || size;
  const finalHeight = height || size;

  // If fileType is provided, use it for the icon
  if (fileType && FileTypeIcons[fileType]) {
    const typeConfig = FileTypeIcons[fileType];
    const IconComponent = typeConfig.icon;

    // Calculate sizes based on thumbnail dimensions
    const containerSize = Math.min(finalWidth, finalHeight);
    const iconSize = containerSize * 0.5; // Icon takes 50% of container for fileType icons
    const borderRadius = customBorderRadius ?? 1.5; // Use consistent theme borderRadius

    return (
      <Box
        onClick={!disabled && onClick ? onClick : undefined}
        sx={{
          width: finalWidth,
          height: finalHeight,
          minWidth: finalWidth,
          minHeight: finalHeight,
          maxWidth: finalWidth,
          maxHeight: finalHeight,
          flexShrink: 0,
          flexGrow: 0,
          margin: 'auto',
          borderRadius,
          backgroundColor: typeConfig.bgColor,
          border: `1px solid ${theme.palette.divider}`,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: !disabled && onClick ? 'pointer' : 'default',
          transition: 'opacity 0.2s ease',
          '&:hover':
            !disabled && onClick
              ? {
                  opacity: 0.8,
                }
              : {},
          ...sx,
        }}
      >
        {/* Icon */}
        <IconComponent
          sx={{
            fontSize: iconSize,
            color: typeConfig.color,
          }}
        />
      </Box>
    );
  }

  // Determine file type and extension for fallback behavior
  const fileTypeMime = 'type' in file ? file.type : '';
  const extension =
    'e' in file ? file.e : (file as File).name.split('.').pop() || '';

  const placeholder = getFilePlaceholder(fileTypeMime, extension);
  const IconComponent =
    IconMap[placeholder.iconName as keyof typeof IconMap] || InsertDriveFile;

  // Calculate sizes based on thumbnail dimensions
  const containerSize = Math.min(finalWidth, finalHeight);
  const iconSize = containerSize * 0.45; // Icon takes 45% of container
  const borderRadius = customBorderRadius ?? 1.5; // Use consistent theme borderRadius

  return (
    <Box
      onClick={!disabled && onClick ? onClick : undefined}
      sx={{
        width: finalWidth,
        height: finalHeight,
        minWidth: finalWidth,
        minHeight: finalHeight,
        maxWidth: finalWidth,
        maxHeight: finalHeight,
        flexShrink: 0,
        flexGrow: 0,
        margin: 'auto',
        borderRadius,
        backgroundColor: placeholder.bgColor,
        border: `1px solid ${theme.palette.divider}`,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: !disabled && onClick ? 'pointer' : 'default',
        transition: 'opacity 0.2s ease',
        '&:hover':
          !disabled && onClick
            ? {
                opacity: 0.8,
              }
            : {},
        ...sx,
      }}
    >
      {/* Icon */}
      <IconComponent
        sx={{
          fontSize: iconSize,
          color: placeholder.color,
        }}
      />
    </Box>
  );
};

export default PlaceholderThumbnail;
