/**
 * Entity Registry - Resource-Agnostic Tree Walking & Entity Detection
 *
 * This module provides functions to walk any nested data structure and
 * identify entities (objects with an `id` field) without knowing the schema.
 */

import { cloneDeep, isEqual } from 'lodash';

import { EntityRecord, NoIdArrayRecord } from './types';

/**
 * Check if a value is a plain object (not array, null, Date, etc.)
 */
export function isPlainObject(value: any): value is Record<string, any> {
  if (value === null || value === undefined) return false;
  if (typeof value !== 'object') return false;
  if (Array.isArray(value)) return false;
  if (value instanceof Date) return false;
  if (value instanceof RegExp) return false;
  if (value instanceof Map) return false;
  if (value instanceof Set) return false;
  // Check for plain object prototype
  const proto = Object.getPrototypeOf(value);
  return proto === Object.prototype || proto === null;
}

/**
 * Compute a signature for a no-ID array to detect structural changes.
 * Uses a deterministic JSON stringification.
 */
export function computeArraySignature(arr: any[]): string {
  try {
    return JSON.stringify(arr, (key, value) => {
      if (isPlainObject(value)) {
        // Sort keys for deterministic output
        const sorted: Record<string, any> = {};
        Object.keys(value)
          .sort()
          .forEach(k => {
            sorted[k] = value[k];
          });
        return sorted;
      }
      return value;
    });
  } catch {
    // Fallback for circular references or other issues
    return `array_length_${arr.length}_${Date.now()}`;
  }
}

/**
 * Normalize a path for pattern matching (pages[0].items[2] -> pages.0.items.2).
 */
export function normalizePathForMatch(path: string): string {
  return path.replace(/\[(\d+)\]/g, '.$1');
}

/**
 * Check if a path matches a wildcard pattern (e.g., pages.*.items).
 */
export function pathMatchesPattern(path: string, pattern: string): boolean {
  if (!pattern) return false;
  const normalizedPath = normalizePathForMatch(path);
  const normalizedPattern = normalizePathForMatch(pattern);
  const regexPattern = normalizedPattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '[^.]+');
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(normalizedPath);
}

/**
 * Check if a path matches any of the provided wildcard patterns.
 */
export function pathMatchesAnyPattern(
  path: string,
  patterns: string[] = []
): boolean {
  if (!patterns.length) return false;
  return patterns.some(pattern => pathMatchesPattern(path, pattern));
}

/**
 * Extract an entity's own fields, excluding nested entities and entity arrays.
 * This gives us the "trackable" fields for conflict detection.
 *
 * @param obj - The entity object
 * @param idField - Default ID field
 * @param excludeFields - Fields to exclude from tracking
 * @param arrayIdFields - Path-specific ID fields for arrays
 * @param currentPath - Current path in the data tree (for resolving arrayIdFields)
 */
export function extractEntityOwnFields(
  obj: Record<string, any>,
  idField: string = 'id',
  excludeFields: string[] = [],
  arrayIdFields: Record<string, string> = {},
  referenceArrayPaths: string[] = [],
  currentPath: string = ''
): Record<string, any> {
  const ownFields: Record<string, any> = {};
  const defaultExcludes = [
    '_u',
    '_c',
    '_ub',
    '_ur',
    '_index',
    ...excludeFields,
  ];

  for (const [key, value] of Object.entries(obj)) {
    // Skip the ID field itself
    if (key === idField) continue;

    // Skip metadata/excluded fields
    if (defaultExcludes.includes(key)) continue;
    if (key.startsWith('_')) continue;

    if (Array.isArray(value)) {
      // Determine the effective ID field for this array
      const arrayPath = currentPath ? `${currentPath}.${key}` : key;

      // Reference arrays should be treated as own fields (not entity arrays)
      if (pathMatchesAnyPattern(arrayPath, referenceArrayPaths)) {
        ownFields[key] = value;
        continue;
      }

      const effectiveIdField = getEffectiveIdField(
        arrayPath,
        idField,
        arrayIdFields
      );

      // Check if it's an entity array (first item has the effective id field)
      if (
        value.length > 0 &&
        isPlainObject(value[0]) &&
        value[0][effectiveIdField] !== undefined
      ) {
        // Entity array - don't include in own fields (tracked separately)
        continue;
      }
      // Non-entity array (primitives or objects without id) - include
      ownFields[key] = value;
    } else if (isPlainObject(value)) {
      if (value[idField] !== undefined) {
        // Nested entity - don't include (tracked separately)
        continue;
      }
      // Plain nested object - include
      ownFields[key] = value;
    } else {
      // Primitive value
      ownFields[key] = value;
    }
  }

  return ownFields;
}

/**
 * Get the effective ID field for a given array path.
 * Checks arrayIdFields for path-specific overrides, falls back to default idField.
 * Supports wildcard patterns like 'pages.*' to match 'pages[0]', 'pages.0', etc.
 */
export function getEffectiveIdField(
  arrayPath: string,
  defaultIdField: string,
  arrayIdFields: Record<string, string> = {}
): string {
  // Check for exact path match first
  if (arrayIdFields[arrayPath]) {
    return arrayIdFields[arrayPath];
  }

  // Normalize path for comparison: pages[0] -> pages.0, pages[0].items[3] -> pages.0.items.3
  const normalizedPath = arrayPath.replace(/\[(\d+)\]/g, '.$1');

  // Check for pattern match with wildcard support
  for (const [pattern, field] of Object.entries(arrayIdFields)) {
    // Convert wildcard pattern to regex: 'pages.*' -> 'pages\.[^.]+', 'pages.*.items' -> 'pages\.[^.]+\.items'
    const regexPattern = pattern.replace(/\./g, '\\.').replace(/\*/g, '[^.]+');
    const regex = new RegExp(`^${regexPattern}$`);

    if (regex.test(normalizedPath)) {
      return field;
    }

    // Also check if pattern ends with the array name (legacy behavior)
    if (arrayPath === pattern || arrayPath.endsWith(`.${pattern}`)) {
      return field;
    }
  }

  return defaultIdField;
}

/**
 * Build complete entity registry from data tree.
 * Walks the entire structure and identifies all entities and no-ID arrays.
 *
 * @param data - The data to process
 * @param idField - Default field that identifies entities (default: 'id')
 * @param excludeFields - Fields to exclude from tracking
 * @param arrayIdFields - Path-specific ID fields for arrays (e.g., { 'items': 'number' })
 */
export function buildEntityRegistry(
  data: any,
  idField: string = 'id',
  excludeFields: string[] = [],
  arrayIdFields: Record<string, string> = {},
  referenceArrayPaths: string[] = []
): {
  entities: Map<string, EntityRecord>;
  noIdArrays: Map<string, NoIdArrayRecord>;
} {
  const entities = new Map<string, EntityRecord>();
  const noIdArrays = new Map<string, NoIdArrayRecord>();

  function processNode(
    obj: any,
    path: string,
    parentEntityId: string,
    effectiveIdField: string
  ): void {
    // Handle arrays
    if (Array.isArray(obj)) {
      if (obj.length === 0) return;

      // Check first item to determine array type
      const firstItem = obj[0];

      // Arrays of arrays (e.g., pages[pageIndex][itemIndex])
      if (Array.isArray(firstItem)) {
        obj.forEach((item, index) => {
          const itemPath = path ? `${path}[${index}]` : `[${index}]`;
          processNode(item, itemPath, parentEntityId, effectiveIdField);
        });
        return;
      }

      // Get effective ID field for this array path
      const arrayItemIdField = getEffectiveIdField(
        path,
        idField,
        arrayIdFields
      );

      // Reference arrays should NOT register entity occurrences
      if (pathMatchesAnyPattern(path, referenceArrayPaths)) {
        return;
      }

      const isEntityArray =
        isPlainObject(firstItem) && firstItem[arrayItemIdField] !== undefined;

      if (isEntityArray) {
        // Entity array - process each entity with the array-specific ID field
        obj.forEach((item, index) => {
          const itemPath = path ? `${path}[${index}]` : `[${index}]`;
          processNode(item, itemPath, parentEntityId, arrayItemIdField);
        });
      } else if (isPlainObject(firstItem)) {
        // Array of plain objects (no id) - track as no-ID array for structural conflict detection
        noIdArrays.set(path, {
          path,
          parentEntityId,
          signatureAtFormOpen: computeArraySignature(obj),
        });

        // Still recurse into array items to find any nested entities
        obj.forEach((item, index) => {
          if (isPlainObject(item)) {
            const itemPath = path ? `${path}[${index}]` : `[${index}]`;
            // Check if this item itself is an entity (using default idField)
            if (item[idField] !== undefined) {
              processNode(item, itemPath, parentEntityId, idField);
            } else {
              // Plain object - recurse to find nested entities
              processObjectProperties(item, itemPath, parentEntityId, idField);
            }
          }
        });
      }
      // Primitive arrays (numbers, strings, etc.) are NOT tracked as noIdArrays
      // They are already tracked as own fields of their parent entity
      return;
    }

    // Handle objects
    if (!isPlainObject(obj)) return;

    const isEntity = obj[effectiveIdField] !== undefined;
    const entityId = isEntity ? String(obj[effectiveIdField]) : null;

    if (isEntity && entityId) {
      // Create occurrence key for uniqueness
      // For non-standard ID fields, prefix the entity ID to avoid collisions
      const entityKey =
        effectiveIdField !== idField
          ? `${effectiveIdField}:${entityId}`
          : entityId;
      const occurrenceKey = `${parentEntityId}:${path}`;

      // Register or update entity
      if (!entities.has(entityKey)) {
        entities.set(entityKey, {
          id: entityKey,
          occurrences: new Map(),
          ownFieldsAtFormOpen: extractEntityOwnFields(
            obj,
            effectiveIdField,
            excludeFields,
            arrayIdFields,
            referenceArrayPaths,
            path
          ),
        });
      }

      // Add this occurrence
      entities.get(entityKey)!.occurrences.set(occurrenceKey, {
        path,
        parentEntityId,
      });
    }

    // Recurse into nested properties
    const newParentEntityId = entityId
      ? effectiveIdField !== idField
        ? `${effectiveIdField}:${entityId}`
        : entityId
      : parentEntityId;
    processObjectProperties(obj, path, newParentEntityId, idField);
  }

  function processObjectProperties(
    obj: Record<string, any>,
    basePath: string,
    parentEntityId: string,
    currentIdField: string
  ): void {
    for (const [key, value] of Object.entries(obj)) {
      if (key === currentIdField) continue; // Skip id field
      if (key.startsWith('_')) continue; // Skip metadata fields

      const newPath = basePath ? `${basePath}.${key}` : key;
      processNode(value, newPath, parentEntityId, idField);
    }
  }

  // Start processing from root with 'ROOT' as parent
  processNode(data, '', 'ROOT', idField);

  return { entities, noIdArrays };
}

/**
 * Find an entity by ID anywhere in the data tree.
 * Returns the first occurrence found.
 *
 * @param data - Data tree to search
 * @param targetId - Entity ID to find. Can be:
 *   - Simple ID like "123" (searches using idField)
 *   - Prefixed ID like "number:5" (extracts field and value from prefix)
 * @param idField - Default field to use for identification
 * @param arrayIdFields - Path-specific ID fields (optional, used with prefixed IDs)
 */
export function findEntityById(
  data: any,
  targetId: string,
  idField: string = 'id',
  arrayIdFields: Record<string, string> = {}
): any | null {
  if (!data || typeof data !== 'object') return null;

  // Check if targetId has a custom field prefix (e.g., "number:5")
  let searchField = idField;
  let searchValue = targetId;

  if (targetId.includes(':') && !targetId.startsWith('http')) {
    const colonIndex = targetId.indexOf(':');
    const potentialField = targetId.substring(0, colonIndex);
    // Only treat as prefix if the field name is in arrayIdFields values or matches idField
    const isValidPrefix =
      potentialField === idField ||
      Object.values(arrayIdFields).includes(potentialField);
    if (isValidPrefix) {
      searchField = potentialField;
      searchValue = targetId.substring(colonIndex + 1);
    }
  }

  function search(obj: any, path: string): any | null {
    if (!obj || typeof obj !== 'object') return null;

    if (Array.isArray(obj)) {
      // Determine effective ID field for this array
      const effectiveField = getEffectiveIdField(path, idField, arrayIdFields);

      for (let i = 0; i < obj.length; i++) {
        const item = obj[i];
        const itemPath = path ? `${path}[${i}]` : `[${i}]`;

        // Check if this item matches
        if (
          isPlainObject(item) &&
          String(item[effectiveField]) === searchValue &&
          effectiveField === searchField
        ) {
          return item;
        }

        // Recurse into item
        const found = search(item, itemPath);
        if (found) return found;
      }
      return null;
    }

    // Check if this object is the target
    if (
      isPlainObject(obj) &&
      obj[searchField] !== undefined &&
      String(obj[searchField]) === searchValue
    ) {
      return obj;
    }

    // Recurse into properties
    if (isPlainObject(obj)) {
      for (const [key, value] of Object.entries(obj)) {
        if (key.startsWith('_')) continue;
        const newPath = path ? `${path}.${key}` : key;
        const found = search(value, newPath);
        if (found) return found;
      }
    }

    return null;
  }

  return search(data, '');
}

/**
 * Find all paths where an entity with given ID appears.
 */
export function findAllEntityPaths(
  data: any,
  targetId: string,
  idField: string = 'id',
  currentPath: string = ''
): string[] {
  const paths: string[] = [];

  if (!data || typeof data !== 'object') return paths;

  if (Array.isArray(data)) {
    data.forEach((item, index) => {
      const newPath = currentPath ? `${currentPath}[${index}]` : `[${index}]`;
      paths.push(...findAllEntityPaths(item, targetId, idField, newPath));
    });
    return paths;
  }

  // Check if this is the target entity
  if (
    isPlainObject(data) &&
    data[idField] !== undefined &&
    String(data[idField]) === targetId
  ) {
    paths.push(currentPath);
  }

  // Recurse into properties
  if (isPlainObject(data)) {
    for (const [key, value] of Object.entries(data)) {
      const newPath = currentPath ? `${currentPath}.${key}` : key;
      paths.push(...findAllEntityPaths(value, targetId, idField, newPath));
    }
  }

  return paths;
}

/**
 * Get value at a path in an object.
 * Supports paths like "pages[0].items[2].price"
 */
export function getValueAtPath(obj: any, path: string): any {
  if (!path) return obj;

  const segments = parsePath(path);
  let current = obj;

  for (const segment of segments) {
    if (current === null || current === undefined) return undefined;

    if (segment.type === 'index') {
      current = current[segment.value];
    } else {
      current = current[segment.value];
    }
  }

  return current;
}

/**
 * Set value at a path in an object (mutates the object).
 * Supports paths like "pages[0].items[2].price"
 */
export function setValueAtPath(obj: any, path: string, value: any): void {
  if (!path) return;

  const segments = parsePath(path);
  let current = obj;

  for (let i = 0; i < segments.length - 1; i++) {
    const segment = segments[i];
    if (segment.type === 'index') {
      current = current[segment.value];
    } else {
      current = current[segment.value];
    }
  }

  const lastSegment = segments[segments.length - 1];
  if (lastSegment.type === 'index') {
    current[lastSegment.value] = value;
  } else {
    current[lastSegment.value] = value;
  }
}

interface PathSegment {
  type: 'property' | 'index';
  value: string | number;
}

/**
 * Parse a path string into segments.
 * "pages[0].items[2].price" → [
 *   { type: 'property', value: 'pages' },
 *   { type: 'index', value: 0 },
 *   { type: 'property', value: 'items' },
 *   { type: 'index', value: 2 },
 *   { type: 'property', value: 'price' }
 * ]
 */
export function parsePath(path: string): PathSegment[] {
  const segments: PathSegment[] = [];
  let current = '';
  let i = 0;

  while (i < path.length) {
    const char = path[i];

    if (char === '.') {
      if (current) {
        segments.push({ type: 'property', value: current });
        current = '';
      }
      i++;
    } else if (char === '[') {
      if (current) {
        segments.push({ type: 'property', value: current });
        current = '';
      }
      // Find closing bracket
      const closeIdx = path.indexOf(']', i);
      if (closeIdx === -1) break;
      const indexStr = path.slice(i + 1, closeIdx);
      segments.push({ type: 'index', value: parseInt(indexStr, 10) });
      i = closeIdx + 1;
    } else {
      current += char;
      i++;
    }
  }

  if (current) {
    segments.push({ type: 'property', value: current });
  }

  return segments;
}

/**
 * Convert path segments back to path string.
 */
export function segmentsToPath(segments: PathSegment[]): string {
  let path = '';
  for (const segment of segments) {
    if (segment.type === 'index') {
      path += `[${segment.value}]`;
    } else {
      path += path ? `.${segment.value}` : segment.value;
    }
  }
  return path;
}

/**
 * Deep clone an object.
 */
export function deepClone<T>(obj: T): T {
  return cloneDeep(obj);
}

/**
 * Deep equality check.
 */
export function deepEqual(a: any, b: any): boolean {
  return isEqual(a, b);
}
