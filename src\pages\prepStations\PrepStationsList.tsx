import MenuBookIcon from '@mui/icons-material/MenuBook';
import { Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  CreateButton,
  DataTable,
  List,
  ReferenceArrayField,
  TopToolbar,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomSearchInput from '~/components/atoms/inputs/CustomSearchInput';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import { useTheme } from '../../contexts';

export const PrepStationsList = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const filters = [
    <CustomSearchInput
      placeholder={t('dashboard.headerSearchPlaceholder')}
      key="search-input"
      source="q"
      alwaysOn
    />,
  ];

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  const PrepStationsListActions = () => (
    <TopToolbar>
      {/* <FilterButton/> */}

      <CreateButton
        variant="contained"
        label={t('prepStations.createPrepStation')}
        {...(isXSmall ? {} : { icon: <></> })}
      />
    </TopToolbar>
  );

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('sm'));

  const CustomEmpty = () => {
    const { t } = useTranslation('');
    return (
      <div style={{ textAlign: 'center' }}>
        <img src="/assets/icons/prepStation-icon.svg" alt="emptyPrepStation" />
        <Typography variant="h3" sx={{ mt: 2 }}>
          {t('prepStations.noPrepStationsYet')}
        </Typography>
        <Typography
          variant="body2"
          my={3}
          maxWidth="550px"
          mx="auto"
          color="text.secondary"
        >
          {t('prepStations.noPrepStationsYetDescription')}
        </Typography>
        <CreateButton
          variant="contained"
          label={t('prepStations.createPrepStation')}
        />
      </div>
    );
  };

  return (
    <List
      pagination={false}
      perPage={Number.MAX_SAFE_INTEGER}
      sort={resourcesInfo[RESOURCES.PREP_STATIONS].defaultSort}
      resource={RESOURCES.PREP_STATIONS}
      component="div"
      filters={filters}
      exporter={false}
      empty={<CustomEmpty />}
      actions={<PrepStationsListActions />}
      sx={{
        '& .RaFilterFormInput-spacer': {
          display: { xs: 'none', md: 'block' },
        },
      }}
    >
      {!isSmall ? (
        <DataTable
          // rowStyle={postRowStyle}
          sx={{
            marginTop: '10px',
            '& .RaDataTable-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
          }}
          bulkActionButtons={false}
        >
          <DataTable.Col source="name" label={t('shared.name')} />
          <DataTable.Col label={t('prepStations.tags')}>
            <ReferenceArrayField
              reference={RESOURCES.HOSPITALITY_CATEGORIES}
              filter={{ _d: false }}
              source="groups"
            />
          </DataTable.Col>

          <DataTable.Col label={t('prepStations.actions')} align="right">
            <ActionsField
              hasEdit
              hasDelete
              deleteMutationMode="pessimistic"
              deleteMutationOptions={{}}
            />
          </DataTable.Col>
        </DataTable>
      ) : (
        <MobileGrid>
          <MobileCard>
            <ReferenceArrayField
              reference={RESOURCES.HOSPITALITY_CATEGORIES}
              filter={{ _d: false }}
              source="groups"
            />
          </MobileCard>
        </MobileGrid>
      )}
      <ListLiveUpdate />
    </List>
  );
};
