import AirplayIcon from '@mui/icons-material/Airplay';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import LoyaltyIcon from '@mui/icons-material/Loyalty';
import { Box, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function RecommendedProducts() {
  const { t } = useTranslation();

  const data = [
    {
      label: t('dashboard.selioRetail'),
      icon: <AirplayIcon fontSize="small" />,
      href: 'https://www.google.co.uk/',
    },
    {
      label: t('dashboard.selioRestaurants'),
      icon: <AirplayIcon fontSize="small" />,
      href: 'https://www.google.co.uk/',
    },
    {
      label: t('dashboard.giftCards'),
      icon: <LoyaltyIcon fontSize="small" />,
      href: 'https://www.google.co.uk/',
    },
    {
      label: t('dashboard.paymentLinks'),
      icon: <LoyaltyIcon fontSize="small" />,
      href: 'https://www.google.co.uk/',
    },
    {
      label: t('dashboard.loyalty'),
      icon: <LoyaltyIcon fontSize="small" />,
      href: 'https://www.google.co.uk/',
    },
    {
      label: t('dashboard.payroll'),
      icon: <FileCopyIcon fontSize="small" />,
      href: 'https://www.google.co.uk/',
    },
  ];

  return (
    <>
      <Typography variant="h2" mb={3}>
        {t('dashboard.recomendedProducts')}
      </Typography>
      <Grid container spacing={2}>
        {data.map((el: any) => (
          <Grid
            key={el.label}
            size={{
              xs: 12,
              md: 6,
              xl: 4
            }}>
            <Box
              px={1}
              py={2}
              sx={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                borderBottom: '1px solid',
                borderColor: 'custom.gray200',
                borderRadius: '6px',
                transition: 'background-color 0.2s',
                bgcolor: 'background.default',
                '&:hover': {
                  bgcolor: 'primary.veryLight',
                },
                '&:hover>div': {
                  bgcolor: 'primary.light',
                },
              }}
            >
              <Box
                pt={1}
                px={1}
                sx={{
                  bgcolor: 'background.tinted',
                  borderRadius: '6px',
                  transition: 'background-color 0.2s',
                }}
              >
                {el.icon}
              </Box>
              <Typography fontWeight={500} ml={2}>
                {el.label}
              </Typography>
            </Box>
          </Grid>
        ))}
      </Grid>
    </>
  );
}
