import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';

import { useTheme } from '../../contexts';

export default function DraggableList({
  selectedItems,
  setSelectedItems,
  handleDeleteItem,
}: {
  selectedItems: any;
  setSelectedItems: any;
  handleDeleteItem: any;
}) {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const onDragEnd = (result: any) => {
    if (!result.destination) {
      return;
    }

    const reorderedItems = Array.from(selectedItems);
    const [removed] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, removed);

    setSelectedItems(reorderedItems);
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="droppable">
        {provided => (
          <Box sx={{ width: '100%' }}>
            <Typography sx={{ fontSize: 15, fontWeight: 600, p: 1 }}>
              {t('menu.quickAccess')}
            </Typography>
            <Box
              sx={{
                width: '100%',
                height: '1px',
                backgroundColor: '#D9D9D9',
              }}
            />
            <Box
              ref={provided.innerRef}
              {...provided.droppableProps}
              sx={{
                width: '100%',
                mt: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  width: '100%',
                  flexDirection: 'column',
                  borderRadius: 2,
                  bgcolor:
                    theme.palette.mode === 'dark'
                      ? '#26262B'
                      : 'background.paper',
                  cursor: 'default',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    width: '100%',
                    px: 1.5,
                    gap: 2,
                    pt: 1.5,
                    mb: 1.5,
                    // opacity: 0.4,
                    alignItems: 'center',
                  }}
                >
                  <img
                    style={{
                      filter: theme.palette.mode === 'light' ? '' : 'invert(1)',
                    }}
                    src="/assets/menu-icons/home.svg"
                    alt="menu-icon-home"
                  />
                  <Typography
                    sx={{
                      fontSize: 14,
                      pt: 0.5,

                      fontWeight: 300,
                    }}
                  >
                    {t('menu.home')}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '1px',
                    backgroundColor:
                      theme.palette.mode === 'light' ? '#F2F2F2' : '#4A4A4E',
                  }}
                />
              </Box>
              {selectedItems.map((item: any, index: number) => (
                <Draggable
                  key={item.label}
                  draggableId={item.label}
                  index={index}
                >
                  {(provided, snapshot) => {
                    const child = (
                      <Box
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        key={index}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            width: '100%',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            borderRadius: 2,
                            px: 1.5,
                            pt: 1.5,
                            py: 1.3,
                            bgcolor:
                              theme.palette.mode === 'dark'
                                ? '#26262B'
                                : 'background.paper',
                            cursor: 'default',
                            boxShadow: snapshot.isDragging
                              ? '0 2px 8px rgba(0, 0, 0, 0.2)'
                              : '',
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              width: '100%',
                              gap: 2,
                              alignItems: 'center',
                            }}
                          >
                            <img
                              style={{
                                filter:
                                  theme.palette.mode === 'light'
                                    ? ''
                                    : 'invert(1)',
                              }}
                              src={item?.icon?.props?.src}
                              alt={item?.icon?.props?.alt}
                            />
                            <Typography
                              sx={{
                                fontSize: 14,
                                pt: 0.5,

                                fontWeight: 300,
                              }}
                            >
                              {t(`menu.${item.label}`)}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              gap: 1.3,
                              cursor: 'pointer',
                            }}
                          >
                            <DeleteOutlineIcon
                              onClick={() => handleDeleteItem(item.label)}
                              sx={{
                                '&:hover': {
                                  color: 'gray',
                                },
                              }}
                            />
                            <Box
                              {...provided.dragHandleProps}
                              sx={{
                                cursor: snapshot.isDragging
                                  ? 'grabbing'
                                  : 'grab',
                                '&:hover': {
                                  color: 'gray',
                                },
                              }}
                            >
                              <DragIndicatorIcon sx={{ mt: 0 }} />
                            </Box>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            width: '100%',
                            height: '1px',
                            backgroundColor:
                              theme.palette.mode === 'light'
                                ? '#F2F2F2'
                                : '#4A4A4E',
                          }}
                        />
                      </Box>
                    );

                    if (snapshot.isDragging) {
                      return <DraggablePortal>{child}</DraggablePortal>;
                    }

                    return child;
                  }}
                </Draggable>
              ))}
              {provided.placeholder}
            </Box>
          </Box>
        )}
      </Droppable>
    </DragDropContext>
  );
}

function DraggablePortal({ children }: { children: React.ReactNode }) {
  const portalElement = document.getElementById('draggable-portal');
  if (!portalElement) {
    return null;
  }
  return createPortal(children, portalElement);
}
