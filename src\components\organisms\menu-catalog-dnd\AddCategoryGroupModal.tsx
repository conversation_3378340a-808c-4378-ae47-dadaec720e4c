import { useState } from 'react';
import { <PERSON>ton, <PERSON>alog, Grid, Typography } from '@mui/material';
import { SimpleForm } from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { generateFirestoreId } from '~/utils/generateFirestoreId';
import { menuColors } from '../../../data/menu-colors';
import MuiCustomInput from '../../atoms/inputs/MuiCustomInput';
import ColorSelectInputGroup from '../../molecules/input-groups/ColorSelectInputGroup';
import ModalHeader from '../../molecules/ModalHeader';
import { Coordinates } from './types';

const initialValue = {
  color: menuColors[0],
  displayName: '',
};

interface AddTileModalProps {
  path: string;
  data: any;
  recordType?: string;
  position?: Coordinates;
  onClose: () => void;
}
export default function AddCategoryGroupModal({
  path,
  data,
  recordType='pos',
  position,
  onClose,
}: AddTileModalProps) {
  const type = 'displayGroup';
  const { t } = useTranslation('');
  const { setValue } = useFormContext();
  const [state, setState] = useState(initialValue);
  const [checkingValidation, setCheckingValidation] = useState(false);
  console.log('recordType', recordType);
  const updateValue = (field: string, value: string) => {
    setCheckingValidation(false);
    setState(prev => {
      return { ...prev, ...{ [field]: value } };
    });
  };

  const createTile = () => {
    if (!state.displayName.trim()) {
      setCheckingValidation(true);
      return;
    }

    const id = generateFirestoreId();

    let tmp = [...data];
    tmp.push({
      ...state,
      type,
      ...{ items: [] },
      ...(path.includes('items') ? { position: { startX: 0, startY: 0, endX: 1, endY: 1 } } : { position }),
      id,
    });
    console.log(tmp);

    // if we are inside display group, we need to arrange items
    // first by type (displayGroup first) and then by display name
    if (path.includes('items')) {
      tmp.sort((a, b) => {
        const typeA = a.type ?? 'product';
        const typeB = b.type ?? 'product';

        // Display groups should come first
        if (typeA === 'displayGroup' && typeB !== 'displayGroup') return -1;
        if (typeA !== 'displayGroup' && typeB === 'displayGroup') return 1;

        // Then sort by display name
        return a.displayName.localeCompare(b.displayName);
      });

      // Remove position if we're inside display group (position exists on element
      // because we add it artificially to know where to display them in the table)
      tmp = tmp.map(item => {
        const { position, ...rest } = item;
        return rest;
      });
    }

    setValue(path, tmp);
    setState(initialValue);
    onClose();
  };

  return (
    <Dialog
      open
      onClose={onClose}
      fullWidth={true}
      maxWidth={recordType === 'pos' ? 'md' : 'sm'}
      transitionDuration={0}
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader handleClose={onClose} title={t('menu.addMenuGroup')}>
          <Button variant="contained" onClick={createTile}>
            {t('shared.create')}
          </Button>
        </ModalHeader>
        <Grid p={3} container spacing={3}>
          <Grid item xs={12} sm={recordType === 'pos' ? 7 : 12}>
            <MuiCustomInput
              fullWidth
              value={state.displayName}
              onChange={event => updateValue('displayName', event.target.value)}
              label={t('shared.name')}
              error={checkingValidation && !state.displayName.trim()}
              required
            />
            <Typography color="text" variant="caption">
              {t('menu.requiredField')}
            </Typography>
          </Grid>
          { !recordType || recordType === 'pos' && <Grid item xs={12} sm={recordType === 'pos' ? 5 : 12}>
            <ColorSelectInputGroup
              onChange={value => updateValue('color', value)}
              value={
                path.includes('items') && type !== 'displayGroup'
                  ? 'no-color'
                  : state.color
              }
              choices={menuColors}
              // we disable color picking if we are inside a display group
              disabled={path.includes('items') && type !== 'displayGroup'}
            />
          </Grid> }
          {/* <Grid item xs={12} sx={{ paddingTop: '5px !important' }}></Grid> */}
        </Grid>
      </SimpleForm>
    </Dialog>
  );
}
