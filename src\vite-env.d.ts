/// <reference types="vite/client" />

declare const __APP_VERSION__: string;

interface ImportMetaEnv {
  readonly VITE_NODE_ENV: string;
  readonly VITE_FIREBASE_API_KEY: string;
  readonly VITE_FIREBASE_AUTH_DOMAIN: string;
  readonly VITE_FIREBASE_DATABASE_URL: string;
  readonly VITE_FIREBASE_PROJECT_ID: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET: string;
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string;
  readonly VITE_FIREBASE_APP_ID: string;
  readonly VITE_FIREBASE_MEASUREMENT_ID: string;
  readonly VITE_FIREBASE_RTDB_INSTANCES: string;
  readonly VITE_USE_FIREBASE_EMULATORS: string;
  readonly VITE_FIREBASE_APP_CHECK_SITE_KEY: string;
  readonly VITE_ORDERNOW_URL_SECRET: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET_DEFAULT: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET_CDN: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET_PRIVATE: string;
  readonly VITE_MUI_PRO_KEY: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
