import {
  collection,
  doc,
  DocumentData,
  FirestoreError,
  onSnapshot,
  orderBy,
  query,
  Query,
  Timestamp,
  Unsubscribe,
  where,
} from 'firebase/firestore';
import { get } from 'lodash';
import {
  CreateParams,
  CreateResult,
  DataProvider,
  DeleteManyParams,
  DeleteManyResult,
  DeleteParams,
  DeleteResult,
  GetListParams,
  GetListResult,
  GetManyParams,
  GetManyReferenceParams,
  GetManyReferenceResult,
  GetManyResult,
  GetOneParams,
  GetOneResult,
  Identifier,
  RaRecord,
  UpdateManyParams,
  UpdateManyResult,
  UpdateParams,
  UpdateResult,
  withLifecycleCallbacks,
} from 'react-admin';

import { RESOURCES, ResourcesInfo, resourcesInfo } from '~/providers/resources';
import { db } from '../configs/firebaseConfig';
import { FirestoreDataProvider } from './firestoreDataProvider';
import { updateSellPointsOnCatalogsChange } from './helpers/updateSellPointsOnCatalogsChange';
import { updateTeamMembersOnPermissionsChange } from './helpers/updateTeamMembersOnPermissionsChange';
import { catalogFileUploadHandler } from './lifecycleCallbacks/catalogFileUploadHandler';
import { createGenericFileUploadHandlers } from './lifecycleCallbacks/genericFileUploadHandler';
import { LocalCacheDataProvider } from './localCacheDataProvider';
import cleanupActions from './utils/cleanupActions';
import { clearUndefinedFields } from './utils/clearUndefinedFields';
import { getFirestoreConverter } from './utils/firestoreConverters';
import { getResourceInfo } from './utils/getResourceInfo';
import { getResourcePath } from './utils/getResourcePath';
import isEqual from './utils/isEqual';

type SyncListenerMap = {
  [resource: string]: {
    unsubscribe?: Unsubscribe;
    type: 'collection' | 'embeddedMap' | 'embeddedArray' | 'list';
    path: string;
    field?: string;
    previousMapData?: { [key: string | number]: RaRecord };
  };
};

type ChangedIds = {
  [type in 'created' | 'updated' | 'deleted']: Identifier[];
};

interface RaRealtimeEventPayload {
  ids: Identifier[];
}

interface RaRealtimeEvent {
  type: 'created' | 'updated' | 'deleted';
  payload: RaRealtimeEventPayload;
}

interface Subscription {
  topic: string;
  subscriptionCallback: (event: RaRealtimeEvent) => void;
}

export interface HybridDataProvider extends FirestoreDataProvider {
  ensureSyncListener: (
    resource: string,
    resourceMetas?: { [key: string]: any }
  ) => Promise<void>;
  stopSyncListener: (resource: string) => void;
  cleanup: () => void;
  getAccountId: () => string;
  getActiveSyncListeners: () => SyncListenerMap;
  getSubscriptions: () => Subscription[];
  getLocalCacheProvider: () => LocalCacheDataProvider;
}

function getResourceFromTopic(topic: string): string {
  const match = topic.match(/^resource\/([^/]+)/);
  return match ? match[1] : topic;
}

function publishChanges(
  hybridProvider: HybridDataProvider,
  resource: string,
  changedIds: ChangedIds
) {
  const resourceTopic = `resource/${resource}`;
  if (changedIds.created.length > 0)
    hybridProvider.publish(resourceTopic, {
      type: 'created',
      payload: { ids: changedIds.created },
    });
  if (changedIds.updated.length > 0) {
    hybridProvider.publish(resourceTopic, {
      type: 'updated',
      payload: { ids: changedIds.updated },
    });
    changedIds.updated.forEach(id =>
      hybridProvider.publish(`${resourceTopic}/${id}`, {
        type: 'updated',
        payload: { ids: [id] },
      })
    );
  }
  if (changedIds.deleted.length > 0) {
    hybridProvider.publish(resourceTopic, {
      type: 'deleted',
      payload: { ids: changedIds.deleted },
    });
    changedIds.deleted.forEach(id =>
      hybridProvider.publish(`${resourceTopic}/${id}`, {
        type: 'deleted',
        payload: { ids: [id] },
      })
    );
  }
}

function registerListenerUnsubscribe(
  activeSyncListeners: SyncListenerMap,
  resource: string,
  resourcePath: string,
  unsubscribe: Unsubscribe
) {
  // Register the unsubscribe function for cleanup
  console.log(
    `HybridDP: Registered cleanup action for ${resource} (${resourcePath})`
  );
  cleanupActions.register(unsubscribe);
  activeSyncListeners[resource].unsubscribe = unsubscribe;
}

function registerActiveSyncListener(
  activeSyncListeners: SyncListenerMap,
  resource: string,
  resourcePath: string,
  resourceInfo: ReturnType<typeof getResourceInfo>,
  previousMapData: { [key: string | number]: RaRecord } | undefined = undefined
) {
  activeSyncListeners[resource] = {
    type: resourceInfo.type,
    path: resourcePath,
    field:
      resourceInfo.type === 'embeddedMap' ||
      resourceInfo.type === 'embeddedArray'
        ? resourceInfo.field
        : undefined,
    previousMapData:
      resourceInfo.type !== 'embeddedMap' &&
      resourceInfo.type !== 'embeddedArray'
        ? undefined
        : (previousMapData ?? {}),
  };
  // Register the listener BEFORE waiting for first sync
  console.log(
    `HybridDP: Registered sync listener for ${resource} (${resourcePath})`
  );
}

function stopSyncListener(
  activeSyncListeners: SyncListenerMap,
  resource: string
) {
  if (activeSyncListeners[resource]) {
    if (typeof activeSyncListeners[resource].unsubscribe === 'function')
      activeSyncListeners[resource].unsubscribe(); // Call unsubscribe from the stored object
    delete activeSyncListeners[resource]; // Remove from map
  }
}

async function createCollectionListener(
  hybridProvider: HybridDataProvider,
  resource: string,
  resourcePath: string,
  resourceMeta: { [key: string]: any } | undefined = undefined
): Promise<Unsubscribe> {
  // get the local cache provider
  const localCacheProvider = hybridProvider.getLocalCacheProvider();
  // get the active sync listeners
  const activeSyncListeners = hybridProvider.getActiveSyncListeners();
  // Get the last update timestamp from the cache if any
  let lastCachedTimestamp: Timestamp | null = null;
  try {
    if (typeof localCacheProvider.getLastUpdateTimestamp === 'function') {
      const lastCachedTimestampMillis =
        await localCacheProvider.getLastUpdateTimestamp(resource, resourceMeta);

      if (lastCachedTimestampMillis !== null) {
        lastCachedTimestamp = Timestamp.fromMillis(lastCachedTimestampMillis);
      }
    }
  } catch (cacheError: unknown) {
    console.warn(
      `HybridDP: Error fetching last update timestamp for ${resource}:`,
      cacheError
    );
    lastCachedTimestamp = null; // Sync all if cache read fails
  }
  console.log(`Resource ${resource} lastCachedTimestamp:`, lastCachedTimestamp);
  // create the collection reference
  const collRef = collection(db, resourcePath).withConverter(
    getFirestoreConverter(resource)
  );
  let q: Query<DocumentData> = query(collRef);
  // if the resource is accounts we filter by the account id because we only want to sync the selected account
  if (resource === RESOURCES.ACCOUNTS) {
    // get the account id from the hybrid provider
    const accountId = hybridProvider.getAccountId();
    // filter the collection by the account id
    q = query(q, where('id', '==', accountId));
  }
  // if there is a last cached timestamp we get only the documents that were updated after that timestamp
  if (lastCachedTimestamp) {
    q = query(q, where('_u', '>', lastCachedTimestamp));
  }
  // we order the documents by the updated timestamp in ascending order so we can detect the changes in the order
  q = query(q, orderBy('_u'));

  // we create a variable to store the unsubscribe function
  let unsubscribeListener: Unsubscribe;
  // we create a promise to indicate when the first sync is complete
  const firstSyncPromise = new Promise<void>(async (resolve, reject) => {
    // create the listener for the collection query created above
    unsubscribeListener = onSnapshot(
      q,
      { includeMetadataChanges: true },
      async querySnapshot => {
        try {
          console.log(
            `HybridDP: Firestore COLLECTION listener triggered for ${resource} with metadata`,
            querySnapshot.metadata
          );
          console.log(
            `HybridDP: Firestore COLLECTION listener triggered for ${resource} with changes`,
            querySnapshot.docChanges().length
          );
          if (querySnapshot.docChanges().length === 0) {
            // if there are no changes detected we resolve the promise
            // this is going to trigger when the listener is first created and there are no changes
            // so we resolve the promise to indicate that the first sync of this resource is complete
            console.log(
              `HybridDP: Firestore COLLECTION listener triggered for ${resource} but no changes detected`
            );
            resolve();
            return;
          } else {
            for (const doc of querySnapshot.docs) {
              console.log(
                `HybridDP: Firestore COLLECTION listener doc detected for ${resource}:`,
                doc.metadata,
                doc.data()
              );
            }
            for (const change of querySnapshot.docChanges()) {
              console.log(
                `HybridDP: Firestore COLLECTION listener change detected for ${resource}:`,
                change.doc.metadata,
                change.type,
                change.doc.data()
              );
            }
          }
          // if there are pending writes we ignore the snapshot
          // because we are only interested in the latest data from the server
          if (
            querySnapshot.metadata.fromCache ||
            querySnapshot.metadata.hasPendingWrites
          ) {
            console.log(
              `HybridDP: Firestore COLLECTION listener triggered for ${resource} but data is from cache or has pending writes`
            );
            return;
          }

          // we create a flag to indicate if the cache was updated
          let cacheUpdated = false;
          // we create a map of changed ids for each type of change
          const changedIds: ChangedIds = {
            created: [],
            updated: [],
            deleted: [],
          };
          // loop through all the changes detected by the listener
          for (const change of querySnapshot.docChanges()) {
            // each changed document is an entity
            const entityFromListener = {
              id: change.doc.id,
              ...change.doc.data(),
            } as RaRecord;
            // console.log(`HybridDP: Firestore COLLECTION listener change detected for ${resource}:`, change.type, dataForCache);
            try {
              // get the data from the cache for the entity id
              const entityFromCache = await localCacheProvider
                .getOne(resource, {
                  id: entityFromListener.id,
                  meta: resourceMeta,
                })
                .then(res => res.data)
                .catch(() => null);

              if (change.type === 'added' || change.type === 'modified') {
                console.log(
                  `HybridDP: Firestore COLLECTION listener CHANGE ${change.type.toUpperCase()} detected for ${resource}:`,
                  entityFromListener.id
                );
                // if the change is added or modified we check if the data in the cache is different from the data in the listener

                // get the timestamps from the cache and the listener
                const entityFromCacheTimestamp: number | null =
                  entityFromCache && typeof entityFromCache._u === 'number'
                    ? entityFromCache._u
                    : null;
                const entityFromListenerTimestamp: number | null =
                  entityFromListener &&
                  typeof entityFromListener._u === 'number'
                    ? entityFromListener._u
                    : null;
                // get the event ids from the cache and the listener
                const entityFromCacheEventId: number | null =
                  entityFromCache && typeof entityFromCache._e === 'number'
                    ? entityFromCache._e
                    : null;
                const entityFromListenerEventId: number | null =
                  entityFromListener &&
                  typeof entityFromListener._e === 'number'
                    ? entityFromListener._e
                    : null;

                try {
                  if (entityFromCache === null) {
                    // if the entity is not in the cache we create it
                    await localCacheProvider.create(resource, {
                      data: entityFromListener,
                      meta: resourceMeta,
                    });
                    // add the id to the created ids
                    changedIds.created.push(entityFromListener.id);
                    // set the cache updated flag to true
                    cacheUpdated = true;
                  } else if (
                    // enity is in the cache and
                    // new data has an updated timestamp and cache data has no updated timestamp OR
                    // if cache data has an updated timestamp that is older than the new data updated timestamp OR
                    // if cache data has a newer updated timestamp but the event ids of both match
                    // we update the cache - this way the cache data has the cloud data because the ServerTimestamp FieldValue
                    // is converted to a number timestamp first here in the listener
                    entityFromListenerTimestamp !== null ||
                    entityFromCacheTimestamp === null ||
                    entityFromListenerTimestamp !== entityFromCacheTimestamp
                  ) {
                    // update the cache
                    await localCacheProvider.update(resource, {
                      id: entityFromListener.id,
                      data: entityFromListener,
                      previousData: entityFromCache,
                      meta: resourceMeta,
                    });
                    // we need to analyze if the change comes from the same event if so it means
                    // that this client made the write that triggered the change and we do not need
                    // to publish the change to the UI
                    if (
                      entityFromListenerEventId === null ||
                      entityFromCacheEventId === null ||
                      entityFromListenerEventId !== entityFromCacheEventId
                    ) {
                      // add the id to the updated ids
                      changedIds.updated.push(entityFromListener.id);
                      // set the cache updated flag to true
                      cacheUpdated = true;
                    }
                  }
                } catch (error: unknown) {
                  console.warn(
                    `HybridDP: Unexpected error handling ${change.type} ${entityFromListener.id}:`,
                    error
                  );
                }
              } else if (change.type === 'removed') {
                console.log(
                  `HybridDP: Firestore COLLECTION listener CHANGE REMOVED detected for ${resource}:`,
                  change.doc.id
                );
                // if the change is removed we need to delete the entity from the cache
                // this is not the same thing as soft delete because the entity is actually deleted from the database
                try {
                  if (entityFromCache !== null) {
                    // if the entity is in the cache we delete it
                    // we use the entity from the cache as the previous data
                    await localCacheProvider.delete(resource, {
                      id: entityFromListener.id,
                      previousData: entityFromCache,
                      meta: resourceMeta,
                    });
                    // add the id to the deleted ids
                    changedIds.deleted.push(change.doc.id);
                    // set the cache updated flag to true
                    cacheUpdated = true;
                  }
                } catch (error: unknown) {
                  console.warn(
                    `HybridDP: Unexpected error handling removed ${entityFromListener.id}:`,
                    error
                  );
                }
              } else {
                console.warn(
                  `HybridDP: Unknown change type ${change.type} for ${resource}:`,
                  entityFromListener.id
                );
              }
            } catch (cacheUpdateError: unknown) {
              console.error(
                `HybridDP: [Cache Failure] Error during cache operation for doc ${entityFromListener.id} (Type: ${change.type}):`,
                cacheUpdateError
              );
              console.error(
                `HybridDP: Failed Data (after conversion attempt):`,
                entityFromListener
              );
            }
          }
          // we publish the changes to the cache
          if (cacheUpdated) {
            publishChanges(hybridProvider, resource, changedIds);
          }

          resolve();
        } catch (error) {
          reject(error);
        }
      },
      (error: FirestoreError) => {
        console.error(
          `HybridDP: Firestore COLLECTION listener error for ${resource}:`,
          error
        );
        hybridProvider.stopSyncListener(resource);
        reject(error);
      }
    );

    registerListenerUnsubscribe(
      activeSyncListeners,
      resource,
      resourcePath,
      unsubscribeListener
    );
  });
  console.log(
    `HybridDP: Firestore COLLECTION listener created for ${resource}`
  );
  // Now wait for the first sync to complete
  await firstSyncPromise;
  // return the unsubscribe function
  return unsubscribeListener!;
}

async function createEmbeddedMapListener(
  hybridProvider: HybridDataProvider,
  resource: string,
  resourcePath: string,
  resourceField: string,
  resourceMeta: { [key: string]: any } | undefined = undefined
): Promise<Unsubscribe> {
  // get the local cache provider
  const localCacheProvider = hybridProvider.getLocalCacheProvider();
  // get the active sync listeners
  const activeSyncListeners = hybridProvider.getActiveSyncListeners();
  // the listener is on the parent document
  const docRef = doc(db, resourcePath).withConverter(
    getFirestoreConverter(resource)
  );
  // we create a variable to store the unsubscribe function
  let unsubscribeListener: Unsubscribe;
  // we create a promise to indicate when the first sync is complete
  const firstSyncPromise = new Promise<void>(async (resolve, reject) => {
    unsubscribeListener = onSnapshot(
      docRef,
      { includeMetadataChanges: true },
      async docSnapshot => {
        try {
          console.log(
            `HybridDP: Firestore DOCUMENT listener triggered for ${resource} (embeddedMap) with metadata =`,
            docSnapshot.metadata
          );

          // if the data is from cache or there are pending writes we ignore the snapshot
          // because we are only interested in the latest data from the server
          if (
            docSnapshot.metadata.fromCache ||
            docSnapshot.metadata.hasPendingWrites
          ) {
            console.log(
              `HybridDP: Firestore DOCUMENT listener triggered for ${resource} (embeddedMap) but data is from cache or has pending writes`
            );
            return;
          }

          const listenerInfo = activeSyncListeners[resource];

          const docData = docSnapshot.data() || {};
          const currentArrayData = (get(docData, resourceField) ||
            []) as RaRecord[];

          let cacheUpdated = false;
          const changedIds: ChangedIds = {
            created: [],
            updated: [],
            deleted: [],
          };

          // Create a map of current items by ID for easier lookup
          const currentMapData: { [key: string]: RaRecord } = {};
          currentArrayData.map((item, index) => {
            const itemId = item.id !== undefined ? item.id : `${index + 1}`;
            currentMapData[itemId] = item;
          });

          // get the previous data from the listener info - when the listener is first created this will be undefined
          const previousMapData = listenerInfo.previousMapData ?? {};

          console.log(
            `HybridDP: Firestore DOCUMENT listener change detected for ${resource} (embeddedMap)`
          );
          console.log(`HybridDP: Previous Map Data:`, previousMapData);
          console.log(`HybridDP: Current Map Data:`, currentMapData);

          // get all the keys from the previous and current data - this are the entity ids from the previous and current data
          const allKeys = new Set([
            ...Object.keys(previousMapData),
            ...Object.keys(currentMapData),
          ]);
          // loop through all the entity ids
          for (const key of allKeys) {
            const entityFromListenerPreviousSnapshot = previousMapData[key]; // From listener state (previous snapshot)
            const entityFromListener = currentMapData[key]; // From current snapshot

            try {
              // get the data from the cache for the entity id
              const entityFromCache: RaRecord | null = await localCacheProvider
                .getOne(resource, { id: key, meta: resourceMeta })
                .then(res => res.data)
                .catch(() => null);

              // get the event ids from the cache and the listener
              const entityFromCacheEventId: number | null =
                entityFromCache && typeof entityFromCache._e === 'number'
                  ? entityFromCache._e
                  : null;
              const entityFromListenerEventId: number | null =
                entityFromListener && typeof entityFromListener._e === 'number'
                  ? entityFromListener._e
                  : null;

              if (
                entityFromListener !== undefined &&
                entityFromListener !== null
              ) {
                // if there is a current item from the current snapshot
                // and there is no previous item from the previous snapshot
                // we treat it as a created item
                // if there is a previous item from the previous snapshot
                // we treat it as an updated item
                if (entityFromCache === null) {
                  // if there is no cached item
                  // we create a new item in the cache
                  console.log(
                    `HybridDP Listener: Embedded item ${key} created in cache`
                  );
                  await localCacheProvider.create(resource, {
                    data: entityFromListener,
                    meta: resourceMeta,
                  });
                  // add the id to the created ids
                  changedIds.created.push(key);
                  // set the cache updated flag to true
                  cacheUpdated = true;
                } else if (!isEqual(entityFromListener, entityFromCache)) {
                  // check if entityFromListener is different from entityFromCache
                  console.log(
                    `HybridDP Listener: Embedded item ${key} updated in cache`
                  );
                  await localCacheProvider.update(resource, {
                    id: key,
                    data: entityFromListener,
                    previousData: entityFromCache,
                    meta: resourceMeta,
                  });
                  // we need to analyze if the change comes from the same event if so it means
                  // that this client made the write that triggered the change and we do not need
                  // to publish the change to the UI
                  if (
                    entityFromListenerEventId === null ||
                    entityFromCacheEventId === null ||
                    entityFromListenerEventId !== entityFromCacheEventId
                  ) {
                    // add the id to the updated ids
                    changedIds.updated.push(key);
                    // set the cache updated flag to true
                    cacheUpdated = true;
                  }
                }
              } else if (
                (entityFromListener === undefined ||
                  entityFromListener === null) &&
                entityFromListenerPreviousSnapshot !== undefined &&
                entityFromListenerPreviousSnapshot !== null
              ) {
                // if there is a previous item from the previous snapshot
                // and there is no current item from the current snapshot
                // we treat it as a deleted item
                if (entityFromCache !== null) {
                  // if there is a cached item
                  // we delete the item from the cache
                  await localCacheProvider.delete(resource, {
                    id: key,
                    previousData: entityFromCache,
                    meta: resourceMeta,
                  });
                  // add the id to the deleted ids
                  changedIds.deleted.push(key);
                  // set the cache updated flag to true
                  cacheUpdated = true;
                }
              }
            } catch (cacheUpdateError: unknown) {
              console.error(
                `HybridDP: [Cache Failure] Error during cache operation for embedded item ${key}:`,
                cacheUpdateError
              );
              console.error(
                `HybridDP: Failed Data:`,
                entityFromListener ?? entityFromListenerPreviousSnapshot
              );
            }
          }

          listenerInfo.previousMapData = currentMapData;

          if (cacheUpdated) {
            publishChanges(hybridProvider, resource, changedIds);
          }

          resolve();
        } catch (error) {
          reject(error);
        }
      },
      (error: FirestoreError) => {
        console.error(
          `HybridDP: Firestore DOCUMENT listener error for ${resource} (${resourcePath}):`,
          error
        );
        hybridProvider.stopSyncListener(resource);
        reject(error);
      }
    );

    registerListenerUnsubscribe(
      activeSyncListeners,
      resource,
      resourcePath,
      unsubscribeListener
    );
  });
  console.log(`HybridDP: Firestore DOCUMENT listener created for ${resource}`);
  // Now wait for the first sync to complete
  await firstSyncPromise;
  // return the unsubscribe function
  return unsubscribeListener!;
}

async function createEmbeddedArrayListener(
  hybridProvider: HybridDataProvider,
  resource: string,
  resourcePath: string,
  resourceField: string,
  resourceMeta: { [key: string]: any } | undefined = undefined
): Promise<Unsubscribe> {
  // get the local cache provider
  const localCacheProvider = hybridProvider.getLocalCacheProvider();
  // get the active sync listeners
  const activeSyncListeners = hybridProvider.getActiveSyncListeners();
  // the listener is on the parent document
  const docRef = doc(db, resourcePath).withConverter(
    getFirestoreConverter(resource)
  );
  // we create a variable to store the unsubscribe function
  let unsubscribeListener: Unsubscribe;
  // we create a promise to indicate when the first sync is complete
  const firstSyncPromise = new Promise<void>(async (resolve, reject) => {
    unsubscribeListener = onSnapshot(
      docRef,
      { includeMetadataChanges: true },
      async docSnapshot => {
        try {
          console.log(
            `HybridDP: Firestore DOCUMENT listener triggered for ${resource} (embeddedArray) with metadata =`,
            docSnapshot.metadata
          );

          // if the data is from cache or there are pending writes we ignore the snapshot
          // because we are only interested in the latest data from the server
          if (
            docSnapshot.metadata.fromCache ||
            docSnapshot.metadata.hasPendingWrites
          ) {
            return;
          }

          const listenerInfo = activeSyncListeners[resource];

          const docData = docSnapshot.data() || {};
          const currentArrayData = (get(docData, resourceField) ||
            []) as RaRecord[];

          let cacheUpdated = false;
          const changedIds: ChangedIds = {
            created: [],
            updated: [],
            deleted: [],
          };

          // Create a map of current items by ID for easier lookup
          const currentMapData: { [key: string]: RaRecord } = {};
          currentArrayData.map((item, index) => {
            const itemId = item.id !== undefined ? item.id : `${index + 1}`;
            currentMapData[itemId] = item;
          });

          // get the previous data from the listener info - when the listener is first created this will be undefined
          const previousMapData = listenerInfo.previousMapData ?? {};

          console.log(
            `HybridDP: Firestore DOCUMENT listener change detected for ${resource} (embeddedArray)`
          );
          console.log(`HybridDP: Previous Map Data:`, previousMapData);
          console.log(`HybridDP: Current Map Data:`, currentMapData);

          const allKeys = new Set([
            ...Object.keys(previousMapData),
            ...Object.keys(currentMapData),
          ]);

          for (const key of allKeys) {
            const entityFromListenerPreviousSnapshot = previousMapData[key]; // From listener state (previous snapshot)
            const entityFromListener = currentMapData[key]; // From current snapshot

            try {
              const entityFromCache: RaRecord | null = await localCacheProvider
                .getOne(resource, { id: key, meta: resourceMeta })
                .then(res => res.data)
                .catch(() => null);

              // get the event ids from the cache and the listener
              const entityFromCacheEventId: number | null =
                entityFromCache && typeof entityFromCache._e === 'number'
                  ? entityFromCache._e
                  : null;
              const entityFromListenerEventId: number | null =
                entityFromListener && typeof entityFromListener._e === 'number'
                  ? entityFromListener._e
                  : null;

              if (
                entityFromListener !== undefined &&
                entityFromListener !== null
              ) {
                if (entityFromCache === null) {
                  await localCacheProvider.create(resource, {
                    data: entityFromListener,
                    meta: resourceMeta,
                  });
                  changedIds.created.push(key);
                  cacheUpdated = true;
                } else if (!isEqual(entityFromListener, entityFromCache)) {
                  // check if entityFromListener is different from entityFromCache
                  await localCacheProvider.update(resource, {
                    id: key,
                    data: entityFromListener,
                    previousData: entityFromCache,
                    meta: resourceMeta,
                  });
                  if (
                    entityFromListenerEventId === null ||
                    entityFromCacheEventId === null ||
                    entityFromListenerEventId !== entityFromCacheEventId
                  ) {
                    // add the id to the updated ids
                    changedIds.updated.push(key);
                    // set the cache updated flag to true
                    cacheUpdated = true;
                  }
                }
              } else if (
                (entityFromListener === undefined ||
                  entityFromListener === null) &&
                entityFromListenerPreviousSnapshot !== undefined &&
                entityFromListenerPreviousSnapshot !== null
              ) {
                if (entityFromCache !== null) {
                  // Only delete if it actually exists in cache
                  await localCacheProvider.delete(resource, {
                    id: key,
                    previousData: entityFromCache,
                    meta: resourceMeta,
                  }); // Use cachedItem as previousData
                  changedIds.deleted.push(key);
                  cacheUpdated = true;
                }
              }
            } catch (cacheUpdateError: unknown) {
              console.error(
                `HybridDP: [Cache Failure] Error during cache operation for embedded item ${key}:`,
                cacheUpdateError
              );
              console.error(
                `HybridDP: Failed Data:`,
                entityFromListener ?? entityFromListenerPreviousSnapshot
              );
            }
          }

          listenerInfo.previousMapData = currentMapData;

          if (cacheUpdated) {
            publishChanges(hybridProvider, resource, changedIds);
          }

          resolve();
        } catch (error) {
          reject(error);
        }
      },
      error => {
        console.error(
          `HybridDP: Firestore DOCUMENT listener error for ${resource}:`,
          error
        );
        hybridProvider.stopSyncListener(resource);
        reject(error);
      }
    );

    registerListenerUnsubscribe(
      activeSyncListeners,
      resource,
      resourcePath,
      unsubscribeListener
    );
  });
  console.log(
    `HybridDP: Firestore DOCUMENT listener created for ${resource} (embeddedArray) at ${resourcePath}`
  );
  // Now wait for the first sync to complete
  await firstSyncPromise;
  // return the unsubscribe function
  return unsubscribeListener!;
}

const getBasicHybridDataProvider = (
  accountId: string,
  firestoreProvider: FirestoreDataProvider,
  localCacheProvider: LocalCacheDataProvider
): HybridDataProvider => {
  console.log('HybridDataProvider initilizing...');
  let activeSyncListeners: SyncListenerMap = {};
  let subscriptions: Subscription[] = [];

  // Helper function to fetch previous data from cache
  const fetchPreviousDataFromCache = async (
    resource: string,
    resourceInfo: ReturnType<typeof getResourceInfo>,
    resourceMeta: { [key: string]: any } | undefined
  ): Promise<{ [key: string]: RaRecord } | undefined> => {
    if (
      resourceInfo.type !== 'embeddedMap' &&
      resourceInfo.type !== 'embeddedArray'
    ) {
      return undefined;
    }
    try {
      const result = await getList(resource, {
        pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
        sort: resourceInfo.defaultSort,
        filter: resourceInfo.defaultFilter ?? {},
        meta: resourceMeta,
      });
      // Transform data based on resource type
      return result.data.reduce(
        (acc, item) => {
          if (item.id !== undefined) {
            acc[item.id] = item;
          }
          return acc;
        },
        {} as { [key: string]: RaRecord }
      );
    } catch (error) {
      // Return appropriate empty result based on type
      return undefined;
    }
  };

  const ensureSyncListener = async (
    resource: string,
    resourceMeta: { [key: string]: any } | undefined = undefined
  ): Promise<void> => {
    console.log(
      `HybridDP: *** Ensuring sync listener for ${resource} with metas: ${JSON.stringify(resourceMeta)}`
    );
    console.log(
      `HybridDP: Active listeners:`,
      Object.keys(activeSyncListeners)
    );
    // get the resource path
    const resourcePath = getResourcePath(resource, accountId, resourceMeta);
    // check if the active listener for the resource is for the same resource path
    if (
      activeSyncListeners[resource] &&
      activeSyncListeners[resource].path !== resourcePath
    ) {
      console.log(
        `HybridDP: Stopping existing listener for ${resource} at ${activeSyncListeners[resource].path}`
      );
      stopSyncListener(activeSyncListeners, resource);
    }
    // check if resource already has a listener
    if (!activeSyncListeners[resource]) {
      // the resource does not have a listener so we create one
      try {
        // get the resource info
        const resourceInfo = getResourceInfo(resource, resourceMeta);
        // get the previous data from the cache because we need it to compare with the new data from the listener
        // and when we register the listener we need to provide the previous data
        const previousMapData = await fetchPreviousDataFromCache(
          resource,
          resourceInfo,
          resourceMeta
        );
        // register the listener as active but without the unsubscribe function
        // which will be created and added after
        // this is to avoid creating multiple listeners for the same resource
        registerActiveSyncListener(
          activeSyncListeners,
          resource,
          resourcePath,
          resourceInfo,
          previousMapData
        );
        // create the actual listener
        if (resourceInfo.type === 'collection') {
          // the listener is on the collection of documents where each document is an entity
          await createCollectionListener(
            instance,
            resource,
            resourcePath,
            resourceMeta
          );
        } else if (resourceInfo.type === 'embeddedMap') {
          // the listener is on a document that contains a map of entities
          await createEmbeddedMapListener(
            instance,
            resource,
            resourcePath,
            resourceInfo.field,
            resourceMeta
          );
        } else if (resourceInfo.type === 'embeddedArray') {
          console.log(
            `HybridDP: Creating embedded array listener for ${resource} at ${resourcePath}`
          );
          // the listener is on a document that contains an array of entities
          await createEmbeddedArrayListener(
            instance,
            resource,
            resourcePath,
            resourceInfo.field,
            resourceMeta
          );
        }
      } catch (error) {
        console.error(
          `HybridDP: Failed to start sync listener for ${resource}:`,
          error
        );
        stopSyncListener(activeSyncListeners, resource);
        throw error;
      }
    }
  };

  const getList = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetListParams
  ): Promise<GetListResult<RecordType>> => {
    if (resource === RESOURCES.ACCOUNTS) {
      params.filter = { id: accountId }; // Filter accounts by accountId
    }
    return localCacheProvider.getList<RecordType>(resource, params);
  };

  const getOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetOneParams
  ): Promise<GetOneResult<RecordType>> => {
    return localCacheProvider.getOne<RecordType>(resource, params);
  };

  const getMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyParams
  ): Promise<GetManyResult<RecordType>> => {
    return localCacheProvider.getMany<RecordType>(resource, params);
  };

  const getManyReference = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyReferenceParams
  ): Promise<GetManyReferenceResult<RecordType>> => {
    return localCacheProvider.getManyReference<RecordType>(resource, params);
  };

  const create = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: CreateParams
  ): Promise<CreateResult<RecordType>> => {
    console.log('HybridDP: create', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);
    // generate a random 6 char number
    const eventId = Math.floor(100000 + Math.random() * 900000);
    // set the operation timestamp
    const eventTimestamp = new Date().getTime();
    // create the params with meta including the timestamp to pass to firestore
    const paramsWithMetas = {
      ...params,
      meta: {
        ...params.meta,
        _eventId: eventId,
        _eventTimestamp: eventTimestamp,
      },
    };
    // create the firestore data
    let result = await firestoreProvider.create<RecordType>(
      resource,
      paramsWithMetas
    );
    try {
      const existingRecord = await localCacheProvider
        .getOne<RecordType>(resource, {
          id: result.data.id,
          meta: params.meta,
        })
        .catch(() => null);
      if (existingRecord?.data?._e === eventId) {
        // do nothing if record already exists in cache because it was created by the listener
        result = existingRecord;
        console.log('HybridDP: create already exists in cache', result);
      } else {
        // create new params with the created data
        const newParams = {
          ...params,
          data: result.data,
          meta: params.meta,
        };
        result = await localCacheProvider.create<RecordType>(
          resource,
          newParams
        );
        console.log('HybridDP: create created in cache', result);
      }
    } catch (error) {
      // do nothing in case record already exists in cache because it was created by the listener
    }
    return result;
  };

  const update = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateParams
  ): Promise<UpdateResult<RecordType>> => {
    console.log('HybridDP: update', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);
    const eventId = Math.floor(100000 + Math.random() * 900000);
    const eventTimestamp = new Date().getTime();
    const paramsWithMetas = {
      ...params,
      meta: {
        ...params.meta,
        _eventId: eventId,
        _eventTimestamp: eventTimestamp,
      },
    };
    let result = await firestoreProvider.update<RecordType>(
      resource,
      paramsWithMetas
    );
    console.log('HybridDP: update result from firestore', result);
    try {
      const existingRecord = await localCacheProvider
        .getOne<RecordType>(resource, {
          id: result.data.id,
          meta: params.meta,
        })
        .catch(() => null);
      console.log('HybridDP: update existing record', existingRecord?.data);
      if (existingRecord?.data?._e === eventId) {
        // do nothing if record already exists in cache because it was created by the listener
        result = existingRecord;
        console.log('HybridDP: update already exists in cache', result);
      } else {
        // create new params with the created data
        const newParams = {
          ...params,
          data: result.data,
          meta: params.meta,
        };
        result = await localCacheProvider.update<RecordType>(
          resource,
          newParams
        );
        console.log('HybridDP: update updated the cache', result);
      }
    } catch (error) {
      // do nothing in case record already exists in cache because it was created by the listener
    }
    return result;
  };

  const updateMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateManyParams
  ): Promise<UpdateManyResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);
    const eventId = Math.floor(100000 + Math.random() * 900000);
    const eventTimestamp = new Date().getTime();
    const paramsWithMetas = {
      ...params,
      meta: {
        ...params.meta,
        _eventId: eventId,
        _eventTimestamp: eventTimestamp,
      },
    };
    // create a new params object
    const newParams = {
      ...params,
      data: { ...clearUndefinedFields(params.data) },
    };
    // if the resource is not an embedded array, add the update timestamp
    if (resourceInfo?.type !== 'embeddedArray') {
      // add the update timestamp
      newParams.data._u = eventTimestamp;
      // add the event id
      newParams.data._e = eventId;
    }
    // update the firestore data
    await firestoreProvider.updateMany<RecordType>(resource, paramsWithMetas);
    // update the local cache data
    return localCacheProvider.updateMany<RecordType>(resource, newParams);
  };

  const deleteOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteParams<RecordType>
  ): Promise<DeleteResult<RecordType>> => {
    console.log('HybridDP: delete', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);
    const eventId = Math.floor(100000 + Math.random() * 900000);
    const eventTimestamp = new Date().getTime();
    const paramsWithMetas = {
      ...params,
      meta: {
        ...params.meta,
        _eventId: eventId,
        _eventTimestamp: eventTimestamp,
      },
    };
    // update the firestore data
    await firestoreProvider.delete<RecordType>(resource, paramsWithMetas);
    // check if resource has soft delete
    if (resourceInfo?.hasSoftDelete) {
      // update the local cache data
      await localCacheProvider.update<RecordType>(resource, {
        ...params,
        previousData: params.previousData ?? {},
        data: { _d: true, _u: eventTimestamp, _e: eventId },
      });
      // create a delete result
      const previousDataWithId = {
        ...(params.previousData ?? {}),
        id: params.id,
      };
      return { data: previousDataWithId as RecordType };
    } else {
      // delete the local cache data
      return localCacheProvider.delete<RecordType>(resource, params);
    }
  };

  const deleteMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteManyParams<RecordType>
  ): Promise<DeleteManyResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);
    const eventId = Math.floor(100000 + Math.random() * 900000);
    const eventTimestamp = new Date().getTime();
    const paramsWithMetas = {
      ...params,
      meta: {
        ...params.meta,
        _eventId: eventId,
        _eventTimestamp: eventTimestamp,
      },
    };
    // update the firestore data
    await firestoreProvider.deleteMany<RecordType>(resource, paramsWithMetas);
    // check if resource has soft delete
    if (resourceInfo?.hasSoftDelete) {
      // update the local cache data
      return localCacheProvider.updateMany<RecordType>(resource, {
        ...params,
        data: { _d: true, _u: eventTimestamp, _e: eventId },
      });
    } else {
      // delete the local cache data
      return localCacheProvider.deleteMany<RecordType>(resource, params);
    }
  };

  const subscribe = async (
    topic: string,
    callback: (event: RaRealtimeEvent) => void
  ): Promise<{ data: null }> => {
    console.log(`HybridDP: Subscribing to topic: ${topic}`);
    const subscription: Subscription = {
      topic: topic,
      subscriptionCallback: callback,
    };
    subscriptions.push(subscription);
    return Promise.resolve({ data: null });
  };

  const unsubscribe = async (
    topic: string,
    callback: (event: RaRealtimeEvent) => void
  ): Promise<{ data: null }> => {
    // TODO check if this is needed anymore because we can leave the listener active until it changes path
    // const resource = getResourceFromTopic(topic);
    // stopSyncListenerForNonGlobalListeners(resource);
    console.log(`HybridDP: Unsubscribing from topic: ${topic}`);
    subscriptions = subscriptions.filter(
      subscription =>
        subscription.topic !== topic ||
        subscription.subscriptionCallback !== callback
    );
    return Promise.resolve({ data: null });
  };

  const publish = async (
    topic: string,
    event: RaRealtimeEvent
  ): Promise<{ data: null }> => {
    if (!topic) {
      return Promise.reject(new Error('missing topic'));
    }
    if (
      !event ||
      !event.type ||
      !event.payload ||
      !Array.isArray(event.payload.ids)
    ) {
      return Promise.reject(new Error('invalid event structure'));
    }
    subscriptions.forEach(
      // Use forEach instead of map for side effects
      subscription => {
        if (topic === subscription.topic) {
          try {
            subscription.subscriptionCallback(event);
          } catch (error) {
            console.error(
              `HybridDP: Error in subscription callback for topic ${topic}:`,
              error
            );
          }
        }
      }
    );
    return Promise.resolve({ data: null }); // Method should be async if it awaits, but here it's sync
  };

  const cleanup = () => {
    console.log('HybridDP: Cleanup called');
    subscriptions = [];
    activeSyncListeners = {};
  };

  const stopSyncListenerForNonGlobalListeners = (resource: any) => {
    if (
      resourcesInfo[resource as keyof ResourcesInfo]?.hasLiveListener ===
        true &&
      resourcesInfo[resource as keyof ResourcesInfo]?.hasGlobalListener ===
        false
    ) {
      stopSyncListener(activeSyncListeners, resource);
    }
  };

  const processDataProviderRequest = async (
    resource: any,
    params: any,
    returnMethod: (resource: any, params: any) => Promise<any>
  ) => {
    const resourceInfo = resourcesInfo[resource as keyof ResourcesInfo];
    if (resourceInfo) {
      if (resourceInfo.hasLiveListener) {
        // if the resource has a live listener and the params have no meta fields
        // or the resource has meta fields and the params have all the meta fields
        // we create a new listener
        if (
          resourceInfo.needsMetaFields === undefined ||
          resourceInfo.needsMetaFields.length === 0 ||
          (resourceInfo.needsMetaFields.length > 0 &&
            params.meta &&
            resourceInfo.needsMetaFields.every(
              field => params.meta[field] !== undefined
            ))
        ) {
          await ensureSyncListener(resource, params.meta);
        }
      }
      return returnMethod(resource, params);
    } else {
      throw new Error(`Resource ${resource} not configured!`);
    }
  };

  const instance: HybridDataProvider = {
    getList: async (resource: any, params: any) => {
      console.log('hybrid getList', resource, params);
      return processDataProviderRequest(resource, params, getList);
    },
    getOne: async (resource, params) => {
      console.log('hybrid getOne', resource, params);
      return processDataProviderRequest(resource, params, getOne);
    },
    getMany: async (resource, params) => {
      console.log('hybrid getMany', resource, params);
      return processDataProviderRequest(resource, params, getMany);
    },
    getManyReference: async (resource, params) => {
      console.log('hybrid getManyReference', resource, params);
      return processDataProviderRequest(resource, params, getManyReference);
    },
    create: async (resource, params) => {
      console.log('hybrid create', resource, params);
      return processDataProviderRequest(resource, params, create);
    },
    update: async (resource, params) => {
      console.log('hybrid update', resource, params);
      return processDataProviderRequest(resource, params, update);
    },
    updateMany: async (resource, params) => {
      console.log('hybrid updateMany', resource, params);
      return processDataProviderRequest(resource, params, updateMany);
    },
    delete: async (resource, params) => {
      console.log('hybrid delete', resource, params);
      return processDataProviderRequest(resource, params, deleteOne);
    },
    deleteMany: async (resource, params) => {
      console.log('hybrid deleteMany', resource, params);
      return processDataProviderRequest(resource, params, deleteMany);
    },
    subscribe: subscribe,
    unsubscribe: unsubscribe,
    publish: publish,
    generateFirestoreId: firestoreProvider.generateFirestoreId,
    getAccountId: () => accountId,
    ensureSyncListener: ensureSyncListener,
    stopSyncListener: (resource: string) =>
      stopSyncListener(activeSyncListeners, resource),
    cleanup: () => cleanup(),
    getActiveSyncListeners: () => activeSyncListeners,
    getSubscriptions: () => subscriptions,
    getLocalCacheProvider: () => localCacheProvider,
  };

  return instance;
};

export const getHybridDataProvider = (
  accountId: string,
  firestoreProvider: FirestoreDataProvider,
  localCacheProvider: LocalCacheDataProvider
): HybridDataProvider => {
  const basicDataProvider = getBasicHybridDataProvider(
    accountId,
    firestoreProvider,
    localCacheProvider
  );
  return withLifecycleCallbacks(basicDataProvider, [
    updateTeamMembersOnPermissionsChange,
    updateSellPointsOnCatalogsChange,
    catalogFileUploadHandler, // Custom handler for catalogs (no file deletion on catalog delete)
    ...createGenericFileUploadHandlers(), // Generic handlers for other resources
  ]) as HybridDataProvider;
};
