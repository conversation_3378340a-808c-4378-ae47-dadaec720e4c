import { useEffect, useRef, useState } from 'react';
import CloudIcon from '@mui/icons-material/Cloud';
import CloudOffIcon from '@mui/icons-material/CloudOff';
import RefreshIcon from '@mui/icons-material/Refresh';
import SyncIcon from '@mui/icons-material/Sync';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import {
  Backdrop,
  Box,
  Button,
  CircularProgress,
  LinearProgress,
  Typography,
} from '@mui/material';
import { onValue, ref } from 'firebase/database';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';
import { useFirebase } from '~/contexts/FirebaseContext';

/**
 * ConnectivityMonitor - UI-only component for monitoring connection status
 *
 * This component:
 * - Monitors browser online/offline status
 * - Monitors Firebase RTDB .info/connected status
 * - Displays appropriate UI when disconnected
 * - Does NOT manage presence (that's handled by PresenceManager in FirebaseContext)
 */

// Types
interface ConnectivityMonitorProps {
  children: React.ReactNode;
}

type VerificationState = 'idle' | 'verifying-initial' | 'verifying-reconnect';

type ConnectionContext = 'initial' | 'reconnecting' | 'normal';

type ConnectionState =
  | 'online'
  | 'offline'
  | 'connecting'
  | 'reconnecting'
  | 'firebase-disconnected'
  | 'syncing';

interface ConnectionInfo {
  icon: React.ComponentType<any>;
  title: string;
  message: string;
  showCheckButton: boolean;
}

// Constants
const CONNECTION_CHECK_INTERVAL = 10000; // 10 seconds
const AUTO_RETRY_INTERVAL = 10000; // 10 seconds for offline auto-retry
const PROGRESS_UPDATE_INTERVAL = 50; // Update progress bar every 50ms for smoother animation
const MIN_CHECKING_DISPLAY_TIME = 1000; // Minimum time to show "Checking..." state (1 second)
const INITIAL_CONNECTION_DELAY = {
  CONNECTED: 100,
  DISCONNECTED: 2000,
};
const MANUAL_CHECK_DELAY = 500;
const BACKGROUND_RETURN_GRACE_PERIOD = 5000; // 5 seconds to detect background return scenario
const GRACE_PERIOD_EXTENSION_WINDOW = 3000; // 3 seconds after grace period expires to still show syncing
const INITIAL_VERIFICATION_UI_THRESHOLD = 2000; // Show UI after 2 seconds for initial verification
const RECONNECT_VERIFICATION_UI_THRESHOLD = 500; // Show UI after 500ms for reconnection
const VERIFICATION_TIMEOUT = 3000; // Maximum time for verification

const ConnectivityMonitor: React.FC<ConnectivityMonitorProps> = ({
  children,
}) => {
  // Hooks
  const { theme } = useTheme();
  const { details: fbDetails, loading: fbLoading } = useFirebase();
  const { t } = useTranslation('');

  // State
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isFirebaseConnected, setIsFirebaseConnected] = useState<
    boolean | null
  >(null);
  const [
    firebaseInitialConnectionReceived,
    setFirebaseInitialConnectionReceived,
  ] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [wasRecentlyBackgrounded, setWasRecentlyBackgrounded] = useState(false);
  const [backgroundReturnTimestamp, setBackgroundReturnTimestamp] = useState<
    number | null
  >(null);
  const [autoCheckProgress, setAutoCheckProgress] = useState(0);
  const [timeUntilNextCheck, setTimeUntilNextCheck] = useState(0);
  const [verificationState, setVerificationState] =
    useState<VerificationState>('verifying-initial');
  const [connectionContext, setConnectionContext] =
    useState<ConnectionContext>('initial');
  const [showVerificationUI, setShowVerificationUI] = useState(false);

  // Refs
  const hasReceivedInitialConnection = useRef(false);
  const cleanupRef = useRef<(() => void) | null>(null);
  const gracePeriodTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const gracePeriodExpiryTimestamp = useRef<number | null>(null);
  const autoCheckTimerRef = useRef<NodeJS.Timeout | null>(null);
  const autoCheckAnimationRef = useRef<number | null>(null);
  const autoCheckStartTimeRef = useRef<number | null>(null);

  // Computed values
  const isDark = theme?.palette?.mode === 'dark';
  const safeTheme = theme || {};
  const safePalette = safeTheme.palette || {};
  const shouldMonitorFirebase = Boolean(
    fbDetails?.rtdb && fbDetails.selectedAccount
  );

  // Connection state logic
  const getConnectionState = (): ConnectionState => {
    // PRIORITY 1: Active verification (with UI)
    if (verificationState !== 'idle' && showVerificationUI) {
      if (connectionContext === 'initial') {
        return 'connecting';
      } else if (connectionContext === 'reconnecting') {
        return 'reconnecting';
      }
    }

    // PRIORITY 2: No internet = offline (Firebase irrelevant)
    if (!isOnline) return 'offline';

    // PRIORITY 3: Internet exists, check Firebase
    if (shouldMonitorFirebase) {
      if (firebaseInitialConnectionReceived && isFirebaseConnected === false) {
        const gracePeriodActive = gracePeriodTimeoutRef.current !== null;
        const recentlyExpired =
          gracePeriodExpiryTimestamp.current !== null &&
          Date.now() - gracePeriodExpiryTimestamp.current <
            GRACE_PERIOD_EXTENSION_WINDOW;

        if (gracePeriodActive || recentlyExpired) {
          return 'online';
        }

        return 'firebase-disconnected';
      }
    }

    // PRIORITY 4: Everything is fine
    return 'online';
  };
  const connectionState = getConnectionState();

  const getShouldShowConnectionMonitor = (): boolean => {
    if (fbLoading) {
      return false;
    }

    if (showVerificationUI) {
      return true;
    }

    if (!isOnline) {
      return true;
    }

    if (shouldMonitorFirebase) {
      const gracePeriodActive = gracePeriodTimeoutRef.current !== null;
      const recentlyExpired =
        gracePeriodExpiryTimestamp.current !== null &&
        Date.now() - gracePeriodExpiryTimestamp.current <
          GRACE_PERIOD_EXTENSION_WINDOW;

      const shouldShow =
        firebaseInitialConnectionReceived &&
        isFirebaseConnected === false &&
        !gracePeriodActive &&
        !recentlyExpired;

      return shouldShow;
    }

    return false;
  };

  const shouldShowConnectionMonitor = getShouldShowConnectionMonitor();

  // Utility functions
  const resetFirebaseConnectionState = () => {
    setFirebaseInitialConnectionReceived(false);
    setIsFirebaseConnected(null);
    hasReceivedInitialConnection.current = false;
  };

  const cleanupCurrentListener = () => {
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
  };

  // Grace period management for Firebase reconnection
  const startGracePeriod = () => {
    const timestamp = Date.now();
    if (gracePeriodTimeoutRef.current) {
      clearTimeout(gracePeriodTimeoutRef.current);
    }

    setWasRecentlyBackgrounded(true);
    setBackgroundReturnTimestamp(timestamp);

    gracePeriodTimeoutRef.current = setTimeout(() => {
      const expiredAt = Date.now();
      setWasRecentlyBackgrounded(false);
      setBackgroundReturnTimestamp(null);
      gracePeriodTimeoutRef.current = null;
      gracePeriodExpiryTimestamp.current = expiredAt;
    }, BACKGROUND_RETURN_GRACE_PERIOD);
  };

  const stopGracePeriod = () => {
    if (gracePeriodTimeoutRef.current) {
      clearTimeout(gracePeriodTimeoutRef.current);
      gracePeriodTimeoutRef.current = null;
    }
    setWasRecentlyBackgrounded(false);
    setBackgroundReturnTimestamp(null);
    gracePeriodExpiryTimestamp.current = null;
  };

  // Auto-check timer management for offline state
  const startAutoCheckTimer = () => {
    // Clear any existing timers
    stopAutoCheckTimer();

    const startTime = Date.now();
    autoCheckStartTimeRef.current = startTime;

    // Smooth animation loop using requestAnimationFrame
    const updateProgress = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min((elapsed / AUTO_RETRY_INTERVAL) * 100, 100);
      const remainingMs = Math.max(AUTO_RETRY_INTERVAL - elapsed, 0);
      const remainingSeconds = Math.ceil(remainingMs / 1000);

      // Update state in a single batch
      setAutoCheckProgress(progress);
      setTimeUntilNextCheck(remainingSeconds);

      // Continue animation if not complete
      if (elapsed < AUTO_RETRY_INTERVAL) {
        autoCheckAnimationRef.current = requestAnimationFrame(updateProgress);
      }
    };

    // Start animation
    autoCheckAnimationRef.current = requestAnimationFrame(updateProgress);

    // Schedule the actual check
    autoCheckTimerRef.current = setTimeout(async () => {
      // Stop animation
      if (autoCheckAnimationRef.current) {
        cancelAnimationFrame(autoCheckAnimationRef.current);
        autoCheckAnimationRef.current = null;
      }

      // Set final progress state
      setAutoCheckProgress(100);
      setTimeUntilNextCheck(0);

      // Small delay to ensure UI updates
      await new Promise(resolve => setTimeout(resolve, 50));

      // Show checking state
      setIsChecking(true);

      const checkStartTime = Date.now();

      try {
        const isConnected = await checkBrowserConnection();

        // Ensure minimum checking display time
        const checkDuration = Date.now() - checkStartTime;
        const remainingDisplayTime = Math.max(
          MIN_CHECKING_DISPLAY_TIME - checkDuration,
          0
        );

        if (remainingDisplayTime > 0) {
          await new Promise(resolve =>
            setTimeout(resolve, remainingDisplayTime)
          );
        }

        if (isConnected) {
          setIsOnline(true);
          setShowVerificationUI(false);
          setIsChecking(false);
          stopAutoCheckTimer();
        } else {
          // Connection still not available, restart timer
          setIsChecking(false);
          startAutoCheckTimer();
        }
      } catch {
        // Error during check, ensure minimum display time then restart
        const checkDuration = Date.now() - checkStartTime;
        const remainingDisplayTime = Math.max(
          MIN_CHECKING_DISPLAY_TIME - checkDuration,
          0
        );

        if (remainingDisplayTime > 0) {
          await new Promise(resolve =>
            setTimeout(resolve, remainingDisplayTime)
          );
        }

        setIsChecking(false);
        startAutoCheckTimer();
      }
    }, AUTO_RETRY_INTERVAL);
  };

  const stopAutoCheckTimer = () => {
    // Clear timeout
    if (autoCheckTimerRef.current) {
      clearTimeout(autoCheckTimerRef.current);
      autoCheckTimerRef.current = null;
    }

    // Cancel animation frame
    if (autoCheckAnimationRef.current) {
      cancelAnimationFrame(autoCheckAnimationRef.current);
      autoCheckAnimationRef.current = null;
    }

    // Reset refs
    autoCheckStartTimeRef.current = null;

    // Reset state
    setAutoCheckProgress(0);
    setTimeUntilNextCheck(0);
  };

  // Connection checking
  const checkBrowserConnection = async (): Promise<boolean> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`/favicon.ico?t=${Date.now()}`, {
        method: 'HEAD',
        cache: 'no-store',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response.ok || response.type === 'opaque';
    } catch {
      return false;
    }
  };

  // Dual verification with timeout
  const verifyConnectionWithTimeout = async (
    timeoutMs = VERIFICATION_TIMEOUT
  ): Promise<boolean> => {
    const navigatorStatus = navigator.onLine;

    try {
      const fetchResult = await Promise.race([
        checkBrowserConnection(),
        new Promise<boolean>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), timeoutMs)
        ),
      ]);

      return navigatorStatus && fetchResult;
    } catch {
      return navigatorStatus;
    }
  };

  const handleCheckAgain = async () => {
    // Stop auto-check timer since user is manually checking
    stopAutoCheckTimer();

    // Immediately set checking state and complete progress
    setAutoCheckProgress(100);
    setTimeUntilNextCheck(0);
    setIsChecking(true);

    // Small delay to ensure UI updates are visible
    await new Promise(resolve => setTimeout(resolve, 100));

    const checkStartTime = Date.now();

    try {
      const isConnected = await checkBrowserConnection();

      // Ensure minimum checking display time (1 second)
      const checkDuration = Date.now() - checkStartTime;
      const remainingDisplayTime = Math.max(
        MIN_CHECKING_DISPLAY_TIME - checkDuration,
        0
      );

      if (remainingDisplayTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingDisplayTime));
      }

      if (isConnected) {
        setIsOnline(true);
        setShowVerificationUI(false);
        setIsChecking(false);
      } else {
        // Connection still not available, restart auto-check timer
        setIsChecking(false);
        startAutoCheckTimer();
      }
    } catch {
      // Error during check, ensure minimum display time then restart auto-check timer
      const checkDuration = Date.now() - checkStartTime;
      const remainingDisplayTime = Math.max(
        MIN_CHECKING_DISPLAY_TIME - checkDuration,
        0
      );

      if (remainingDisplayTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingDisplayTime));
      }

      setIsChecking(false);
      startAutoCheckTimer();
    }
  };

  // Connection info configuration
  const getConnectionInfo = (): ConnectionInfo | null => {
    switch (connectionState) {
      case 'offline':
        return {
          icon: WifiOffIcon,
          title: t('connectionMonitor.titleNoInternet'),
          message: t('connectionMonitor.descriptionNoInternet'),
          showCheckButton: true,
        };
      case 'firebase-disconnected':
        return {
          icon: CloudOffIcon,
          title: t('connectionMonitor.titleFirebaseDisconnected'),
          message: t('connectionMonitor.descriptionFirebaseDisconnected'),
          showCheckButton: false,
        };
      default:
        return null;
    }
  };

  // Effects

  // Initial mount verification
  useEffect(() => {
    let isMounted = true;
    let isStillVerifying = true;
    let uiTimeout: NodeJS.Timeout | null = null;

    const verifyInitialConnection = async () => {
      setVerificationState('verifying-initial');
      setConnectionContext('initial');

      uiTimeout = setTimeout(() => {
        if (isMounted && isStillVerifying) {
          setShowVerificationUI(true);
        }
      }, INITIAL_VERIFICATION_UI_THRESHOLD);

      try {
        const actualStatus = await verifyConnectionWithTimeout();

        if (isMounted) {
          if (uiTimeout) clearTimeout(uiTimeout);
          setIsOnline(actualStatus);
          setShowVerificationUI(!actualStatus);
        }
      } catch {
        if (isMounted) {
          if (uiTimeout) clearTimeout(uiTimeout);
          setIsOnline(false);
          setShowVerificationUI(true);
        }
      } finally {
        if (isMounted) {
          isStillVerifying = false;
          setVerificationState('idle');
          setConnectionContext('normal');
        }
      }
    };

    verifyInitialConnection();

    return () => {
      isMounted = false;
      isStillVerifying = false;
      if (uiTimeout) clearTimeout(uiTimeout);
    };
  }, []);

  // Page visibility monitoring (detect mobile background/foreground)
  useEffect(() => {
    let uiTimeout: NodeJS.Timeout | null = null;
    let isVerifying = false;

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        if (!navigator.onLine || !isOnline) {
          return;
        }

        if (verificationState !== 'idle' || isVerifying) {
          return;
        }

        isVerifying = true;
        setVerificationState('verifying-reconnect');
        setConnectionContext('reconnecting');
        setWasRecentlyBackgrounded(true);
        setBackgroundReturnTimestamp(Date.now());

        uiTimeout = setTimeout(() => {
          setShowVerificationUI(true);
        }, RECONNECT_VERIFICATION_UI_THRESHOLD);

        try {
          const actualStatus = await verifyConnectionWithTimeout();

          if (uiTimeout) clearTimeout(uiTimeout);
          setIsOnline(actualStatus);
          setShowVerificationUI(!actualStatus);
        } catch {
          if (uiTimeout) clearTimeout(uiTimeout);
          setIsOnline(false);
          setShowVerificationUI(true);
        } finally {
          isVerifying = false;
          setVerificationState('idle');
          setConnectionContext('normal');

          setTimeout(() => {
            setWasRecentlyBackgrounded(false);
            setBackgroundReturnTimestamp(null);
          }, BACKGROUND_RETURN_GRACE_PERIOD + 1000);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (uiTimeout) clearTimeout(uiTimeout);
    };
  }, [verificationState]);

  // Auto-check timer for offline state
  useEffect(() => {
    if (
      !isOnline &&
      shouldShowConnectionMonitor &&
      connectionState === 'offline'
    ) {
      // Start auto-check timer when offline
      startAutoCheckTimer();
    } else {
      // Stop auto-check timer when online or not showing offline state
      stopAutoCheckTimer();
    }

    return () => {
      stopAutoCheckTimer();
    };
  }, [isOnline, shouldShowConnectionMonitor, connectionState]);

  // Browser connection monitoring
  useEffect(() => {
    let isVerifyingOnline = false;

    const handleOnline = async () => {
      // Guard: Prevent multiple simultaneous verifications
      if (verificationState !== 'idle' || isVerifyingOnline) {
        console.log(
          '[ConnectivityMonitor] ⚠️ Verification already in progress, ignoring online event'
        );
        return;
      }

      isVerifyingOnline = true;

      console.log(
        '[ConnectivityMonitor] 🌐 Browser connection restored (online event fired)'
      );

      // IMMEDIATELY show "Restoring Your Connection" modal
      console.log(
        '[ConnectivityMonitor] 🌐 Showing reconnection UI immediately'
      );
      setVerificationState('verifying-reconnect');
      setConnectionContext('reconnecting');
      setShowVerificationUI(true);
      setIsOnline(true);

      // Verify the connection is actually working
      console.log(
        '[ConnectivityMonitor] 🌐 Verifying connection with fetch test...'
      );

      try {
        const actualStatus = await verifyConnectionWithTimeout(3000);

        if (!actualStatus) {
          setIsOnline(false);
          setVerificationState('idle');
          setConnectionContext('normal');
          setShowVerificationUI(false);
        } else {
          startGracePeriod();

          setTimeout(() => {
            setVerificationState('idle');
            setConnectionContext('normal');
            setShowVerificationUI(false);
          }, 1500);
        }
      } finally {
        isVerifyingOnline = false;
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowVerificationUI(true);
      stopGracePeriod();
      // Auto-check timer will be started by the useEffect
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    let intervalId: number;
    if (isOnline) {
      intervalId = window.setInterval(async () => {
        const isConnected = await checkBrowserConnection();
        if (!isConnected) {
          setIsOnline(false);
          setShowVerificationUI(true);
        }
      }, CONNECTION_CHECK_INTERVAL);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (intervalId) clearInterval(intervalId);
      // Clean up grace period timeout and refs
      if (gracePeriodTimeoutRef.current) {
        clearTimeout(gracePeriodTimeoutRef.current);
        gracePeriodTimeoutRef.current = null;
      }
      gracePeriodExpiryTimestamp.current = null;
    };
  }, [isOnline]);

  // Firebase connection monitoring
  useEffect(() => {
    if (!shouldMonitorFirebase) {
      cleanupCurrentListener();
      resetFirebaseConnectionState();
      return;
    }

    resetFirebaseConnectionState();

    const connectedRef = ref(fbDetails.rtdb!, '.info/connected');

    let initialConnectionTimeout: NodeJS.Timeout | null = null;

    const unsubscribe = onValue(connectedRef, snapshot => {
      const connected = snapshot.val() === true;

      if (connected && !isOnline) {
        return;
      }

      setIsFirebaseConnected(connected);

      const gracePeriodActive = gracePeriodTimeoutRef.current !== null;

      if (connected && gracePeriodActive) {
        setVerificationState('idle');
        setConnectionContext('normal');
        setShowVerificationUI(false);
      } else if (connected && gracePeriodExpiryTimestamp.current) {
        gracePeriodExpiryTimestamp.current = null;
      }

      if (!hasReceivedInitialConnection.current) {
        hasReceivedInitialConnection.current = true;
        const delay = connected
          ? INITIAL_CONNECTION_DELAY.CONNECTED
          : INITIAL_CONNECTION_DELAY.DISCONNECTED;

        initialConnectionTimeout = setTimeout(() => {
          setFirebaseInitialConnectionReceived(true);
        }, delay);
      } else {
        setFirebaseInitialConnectionReceived(true);
      }
    });

    cleanupRef.current = () => {
      if (initialConnectionTimeout) {
        clearTimeout(initialConnectionTimeout);
        initialConnectionTimeout = null;
      }
      unsubscribe();
    };

    return () => {
      cleanupCurrentListener();
    };
  }, [
    shouldMonitorFirebase,
    fbDetails.rtdb,
    fbDetails.selectedAccount,
    isOnline,
  ]);

  // Modal content helper
  const getModalContent = () => {
    switch (connectionState) {
      case 'connecting':
        return {
          icon: (
            <CloudIcon
              sx={{
                fontSize: 48,
                color: safePalette.primary?.main || '#1976d2',
                mb: 2,
                animation: 'pulse 2s ease-in-out infinite',
                '@keyframes pulse': {
                  '0%, 100%': { opacity: 0.6, transform: 'scale(1)' },
                  '50%': { opacity: 1, transform: 'scale(1.05)' },
                },
              }}
            />
          ),
          title: t('connectionMonitor.connectingToCloudServers'),
          message: t('connectionMonitor.establishingSecureConnection'),
        };

      case 'reconnecting':
        return {
          icon: (
            <SyncIcon
              sx={{
                fontSize: 48,
                color: safePalette.primary?.main || '#1976d2',
                mb: 2,
                animation: 'rotate 2s linear infinite',
                '@keyframes rotate': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' },
                },
              }}
            />
          ),
          title: t('connectionMonitor.restoringYourConnection'),
          message: t('connectionMonitor.reconnectingToCloudServers'),
        };

      case 'syncing':
        return {
          icon: null, // Use animated dots
          title: t('connectionMonitor.almostThere'),
          message: t('connectionMonitor.connectionBeingRestored'),
        };

      default:
        return null;
    }
  };

  // Render
  const shouldUseModalStyle = [
    'connecting',
    'reconnecting',
    'syncing',
  ].includes(connectionState);

  // Render modal style for in-progress states
  if (shouldUseModalStyle) {
    const modalContent = getModalContent();

    if (!modalContent) return <>{children}</>;

    return (
      <>
        {children}
        <Backdrop
          open={shouldShowConnectionMonitor}
          sx={{
            color: '#fff',
            zIndex: theme => theme.zIndex.drawer + 1,
            backgroundColor: isDark
              ? 'rgba(0, 0, 0, 0.6)'
              : 'rgba(255, 255, 255, 0.6)',
            backdropFilter: 'blur(4px)',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              padding: 4,
              borderRadius: '12px',
              backgroundColor: isDark
                ? safePalette.background?.paper || '#1e1e1e'
                : safePalette.background?.paper || '#ffffff',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
              maxWidth: '400px',
            }}
          >
            {modalContent.icon ? (
              modalContent.icon
            ) : (
              /* Animated dots for syncing */
              (<Box
                sx={{
                  display: 'flex',
                  gap: 1.5,
                  mb: 3,
                }}
              >
                {[0, 1, 2].map(index => (
                  <Box
                    key={index}
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: safePalette.primary?.main || '#1976d2',
                      animation: 'pulse 1.8s ease-in-out infinite',
                      animationDelay: `${index * 0.15}s`,
                      '@keyframes pulse': {
                        '0%, 100%': {
                          transform: 'scale(1)',
                          opacity: 0.6,
                        },
                        '50%': {
                          transform: 'scale(1.3)',
                          opacity: 1,
                        },
                      },
                    }}
                  />
                ))}
              </Box>)
            )}

            <Typography
              variant="h6"
              sx={{
                mb: 1,
                fontWeight: 600,
                color: isDark
                  ? safePalette.custom?.text || '#ffffff'
                  : safePalette.text?.primary || '#000000',
              }}
            >
              {modalContent.title}
            </Typography>

            <Typography
              variant="body2"
              sx={{
                color: isDark
                  ? safePalette.custom?.fadedText || '#b0b0b0'
                  : safePalette.text?.secondary || '#666666',
              }}
            >
              {modalContent.message}
            </Typography>
          </Box>
        </Backdrop>
      </>
    );
  }

  // Render full-page style for error states
  const connectionInfo = getConnectionInfo();
  const IconComponent = connectionInfo?.icon;

  // Render full-page error states
  return (
    <>
      {children}
      <Backdrop
        open={shouldShowConnectionMonitor}
        sx={{
          color: '#fff',
          zIndex: theme => theme.zIndex.drawer + 1,
          backgroundColor: isDark
            ? 'rgba(0, 0, 0, 0.95)'
            : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(8px)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
            padding: { xs: 3, sm: 4 },
            maxWidth: { xs: '90%', sm: '600px' },
            width: '100%',
          }}
        >
          {connectionInfo && (
            <>
              <Box
                sx={{
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {IconComponent && (
                  <IconComponent
                    sx={{
                      fontSize: { xs: 56, sm: 64 },
                      color: isDark
                        ? safePalette.custom?.fadedText || '#b0b0b0'
                        : safePalette.text?.secondary || '#666666',
                    }}
                  />
                )}
              </Box>

              <Typography
                variant="h5"
                component="h1"
                sx={{
                  mb: 2,
                  fontWeight: 600,
                  color: isDark
                    ? safePalette.custom?.text || '#ffffff'
                    : safePalette.text?.primary || '#000000',
                  fontSize: { xs: '1.5rem', sm: '1.75rem' },
                }}
              >
                {connectionInfo.title}
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  mb: 3,
                  color: isDark
                    ? safePalette.custom?.fadedText || '#b0b0b0'
                    : safePalette.text?.secondary || '#666666',
                  fontSize: { xs: '1rem', sm: '1.125rem' },
                  maxWidth: { xs: '100%', sm: '500px' },
                }}
              >
                {connectionInfo.message}
              </Typography>

              {connectionInfo.showCheckButton && (
                <>
                  <Button
                    variant="contained"
                    onClick={handleCheckAgain}
                    disabled={isChecking}
                    startIcon={
                      isChecking ? (
                        <CircularProgress size={16} color="inherit" />
                      ) : (
                        <RefreshIcon />
                      )
                    }
                    sx={{
                      mb: 2,
                      minWidth: 120,
                      backgroundColor: safePalette.primary?.main || '#1976d2',
                      color: 'white',
                      borderRadius: '6px',
                      padding: { xs: '12px 20px', sm: '10px 20px' },
                      fontWeight: '500',
                      fontSize: { xs: '15px', sm: '14px' },
                      textTransform: 'none',
                      boxShadow: 'none',
                      '&:hover': {
                        backgroundColor: isChecking
                          ? safePalette.primary?.main || '#1976d2'
                          : safePalette.primary?.dark || '#1565c0',
                        boxShadow: 'none',
                      },
                      '&:disabled': {
                        backgroundColor: safePalette.primary?.main || '#1976d2',
                        color: 'white',
                        opacity: 0.7,
                      },
                    }}
                  >
                    {isChecking
                      ? t('connectionMonitor.checking')
                      : t('connectionMonitor.checkNow')}
                  </Button>

                  {/* Auto-check progress indicator */}
                  <Box
                    sx={{
                      width: '100%',
                      maxWidth: { xs: '100%', sm: '400px' },
                      mb: 4,
                    }}
                  >
                    <LinearProgress
                      variant="determinate"
                      value={autoCheckProgress}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: isDark
                          ? 'rgba(255, 255, 255, 0.1)'
                          : 'rgba(0, 0, 0, 0.1)',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 3,
                          backgroundColor:
                            safePalette.primary?.main || '#1976d2',
                        },
                      }}
                    />
                    <Typography
                      variant="caption"
                      sx={{
                        display: 'block',
                        textAlign: 'center',
                        mt: 1,
                        color: isDark
                          ? safePalette.custom?.fadedText || '#b0b0b0'
                          : safePalette.text?.secondary || '#666666',
                        fontSize: { xs: '0.75rem', sm: '0.8rem' },
                      }}
                    >
                      {timeUntilNextCheck > 0
                        ? t('connectionMonitor.autoCheckingIn', {
                            seconds: timeUntilNextCheck,
                          })
                        : t('connectionMonitor.checking')}
                    </Typography>
                  </Box>
                </>
              )}

              <Box
                sx={{
                  p: 2,
                  borderRadius: '6px',
                  bgcolor:
                    safePalette.background?.paper ||
                    (isDark ? '#2a2a2a' : '#f5f5f5'),
                  border: '1px solid',
                  borderColor: isDark
                    ? safePalette.custom?.gray400 || '#404040'
                    : safePalette.divider || '#e0e0e0',
                  maxWidth: { xs: '100%', sm: '500px' },
                  width: '100%',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: isDark
                      ? safePalette.custom?.text || '#ffffff'
                      : safePalette.text?.primary || '#000000',
                    fontWeight: 600,
                  }}
                >
                  {connectionState === 'firebase-disconnected'
                    ? t('connectionMonitor.whatDoesThisMean')
                    : t('connectionMonitor.whyDoINeedAnInternetConnection')}
                </Typography>
                <Typography
                  variant="body2"
                  component="ul"
                  sx={{
                    mt: 1,
                    pl: 2,
                    textAlign: 'left',
                    color: isDark
                      ? safePalette.custom?.fadedText || '#b0b0b0'
                      : safePalette.text?.secondary || '#666666',
                  }}
                >
                  {connectionState === 'firebase-disconnected' ? (
                    <>
                      <li>
                        {t(
                          'connectionMonitor.yourInternetConnectionIsWorkingFine'
                        )}
                      </li>
                      <li>
                        {t(
                          'connectionMonitor.thereIsATemporaryIssueConnectingToOurDataServers'
                        )}
                      </li>
                      <li>
                        {t(
                          'connectionMonitor.weAreAutomaticallyTryingToReconnectInTheBackground'
                        )}
                      </li>
                      <li>
                        {t(
                          'connectionMonitor.yourWorkIsSafeAndWillSyncOnceReconnected'
                        )}
                      </li>
                    </>
                  ) : (
                    <>
                      <li>
                        {t(
                          'connectionMonitor.yourChangesNeedToBeSavedToOurSecureServers'
                        )}
                      </li>
                      <li>
                        {t(
                          'connectionMonitor.dataMustBeSynchronizedAcrossAllYourDevices'
                        )}
                      </li>
                      <li>
                        {t(
                          'connectionMonitor.realTimeUpdatesEnsureEveryoneSeesTheLatestInformation'
                        )}
                      </li>
                      <li>
                        {t(
                          'connectionMonitor.yourWorkIsAutomaticallyBackedUpAsYouGo'
                        )}
                      </li>
                    </>
                  )}
                </Typography>
              </Box>
            </>
          )}
        </Box>
      </Backdrop>
    </>
  );
};

export default ConnectivityMonitor;
