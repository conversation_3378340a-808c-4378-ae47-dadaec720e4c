/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Firestore Data Provider for React Admin
 *
 * This data provider connects React Admin to Firebase Firestore, supporting both:
 * 1. Collection resources - stored as documents in Firestore collections
 * 2. Embedded map resources - stored as fields within Firestore documents
 *
 * Key features:
 * - Collection resources use soft delete (setting _d flag)
 * - Embedded map resources use real delete (removing the field)
 * - Optimized batch operations for embedded map resources
 * - Timestamp tracking for all write operations
 */

import {
  collection,
  deleteDoc,
  deleteField,
  doc,
  documentId,
  getCountFromServer,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  Query,
  runTransaction,
  serverTimestamp,
  setDoc,
  startAfter,
  updateDoc,
  where,
  WhereFilterOp,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { get, set } from 'lodash';
import {
  CreateParams,
  CreateResult,
  DataProvider,
  DeleteManyParams,
  DeleteManyResult,
  DeleteParams,
  DeleteResult,
  GetListParams,
  GetListResult,
  GetManyParams,
  GetManyReferenceParams,
  GetManyReferenceResult,
  GetManyResult,
  GetOneParams,
  GetOneResult,
  RaRecord,
  UpdateManyParams,
  UpdateManyResult,
  UpdateParams,
  UpdateResult,
  withLifecycleCallbacks,
} from 'react-admin';

import { isObject } from '~/fake-provider/reports/utils/isObject';
import { auth, db } from '../configs/firebaseConfig';
import { generateDeviceCodeBeforeDeviceCreate } from './helpers/generateDeviceCodeBeforeDeviceCreate';
import { getOrCreateMemberIdBeforeMemberCreate } from './helpers/getOrCreateMemberIdBeforeMemberCreate';
import {
  RecordWithCollectionMetadata,
  ResourceMetadataCommon,
  ResourceMetadataUpdatedAt,
} from './types';
import { clearUndefinedFields } from './utils/clearUndefinedFields';
import { getFirestoreConverter } from './utils/firestoreConverters';
import { generateFirestoreId } from './utils/generateFirestoreId';
import { getResourceInfo } from './utils/getResourceInfo';
import { getResourcePath } from './utils/getResourcePath';

export interface FirestoreDataProvider extends DataProvider {
  generateFirestoreId: () => string;
  getAccountId: () => string;
}

interface CursorMap {
  [resource: string]: {
    lastDoc: { [page: number]: unknown };
    total?: number;
  };
}

/*
filters :
    _ninc_any - does not include any
    _inc - includes value
    _inc_any - include any
    _eq - equal
    _eq_any - equal any
    _neq - not equal
    _neq_any - not equal any
    _gt - greater than
    _gte - greater than or equal
    _lt - less than
    _lte - less than or equal
    _q - text search
*/

interface FilterState {
  hasInequality: boolean;
  inequalityField?: string;
  hasArrayContains: boolean;
  hasArrayContainsAny: boolean;
  hasIn: boolean;
  hasNotIn: boolean;
  hasNotEqual: boolean;
}

const applyFilters = (baseQuery: Query, filters: any) => {
  let q = baseQuery;
  let countQuery = baseQuery;

  const state: FilterState = {
    hasInequality: false,
    inequalityField: undefined,
    hasArrayContains: false,
    hasArrayContainsAny: false,
    hasIn: false,
    hasNotIn: false,
    hasNotEqual: false,
  };

  const addFilter = (
    fieldPath: string,
    opStr: WhereFilterOp,
    val: any
  ): boolean => {
    if (['<', '<=', '>', '>='].includes(opStr)) {
      if (state.hasInequality && state.inequalityField !== fieldPath) {
        console.warn(
          `Ignoring inequality filter on ${fieldPath} as inequality already exists on ${state.inequalityField}`
        );
        return false;
      }
      state.hasInequality = true;
      state.inequalityField = fieldPath;
    } else if (opStr === 'array-contains' && state.hasArrayContains) {
      console.warn(
        `Ignoring array-contains filter on ${fieldPath} as another array-contains already exists`
      );
      return false;
    } else if (opStr === 'array-contains-any' && state.hasArrayContainsAny) {
      console.warn(
        `Ignoring array-contains-any filter on ${fieldPath} as another array-contains-any already exists`
      );
      return false;
    } else if (opStr === 'in' && state.hasIn) {
      console.warn(
        `Ignoring 'in' filter on ${fieldPath} as another 'in' already exists`
      );
      return false;
    } else if (opStr === 'not-in') {
      if (state.hasNotIn || state.hasNotEqual) {
        console.warn(
          `Ignoring 'not-in' filter on ${fieldPath} as another 'not-in' or '!=' already exists`
        );
        return false;
      }
      state.hasNotIn = true;
    } else if (opStr === '!=') {
      if (state.hasNotIn || state.hasNotEqual) {
        console.warn(
          `Ignoring '!=' filter on ${fieldPath} as another 'not-in' or '!=' already exists`
        );
        return false;
      }
      state.hasNotEqual = true;
    }

    q = query(q, where(fieldPath, opStr, val));
    countQuery = query(countQuery, where(fieldPath, opStr, val));
    return true;
  };

  Object.entries(filters).forEach(([key, value]) => {
    if (key.endsWith('_ninc_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -9);
      if (value.length > 0) {
        addFilter(actualKey, 'not-in', value.slice(0, 10));
      }
    } else if (key.endsWith('_inc')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, 'array-contains', value);
    } else if (key.endsWith('_inc_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -8);
      if (value.length > 0) {
        addFilter(actualKey, 'array-contains-any', value.slice(0, 10));
      }
    } else if (key.endsWith('_eq')) {
      const actualKey = key.slice(0, -3);
      addFilter(actualKey, '==', value);
    } else if (key.endsWith('_eq_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -7);
      if (value.length > 0) {
        addFilter(actualKey, 'in', value.slice(0, 30));
      }
    } else if (key.endsWith('_neq')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, '!=', value);
    } else if (key.endsWith('_neq_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -8);
      if (value.length > 0) {
        addFilter(actualKey, 'not-in', value.slice(0, 10));
      }
    } else if (key.endsWith('_gt')) {
      const actualKey = key.slice(0, -3);
      addFilter(actualKey, '>', value);
    } else if (key.endsWith('_gte')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, '>=', value);
    } else if (key.endsWith('_lt')) {
      const actualKey = key.slice(0, -3);
      addFilter(actualKey, '<', value);
    } else if (key.endsWith('_lte')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, '<=', value);
    } else {
      // Default to exact match
      addFilter(key, '==', value);
    }
  });

  return { q, countQuery };
};

const applyFilterToArray = (dataArray: RaRecord[], filter: any) => {
  return dataArray.filter(item => {
    return Object.entries(filter).every(([key, filterValue]) => {
      if (key.endsWith('_q')) {
        const actualKey = key.slice(0, -2);
        const itemValue = item[actualKey];

        if (typeof filterValue !== 'string' || typeof itemValue !== 'string') {
          return false;
        }

        const searchValue = filterValue.toLowerCase();
        const fieldValue = itemValue.toLowerCase();

        return fieldValue.includes(searchValue);
      }
      if (key.endsWith('_ninc_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -9);
        return !filterValue.includes(item[actualKey]);
      }
      if (key.endsWith('_inc') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -4);
        const itemValue = item[actualKey];
        return (
          Array.isArray(itemValue) &&
          filterValue.every(v => itemValue.includes(v))
        );
      }
      if (key.endsWith('_inc_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -8);
        const itemValue = item[actualKey];
        return (
          Array.isArray(itemValue) &&
          filterValue.some(v => itemValue.includes(v))
        );
      }
      if (key.endsWith('_eq')) {
        const actualKey = key.slice(0, -3);
        return item[actualKey] === filterValue;
      }
      if (key.endsWith('_eq_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -7);
        return filterValue.includes(item[actualKey]);
      }
      if (key.endsWith('_neq')) {
        const actualKey = key.slice(0, -4);
        return item[actualKey] !== filterValue;
      }
      if (key.endsWith('_neq_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -8);
        return !filterValue.includes(item[actualKey]);
      }
      if (key.endsWith('_gt')) {
        const actualKey = key.slice(0, -3);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] > filterValue
        );
      }
      if (key.endsWith('_gte')) {
        const actualKey = key.slice(0, -4);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] >= filterValue
        );
      }
      if (key.endsWith('_lt')) {
        const actualKey = key.slice(0, -3);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] < filterValue
        );
      }
      if (key.endsWith('_lte')) {
        const actualKey = key.slice(0, -4);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] <= filterValue
        );
      }
      // Default exact match
      return item[key] === filterValue;
    });
  });
};

const getBasicFirestoreDataProvider = (
  accountId: string
): FirestoreDataProvider => {
  const cursorMap: CursorMap = {};

  const getList = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetListParams
  ): Promise<GetListResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const { page = 1, perPage = 10 } = params.pagination ?? {};
    const { field = 'id', order = 'ASC' } = params.sort ?? {};
    const filter = params.filter ?? {};

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    // If resourcePath is null, required metadata is missing - return empty result
    if (resourcePath === null) {
      console.log(
        `FirestoreDP: Skipping getList for ${resource} - required metadata not available`
      );
      return { data: [], total: 0 };
    }

    let allData: RaRecord[] = [];
    let total = 0;

    try {
      if (resourceInfo.type === 'collection') {
        if (!cursorMap[resource]) {
          cursorMap[resource] = { lastDoc: {} };
        }

        total = cursorMap[resource].total ?? 0;

        const collRef = collection(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const baseQuery = query(collRef);
        const { q: filteredQuery } = applyFilters(baseQuery, filter);

        // const countSnapshot = await getCountFromServer(countQuery);
        // total = countSnapshot.data().count;

        // Always order by _c desc for consistent pagination
        let finalQuery = query(filteredQuery, orderBy('_c', 'desc'));

        // Apply cursor if not first page
        if (page > 1) {
          const previousPageCursor = cursorMap[resource].lastDoc[page - 1];
          if (previousPageCursor) {
            finalQuery = query(finalQuery, startAfter(previousPageCursor));
          }
        }

        // Apply limit
        finalQuery = query(finalQuery, limit(perPage));

        const querySnapshot = await getDocs(finalQuery);
        const docs = querySnapshot.docs;

        allData = docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as RaRecord[];

        // Store cursor for next page
        if (docs.length > 0) {
          const lastDoc = docs[docs.length - 1];
          cursorMap[resource].lastDoc[page] = lastDoc;
        }

        // Only count total on first page
        if (page === 1) {
          const countSnapshot = await getCountFromServer(filteredQuery);
          total = countSnapshot.data().count;
          cursorMap[resource].total = total;
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          return { data: [], total: 0 };
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        // Apply filters
        const filteredData = applyFilterToArray(arrayData, filter);

        total = filteredData.length;

        // Apply sorting
        if (field && order) {
          filteredData.sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];
            if (aValue < bValue) return order === 'ASC' ? -1 : 1;
            if (aValue > bValue) return order === 'ASC' ? 1 : -1;
            return 0;
          });
        }

        // Apply pagination
        if (perPage === -1) {
          allData = filteredData;
        } else {
          const startIndex = (page - 1) * perPage;
          allData = filteredData.slice(startIndex, startIndex + perPage);
        }
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          return { data: [], total: 0 };
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        // Apply filters
        const filteredData = applyFilterToArray(arrayData, filter);

        total = filteredData.length;

        // Apply sorting
        if (field && order) {
          filteredData.sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];
            if (aValue < bValue) return order === 'ASC' ? -1 : 1;
            if (aValue > bValue) return order === 'ASC' ? 1 : -1;
            return 0;
          });
        }

        // Apply pagination
        if (perPage === -1) {
          allData = filteredData;
        } else {
          const startIndex = (page - 1) * perPage;
          allData = filteredData.slice(startIndex, startIndex + perPage);
        }
      }

      return {
        data: allData as RecordType[],
        total: total,
      };
    } catch (error) {
      console.error(`FirestoreDP: getList ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const getOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetOneParams
  ): Promise<GetOneResult<RecordType>> => {
    console.log('FirestoreDP: getOne', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      throw new Error(
        `Cannot fetch ${resource} - required metadata not available`
      );
    }

    const recordId = params.id;

    try {
      if (resourceInfo.type === 'collection') {
        const docRef = doc(db, resourcePath, recordId).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const data = {
            id: docSnapshot.id,
            ...docSnapshot.data(),
          } as RaRecord;
          if (data._d === true) {
            throw new Error('Document not found'); // Treat soft-deleted as not found
          }
          return { data: data as RecordType };
        } else {
          throw new Error('Document not found');
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found'); // Or 'Parent document not found'
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];
        const item = arrayData.find((item: any) => {
          if (isObject(item)) {
            return item.id === recordId;
          } else {
            return false;
          }
        });

        if (!item) {
          throw new Error('Item not found');
        }

        return { data: item as RecordType };
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found');
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];
        const item = arrayData.find((item: any) => {
          if (isObject(item)) {
            return item.id === recordId;
          } else {
            return false;
          }
        });

        if (!item) {
          throw new Error('Item not found');
        }

        return { data: item as RecordType };
      }
      return Promise.resolve<GetOneResult>({ data: null });
    } catch (error) {
      if (error instanceof Error && error.message === 'Document not found') {
        throw error;
      }
      // Throw a generic error for other issues
      throw new Error(
        `Failed to get record ${resource}/${recordId}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  };

  const getMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyParams
  ): Promise<GetManyResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      console.log(
        `FirestoreDP: Skipping getMany for ${resource} - required metadata not available`
      );
      return { data: [] };
    }

    const ids = params.ids;
    if (!ids || ids.length === 0) {
      return { data: [] };
    }

    let allData: RecordType[] = [];

    try {
      if (resourceInfo.type === 'collection') {
        const collRef = collection(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const batchSize = 30; // Firestore 'in' query limit

        for (let i = 0; i < ids.length; i += batchSize) {
          const batchIds = ids.slice(i, i + batchSize);
          if (batchIds.length > 0) {
            const q = query(collRef, where(documentId(), 'in', batchIds));
            const querySnapshot = await getDocs(q);
            const batchData = querySnapshot.docs.map(
              doc => (({
                id: doc.id,
                ...doc.data()
              }) as RecordType)
            );
            allData.push(...batchData);
          }
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          // Filter array to get only requested items
          allData = arrayData.filter((item: any) => {
            if (isObject(item)) {
              return ids.includes(item.id);
            } else {
              return false;
            }
          });
        }
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          // Filter array to get only requested items
          allData = arrayData.filter((item: any) => {
            if (isObject(item)) {
              return ids.includes(item.id);
            } else {
              return false;
            }
          });
        }
      }

      return { data: allData };
    } catch (error) {
      console.error(`FirestoreDP: getMany ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const getManyReference = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyReferenceParams
  ): Promise<GetManyReferenceResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const { page = 1, perPage = 10 } = params.pagination ?? {};
    const { field = 'id', order = 'ASC' } = params.sort ?? {};
    const filter = params.filter ?? {}; // Get base filters

    filter[params.target] = params.id;

    return getList<RecordType>(resource, {
      pagination: { page, perPage },
      sort: { field, order },
      filter: filter,
      meta: params.meta,
      signal: params.signal,
    });
  };

  const create = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: CreateParams
  ): Promise<CreateResult<RecordType>> => {
    console.log('firestore create', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);

    const publishOwnChanges = params.meta?._publishOwnChanges ?? false;

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      throw new Error(
        `Cannot create ${resource} - required metadata not available`
      );
    }

    const resourceMetaUpdatedBy = `_${resourceInfo.field}._ub`;
    const currentUserId = auth.currentUser?.uid ?? '@na';

    const { id: inputId, ...coreData } = params.data;
    const metaCommon: ResourceMetadataCommon = {
      _c: serverTimestamp(),
      _u: serverTimestamp(),
    };

    try {
      if (resourceInfo.type === 'collection') {
        let docId: string;
        if (inputId) {
          // Use the provided ID
          docId = inputId.toString();
        } else {
          // Generate firebase auto-generated ID
          docId = generateFirestoreId();
        }
        const docRef = doc(db, resourcePath, docId).withConverter(
          getFirestoreConverter(resource, false)
        );

        const dataWithCollectionMetaAndId: RecordWithCollectionMetadata = {
          ...coreData,
          ...metaCommon,
          _a: accountId,
          _b: resourceInfo.businessType,
          _d: resourceInfo.hasSoftDelete ? false : undefined,
          _e: eventId,
          _pc: publishOwnChanges,
          _ub: currentUserId,
          id: docId,
        };
        await setDoc(docRef, dataWithCollectionMetaAndId);

        const resultData = {
          ...clearUndefinedFields(dataWithCollectionMetaAndId),
          _c: eventTimestamp,
          _u: eventTimestamp,
        };

        return { data: resultData as unknown as RecordType, meta: params.meta };
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );
        let recordId: string | number;

        if (inputId) {
          recordId = inputId;
        } else {
          recordId = generateFirestoreId();
        }
        // create the update payload that will be used to update the document
        const updatePayload = {
          _a: accountId,
        };
        set(updatePayload, resourceMetaUpdatedBy, currentUserId);
        let payload = {};
        let resultData = {};
        if (coreData._isJustAValue) {
          payload = {
            ...coreData,
            id: String(recordId),
            _ub: currentUserId,
          };
          resultData = {
            ...clearUndefinedFields(payload),
          };
        } else {
          payload = {
            ...coreData,
            ...metaCommon,
            _d: resourceInfo.hasSoftDelete ? false : undefined,
            _e: eventId,
            _pc: publishOwnChanges,
            _ub: currentUserId,
            id: String(recordId),
          };
          resultData = {
            ...clearUndefinedFields(payload),
            _c: eventTimestamp,
            _u: eventTimestamp,
          };
        }
        set(updatePayload, `${resourceInfo.field}.${recordId}`, payload);

        // Use setDoc with merge option to update the embedded map because it is possible that the parent document does not exist yet.
        await setDoc(docRef, updatePayload, { merge: true });

        return {
          data: { ...resultData, _ub: currentUserId } as unknown as RecordType,
          meta: params.meta,
        };
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );

        const result = await runTransaction(db, async transaction => {
          const docSnapshot = await transaction.get(docRef);
          const existingData = docSnapshot.exists() ? docSnapshot.data() : {};
          const existingArray = get(existingData, resourceInfo.field) || [];

          let dataWithId: any;
          let recordId: string | number;

          const updatedArray = [];
          updatedArray.push(...existingArray);

          const newIndex = updatedArray.length;

          if (coreData._isJustAValue !== undefined && coreData._isJustAValue) {
            recordId = `${newIndex + 1}`;
            dataWithId = {
              id: recordId,
              value: coreData.value,
              _index: newIndex,
              _isJustAValue: true,
              _ub: currentUserId,
            };
          } else {
            if (inputId) {
              recordId = inputId;
            } else {
              recordId = `${newIndex + 1}`;
            }
            dataWithId = {
              ...coreData,
              id: recordId,
              _index: newIndex,
              _e: eventId,
              _pc: publishOwnChanges,
              _ub: currentUserId,
            };
          }
          updatedArray.push(dataWithId);

          const updatePayload = {
            _a: accountId,
          };
          set(updatePayload, resourceMetaUpdatedBy, currentUserId);
          set(updatePayload, resourceInfo.field, updatedArray);

          // Update document
          transaction.set(docRef, updatePayload, { merge: true });
          // we return the dataWithId to be used in the result
          return {
            ...dataWithId,
            _ub: currentUserId,
          };
        });

        return { data: result as unknown as RecordType, meta: params.meta };
      }
      return Promise.resolve<GetOneResult>({ data: null });
    } catch (error) {
      console.error(`FirestoreDP: create ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const update = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateParams
  ): Promise<UpdateResult<RecordType>> => {
    console.log('FirestoreDP: update', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);

    const publishOwnChanges = params.meta?._publishOwnChanges ?? false;

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      throw new Error(
        `Cannot update ${resource} - required metadata not available`
      );
    }

    const resourceMetaUpdatedBy = `_${resourceInfo.field}._ub`;
    const currentUserId = auth.currentUser?.uid ?? '@na';

    const recordId = params.id;
    // Exclude 'id' from the data payload, as it shouldn't be changed.
    const { id: _dataId, ...coreData } = params.data; // Use _dataId to indicate unused variable
    const resultData = {
      ...(params.previousData ?? {}),
      ...coreData,
      id: recordId,
    };
    const metaUpdatedAt: ResourceMetadataUpdatedAt = {
      _u: serverTimestamp(), // Update timestamp on modification
    };

    try {
      if (resourceInfo.type === 'collection') {
        // we will get the document ref without a converter because
        // the converter is not triggered when using updateDoc
        // instead we are going to do it manually
        const payloadConverter = getFirestoreConverter(resource, true);
        const docRef = doc(db, resourcePath, recordId);

        const dataToUpdate = {
          ...coreData,
          ...metaUpdatedAt,
          id: recordId,
          _e: eventId,
          _pc: publishOwnChanges,
          _ub: currentUserId,
        };
        // convert the data to firestore format
        const convertedPayload = payloadConverter.toFirestore(dataToUpdate);
        // update the document
        await updateDoc(docRef, convertedPayload);
        // we add the updated timestamp for the result
        resultData._u = eventTimestamp;
        resultData._e = eventId;
        resultData._pc = publishOwnChanges;
        resultData._ub = currentUserId;
      } else if (resourceInfo.type === 'embeddedMap') {
        // we will not get the document ref with converter because
        // the converter is not triggered when using updateDoc
        // instead we are going to do it manually by converting only the payload
        const payloadConverter = getFirestoreConverter(resource, true);
        const docRef = doc(db, resourcePath);

        let payload = {};
        if (coreData._isJustAValue) {
          payload = {
            ...coreData,
            id: String(recordId),
            _ub: currentUserId,
          };
        } else {
          payload = {
            ...coreData,
            ...metaUpdatedAt,
            id: String(recordId),
            _e: eventId,
            _pc: publishOwnChanges,
            _ub: currentUserId,
          };
          // we add the updated timestamp for the result
          resultData._u = eventTimestamp;
          resultData._e = eventId;
          resultData._pc = publishOwnChanges;
          resultData._ub = currentUserId;
        }
        const convertedPayload = payloadConverter.toFirestore(payload);
        const updatePayload = {
          _a: accountId,
          [resourceMetaUpdatedBy]: currentUserId,
          [`${resourceInfo.field}.${recordId}`]: convertedPayload,
        };
        await updateDoc(docRef, updatePayload);
      } else if (resourceInfo.type === 'embeddedArray') {
        // we will get the document ref with a converter because
        // while the converter is not triggered when using update inside a transaction
        // it is triggered when using get inside a transaction
        // so we will also get the converter so we can manually convert before update
        const payloadConverter = getFirestoreConverter(resource, true);
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );
        const result = await runTransaction(db, async transaction => {
          const docSnapshot = await transaction.get(docRef);

          if (!docSnapshot.exists()) {
            throw new Error('Document not found');
          }

          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          const itemIndex = arrayData.findIndex((item: any) => {
            if (isObject(item)) {
              return item.id === recordId;
            } else {
              return false;
            }
          });

          if (itemIndex === -1) {
            throw new Error('Item not found');
          }

          const updatedItem = {
            ...arrayData[itemIndex],
            ...coreData,
            _e: eventId,
            _pc: publishOwnChanges,
            _ub: currentUserId,
          };
          if (
            updatedItem._isJustAValue !== undefined &&
            updatedItem._isJustAValue
          ) {
            delete updatedItem._e;
            delete updatedItem._pc;
          }
          // serverTimestamp() is not supported in embedded arrays we use coreData
          // Update array
          const newArray = [...arrayData];
          newArray[itemIndex] = updatedItem;

          const convertedPayload = payloadConverter.toFirestore(newArray);
          // Update document
          const updatePayload = {
            _a: accountId,
            [resourceMetaUpdatedBy]: currentUserId,
            [resourceInfo.field]: convertedPayload,
          };
          transaction.update(docRef, updatePayload);
          return updatedItem;
        });
        if (result._isJustAValue !== undefined && result._isJustAValue) {
          delete resultData._e;
          delete resultData._pc;
        } else {
          resultData._e = result._e;
          resultData._pc = result._pc;
        }
        resultData._ub = result._ub;
      }
      return { data: resultData as unknown as RecordType };
    } catch (error) {
      console.error(
        `FirestoreDP: update ERROR [${resource}/${recordId}]:`,
        error
      );
      throw error;
    }
  };

  const updateMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateManyParams
  ): Promise<UpdateManyResult> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const publishOwnChanges = params.meta?._publishOwnChanges ?? false;

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      throw new Error(
        `Cannot updateMany ${resource} - required metadata not available`
      );
    }

    const resourceMetaUpdatedBy = `_${resourceInfo.field}._ub`;
    const currentUserId = auth.currentUser?.uid ?? '@na';

    const ids = params.ids;

    const { id: _dataId, ...coreData } = params.data; // Use _dataId to indicate unused variable
    const dataToUpdate = {
      ...coreData,
      _u: serverTimestamp(), // Update timestamp on modification
      _e: eventId,
      _pc: publishOwnChanges,
    };

    try {
      if (resourceInfo.type === 'collection') {
        await Promise.all(
          ids.map(id =>
            update<RecordType>(resource, {
              id,
              data: params.data,
              previousData: { id } as Partial<RecordType>, // Provide at least the ID
              meta: params.meta,
            })
          )
        );
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );
        const updatePayload = {
          _a: accountId,
        };
        set(updatePayload, resourceMetaUpdatedBy, currentUserId);
        let payload = {};
        if (coreData._isJustAValue !== undefined && coreData._isJustAValue) {
          // ! Watch out when using this method because coreData needs to contain _isJustAValue field
          // ! to correctly identify that it is a key value pair and not an object
          delete coreData._u;
          delete coreData._e;
          delete coreData._pc;
          payload = coreData;
        } else {
          payload = dataToUpdate;
        }
        set(payload, '_ub', currentUserId);
        ids.forEach(id => {
          set(updatePayload, `${resourceInfo.field}.${id}`, payload);
        });
        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        // we will get the document ref with a converter because
        // while the converter is not triggered when using update inside a transaction
        // it is triggered when using get inside a transaction
        // so we will also get the converter so we can manually convert before update
        const payloadConverter = getFirestoreConverter(resource, true);
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );

        await runTransaction(db, async transaction => {
          const docSnapshot = await transaction.get(docRef);

          if (!docSnapshot.exists()) {
            throw new Error('Document not found');
          }

          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          let payload = {};
          if (coreData._isJustAValue !== undefined && coreData._isJustAValue) {
            // ! Watch out when using this method because coreData needs to contain _isJustAValue field
            // ! to correctly identify that it is a key value pair and not an object
            delete coreData._e;
            delete coreData._pc;
            payload = coreData;
          } else {
            payload = {
              ...coreData,
              _e: eventId,
              _pc: publishOwnChanges,
            };
          }
          set(payload, '_ub', currentUserId);

          // Update matching items
          const updatedArray = arrayData.map((item: any) => {
            if (isObject(item)) {
              if (ids.includes(item.id)) {
                return {
                  ...item,
                  ...payload,
                };
              }
            } else {
              return item;
            }
          });
          const convertedPayload = payloadConverter.toFirestore(updatedArray);
          const updatePayload = {
            _a: accountId,
            [resourceMetaUpdatedBy]: currentUserId,
            [resourceInfo.field]: convertedPayload,
          };
          // Update document
          transaction.update(docRef, updatePayload);
        });
      }

      return { data: ids }; // Return affected ids
    } catch (error) {
      console.error(`FirestoreDP: updateMany ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const deleteOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteParams<RecordType>
  ): Promise<DeleteResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const eventId: number | undefined = params.meta?._eventId;

    const publishOwnChanges = params.meta?._publishOwnChanges ?? false;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      throw new Error(
        `Cannot delete ${resource} - required metadata not available`
      );
    }

    const resourceMetaUpdatedBy = `_${resourceInfo.field}._ub`;
    const currentUserId = auth.currentUser?.uid ?? '@na';

    const recordId = params.id;

    try {
      if (resourceInfo.type === 'collection') {
        const docRef = doc(db, resourcePath, recordId as string).withConverter(
          getFirestoreConverter(resource)
        );
        if (resourceInfo.hasSoftDelete) {
          await setDoc(
            docRef,
            {
              _d: true,
              _u: serverTimestamp(),
              _e: eventId,
              _pc: publishOwnChanges,
              _ub: currentUserId,
            },
            { merge: true }
          );
        } else {
          await deleteDoc(docRef);
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );
        const updatePayload = {
          _a: accountId,
        };
        set(updatePayload, resourceMetaUpdatedBy, currentUserId);
        if (resourceInfo.hasSoftDelete) {
          set(updatePayload, `${resourceInfo.field}.${recordId}`, {
            id: recordId,
            _d: true,
            _u: serverTimestamp(),
            _e: eventId,
            _pc: publishOwnChanges,
            _ub: currentUserId,
          });
        } else {
          // delete the field
          set(
            updatePayload,
            `${resourceInfo.field}.${recordId}`,
            deleteField()
          );
        }
        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        const payloadConverter = getFirestoreConverter(resource, true);
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );

        await runTransaction(db, async transaction => {
          const docSnapshot = await transaction.get(docRef);

          if (!docSnapshot.exists()) {
            throw new Error('Document not found');
          }

          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];
          console.log('FirestoreDP: delete arrayData', arrayData);
          // Filter out the item to delete
          const newArray = arrayData.filter((item: any) => {
            if (isObject(item)) {
              return item.id !== recordId;
            } else {
              return true;
            }
          });
          const convertedPayload = payloadConverter.toFirestore(newArray);
          const updatePayload = {
            _a: accountId,
            [resourceMetaUpdatedBy]: currentUserId,
            [resourceInfo.field]: convertedPayload,
          };

          transaction.update(docRef, updatePayload);
        });
      }
      const previousDataWithId = {
        ...(params.previousData ?? {}),
        id: recordId,
      };
      return { data: previousDataWithId as RecordType };
    } catch (error) {
      console.error(
        `FirestoreDP: delete ERROR [${resource}/${recordId}]:`,
        error
      );
      throw error;
    }
  };

  const deleteMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteManyParams<RecordType>
  ): Promise<DeleteManyResult> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const publishOwnChanges = params.meta?._publishOwnChanges ?? false;

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    if (resourcePath === null) {
      throw new Error(
        `Cannot deleteMany ${resource} - required metadata not available`
      );
    }

    const resourceMetaUpdatedBy = `_${resourceInfo.field}._ub`;
    const currentUserId = auth.currentUser?.uid ?? '@na';

    const ids = params.ids;
    try {
      if (resourceInfo.type === 'collection') {
        await Promise.all(
          ids.map(id =>
            deleteOne<RecordType>(resource, {
              id,
              previousData: undefined, // deleteOne handles undefined previousData
              meta: params.meta,
            })
          )
        );
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );
        const updatePayload = {
          _a: accountId,
        };
        set(updatePayload, resourceMetaUpdatedBy, currentUserId);
        if (resourceInfo.hasSoftDelete) {
          ids.forEach(id => {
            set(updatePayload, `${resourceInfo.field}.${id}`, {
              _d: true,
              _u: serverTimestamp(),
              _e: eventId,
              _pc: publishOwnChanges,
              _ub: currentUserId,
            });
          });
        } else {
          ids.forEach(id => {
            set(updatePayload, `${resourceInfo.field}.${id}`, deleteField());
          });
        }

        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        const payloadConverter = getFirestoreConverter(resource, true);
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource, false)
        );

        await runTransaction(db, async transaction => {
          const docSnapshot = await transaction.get(docRef);

          if (!docSnapshot.exists()) {
            throw new Error('Document not found');
          }

          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          // Filter out items to be deleted
          const filteredArray = arrayData.filter((item: any) => {
            if (isObject(item)) {
              return !ids.includes(item.id);
            } else {
              return true;
            }
          });
          const convertedPayload = payloadConverter.toFirestore(filteredArray);
          const updatePayload = {
            _a: accountId,
            [resourceMetaUpdatedBy]: currentUserId,
            [resourceInfo.field]: convertedPayload,
          };

          transaction.update(docRef, updatePayload);
        });
      }
      // Return the IDs of the deleted records
      return { data: ids };
    } catch (error) {
      console.error(`FirestoreDP: deleteMany ERROR [${resource}]:`, error);
      throw error;
    }
  };

  // Return the provider object
  return {
    getList: getList,
    getOne: getOne,
    getMany: getMany,
    getManyReference: getManyReference,
    create: create,
    update: update,
    updateMany: updateMany,
    delete: deleteOne,
    deleteMany: deleteMany,
    subscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    unsubscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    publish: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    generateFirestoreId: generateFirestoreId,
    getAccountId: () => accountId,
  };
};

export const getFirestoreDataProvider = (
  accountId: string
): FirestoreDataProvider => {
  const basicDataProvider = getBasicFirestoreDataProvider(accountId);
  return withLifecycleCallbacks(basicDataProvider, [
    getOrCreateMemberIdBeforeMemberCreate,
    generateDeviceCodeBeforeDeviceCreate,
  ]) as FirestoreDataProvider;
};
