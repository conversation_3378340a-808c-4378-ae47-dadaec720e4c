/**
 * ConflictAwareInput - Wrapper for react-admin inputs with conflict detection
 *
 * Wraps any react-admin input to provide conflict indicators and
 * remote update highlighting. Works with any input type.
 */

import React from 'react';
import EditIcon from '@mui/icons-material/Edit';
import UpdateIcon from '@mui/icons-material/Update';
import WarningIcon from '@mui/icons-material/Warning';
import {
  Box,
  SxProps,
  Theme,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useOptionalTrackedField } from '../../hooks/useConflictDetection';
import { useResolveUserName } from '../../hooks/useResolveUserName';

/** Generic input props that we support */
interface GenericInputProps {
  source?: string;
  sx?: SxProps<Theme>;
  [key: string]: any;
}

export interface ConflictAwareInputProps {
  /**
   * The react-admin input component to wrap
   */
  children: React.ReactElement<GenericInputProps>;
  /**
   * The source/path for this input (normally extracted from children)
   */
  source?: string;
  /**
   * Whether to show conflict indicator
   * @default true
   */
  showConflictIndicator?: boolean;
  /**
   * Whether to show remote update indicator
   * @default true
   */
  showRemoteUpdateIndicator?: boolean;
  /**
   * Whether to show local changes indicator (when field has local changes AND remote changes)
   * @default true
   */
  showLocalChangesIndicator?: boolean;
  /**
   * Custom styling for the wrapper
   */
  sx?: SxProps<Theme>;
  /**
   * Custom conflict indicator component
   */
  conflictIndicator?: React.ReactNode;
  /**
   * Custom remote update indicator component
   */
  remoteUpdateIndicator?: React.ReactNode;
  /**
   * Custom local changes indicator component
   */
  localChangesIndicator?: React.ReactNode;
  /**
   * Name of user who made the remote update (for tooltip)
   */
  updatedByName?: string;
  /**
   * Timestamp of the remote update (for tooltip)
   */
  updatedAt?: Date | number;
}

/**
 * Wraps a react-admin input with conflict detection awareness.
 * Shows visual indicators when fields are updated remotely or have conflicts.
 *
 * @example
 * ```tsx
 * <ConflictAwareInput>
 *   <TextInput source="name" />
 * </ConflictAwareInput>
 * ```
 *
 * @example With custom indicators
 * ```tsx
 * <ConflictAwareInput
 *   conflictIndicator={<MyCustomConflictBadge />}
 *   showRemoteUpdateIndicator={false}
 * >
 *   <NumberInput source="price" />
 * </ConflictAwareInput>
 * ```
 */
export function ConflictAwareInput({
  children,
  source: sourceProp,
  showConflictIndicator = true,
  showRemoteUpdateIndicator = true,
  showLocalChangesIndicator = true,
  sx,
  conflictIndicator,
  remoteUpdateIndicator,
  localChangesIndicator,
  updatedByName: updatedByNameProp,
  updatedAt: updatedAtProp,
}: ConflictAwareInputProps) {
  // Get source from children props if not provided
  const source = sourceProp ?? children.props.source;

  // Get format function from children props (if available)
  const formatFn = children.props.format as ((value: any) => any) | undefined;

  // Detect if child is CustomInput with ui="custom" mode
  const isCustomUI = children.props.ui === 'custom';

  // Detect if child is specifically a modernBoolean (requires full width)
  const isModernBoolean = children.props.type === 'modernBoolean';

  // Detect if child is fileUpload type
  const isFileUpload = children.props.type === 'fileUpload';

  // Get forceMobileUi prop from children (if available)
  const forceMobileUi = children.props.forceMobileUi ?? false;

  // Get roundedCorners prop from children (if available)
  const roundedCorners = children.props.roundedCorners as
    | 'top'
    | 'bottom'
    | 'both'
    | 'none'
    | undefined;

  if (!source) {
    console.warn('ConflictAwareInput: No source prop found');
    return children;
  }

  // Use optional tracked field (works with or without conflict context)
  const {
    hasConflict,
    wasUpdatedRemotely,
    remoteValue,
    remoteUserId,
    remoteTimestamp,
    isDirty,
  } = useOptionalTrackedField(source);

  // Translation hook
  const { t } = useTranslation();

  // Mobile breakpoint detection
  const isXSmallScreen = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down('sm')
  );

  // Use mobile layout if on small screen OR if forceMobileUi is enabled
  const isXSmall = isXSmallScreen || forceMobileUi;

  // Resolve user name from ID (handles @system, @na, and fetches displayName)
  const resolvedUserName = useResolveUserName(remoteUserId);

  // Use props if provided, otherwise fall back to resolved name
  const updatedByName = updatedByNameProp ?? resolvedUserName;
  const updatedAt = updatedAtProp ?? remoteTimestamp;

  // Determine what indicators to show
  // Priority: conflict > local changes (when also updated remotely) > remote update only
  const showConflict = showConflictIndicator && hasConflict;
  const showLocalChanges =
    showLocalChangesIndicator && isDirty && wasUpdatedRemotely && !hasConflict;
  const showRemoteUpdate =
    showRemoteUpdateIndicator && wasUpdatedRemotely && !hasConflict && !isDirty;

  // Format timestamp for display
  const formatTime = (time?: Date | number) => {
    if (!time) return null;
    const date = time instanceof Date ? time : new Date(time);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Check if value is a primitive (string, number, boolean, null, undefined)
  const isPrimitive = (value: any): boolean => {
    return (
      value === null ||
      value === undefined ||
      typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean'
    );
  };

  // Format value for display - only show primitives
  // Apply the input's format function if available (e.g., for percentage display)
  const formatValue = (value: any): string | null => {
    if (!isPrimitive(value)) return null;
    if (value === null || value === undefined) return t('shared.emptyValue');

    // Apply the input's format function if available
    const displayValue = formatFn ? formatFn(value) : value;
    return String(displayValue);
  };

  const formattedRemoteValue = formatValue(remoteValue);

  // Build rich tooltip content for conflict
  const conflictTooltipContent = (
    <Box sx={{ p: 0.5 }}>
      <Typography
        variant="subtitle2"
        sx={{ fontWeight: 'bold', color: 'error.light' }}
      >
        {t('shared.conflictDetected')}
      </Typography>
      <Typography variant="body2" sx={{ mt: 0.5 }}>
        {t('shared.anotherUserChangedFieldTo')}{' '}
        <strong>"{formattedRemoteValue ?? String(remoteValue)}"</strong>
      </Typography>
      {updatedByName && (
        <Typography
          variant="caption"
          sx={{ display: 'block', mt: 0.5, opacity: 0.9 }}
        >
          {t('shared.updatedBy')}: {updatedByName}
        </Typography>
      )}
      {updatedAt && (
        <Typography variant="caption" sx={{ display: 'block', opacity: 0.9 }}>
          {t('shared.at')}: {formatTime(updatedAt)}
        </Typography>
      )}
      <Typography
        variant="caption"
        sx={{ display: 'block', mt: 0.5, fontStyle: 'italic' }}
      >
        {t('shared.yourChangesConflict')}
      </Typography>
    </Box>
  );

  // Build rich tooltip content for remote update
  const updateTooltipContent = (
    <Box sx={{ p: 0.5 }}>
      <Typography
        variant="subtitle2"
        sx={{ fontWeight: 'bold', color: 'info.light' }}
      >
        🔄 {t('shared.remoteUpdate')}
      </Typography>
      <Typography variant="body2" sx={{ mt: 0.5, color: 'info.contrastText' }}>
        {t('shared.fieldUpdatedByAnotherUser')}
      </Typography>
      {(updatedByName || updatedAt) && (
        <Box
          sx={{
            mt: 1,
            p: 1,
            bgcolor: 'rgba(0,0,0,0.25)',
            borderRadius: 1,
            border: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.4)',
          }}
        >
          {updatedByName && (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.85 }}
            >
              👤 {t('shared.changedBy')} <strong>{updatedByName}</strong>
            </Typography>
          )}
          {updatedAt && (
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                opacity: 0.85,
                mt: updatedByName ? 0.5 : 0,
              }}
            >
              🕒 {t('shared.at')}: {formatTime(updatedAt)}
            </Typography>
          )}
        </Box>
      )}
      <Typography
        variant="caption"
        sx={{
          display: 'block',
          mt: 1,
          pt: 1,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          fontStyle: 'italic',
          opacity: 0.9,
        }}
      >
        ✅ {t('shared.changeAppliedAutomatically')}
      </Typography>
    </Box>
  );

  // Build rich tooltip content for local changes (when also updated remotely)
  const localChangesTooltipContent = (
    <Box sx={{ p: 0.5 }}>
      <Typography
        variant="subtitle2"
        sx={{ fontWeight: 'bold', color: 'warning.light' }}
      >
        ✏️ {t('shared.youHaveUnsavedChanges')}
      </Typography>
      <Typography
        variant="body2"
        sx={{ mt: 0.5, color: 'warning.contrastText' }}
      >
        {t('shared.fieldContainsYourEdits')}
      </Typography>
      {remoteValue !== undefined && (
        <Box
          sx={{
            mt: 1,
            p: 1,
            bgcolor: 'rgba(0,0,0,0.25)',
            borderRadius: 1,
            border: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.4)',
          }}
        >
          {formattedRemoteValue !== null ? (
            <>
              <Typography
                variant="caption"
                sx={{ display: 'block', opacity: 0.9, mb: 0.5 }}
              >
                {t('shared.meanwhileAnotherUserChangedTo')}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: 'monospace',
                  wordBreak: 'break-word',
                  color: 'warning.contrastText',
                  fontWeight: 500,
                }}
              >
                {formattedRemoteValue}
              </Typography>
            </>
          ) : (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.9, fontStyle: 'italic' }}
            >
              {t('shared.meanwhileAnotherUserUpdatedComplex')}
            </Typography>
          )}
          {updatedByName && (
            <Typography
              variant="caption"
              sx={{ display: 'block', mt: 1, opacity: 0.85 }}
            >
              👤 {t('shared.changedBy')} <strong>{updatedByName}</strong>
            </Typography>
          )}
          {updatedAt && (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.85 }}
            >
              🕒 {t('shared.at')}: {formatTime(updatedAt)}
            </Typography>
          )}
        </Box>
      )}
      <Typography
        variant="caption"
        sx={{
          display: 'block',
          mt: 1,
          pt: 1,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          fontStyle: 'italic',
          opacity: 0.9,
        }}
      >
        💾 {t('shared.yourVersionWillBeSaved')}
      </Typography>
    </Box>
  );

  // Default conflict indicator
  const defaultConflictIndicator = (
    <Tooltip
      title={conflictTooltipContent}
      placement="top"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: 'error.dark',
            '& .MuiTooltip-arrow': { color: 'error.dark' },
            maxWidth: 280,
          },
        },
      }}
    >
      <WarningIcon
        sx={{
          color: 'error.main',
          fontSize: 20,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))',
        }}
      />
    </Tooltip>
  );

  // Default remote update indicator
  const defaultRemoteUpdateIndicator = (
    <Tooltip
      title={updateTooltipContent}
      placement="top"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: '#1565c0', // Deeper blue for better contrast
            '& .MuiTooltip-arrow': { color: '#1565c0' },
            maxWidth: 320,
            boxShadow: '0 4px 20px rgba(21, 101, 192, 0.3)',
          },
        },
      }}
    >
      <UpdateIcon
        sx={{
          color: 'info.main',
          fontSize: 22,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))',
        }}
      />
    </Tooltip>
  );

  // Default local changes indicator (orange/warning color)
  const defaultLocalChangesIndicator = (
    <Tooltip
      title={localChangesTooltipContent}
      placement="top"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: '#e65100', // Deep orange for better contrast
            '& .MuiTooltip-arrow': { color: '#e65100' },
            maxWidth: 320,
            boxShadow: '0 4px 20px rgba(230, 81, 0, 0.3)',
          },
        },
      }}
    >
      <EditIcon
        sx={{
          color: 'rgba(255, 152, 0, 0.7)', // Same as the border highlight color
          fontSize: 20,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))',
        }}
      />
    </Tooltip>
  );

  // If no indicators needed, return unchanged
  if (!showConflict && !showRemoteUpdate && !showLocalChanges) {
    return children;
  }

  // Color for the highlight
  // Red for conflicts, orange for local changes, blue for remote updates
  const highlightColor = showConflict
    ? 'rgb(244, 67, 54)' // red
    : showLocalChanges
      ? 'rgb(255, 152, 0)' // orange
      : 'rgb(33, 150, 243)'; // blue

  // Calculate border radius based on roundedCorners prop
  // For CustomInput with ui="custom":
  // - Mobile: Remove TOP corners (label has them)
  // - Desktop: Remove LEFT corners (label has them)
  const getBorderRadiusStyles = () => {
    if (!roundedCorners || roundedCorners === 'none') return {};
    const radius = '6px'; // Standard app-wide border radius

    // For CustomInput in custom UI mode, adjust corners based on layout
    if (isCustomUI) {
      if (isXSmall) {
        // Mobile: Label is on top, so input section only gets bottom corners
        switch (roundedCorners) {
          case 'bottom':
          case 'both':
            return {
              borderBottomLeftRadius: radius,
              borderBottomRightRadius: radius,
            };
          case 'top':
          default:
            return {};
        }
      } else {
        // Desktop: Label is on left, so input section only gets right corners
        switch (roundedCorners) {
          case 'top':
            return {
              borderTopRightRadius: radius,
            };
          case 'bottom':
            return {
              borderBottomRightRadius: radius,
            };
          case 'both':
            return {
              borderTopRightRadius: radius,
              borderBottomRightRadius: radius,
            };
          default:
            return {};
        }
      }
    }

    // For other inputs (PopoverInput, original UI), apply full rounded corners
    switch (roundedCorners) {
      case 'top':
        return {
          borderTopLeftRadius: radius,
          borderTopRightRadius: radius,
        };
      case 'bottom':
        return {
          borderBottomLeftRadius: radius,
          borderBottomRightRadius: radius,
        };
      case 'both':
        return {
          borderRadius: radius,
        };
      default:
        return {};
    }
  };

  const borderRadiusStyles = getBorderRadiusStyles();

  // Wrap with visual indicator - target the actual input elements inside
  return (
    <Box
      sx={{
        position: 'relative',
        width: isModernBoolean ? '100%' : undefined,
        overflow: 'visible', // Ensure conflict badge is visible even in containers with overflow:hidden
        // Target MUI outlined input fieldset (the actual border element) - for ui="original"
        // Only apply to direct descendants (not nested inside another Box)
        '& > .MuiFormControl-root .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline':
          {
            borderColor: `${highlightColor} !important`,
            borderWidth: '2px !important',
          },
        // Also target focused state to maintain color
        '& > .MuiFormControl-root .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline':
          {
            borderColor: `${highlightColor} !important`,
            borderWidth: '2px !important',
          },
        // Target Autocomplete inputs - more generic selector to catch all autocomplete structures
        '& .MuiAutocomplete-root .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline':
          {
            borderColor: `${highlightColor} !important`,
            borderWidth: '2px !important',
          },
        '& .MuiAutocomplete-root .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline':
          {
            borderColor: `${highlightColor} !important`,
            borderWidth: '2px !important',
          },
        // Target MUI filled input
        '& .MuiFilledInput-root': {
          borderBottom: `2px solid ${highlightColor} !important`,
        },
        // Target standard input
        '& .MuiInput-root:before': {
          borderBottom: `2px solid ${highlightColor} !important`,
        },
        // For CustomInput ui="custom": Target FormControl inside CustomInput's wrapper Box
        // The structure is: ConflictAwareInput > CustomInput's Box > FormControl
        // On desktop (non-mobile), only the input section (right side) gets rounded corners
        // On mobile, the entire box is stacked vertically and gets full rounded corners
        '& > .MuiBox-root .MuiFormControl-root': {
          boxShadow: `inset 0 0 0 2px ${highlightColor}`,
          ...borderRadiusStyles,
          // Hide the notched outline border since CustomInput already has outer border
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
          },
        },
        // For PopoverInput: Target the root Box with data-popover-input attribute
        '& [data-popover-input]': {
          boxShadow: `inset 0 0 0 2px ${highlightColor}`,
          ...borderRadiusStyles,
        },
        // For modernBoolean: Add outer ring around the switch pill with 2px gap
        ...(isModernBoolean && {
          '& .MuiSwitch-root': {
            overflow: 'visible',
          },
          '& .MuiSwitch-track': {
            outline: `2px solid ${highlightColor}`,
            outlineOffset: '2px',
            boxSizing: 'border-box',
          },
        }),
        // For FileUpload (original UI, default variant): Add border around entire component
        ...(isFileUpload && {
          border: `2px solid ${highlightColor}`,
          borderRadius: '6px',
          padding: '0px', // No extra padding needed
        }),
        // Ensure the label doesn't get covered
        '& .MuiInputLabel-root': {
          zIndex: 1,
        },
        ...sx,
      }}
    >
      {/* Indicator icon positioned at top-right, outside the flow */}
      <Box
        sx={{
          position: 'absolute',
          // CustomInput with custom UI on mobile: position on top of input section (below label)
          // CustomInput with custom UI on desktop: top-right corner
          // modernBoolean: always top-aligned (flex-start), so badge goes to default top position
          // FileUpload: position at top-right of entire wrapper
          // Original UI: slightly above to align with input
          top: isModernBoolean
            ? { xs: -12, sm: -10 }
            : isFileUpload
              ? -10
              : isCustomUI && isXSmall && !isModernBoolean
                ? '26px'
                : -10,
          marginTop: undefined,
          right: -10,
          zIndex: 10,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: 24,
          height: 24,
          backgroundColor: 'background.paper',
          borderRadius: '50%',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
        }}
      >
        {showConflict && (conflictIndicator ?? defaultConflictIndicator)}
        {showLocalChanges &&
          (localChangesIndicator ?? defaultLocalChangesIndicator)}
        {showRemoteUpdate &&
          (remoteUpdateIndicator ?? defaultRemoteUpdateIndicator)}
      </Box>

      {/* Original input - unchanged */}
      {children}
    </Box>
  );
}

/**
 * Higher-order component to make any input conflict-aware.
 *
 * @example
 * ```tsx
 * const ConflictAwareTextInput = withConflictAwareness(TextInput);
 *
 * // Usage
 * <ConflictAwareTextInput source="name" />
 * ```
 */
export function withConflictAwareness<P extends GenericInputProps>(
  WrappedComponent: React.ComponentType<P>
) {
  return function ConflictAwareWrappedInput(
    props: P & Partial<Omit<ConflictAwareInputProps, 'children'>>
  ) {
    const {
      showConflictIndicator,
      showRemoteUpdateIndicator,
      showLocalChangesIndicator,
      conflictIndicator,
      remoteUpdateIndicator,
      localChangesIndicator,
      ...inputProps
    } = props;

    return (
      <ConflictAwareInput
        showConflictIndicator={showConflictIndicator}
        showRemoteUpdateIndicator={showRemoteUpdateIndicator}
        showLocalChangesIndicator={showLocalChangesIndicator}
        conflictIndicator={conflictIndicator}
        remoteUpdateIndicator={remoteUpdateIndicator}
        localChangesIndicator={localChangesIndicator}
      >
        <WrappedComponent {...(inputProps as P)} />
      </ConflictAwareInput>
    );
  };
}

export default ConflictAwareInput;
