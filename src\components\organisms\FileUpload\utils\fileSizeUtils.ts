import { getMetadata, ref } from 'firebase/storage';

import { PrivateFileContext, UploadedFile } from '../types/fileUpload';
import { getStorageInstanceForFile } from './bucketManager';
import {
  getFileSizeInfo,
  hasFileSizeInfo as resolverHasFileSizeInfo,
} from './fileMetadataResolver';
import { generateFileUrl, getDestinationPath } from './urlGeneration';

/**
 * Cache for file sizes to avoid repeated requests
 * Enhanced with localStorage persistence for cross-session caching
 */
const fileSizeCache = new Map<string, number>();
const cacheTTL = 30 * 60 * 1000; // 30 minutes
const cacheTimestamps = new Map<string, number>();

// Cache for browser-retrieved variant sizes
const browserSizeCache = new Map<string, number>();
const browserCacheTimestamps = new Map<string, number>();
const BROWSER_CACHE_TTL = 60 * 60 * 1000; // 1 hour for browser cache

// LocalStorage keys for persistent caching
// File size cache configuration
const CACHE_STORAGE_KEY = 'selio_file_sizes';
const CACHE_DURATION = 1000 * 60 * 60; // 1 hour
const MAX_CACHE_ENTRIES = 500;
const CACHE_TIMESTAMPS_KEY = 'selio_file_size_timestamps';

/**
 * Load cache from localStorage on initialization
 */
const loadCacheFromStorage = (): void => {
  if (typeof window === 'undefined') return;

  try {
    const cachedSizes = localStorage.getItem(CACHE_STORAGE_KEY);
    const cachedTimestamps = localStorage.getItem(CACHE_TIMESTAMPS_KEY);

    if (cachedSizes && cachedTimestamps) {
      const sizes = JSON.parse(cachedSizes);
      const timestamps = JSON.parse(cachedTimestamps);

      // Only load entries that haven't expired
      const now = Date.now();
      Object.entries(timestamps).forEach(([key, timestamp]) => {
        if (now - (timestamp as number) < cacheTTL && sizes[key]) {
          fileSizeCache.set(key, sizes[key]);
          cacheTimestamps.set(key, timestamp as number);
        }
      });
    }
  } catch (error) {
    // Failed to load cache from localStorage
  }
};

/**
 * Save cache to localStorage
 */
const saveCacheToStorage = (): void => {
  if (typeof window === 'undefined') return;

  try {
    const sizes = Object.fromEntries(fileSizeCache);
    const timestamps = Object.fromEntries(cacheTimestamps);

    localStorage.setItem(CACHE_STORAGE_KEY, JSON.stringify(sizes));
    localStorage.setItem(CACHE_TIMESTAMPS_KEY, JSON.stringify(timestamps));
  } catch (error) {
    // Failed to save cache to localStorage
  }
};

// Load cache on module initialization
loadCacheFromStorage();

/**
 * Format bytes to human readable format
 */
export function formatFileSize(bytes: number, precision: number = 2): string {
  if (bytes === 0) return '0 B';
  if (bytes < 0) return 'Unknown';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  const value = bytes / Math.pow(k, i);
  const formatted = i === 0 ? value.toString() : value.toFixed(precision);

  return `${formatted} ${sizes[i]}`;
}

/**
 * Generate cache key for a file
 */
const getFileSizeKey = (file: UploadedFile): string => {
  return `${file.f}.${file.e}${file.x ? '_temp' : ''}`;
};

/**
 * Check if cached size is still valid
 */
const isCacheValid = (key: string): boolean => {
  const timestamp = cacheTimestamps.get(key);
  if (!timestamp) return false;
  return Date.now() - timestamp < cacheTTL;
};

/**
 * Cache file size with timestamp and persist to localStorage
 */
const cacheFileSize = (key: string, size: number): void => {
  fileSizeCache.set(key, size);
  cacheTimestamps.set(key, Date.now());

  // Debounced save to localStorage to avoid excessive writes
  debouncedSaveToStorage();
};

// Debounced save function to avoid excessive localStorage writes
let saveTimeout: NodeJS.Timeout | null = null;
const debouncedSaveToStorage = (): void => {
  if (saveTimeout) clearTimeout(saveTimeout);
  saveTimeout = setTimeout(() => {
    saveCacheToStorage();
  }, 500);
};

/**
 * Get file size for uploaded file (with caching and Firebase storage support)
 */
export const getFileSize = async (
  file: UploadedFile,
  context?: PrivateFileContext
): Promise<number> => {
  const cacheKey = getFileSizeKey(file);

  // For in-memory files, get size directly from metadata
  if (file.inMemoryData) {
    return file.inMemoryData.metadata.fileSize;
  }

  // Check cache first
  if (isCacheValid(cacheKey)) {
    const cachedSize = fileSizeCache.get(cacheKey);
    if (cachedSize !== undefined) {
      return cachedSize;
    }
  }

  try {
    let fileRef;

    if (file.x) {
      // Temporary file - use temp bucket
      const storage = getStorageInstanceForFile(file.t, true);
      fileRef = ref(storage, `${file.f}.${file.e}`);
    } else {
      // Permanent file - use destination path logic
      const storage = getStorageInstanceForFile(file.t, false);
      const filePath = getDestinationPath(file, context);
      fileRef = ref(storage, filePath);
    }

    const metadata = await getMetadata(fileRef);
    const size = metadata.size;

    // Cache the result
    cacheFileSize(cacheKey, size);

    return size;
  } catch (error) {
    // Fallback: try HTTP HEAD request if we can generate a URL
    try {
      const url = await generateFileUrl(file, { context });
      return await getFileSizeFromUrl(url);
    } catch (urlError) {
      throw new Error('Unable to determine file size');
    }
  }
};

/**
 * Get file size from URL using HEAD request
 */
const getFileSizeFromUrl = async (url: string): Promise<number> => {
  if (url.startsWith('blob:')) {
    throw new Error('Cannot get file size from blob URL using HEAD request');
  }

  try {
    const response = await fetch(url, { method: 'HEAD' });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentLength = response.headers.get('content-length');
    if (!contentLength) {
      throw new Error('Content-Length header not found');
    }

    return parseInt(contentLength, 10);
  } catch (error) {
    throw error;
  }
};

/**
 * Batch file size loading for improved performance
 */
export const getBatchFileSizes = async (
  files: UploadedFile[],
  context?: PrivateFileContext
): Promise<Map<string, number>> => {
  const results = new Map<string, number>();
  const filesToLoad: UploadedFile[] = [];

  // First pass: check cache and in-memory data for all files
  for (const file of files) {
    const cacheKey = getFileSizeKey(file);

    // For in-memory files, get size directly from metadata
    if (file.inMemoryData) {
      results.set(cacheKey, file.inMemoryData.metadata.fileSize);
      continue;
    }

    // Check cache for non-in-memory files
    if (isCacheValid(cacheKey)) {
      const cachedSize = fileSizeCache.get(cacheKey);
      if (cachedSize !== undefined) {
        results.set(cacheKey, cachedSize);
        continue;
      }
    }

    filesToLoad.push(file);
  }

  // Second pass: load uncached files in parallel
  if (filesToLoad.length > 0) {
    const loadPromises = filesToLoad.map(async file => {
      try {
        const size = await getFileSize(file, context);
        const cacheKey = getFileSizeKey(file);
        results.set(cacheKey, size);
        return { file, size };
      } catch (error) {
        console.warn(
          `Failed to load size for file ${file.f}.${file.e}:`,
          error
        );
        return { file, size: null };
      }
    });

    await Promise.all(loadPromises);
  }

  return results;
};

/**
 * Get variant file size (for images with generated variants)
 */
export const getVariantFileSize = async (
  file: UploadedFile,
  variantKey: string,
  context?: PrivateFileContext
): Promise<number | null> => {
  // For in-memory files with variants
  if (file.inMemoryData?.metadata.imageMetadata?.variants) {
    const variant = file.inMemoryData.metadata.imageMetadata.variants.find(
      v => v.key === variantKey
    );
    return variant ? variant.size : null;
  }

  // For permanent files with variants, try to get size from browser first
  if (!file.x && file.t === 'i') {
    try {
      // First, try to get the size from the browser if the file is already loaded
      const variantExtension = variantKey === 'original' ? file.e : 'webp';
      const variantPath = `i/${file.f}/${variantKey}.${variantExtension}`;

      // Check if we can get the size from a loaded image in the DOM
      const sizeFromBrowser = await getSizeFromLoadedImage(file, variantKey);
      if (sizeFromBrowser !== null) {
        return sizeFromBrowser;
      }

      // If not available from browser and not original, fetch from Firebase Storage metadata
      if (variantKey !== 'original') {
        const storage = getStorageInstanceForFile(file.t, false);
        const variantRef = ref(storage, variantPath);
        const metadata = await getMetadata(variantRef);

        return metadata.size || null;
      }
    } catch (error) {
      // If variant doesn't exist or error occurred, return null
      return null;
    }
  }

  // For original variant of permanent files or other cases, fall back to standard size
  return null;
};

/**
 * Try to get file size from browser's loaded resources or via HEAD request
 * This avoids additional requests to Firebase Storage when possible
 */
const getSizeFromLoadedImage = async (
  file: UploadedFile,
  variantKey: string
): Promise<number | null> => {
  const cacheKey = `${file.f}_${variantKey}_browser`;

  // Check browser cache first
  const cachedSize = browserSizeCache.get(cacheKey);
  const cacheTime = browserCacheTimestamps.get(cacheKey);

  if (cachedSize && cacheTime && Date.now() - cacheTime < BROWSER_CACHE_TTL) {
    return cachedSize;
  }

  try {
    // Generate the expected URL for this variant
    const { generateVariantUrl } = await import('./imageVariants');
    const { generateFileUrl } = await import('./urlGeneration');

    const expectedUrl =
      variantKey === 'original'
        ? await generateFileUrl(file)
        : await generateVariantUrl(file, variantKey);

    // Use HEAD request to get content-length without downloading the file
    // This works well with CDN URLs and is much faster than Firebase Storage metadata requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      const response = await fetch(expectedUrl, {
        method: 'HEAD',
        cache: 'default', // Use browser cache if the resource was already loaded
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const contentLength = response.headers.get('content-length');
        if (contentLength) {
          const size = parseInt(contentLength, 10);

          // Cache the result
          browserSizeCache.set(cacheKey, size);
          browserCacheTimestamps.set(cacheKey, Date.now());

          // Clean up old cache entries
          if (browserSizeCache.size > 100) {
            const oldestKey = Array.from(browserCacheTimestamps.entries()).sort(
              ([, a], [, b]) => a - b
            )[0][0];
            browserSizeCache.delete(oldestKey);
            browserCacheTimestamps.delete(oldestKey);
          }

          return size;
        }
      }
    } finally {
      clearTimeout(timeoutId);
    }
  } catch (error) {
    // Silently fail and fall back to Firebase Storage metadata
    // This includes timeout errors, network errors, etc.
  }

  return null;
};

/**
 * Get file size information from uploaded file (synchronous version for immediate data)
 */
export function getFileSizes(file: UploadedFile): {
  original: number | null;
  variants: number | null;
} {
  // For temporary files, get sizes from in-memory data
  if (file.x && file.inMemoryData) {
    const originalSize = file.inMemoryData.metadata.fileSize;

    // Calculate variants size from image metadata
    let variantsSize = 0;
    if (file.inMemoryData.metadata.imageMetadata?.variants) {
      variantsSize = file.inMemoryData.metadata.imageMetadata.variants.reduce(
        (total, variant) => total + variant.size,
        0
      );
    }

    return {
      original: originalSize,
      variants: variantsSize > 0 ? variantsSize : null,
    };
  }

  // For permanent files, get sizes from metadata (new uploads)
  if (file.sizes) {
    return {
      original: file.sizes.original,
      variants: file.sizes.variants || null,
    };
  }

  // No immediate size information available
  return {
    original: null,
    variants: null,
  };
}

/**
 * Clear expired cache entries from localStorage
 */
const clearExpiredCache = (): void => {
  try {
    const cached = localStorage.getItem(CACHE_STORAGE_KEY);
    if (!cached) return;

    const data = JSON.parse(cached);
    const now = Date.now();
    const validEntries: Record<string, any> = {};

    for (const [key, entry] of Object.entries(data)) {
      if (typeof entry === 'object' && entry !== null) {
        const typedEntry = entry as { size: number; timestamp: number };
        if (now - typedEntry.timestamp < CACHE_DURATION) {
          validEntries[key] = entry;
        }
      }
    }

    localStorage.setItem(CACHE_STORAGE_KEY, JSON.stringify(validEntries));
  } catch (error) {
    console.warn('Failed to clear expired cache:', error);
    // Clear all cache on error
    localStorage.removeItem(CACHE_STORAGE_KEY);
  }
};

/**
 * Validate cache consistency and clear if corrupted
 */
const validateCache = (): void => {
  try {
    const cached = localStorage.getItem(CACHE_STORAGE_KEY);
    if (!cached) return;

    const data = JSON.parse(cached);
    if (typeof data !== 'object' || data === null) {
      throw new Error('Invalid cache format');
    }

    // Validate each entry structure
    for (const [key, entry] of Object.entries(data)) {
      if (typeof entry !== 'object' || entry === null) {
        throw new Error(`Invalid entry format for key: ${key}`);
      }

      const typedEntry = entry as any;
      if (
        typeof typedEntry.size !== 'number' ||
        typeof typedEntry.timestamp !== 'number'
      ) {
        throw new Error(`Invalid entry data for key: ${key}`);
      }
    }
  } catch (error) {
    console.warn('Cache validation failed, clearing cache:', error);
    localStorage.removeItem(CACHE_STORAGE_KEY);
    fileSizeCache.clear();
  }
};

// Initialize cache validation
validateCache();

/**
 * Get file size information from uploaded file (async version for permanent files)
 */
export async function getFileSizesAsync(
  file: UploadedFile,
  context?: PrivateFileContext
): Promise<{
  original: number | null;
  variants: number | null;
}> {
  return getFileSizeInfo(file, context);
}

/**
 * Check if file has size information to display (synchronous check for immediate data)
 */
export function hasFileSizeInfo(file: UploadedFile): boolean {
  const sizes = getFileSizes(file);
  return sizes.original !== null;
}

/**
 * Check if file has size information available (includes cached data)
 */
export function hasAnyFileSizeInfo(file: UploadedFile): boolean {
  return resolverHasFileSizeInfo(file);
}

/**
 * Fetch file size information for permanent files from storage and cache it
 * This is an async operation that can be used to populate size data on demand
 */
export async function fetchAndCacheFileSizeInfo(
  file: UploadedFile,
  context?: PrivateFileContext
): Promise<{
  original: number | null;
  variants: number | null;
} | null> {
  // Only fetch for permanent files that don't already have immediate size info
  if ((file.x && file.inMemoryData) || file.sizes) {
    return null; // Already has immediate data
  }

  try {
    const sizeInfo = await getFileSizeInfo(file, context);
    return sizeInfo;
  } catch (error) {
    console.warn('Failed to fetch file size information:', error);
    return null;
  }
}
