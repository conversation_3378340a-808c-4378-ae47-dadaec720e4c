import { useEffect, useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { groupReport } from '~/fake-provider/reports/groupReport';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';

type TableRow = {
  member: string;
  payments: {
    [key: string]: number;
  };
  comps: {
    [key: string]: number;
  };
  netSalesValue: number;
};

export default function ReportTeamRevenueTable({
  tableData,
  uniqueFieldsPayments,
  uniqueFieldsComps,
}: {
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
  uniqueFieldsPayments: any[];
  uniqueFieldsComps: any[];
}) {
  console.log(tableData);

  const fieldsConstant = useMemo(
    () => [
      { isChecked: true, value: 'netSalesValue' },
      { isChecked: true, value: 'member' },
      ...uniqueFieldsPayments.map((field: any) => ({
        isChecked: true,
        value: field.value,
      })),
      ...uniqueFieldsComps.map((field: any) => ({
        isChecked: false,
        value: field.value,
      })),
      { isChecked: true, value: 'total' },
    ],
    [uniqueFieldsPayments, uniqueFieldsComps]
  );

  const [fields, setFields] = useState<FieldOption[]>(fieldsConstant);

  useEffect(() => {
    setFields(fieldsConstant);
  }, [fieldsConstant]);

  const reportTeamRevenueData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];

      const totalItemsData: TableRow = {
        member: 'Total',
        payments: {},
        comps: {},
        netSalesValue: 0,
      };

      uniqueFieldsPayments.forEach(field => {
        totalItemsData.payments[field.value] = mappedTableData.reduce(
          (sum, row) => sum + (row.payments?.[field.value] || 0),
          0
        );
      });

      uniqueFieldsComps.forEach(field => {
        totalItemsData.comps[field.value] = mappedTableData.reduce(
          (sum, row) => sum + (row.comps?.[field.value] || 0),
          0
        );
      });

      totalItemsData.netSalesValue = mappedTableData.reduce(
        (sum, row) => sum + (row.netSalesValue || 0),
        0
      );

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }

    return [];
  }, [tableData]);

  const { t } = useTranslation();

  const reportTeamRevenueConfig: any[] = useMemo(
    () => [
      {
        id: 'member',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                // @ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {row.member}
            </div>
          );
        },
        label: t('tips.teamMember'),
        textAlign: 'start',
      },
      {
        id: 'netSalesValue',
        label: t('shared.netSales'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(row.netSalesValue)}</>;
        },
      },
      ...uniqueFieldsPayments.map((field: any) => ({
        id: field.value,
        label: t(`paymentMethods.${field.value}`),
        textAlign: 'end',
        render: (row: any) => {
          return <>{formatAndDivideNumber(row.payments?.[field.value] || 0)}</>;
        },
      })),
      ...uniqueFieldsComps.map((field: any) => ({
        id: field.value,
        label: field.value,
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(row.comps?.[field.value])}</>;
        },
      })),
      {
        id: 'total',
        label: 'Total',
        textAlign: 'end',
        render: (row: TableRow) => {
          const total = fields.reduce((sum, field) => {
            if (!field.isChecked) return sum;

            if (
              uniqueFieldsPayments.some(
                paymentField => paymentField.value === field.value
              )
            ) {
              return sum + (row.payments?.[field.value] || 0);
            }

            if (
              uniqueFieldsComps.some(
                compField => compField.value === field.value
              )
            ) {
              return sum + (row.comps?.[field.value] || 0);
            }

            if (field.value === 'netSalesValue' && field.isChecked) {
              return sum + (row.netSalesValue || 0);
            }

            return sum;
          }, 0);

          return <>{formatAndDivideNumber(total)}</>;
        },
      },
    ],
    [fields]
  );

  const columnsToFilter = [
    'netSalesValue',
    ...uniqueFieldsPayments.map((field: any) => field.value),
    ...uniqueFieldsComps.map((field: any) => field.value),
    'total',
  ];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <CustomTable
          filter={true}
          greyLastRow={true}
          fields={fields}
          setFields={setFields}
          fixedFirstColumn={true}
          maxWidthFirstColumn="200px"
          columnsToFilter={columnsToFilter}
          config={reportTeamRevenueConfig}
          data={reportTeamRevenueData}
          fixLastRow={true}
          alignLastColumnRight={false}
        />
      </Box>
    </>
  );
}
