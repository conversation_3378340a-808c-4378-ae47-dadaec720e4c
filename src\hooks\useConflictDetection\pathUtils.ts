/**
 * Path Utilities
 *
 * Functions for parsing and manipulating JSON paths,
 * and resolving paths to their containing entities.
 */

import {
  getEffectiveIdField,
  getValueAtPath,
  isPlainObject,
  parsePath,
  pathMatchesAnyPattern,
} from './entityRegistry';
import { TrackerState } from './types';

/**
 * Given a full path (e.g., "pages[0].items[2].price"), find the deepest entity
 * and return the entityId, the path to that entity, and the remaining field path.
 *
 * @param fullPath - Full JSON path to parse
 * @param data - Data object to traverse
 * @param idField - Default ID field (default: 'id')
 * @param arrayIdFields - Path-specific ID fields for arrays (e.g., { 'items': 'number' })
 */
export function parsePathToEntity(
  fullPath: string,
  data: any,
  idField: string = 'id',
  arrayIdFields: Record<string, string> = {},
  referenceArrayPaths: string[] = []
): {
  entityId: string | null;
  entityPath: string | null;
  fieldPath: string;
} {
  if (!data || !fullPath) {
    return { entityId: 'ROOT', entityPath: null, fieldPath: fullPath };
  }

  // Check if root data is an entity
  const rootIsEntity = isPlainObject(data) && data[idField] !== undefined;
  const rootEntityId = rootIsEntity ? String(data[idField]) : 'ROOT';

  const segments = parsePath(fullPath);
  let currentObj = data;
  let currentPath = '';
  let lastEntityId: string | null = rootIsEntity ? rootEntityId : null;
  let lastEntityPath: string | null = rootIsEntity ? '' : null;
  let currentArrayPath = ''; // Track the array path for determining ID field

  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];

    // Build path to this point
    if (segment.type === 'index') {
      currentPath += `[${segment.value}]`;
      currentObj = currentObj?.[segment.value];
    } else {
      // When we hit a property, that might be an array name
      currentArrayPath = currentPath
        ? `${currentPath}.${String(segment.value)}`
        : String(segment.value);
      currentPath += currentPath
        ? `.${String(segment.value)}`
        : String(segment.value);
      currentObj = currentObj?.[segment.value];
    }

    // Check if current object is an entity
    if (currentObj && isPlainObject(currentObj)) {
      const inReferenceArray =
        segment.type === 'index' &&
        pathMatchesAnyPattern(currentArrayPath, referenceArrayPaths);

      // Determine the effective ID field for this position
      // If we just traversed into an array item, use the array's ID field
      const effectiveField =
        segment.type === 'index'
          ? getEffectiveIdField(currentArrayPath, idField, arrayIdFields)
          : idField;

      if (!inReferenceArray && currentObj[effectiveField] !== undefined) {
        // For non-standard ID fields, prefix the entity ID
        const entityIdValue = String(currentObj[effectiveField]);
        lastEntityId =
          effectiveField !== idField
            ? `${effectiveField}:${entityIdValue}`
            : entityIdValue;
        lastEntityPath = currentPath;
      }
    }
  }

  if (lastEntityId && lastEntityPath !== null) {
    // Calculate remaining field path after the entity
    let fieldPath = fullPath;
    if (lastEntityPath !== '') {
      fieldPath = fullPath.slice(lastEntityPath.length);
      // Remove leading dot if present
      if (fieldPath.startsWith('.')) {
        fieldPath = fieldPath.slice(1);
      }
    }
    return { entityId: lastEntityId, entityPath: lastEntityPath, fieldPath };
  }

  // No entity in path - treat as root-level field
  return { entityId: rootEntityId, entityPath: null, fieldPath: fullPath };
}

/**
 * Build full path from entity path and field path.
 */
export function buildFullPath(
  entityPath: string | null,
  fieldPath: string
): string {
  if (!entityPath) return fieldPath;
  if (!fieldPath) return entityPath;
  return `${entityPath}.${fieldPath}`;
}

/**
 * Get the parent path from a full path.
 * "pages[0].items[2].price" → "pages[0].items[2]"
 * "pages[0].items[2]" → "pages[0].items"
 * "pages[0]" → "pages"
 * "pages" → ""
 */
export function getParentPath(path: string): string {
  if (!path) return '';

  // Handle array index at end
  const lastBracket = path.lastIndexOf('[');
  const lastDot = path.lastIndexOf('.');

  if (lastBracket > lastDot) {
    // Path ends with array index
    return path.slice(0, lastBracket);
  } else if (lastDot > 0) {
    // Path ends with property
    return path.slice(0, lastDot);
  }

  return '';
}

/**
 * Get the last segment of a path.
 * "pages[0].items[2].price" → "price"
 * "pages[0].items[2]" → "[2]"
 */
export function getLastPathSegment(path: string): string {
  if (!path) return '';

  const lastBracket = path.lastIndexOf('[');
  const lastDot = path.lastIndexOf('.');

  if (lastBracket > lastDot) {
    return path.slice(lastBracket);
  } else if (lastDot >= 0) {
    return path.slice(lastDot + 1);
  }

  return path;
}

/**
 * Check if a path is a child of another path.
 * "pages[0].items[2].price" is child of "pages[0].items[2]"
 * "pages[0].items[2]" is child of "pages[0].items"
 */
export function isChildPath(childPath: string, parentPath: string): boolean {
  if (!parentPath) return true;
  if (!childPath) return false;
  return (
    childPath.startsWith(parentPath) && childPath.length > parentPath.length
  );
}

/**
 * Normalize a path for comparison.
 * Ensures consistent formatting.
 */
export function normalizePath(path: string): string {
  // Already normalized by parsePath/segmentsToPath
  return path;
}

/**
 * Extract array index from a path segment like "[2]".
 * Returns null if not an array index.
 */
export function extractArrayIndex(segment: string): number | null {
  const match = segment.match(/^\[(\d+)\]$/);
  if (match) {
    return parseInt(match[1], 10);
  }
  return null;
}

/**
 * Find the entity that contains a given path.
 * Returns the entity ID and the path within that entity.
 */
export function findContainingEntity(
  fullPath: string,
  state: TrackerState
): { entityId: string; fieldPath: string } | null {
  const result = parsePathToEntity(
    fullPath,
    state.latestServerSnapshot,
    state.idField,
    state.arrayIdFields,
    state.referenceArrayPaths
  );

  if (result.entityId) {
    return {
      entityId: result.entityId,
      fieldPath: result.fieldPath,
    };
  }

  return null;
}
