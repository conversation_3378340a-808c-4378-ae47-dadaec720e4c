/**
 * ConflictAwareSimpleForm - SimpleForm with built-in conflict detection
 *
 * This is a drop-in replacement for react-admin's SimpleForm that provides:
 * 1. Realtime subscription to record changes
 * 2. Conflict detection and resolution
 * 3. Visual indicators for remotely changed fields (via ConflictAwareInput children)
 * 4. Auto-merge for non-conflicting changes
 *
 * Use this inside EditDialog to get conflict detection without wrapping Edit.
 */

import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Alert,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Stack,
  Typography,
} from '@mui/material';
import { useSubscribeToRecord } from '@react-admin/ra-realtime';
import {
  SimpleForm,
  SimpleFormProps,
  useDataProvider,
  useNotify,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  ConflictDetectionProvider,
  useConflictDetection,
} from '../../hooks/useConflictDetection';
import { buildEntityRegistry } from '../../hooks/useConflictDetection/entityRegistry';
import {
  ConflictDetectionConfig,
  ProcessUpdateResult,
} from '../../hooks/useConflictDetection/types';
import { useResolveUserName } from '../../hooks/useResolveUserName';

export interface ConflictAwareSimpleFormProps extends SimpleFormProps {
  /**
   * Configuration for conflict detection
   */
  conflictConfig?: Partial<ConflictDetectionConfig>;

  /**
   * Translation namespace for conflict messages
   * @default 'shared'
   */
  translationNamespace?: string;

  /**
   * Called when conflicts are detected
   */
  onConflictsDetected?: (result: ProcessUpdateResult) => void;

  /**
   * Called when remote updates are applied automatically (no conflicts)
   */
  onAutoMerged?: (result: ProcessUpdateResult) => void;

  /**
   * Called when record is deleted by another user
   */
  onRecordDeleted?: () => void;

  /**
   * Whether to auto-refresh when the form is not dirty
   * @default true
   */
  autoRefreshWhenClean?: boolean;

  /**
   * Whether to show notifications for updates
   * @default true
   */
  showNotifications?: boolean;

  /**
   * Enable debug logging
   * @default false
   */
  debug?: boolean;

  /**
   * Meta options to pass to dataProvider.getOne when fetching fresh data
   * This should match the queryOptions.meta from EditDialog
   */
  queryMeta?: Record<string, any>;
}

/**
 * Inner component that handles conflict detection logic.
 * Must be inside ConflictDetectionProvider AND SimpleForm (for useFormContext).
 */
function ConflictHandler({
  children,
  translationNamespace = 'shared',
  onConflictsDetected,
  onAutoMerged,
  onRecordDeleted,
  autoRefreshWhenClean = true,
  showNotifications = true,
  debug = false,
  queryMeta,
}: {
  children: React.ReactNode;
  translationNamespace?: string;
  onConflictsDetected?: (result: ProcessUpdateResult) => void;
  onAutoMerged?: (result: ProcessUpdateResult) => void;
  onRecordDeleted?: () => void;
  autoRefreshWhenClean?: boolean;
  showNotifications?: boolean;
  debug?: boolean;
  queryMeta?: Record<string, any>;
}) {
  const { t } = useTranslation('');
  const ctx = useConflictDetection();
  const resource = useResourceContext();
  const record = useRecordContext();
  const redirect = useRedirect();
  const notify = useNotify();
  const dataProvider = useDataProvider();
  const formContext = useFormContext();
  const {
    formState: { isDirty },
    reset,
    setValue,
  } = formContext;

  const [conflictDialogOpen, setConflictDialogOpen] = useState(false);
  const [pendingAutoMerges, setPendingAutoMerges] =
    useState<ProcessUpdateResult | null>(null);
  const [modifiedByUserId, setModifiedByUserId] = useState<string | null>(null);
  const [remoteData, setRemoteData] = useState<any>(null);

  const initializedRef = useRef(false);
  const recordId = record?.id;

  const log = useCallback(
    (...args: any[]) => {
      if (debug) {
        console.log('[ConflictAwareSimpleForm]', ...args);
      }
    },
    [debug]
  );

  // Initialize conflict detection when record loads
  useEffect(() => {
    if (record && record.id && !initializedRef.current) {
      log('Initializing for record:', record.id);
      ctx.initializeForRecord(record);
      initializedRef.current = true;
    }
  }, [record, ctx, log]);

  // Resolve display name for the user who made the modification
  const modifiedByUserName = useResolveUserName(modifiedByUserId);

  // Handle keeping local changes
  const handleKeepMyChanges = useCallback(() => {
    log('Keeping local changes');
    ctx.resolveAllConflicts('local');

    if (pendingAutoMerges && remoteData) {
      const { entities: newEntities } = buildEntityRegistry(
        remoteData,
        ctx.state.idField,
        ctx.state.excludeFields,
        ctx.state.arrayIdFields,
        ctx.state.referenceArrayPaths
      );
      applyAutoMerges(pendingAutoMerges, newEntities);
    }

    setConflictDialogOpen(false);
    setRemoteData(null);
    setPendingAutoMerges(null);

    if (showNotifications) {
      notify(
        t(`${translationNamespace}.changesMergedMyChangesKept`) ||
          'Your changes have been preserved',
        { type: 'success', autoHideDuration: 3000 }
      );
    }
  }, [ctx, notify, t, translationNamespace, showNotifications, log]);

  // Handle accepting remote changes
  const handleGetTheirChanges = useCallback(() => {
    log('Accepting remote changes');
    ctx.startApplyingRemoteChanges();

    // Clear local changes only for the entities involved in current conflicts
    // This preserves local changes for entities where user previously chose "Keep My Changes"
    ctx.clearLocalChangesForConflicts(ctx.conflicts);

    ctx.resolveAllConflicts('remote');

    // Reset form with remote data
    if (remoteData) {
      reset(remoteData, { keepDirty: false, keepDirtyValues: false });
      // DON'T call reinitializeAfterSave - we want to keep the original formOpenSnapshot
      // so that visual indicators still show the fields that changed since form was opened.
      // Just update the latest server snapshot.
      ctx.updateServerSnapshot(remoteData);
    }

    ctx.stopApplyingRemoteChanges();

    setConflictDialogOpen(false);
    setRemoteData(null);
    setPendingAutoMerges(null);

    if (showNotifications) {
      notify(
        t(`${translationNamespace}.formRefreshedWithLatestData`) ||
          'Form updated with latest changes',
        { type: 'success', autoHideDuration: 2000 }
      );
    }
  }, [
    ctx,
    remoteData,
    reset,
    notify,
    t,
    translationNamespace,
    showNotifications,
    log,
  ]);

  // Handle record deletion
  const handleRecordDeleted = useCallback(() => {
    if (showNotifications) {
      notify(
        t(`${translationNamespace}.itemDeletedByOtherUser`) ||
          'This item has been deleted by another user',
        { type: 'error' }
      );
    }

    redirect('list', resource);
    onRecordDeleted?.();
  }, [
    notify,
    t,
    translationNamespace,
    redirect,
    resource,
    showNotifications,
    onRecordDeleted,
  ]);

  // Apply auto-merges to the form
  const applyAutoMerges = useCallback(
    (result: ProcessUpdateResult, newEntities: Map<string, any>) => {
      if (result.autoMerges.length === 0) return;

      log('Applying auto-merges:', result.autoMerges.length);

      const hasLocalChangesInPath = (arrayPath: string): boolean => {
        for (const [, change] of ctx.state.localChanges) {
          if (change.entityId === 'ROOT') {
            const fullFieldPath = change.fieldPath;
            if (
              fullFieldPath === arrayPath ||
              fullFieldPath.startsWith(arrayPath + '[') ||
              fullFieldPath.startsWith(arrayPath + '.')
            ) {
              return true;
            }
            continue;
          }

          const entity = ctx.state.entities.get(change.entityId);
          if (!entity) continue;
          for (const [, occurrence] of entity.occurrences) {
            const fullFieldPath = occurrence.path
              ? `${occurrence.path}.${change.fieldPath}`
              : change.fieldPath;
            if (
              fullFieldPath === arrayPath ||
              fullFieldPath.startsWith(arrayPath + '[') ||
              fullFieldPath.startsWith(arrayPath + '.')
            ) {
              return true;
            }
          }
        }

        return false;
      };

      // Disable local change tracking while applying remote changes
      ctx.startApplyingRemoteChanges();

      try {
        for (const merge of result.autoMerges) {
          const entity = newEntities.get(merge.entityId);
          if (entity) {
            // Skip array auto-merges that would overwrite local edits in that array
            if (Array.isArray(merge.newValue)) {
              const occurrences = entity.occurrences as Map<
                string,
                { path?: string }
              >;
              const shouldSkip = Array.from(occurrences.values()).some(
                occurrence =>
                  hasLocalChangesInPath(
                    occurrence.path
                      ? `${occurrence.path}.${merge.fieldPath}`
                      : merge.fieldPath
                  )
              );
              if (shouldSkip) {
                log('Skipping auto-merge for array with local changes:', merge);
                continue;
              }
            }
            // Apply to all occurrences of this entity
            for (const [, occurrence] of entity.occurrences) {
              const fullPath = occurrence.path
                ? `${occurrence.path}.${merge.fieldPath}`
                : merge.fieldPath;
              log('Setting value at path:', fullPath, '=', merge.newValue);
              setValue(fullPath, merge.newValue);
            }
          } else {
            if (Array.isArray(merge.newValue)) {
              if (hasLocalChangesInPath(merge.fieldPath)) {
                log('Skipping auto-merge for array with local changes:', merge);
                continue;
              }
            }
            // Root-level field or entity not in registry
            log('Setting value at path:', merge.fieldPath, '=', merge.newValue);
            setValue(merge.fieldPath, merge.newValue);
          }
        }
      } finally {
        // Re-enable local change tracking after a short delay
        // to allow React to process the setValue updates
        setTimeout(() => {
          ctx.stopApplyingRemoteChanges();
        }, 100);
      }
    },
    [ctx, setValue, log]
  );

  // Realtime event handler
  const handleRealtimeEvent = useCallback(
    (event: any) => {
      log('========== REALTIME EVENT RECEIVED ==========');
      log('Event type:', event.type);
      log('Event payload:', event.payload);
      log('Resource:', resource);
      log('RecordId:', recordId);

      const modifiedBy = event.payload?.by || null;

      if (event.type === 'deleted') {
        handleRecordDeleted();
        return;
      }

      if (event.type === 'updated') {
        log('Processing UPDATE event');
        if (modifiedBy) {
          setModifiedByUserId(modifiedBy);
        }

        if (!resource || !recordId) {
          console.warn(
            '[ConflictAwareSimpleForm] Missing resource or recordId'
          );
          return;
        }

        // Fetch fresh data
        log(
          'Fetching fresh data for resource:',
          resource,
          'id:',
          recordId,
          'meta:',
          queryMeta
        );
        dataProvider
          .getOne(resource, { id: recordId, meta: queryMeta })
          .then((result: any) => {
            const freshRemoteData = result?.data;
            log('dataProvider.getOne result:', result);

            if (!freshRemoteData) {
              console.warn('[ConflictAwareSimpleForm] No data in fetch result');
              return;
            }

            log('Fresh remote data:', freshRemoteData);

            // Check if form has local changes
            const ctxHasLocalChanges = ctx.hasLocalChanges();
            const hasLocalChanges = isDirty || ctxHasLocalChanges;
            log(
              'Has local changes:',
              hasLocalChanges,
              '(react-hook-form isDirty:',
              isDirty,
              ', ctx.hasLocalChanges():',
              ctxHasLocalChanges,
              ')'
            );
            log('autoRefreshWhenClean:', autoRefreshWhenClean);

            if (!hasLocalChanges && autoRefreshWhenClean) {
              // No local changes - auto-refresh entire form
              log('No local changes, auto-refreshing form');
              reset(freshRemoteData, {
                keepDirty: false,
                keepDirtyValues: false,
              });
              ctx.reinitializeAfterSave(freshRemoteData);

              if (showNotifications) {
                notify(
                  t(`${translationNamespace}.itemRefreshedWithLatestChanges`) ||
                    'Form refreshed with latest changes',
                  { type: 'info', autoHideDuration: 2000 }
                );
              }
              return;
            }

            // Process remote update through conflict detection
            // Pass the raw user ID so ConflictAwareInput can resolve the display name
            const processResult = ctx.processRemoteUpdate(
              freshRemoteData,
              modifiedBy || 'unknown'
            );
            log('Process result:', processResult);

            // Build new entity registry for applying auto-merges (with arrayIdFields)
            const { entities: newEntities } = buildEntityRegistry(
              freshRemoteData,
              ctx.state.idField,
              ctx.state.excludeFields,
              ctx.state.arrayIdFields,
              ctx.state.referenceArrayPaths
            );

            setRemoteData(freshRemoteData);

            if (processResult.conflicts.length > 0) {
              // Show conflict dialog
              log('Conflicts detected:', processResult.conflicts.length);
              setConflictDialogOpen(true);
              onConflictsDetected?.(processResult);

              if (processResult.autoMerges.length > 0) {
                setPendingAutoMerges(processResult);
              }

              if (showNotifications) {
                notify(
                  t(`${translationNamespace}.conflictsDetected`) ||
                    'Conflicts detected with remote changes',
                  { type: 'warning' }
                );
              }
            } else if (processResult.autoMerges.length > 0) {
              // Apply auto-merges to form
              log('Auto-merging:', processResult.autoMerges.length, 'changes');
              applyAutoMerges(processResult, newEntities);
              onAutoMerged?.(processResult);

              if (showNotifications) {
                notify(
                  t(`${translationNamespace}.changesMergedAutomatically`) ||
                    'Remote changes merged automatically',
                  { type: 'info', autoHideDuration: 2000 }
                );
              }
            }
          })
          .catch((error: any) => {
            console.error(
              '[ConflictAwareSimpleForm] Error fetching fresh data:',
              error
            );
          });
      }
    },
    [
      resource,
      recordId,
      isDirty,
      autoRefreshWhenClean,
      dataProvider,
      ctx,
      reset,
      notify,
      t,
      translationNamespace,
      showNotifications,
      handleRecordDeleted,
      applyAutoMerges,
      onConflictsDetected,
      onAutoMerged,
      modifiedByUserName,
      log,
      queryMeta,
    ]
  );

  // Subscribe to real-time updates
  useSubscribeToRecord(handleRealtimeEvent, resource, recordId, {
    enabled: !!recordId,
  });

  return (
    <>
      {/* Form content */}
      {children}

      {/* Conflict Resolution Dialog */}
      <Dialog
        open={conflictDialogOpen}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown
      >
        <DialogTitle>
          {t(`${translationNamespace}.conflictDialogTitle`) || 'Edit Conflict'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t(`${translationNamespace}.conflictDialogDescription`) ||
              'This record has been modified by another user while you were editing it.'}
          </DialogContentText>
          {modifiedByUserName && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {t(`${translationNamespace}.modifiedBy`) || 'Modified by'}:{' '}
              <strong>{modifiedByUserName}</strong>
            </Typography>
          )}
          {ctx.conflicts.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                {ctx.conflicts.length} conflict
                {ctx.conflicts.length > 1 ? 's' : ''}:
              </Typography>
              <Stack spacing={1}>
                {ctx.conflicts.slice(0, 5).map((conflict, index) => (
                  <Alert key={index} severity="warning" sx={{ py: 0.5 }}>
                    {conflict.type === 'field_conflict' && (
                      <>
                        <strong>{conflict.fieldPath}</strong>
                        <Typography variant="caption" sx={{ ml: 1 }}>
                          {t(
                            `${translationNamespace}.fieldHasConflictingChanges`
                          ) || 'has conflicting changes'}
                        </Typography>
                      </>
                    )}
                    {conflict.type === 'entity_deleted' && (
                      <>Entity {conflict.entityId} was deleted</>
                    )}
                    {conflict.type === 'no_id_array_conflict' && (
                      <>Array at {conflict.path} was modified</>
                    )}
                  </Alert>
                ))}
                {ctx.conflicts.length > 5 && (
                  <Typography variant="caption" color="text.secondary">
                    ...and {ctx.conflicts.length - 5} more
                  </Typography>
                )}
              </Stack>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleGetTheirChanges} color="primary">
            {t(`${translationNamespace}.getTheirChanges`) || 'Accept Remote'}
          </Button>
          <Button
            onClick={handleKeepMyChanges}
            color="primary"
            variant="contained"
          >
            {t(`${translationNamespace}.keepMyChanges`) || 'Keep Mine'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

/**
 * SimpleForm with built-in conflict detection.
 *
 * Drop-in replacement for react-admin's SimpleForm that provides:
 * - Realtime subscription to record changes
 * - Conflict detection and resolution UI
 * - Visual indicators for remotely changed fields (when using ConflictAwareInput)
 *
 * @example Basic usage
 * ```tsx
 * <EditDialog>
 *   <ConflictAwareSimpleForm translationNamespace="categories" debug>
 *     <ConflictAwareInput>
 *       <TextInput source="name" />
 *     </ConflictAwareInput>
 *   </ConflictAwareSimpleForm>
 * </EditDialog>
 * ```
 */
export function ConflictAwareSimpleForm({
  children,
  conflictConfig,
  translationNamespace = 'shared',
  onConflictsDetected,
  onAutoMerged,
  onRecordDeleted,
  autoRefreshWhenClean = true,
  showNotifications = true,
  debug = false,
  queryMeta,
  ...simpleFormProps
}: ConflictAwareSimpleFormProps) {
  return (
    <SimpleForm {...simpleFormProps}>
      <ConflictDetectionProvider config={{ ...conflictConfig, debug }}>
        <ConflictHandler
          translationNamespace={translationNamespace}
          onConflictsDetected={onConflictsDetected}
          onAutoMerged={onAutoMerged}
          onRecordDeleted={onRecordDeleted}
          autoRefreshWhenClean={autoRefreshWhenClean}
          showNotifications={showNotifications}
          debug={debug}
          queryMeta={queryMeta}
        >
          {children}
        </ConflictHandler>
      </ConflictDetectionProvider>
    </SimpleForm>
  );
}

export default ConflictAwareSimpleForm;
