import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  Dialog,
  Grid,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { required } from 'react-admin';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import { useConflictDetectionOptional } from '~/hooks/useConflictDetection/ConflictDetectionContext';
import { findNextAvailablePosition, getValueByPath } from '~/pages/menus/utils';
import getFullscreenModalProps from '~/utils/getFullscreenModalProps';
import { menuColors } from '../../../../data/menu-colors';
import ColorSelectInputGroup from '../../../molecules/input-groups/ColorSelectInputGroup';
import ModalHeader from '../../../molecules/ModalHeader';
import { Coordinates } from '../types';
import {
  cleanupTempFields,
  handleCreateDisplayGroup,
  initializeTempFields,
} from './helpers';

interface POSModeModalProps {
  mode: 'create' | 'edit';
  path: string;
  data?: any[]; // Required for create mode
  position?: Coordinates; // Required for create mode
  initialColorValue?: string; // Required for edit mode
  onClose: () => void;
}

/**
 * Display Group Modal for POS Mode (recordType='pos' or default)
 *
 * Features:
 * - Create mode: Uses temp fields, generates ID, adds to data array
 * - Edit mode: Directly edits fields at path, includes page navigation
 * - Fields: displayName, color picker
 * - Edit mode includes page selection/movement
 * - No images or translatable fields
 */
export default function POSModeModal({
  mode,
  path,
  data = [],
  position,
  initialColorValue,
  onClose,
}: POSModeModalProps) {
  const { t } = useTranslation('');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { setValue, getValues, trigger, clearErrors, resetField } =
    useFormContext();
  const conflictCtx = useConflictDetectionOptional();

  const tempPrefix = '__modal_displayGroup_pos_temp';
  const isCreateMode = mode === 'create';
  // Always use temp fields to avoid marking parent form as dirty
  const fieldPath = tempPrefix;

  // Watch fields for live updates
  const watchedData = useWatch({ name: fieldPath });

  // Store original values synchronously for edit mode
  const originalValuesRef = useRef<any>(null);
  if (!isCreateMode && !originalValuesRef.current) {
    originalValuesRef.current = JSON.parse(JSON.stringify(getValues(path)));
  }

  // Page navigation state (edit mode only)
  const [selectedPage, setSelectedPage] = useState(0);
  const [initialPage, setInitialPage] = useState(0);
  const [numberOfPages, setNumberOfPages] = useState<number>();
  const [showError, setShowError] = useState(false);

  // Initialize temp fields on mount
  useEffect(() => {
    if (isCreateMode) {
      // Create mode: empty fields
      initializeTempFields(setValue, clearErrors, tempPrefix, {
        color: menuColors[0],
        displayName: '',
      });
    } else {
      // Edit mode: copy existing values to temp fields
      const existingValues = getValues(path);
      initializeTempFields(
        setValue,
        clearErrors,
        tempPrefix,
        JSON.parse(JSON.stringify(existingValues))
      );
    }

    // Cleanup on unmount
    return () => {
      cleanupTempFields(resetField, tempPrefix);
    };
  }, [isCreateMode, setValue, clearErrors, resetField, path, getValues]);

  // Check if any fields have changed (compare temp fields with original)
  const hasChanges = useMemo(() => {
    if (isCreateMode) return true; // Always enabled in create mode

    if (!originalValuesRef.current || !watchedData) return false;

    const original = originalValuesRef.current;
    const current = watchedData;

    // Compare displayName
    if (original.displayName !== current.displayName) return true;

    // Compare color
    if (original.color !== current.color) return true;

    // Check if page has changed
    if (selectedPage !== initialPage) return true;

    return false;
  }, [
    isCreateMode,
    originalValuesRef.current,
    watchedData,
    selectedPage,
    initialPage,
  ]);

  // Initialize page navigation (edit mode only)
  useEffect(() => {
    if (!isCreateMode) {
      const match = path.match(/^pages\.(\d+)\[/);
      if (match) {
        const number = parseInt(match[1], 10);
        const values = getValues();

        setInitialPage(number);
        setSelectedPage(number);
        setNumberOfPages(values?.pages?.length ?? 1);
      }
    }
  }, [isCreateMode, path, getValues]);

  const handleSave = async () => {
    if (isCreateMode) {
      // Create mode: validate, create item, add to data
      const success = await handleCreateDisplayGroup(
        tempPrefix,
        path,
        data,
        position,
        trigger,
        getValues,
        setValue,
        conflictCtx
      );

      if (success) {
        onClose();
      }
    } else {
      // Edit mode: validate temp fields, then copy to actual path
      const isValid = await trigger(`${tempPrefix}.displayName`);

      if (!isValid) {
        return;
      }

      // Copy temp values to actual path
      const tempValues = getValues(tempPrefix);
      setValue(path, tempValues, { shouldDirty: true });
      // Track change for conflict detection
      conflictCtx?.trackChange(path, tempValues);

      if (selectedPage !== initialPage) {
        // Move to different page
        const values = getValues();
        const newPosition = findNextAvailablePosition(
          values?.pages?.[selectedPage]
        );

        if (!newPosition) {
          setShowError(true);
          return;
        }

        const currentGroup = getValueByPath(values, path);
        currentGroup.position = newPosition;

        const pages = [...values.pages];
        pages[selectedPage] = [...pages[selectedPage], currentGroup];
        pages[initialPage] = pages[initialPage].filter(
          (group: any) => group.id !== currentGroup.id
        );

        setValue('pages', pages, {
          shouldDirty: true,
          shouldTouch: true,
        });
        // Track change for conflict detection
        conflictCtx?.trackChange('pages', pages);
      }

      onClose();
    }
  };

  const handleClose = () => {
    // No need to restore values - temp fields are discarded automatically
    onClose();
  };

  return (
    <Dialog
      open
      onClose={handleClose}
      fullWidth={true}
      maxWidth="md"
      transitionDuration={0}
      fullScreen={isMobile}
      {...(isMobile
        ? { TransitionComponent: getFullscreenModalProps().TransitionComponent }
        : {})}
    >
      <ModalHeader
        handleClose={handleClose}
        title={
          isCreateMode
            ? t('menu.addMenuGroup')
            : `${t('shared.edit')} ${t('menu.menuGroup')}`
        }
      >
        <Button variant="contained" onClick={handleSave} disabled={!hasChanges}>
          {isCreateMode ? t('shared.create') : t('shared.save')}
        </Button>
      </ModalHeader>
      <Grid p={3} container spacing={3}>
        {/* Display Name */}
        <Grid
          size={{
            xs: 12,
            sm: isCreateMode ? 7 : 7,
          }}
        >
          <CustomInput
            type="text"
            source={`${fieldPath}.displayName`}
            label={isCreateMode ? t('shared.name') : t('menu.itemName')}
            validate={required()}
            sanitize="singleLine"
            ui="custom"
            roundedCorners="both"
          />

          {/* Page Navigation (edit mode only) */}
          {!isCreateMode && numberOfPages && (
            <>
              <Typography variant="body2" fontWeight={600} mt={3}>
                {selectedPage !== initialPage
                  ? t('menu.moveToPage')
                  : t('menu.currentPage')}
                :
              </Typography>
              <Grid container spacing={1} mt={1}>
                {[...Array(numberOfPages)].map((_, pageNum) => (
                  <Grid key={pageNum}>
                    <Button
                      variant={
                        selectedPage === pageNum ? 'contained' : 'outlined'
                      }
                      color={selectedPage === pageNum ? 'primary' : 'inherit'}
                      onClick={() => {
                        setSelectedPage(pageNum);
                        setShowError(false);
                      }}
                      sx={{
                        minWidth: 36,
                        minHeight: 36,
                        borderRadius: '50%',
                        fontWeight: selectedPage === pageNum ? 700 : 400,
                      }}
                    >
                      {pageNum + 1}
                    </Button>
                  </Grid>
                ))}
                {showError && (
                  <Grid size={12}>
                    <Typography variant="body2" color="error">
                      {t('menu.pageFullError')}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </>
          )}
        </Grid>

        {/* Color Picker */}
        <Grid
          size={{
            xs: 12,
            sm: 5,
          }}
        >
          <ColorSelectInputGroup
            onChange={value => {
              setValue(`${fieldPath}.color`, value, {
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
            value={
              watchedData?.color ||
              originalValuesRef.current?.color ||
              menuColors[0]
            }
            choices={menuColors}
          />
        </Grid>
      </Grid>
    </Dialog>
  );
}
