import { useEffect, useMemo, useState } from 'react';
import AddIcon from '@mui/icons-material/Add';
import { Button, Menu, MenuItem } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { BackgroundGrid } from '../../molecules/BackgroundGrid';
import {
  MENU_TILE_HEIGHT,
  MENU_TILE_WIDTH,
  MOBILE_MENU_TILE_WIDTH,
  ROWS_NO,
} from './CatalogContainer';

interface CatalogBackgroundGridProps {
  totalItems: number;
  columns: number;
  startIndex: number;
  resetSelectionChild: number;
  resetSelectionParent: () => void;
  setTileAction: (val: any) => void;
  recordType?: string;
}

export default function CatalogBackgroundGrid({
  totalItems,
  columns,
  startIndex,
  resetSelectionChild,
  resetSelectionParent,
  setTileAction,
  recordType,
}: CatalogBackgroundGridProps) {
  const { t } = useTranslation('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTile, setSelectedTile] = useState<number>(-1);
  const open = Boolean(anchorEl);

  useEffect(() => {
    setSelectedTile(-1);
  }, [resetSelectionChild]);

  const handleClick = (
    event: React.MouseEvent<HTMLButtonElement>,
    index: number
  ) => {
    resetSelectionParent();
    setAnchorEl(event.currentTarget);
    setSelectedTile(index);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const openTileModal = ({
    type,
    create = false,
    addExisting = false,
  }: {
    type: 'product' | 'displayGroup' | 'function' | 'productCombo';
    create?: boolean;
    addExisting?: boolean;
  }) => {
    const x = selectedTile % columns;
    const y = Math.floor(selectedTile / columns);
    const obj = {
      position: {
        startX: x + startIndex,
        startY: y,
        endX: x + 1 + startIndex,
        endY: y + 1,
      },
      type,
      create,
      addExisting,
      recordType,
    };

    console.log(obj);

    setTileAction(obj);
    handleClose();
  };

  const menuItems: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
  }[] = [
    {
      label: t('menu.addItem'),
      onClick: () => openTileModal({ type: 'product', create: true }),
    },
    {
      label: t('menu.addGroupIn'),
      onClick: () => openTileModal({ type: 'displayGroup', create: true }),
    },
    {
      label: t('menu.addFunction'),
      onClick: () => openTileModal({ type: 'function', create: true }),
    },
    {
      label: t('menu.addComboProduct'),
      onClick: () => openTileModal({ type: 'productCombo', create: true }),
    },
  ];

  const rows = useMemo(() => {
    return Math.max(
      ROWS_NO,
      Math.max(
        Math.floor(totalItems / columns) + 1,
        Math.ceil(totalItems / columns)
      )
    );
  }, [totalItems, columns]);

  return (
    <BackgroundGrid
      tileWidth={columns === 2 ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH}
      tileHeight={MENU_TILE_HEIGHT}
      style={{
        gridTemplateColumns: `repeat(${columns}, ${
          columns === 2 ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH
        }px)`,
        gridTemplateRows: `repeat(${rows}, ${MENU_TILE_HEIGHT}px)`,
        height: 'fit-content',
      }}
    >
      {Array.from(Array(columns * rows)).map((_, index) => (
        <Button
          key={index}
          onClick={event => handleClick(event, index)}
          aria-controls={open ? 'empty-tile-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          sx={{
            width: 'calc(100% - 5px)',
            height: 'calc(100% - 5px)',
            margin: 'auto',
            cursor: 'pointer',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '6px',
            bgcolor:
              selectedTile == index ? 'rgba(0,106,255,.45)' : 'transparent',
            border: selectedTile == index ? '1px solid #006aff' : 'none',
            ':hover': {
              bgcolor: 'rgba(0,106,255,.3)',
            },
          }}
        >
          <AddIcon fontSize="small" color="disabled" />
        </Button>
      ))}
      <Menu
        id="empty-tile-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'empty-tile-button',
        }}
      >
        {menuItems.map(item => (
          <MenuItem
            key={item.label}
            onClick={item.onClick}
            disabled={!!item.disabled}
          >
            {item.label}
          </MenuItem>
        ))}
      </Menu>
    </BackgroundGrid>
  );
}
