import { Fragment } from 'react';
import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import { t } from 'i18next';
import {
  BooleanField,
  BulkDeleteWithConfirmButton,
  CreateButton,
  DataTable,
  List,
  ReferenceManyCount,
  TopToolbar,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomSearchInput from '~/components/atoms/inputs/CustomSearchInput';
import Pill from '~/components/atoms/Pill';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import { useTheme } from '../../contexts';

const AssetBulkActionButtons = (props: any) => (
  <Fragment>
    <BulkDeleteWithConfirmButton {...props} />
  </Fragment>
);

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ textAlign: 'center' }}>
      <img src="/assets/icons/category-icon.svg" width="45px" />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('categoryLibrary.noCategoriesYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('categoryLibrary.noCategoriesYetDescription')}
      </Typography>
      <CreateButton
        variant="contained"
        label={t('categoryLibrary.createCategory')}
      />
    </div>
  );
};

export const CategoryList = () => {
  const { theme } = useTheme();

  const filters = [
    <CustomSearchInput
      placeholder={t('dashboard.headerSearchPlaceholder')}
      key="search-input"
      source="q"
      alwaysOn
    />,
  ];

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  const CategoryListActions = () => (
    <TopToolbar>
      <CreateButton
        variant="contained"
        label={t('categoryLibrary.createCategory')}
        {...(isXSmall ? {} : { icon: <></> })}
      />
    </TopToolbar>
  );

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('sm'));

  return (
    <List
      empty={<CustomEmpty />}
      pagination={false}
      perPage={Number.MAX_SAFE_INTEGER}
      resource={RESOURCES.HOSPITALITY_CATEGORIES}
      sort={resourcesInfo[RESOURCES.HOSPITALITY_CATEGORIES].defaultSort}
      filter={{ _d: false }}
      component="div"
      filters={filters}
      actions={<CategoryListActions />}
      sx={{
        '& .RaFilterFormInput-spacer': {
          display: { xs: 'none', md: 'block' },
        },
      }}
    >
      {isSmall ? (
        <MobileGrid>
          <MobileCard>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="body1"
                fontWeight={300}
                fontSize={14}
                sx={{ textTransform: 'capitalize' }}
              >
                {t('shared.items')}
              </Typography>
              <Pill>
                <>
                  <ReferenceManyCount
                    reference={RESOURCES.HOSPITALITY_ITEMS}
                    target="groupId"
                    filter={{ _d: false }}
                  />
                  <img
                    src="/assets/icons/food.svg"
                    width="17px"
                    style={{
                      filter:
                        theme.palette.mode === 'dark' ? 'invert(1)' : 'none',
                    }}
                  />
                </>
              </Pill>
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                Straight fire
              </Typography>
              <BooleanField source="straightFire" />
            </Box>
          </MobileCard>
        </MobileGrid>
      ) : (
        <DataTable
          sx={{
            marginTop: '10px',
            '& .RaDataTable-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
          }}
          bulkActionButtons={false}
        >
          <DataTable.Col source="name" label={t('shared.name')} />
          <DataTable.Col
            label={t('shared.items')}
            sx={{ textAlign: 'left', textTransform: 'capitalize' }}
          >
            <Pill>
              <>
                <ReferenceManyCount
                  reference={RESOURCES.HOSPITALITY_ITEMS}
                  target="groupId"
                  filter={{ _d: false }}
                />
                <img
                  src="/assets/icons/food.svg"
                  width="17px"
                  style={{
                    filter:
                      theme.palette.mode === 'dark' ? 'invert(1)' : 'none',
                  }}
                />
              </>
            </Pill>
          </DataTable.Col>
          <DataTable.Col label="Straight Fire" sx={{ textAlign: 'center' }}>
            <BooleanField source="straightFire" textAlign="right" />
          </DataTable.Col>
          <DataTable.Col label={t('prepStations.actions')} align="right">
            <ActionsField
              textAlign="right"
              hasEdit={true}
              hasDelete={true}
              deleteMutationMode="pessimistic"
            />
          </DataTable.Col>
        </DataTable>
      )}
      <ListLiveUpdate />
    </List>
  );
};
