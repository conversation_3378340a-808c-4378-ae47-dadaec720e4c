import { useEffect, useMemo, useState } from 'react';
import { InfoOutlined } from '@mui/icons-material';
import { Box, Button, InputAdornment, Typography } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  ReferenceInput,
  required,
  SaveButton,
  useRecordContext,
  useRedirect,
} from 'react-admin';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import { MuiSwitchInput } from '~/components/atoms/inputs/SwitchInput';
import {
  ConflictAwareInput,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import ControlledRadioInputGroup from '~/components/molecules/controlled-input-groups/ControlledRadioInputGroup';
import { RESOURCES, useGetListVatsLive } from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';
import { ItemsDialog } from './ItemsDialog';

function SectionEditInner(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const { setValue, getValues } = useFormContext();
  const record = useRecordContext();

  // Watch form values for items (this is actual data that gets saved)
  const selectedItems = useWatch({ name: 'conditions[0].constraint' }) || [];

  // Local state for UI controls (dialog open state and switches)
  const [itemsDialogOpen, setItemsDialogOpen] = useState(false);
  const [toGo, setToGo] = useState<boolean>(false);
  const [appliedTo, setAppliedTo] = useState<'product' | 'bill'>('product');

  // Initialize conditions only once when record loads
  useEffect(() => {
    if (record) {
      setValue('conditions[0].operator', 'in');
      setValue('conditions[0].source', 'product');
      setValue('conditions[0].property', 'id');
      setValue(
        'conditions[0].constraint',
        record?.conditions?.[0]?.constraint || []
      );

      setValue('conditions[1].constraint', ['100']);
      setValue('conditions[1].operator', 'neq');
      setValue('conditions[1].source', 'bill');
      setValue('conditions[1].property', 'discount');

      // Initialize local state from record
      const hasToGo = !!record?.conditions?.find(
        (c: any) => c.property === 'dinningOption'
      );
      setToGo(hasToGo);
      setAppliedTo(record?.appliesTo || 'product');

      // If record has toGo condition, set it in form
      if (hasToGo) {
        const toGoCondition = record?.conditions?.find(
          (c: any) => c.property === 'dinningOption'
        );
        if (toGoCondition) {
          setValue('conditions[2].constraint', toGoCondition.constraint);
          setValue('conditions[2].operator', toGoCondition.operator);
          setValue('conditions[2].source', toGoCondition.source);
          setValue('conditions[2].property', toGoCondition.property);
        }
      }
    }
  }, [record?.id, setValue]);

  /**
   * This function is called before submitting a form to ensure data integrity.
   */
  const beforeHandleSubmit = () => {
    // Ensure appliesTo is set
    setValue('appliesTo', appliedTo);

    // Ensure conditions array is clean (no sparse entries, no undefined)
    const currentConditions = getValues('conditions') || [];
    const cleanConditions = currentConditions.filter(
      (c: any) => c !== null && c !== undefined
    );
    setValue('conditions', cleanConditions);
  };

  const handleClose = () => {
    redirect('list', RESOURCES.EXTRA_CHARGES);
  };

  const { t } = useTranslation();

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      label: `${vat.value}%`,
    }));
  }, [vats]);

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('extraCharges.editExtraCharge')}
      >
        <SaveButton
          onClick={() => beforeHandleSubmit()}
          type="button"
          label={t('shared.save')}
          icon={<></>}
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: { xs: 2, md: 8 },
          gap: 0,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            border: '1px solid',
            backgroundColor: '#E6F0FF',
            borderColor: '#0064F0',
            p: 2,
            borderRadius: '8px',
            mb: 2,
          }}
        >
          <InfoOutlined sx={{ color: '#0064F0' }} />
          <Typography variant="body2">
            {t('extraCharges.sectionCreateInnerDescription')}
          </Typography>
        </Box>
        <ReferenceInput source="sellPointId" reference={RESOURCES.LOCATIONS}>
          <CustomInput
            type="select"
            source="sellPointId"
            ui="custom"
            label={t('shared.location')}
            optionText="name"
            optionValue="id"
            placeholder={t('shared.none', { context: 'female' })}
            defaultValue={sellPointId}
            readOnly
            roundedCorners="top"
          />
        </ReferenceInput>
        <ConflictAwareInput>
          <CustomInput
            source="name"
            type="text"
            ui="custom"
            sanitize="singleLine"
            label={t('shared.name')}
            placeholder={t('extraCharges.sgrFeePlaceholder')}
            isRequired
            validate={required()}
          />
        </ConflictAwareInput>
        <ControlledRadioInputGroup
          title={t('extraCharges.appliedTo')}
          value={appliedTo}
          setValue={val => {
            setAppliedTo(val);
            // Update form to mark as dirty
            setValue('appliesTo', val, { shouldDirty: true });
          }}
          choices={[
            {
              id: 'product',
              name: t('extraCharges.item'),
              description: t('extraCharges.itemDescription'),
            },
            {
              id: 'bill',
              name: t('extraCharges.bill'),
              description: t('extraCharges.billDescription'),
              disabled: true,
            },
          ]}
        />
        <ConflictAwareInput>
          <CustomInput
            ui="custom"
            source="charge.fixed.value"
            label={t('shared.amount')}
            type="number"
            // locale="ro-RO"
            format={v => (v ? v / 10000 : '')}
            parse={v => Math.floor(v * 10000)}
            // options={{
            //   style: 'currency',
            //   currency: 'RON',
            // }}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">Lei</InputAdornment>
                ),
              },
            }}
            isRequired
            validate={required()}
          />
        </ConflictAwareInput>
        <ConflictAwareInput>
          <CustomInput
            source="charge.fixed.vat"
            type="autocomplete"
            ui="custom"
            label={t('shared.tva')}
            choices={vatsChoices}
            optionText="label"
            optionValue="id"
            selectOnFocus={false}
            validate={[required()]}
            roundedCorners="bottom"
          />
        </ConflictAwareInput>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            pl: 2,
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography>{t('extraCharges.applyToItems')}</Typography>
            <Typography variant="subtitle2">
              {selectedItems.length > 0
                ? `${selectedItems.length > 1 ? t('shared.item_few', { count: selectedItems.length }) : t('shared.item_one', { count: selectedItems.length })}`
                : t('shared.none')}
            </Typography>
          </Box>
          <Button
            variant="text"
            color="primary"
            onClick={() => {
              setItemsDialogOpen(true);
            }}
          >
            {t('shared.select')}
          </Button>
        </Box>

        <CustomDivider />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            pl: 2,
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography>
              {t('extraCharges.applyAutomaticallyOnlyForTOGOOrders')}
            </Typography>
            <Typography variant="subtitle2">
              {t('extraCharges.applyAutomaticallyOnlyForTOGOOrdersDescription')}
            </Typography>
          </Box>
          <MuiSwitchInput
            checked={toGo}
            onChange={event => {
              const newValue = event.target.checked;
              setToGo(newValue);
              // Build and set the complete conditions array to mark form as dirty
              const newConditions: any[] = [
                {
                  operator: 'in',
                  source: 'product',
                  property: 'id',
                  constraint: getValues('conditions[0].constraint') || [],
                },
                {
                  constraint: ['100'],
                  operator: 'neq',
                  source: 'bill',
                  property: 'discount',
                },
              ];
              if (newValue) {
                newConditions.push({
                  constraint: ['togo'],
                  operator: 'eq',
                  source: 'bill',
                  property: 'dinningOption',
                });
              }
              setValue('conditions', newConditions, { shouldDirty: true });
            }}
            sx={{
              marginRight: 0,
            }}
          />
        </Box>

        <CustomDivider />
      </Box>

      <ItemsDialog
        open={itemsDialogOpen}
        selectedItems={selectedItems}
        setSelectedItems={(items: any) => {
          setValue('conditions[0].constraint', items, { shouldDirty: true });
        }}
        onClose={(items: any) => {
          setValue('conditions[0].constraint', items, { shouldDirty: true });
          setItemsDialogOpen(false);
        }}
      />
    </>
  );
}

// Transform function to clean up form data before submission
const transformExtraChargeData = (data: any) => {
  // Remove any undefined or null values from conditions array
  if (data.conditions && Array.isArray(data.conditions)) {
    data.conditions = data.conditions.filter(
      (c: any) => c !== null && c !== undefined
    );
  }

  // Remove any temporary/internal fields (prefixed with _) and undefined values
  const cleanedData = { ...data };
  Object.keys(cleanedData).forEach(key => {
    // Remove undefined values
    if (cleanedData[key] === undefined) {
      delete cleanedData[key];
      return;
    }
    // Remove temporary fields (prefixed with _) except legitimate metadata
    if (
      key.startsWith('_') &&
      !['_c', '_u', '_ub', '_e', '_pc'].includes(key)
    ) {
      delete cleanedData[key];
    }
  });

  // Remove 'q' field (react-admin search field)
  delete cleanedData.q;

  // Remove groupId_eq_any if it exists (filter field)
  delete cleanedData.groupId_eq_any;

  return cleanedData;
};

export default function ExtraChargesEdit(props: { sellPointId: string }) {
  const { sellPointId } = props;
  if (!sellPointId) {
    return null;
  }
  return (
    <EditDialog
      maxWidth="sm"
      fullWidth
      fullScreen
      mutationMode="pessimistic"
      mutationOptions={{ meta: { sellPointId: sellPointId } }}
      queryOptions={{ meta: { sellPointId: sellPointId } }}
      transform={transformExtraChargeData}
    >
      <ConflictAwareSimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        translationNamespace="shared"
        queryMeta={{ sellPointId: sellPointId }}
      >
        <SectionEditInner sellPointId={sellPointId} />
      </ConflictAwareSimpleForm>
    </EditDialog>
  );
}
