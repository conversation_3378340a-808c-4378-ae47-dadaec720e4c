import { forwardRef } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Button, Typography } from '@mui/material';
import { usePickerContext } from '@mui/x-date-pickers/hooks';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts/ThemeContext';

import type {
  FieldType,
  SingleInputDateRangeFieldProps,
} from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

interface DatePickerFilterBtnProps extends SingleInputDateRangeFieldProps {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

type DatePickerFilterBtnComponent = ((
  props: DatePickerFilterBtnProps & React.RefAttributes<HTMLDivElement>
) => React.JSX.Element) & { fieldType?: FieldType };

const DatePickerFilterBtn = forwardRef(
  (props: DatePickerFilterBtnProps, ref: React.Ref<HTMLElement>) => {
    const {
      setOpen,
      label,
      id,
      disabled,
      inputProps: { 'aria-label': ariaLabel } = {},
    } = props;

    const pickerContext = usePickerContext();
    const { t } = useTranslation('');
    const { theme } = useTheme();

    return (
      <Button
        //@ts-ignore
        variant="outlined-2"
        id={id}
        disabled={disabled}
        ref={pickerContext?.triggerRef}
        aria-label={ariaLabel}
        onClick={() => setOpen?.(prev => !prev)}
        sx={{ height: '40px' }}
      >
        <Typography variant="body2" sx={{ mr: 1 }} color="custom.gray600">
          {t('dashboard.date')}
        </Typography>
        <Typography
          variant="body2"
          fontWeight={500}
          color={theme.palette.mode === 'dark' ? 'white' : 'black'}
        >
          {label}
        </Typography>

        {/* <KeyboardArrowDownIcon color="disabled" /> */}
      </Button>
    );
  }
) as DatePickerFilterBtnComponent;

DatePickerFilterBtn.fieldType = 'single-input';

export default DatePickerFilterBtn;
