import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { groupReport } from '~/fake-provider/reports/groupReport';
import camelCaseToNormalWords from '~/utils/camelCaseToNormalWords';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import ExtraDataPaymentModal from './ExtraDataPaymentModal';

type TableRow = {
  type: string;
  quantity: string;
  value: string;
  field1: string;
  field2: string;
  items?: TableRow[];
  subItems?: TableRow[];
};

export default function PaymentMethodTable({
  tableData,
  fields,
  setFields,
  reportType,
  rawData,
  composedFilters,
  formattedFilters,
  filters,
}: {
  reportType: string;
  rawData: any;
  composedFilters: any;
  formattedFilters: any;
  filters: any;
  tableData: ReturnType<typeof groupReport>[number]['report'];
  fields: FieldOption[];
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
}) {
  const { t } = useTranslation();
  const PaymentMethodData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData.map((data: any) => ({
        type: data.type || '',
        quantity: data.quantity || '',
        value: data.value || '',
        field1: data.field1 || '',
        field2: data.field2 || '',
      }));

      let totalItemsData = mergeAndSumObjects(mappedTableData || []);
      totalItemsData.type = 'Total';
      totalItemsData.field1 = '';
      totalItemsData.field2 = '';
      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }

    return [];
  }, [tableData]);

  const paymentMethodsMap = {
    card: t('paymentMethods.card'),
    cash: t('paymentMethods.cash'),
    giftCard: t('paymentMethods.giftCard'),
    mealTicket: t('paymentMethods.mealTicket'),
    online: t('paymentMethods.online'),
    valueTicket: t('paymentMethods.valueTicket'),
    voucher: t('paymentMethods.voucher'),
    wireTransfer: t('paymentMethods.wireTransfer'),
    cashless: t('paymentMethods.cashless'),
    '3rdParty': t('paymentMethods.3rdParty'),
  };

  const PaymentMethodConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'type',
      textAlign: 'start',
      label: t('paymentMethods.paymentMethod2'),
      render: (row: TableRow) => {
        return (
          <>
            {paymentMethodsMap[row.type as keyof typeof paymentMethodsMap]
              ? paymentMethodsMap[row.type as keyof typeof paymentMethodsMap]
              : camelCaseToNormalWords(row.type)}
          </>
        );
      },
    },
    {
      id: 'quantity',
      textAlign: 'end',
      label: t('paymentMethods.count'),
      render: (row: TableRow) => {
        return <>{formatNumberIntl(Number(row?.quantity), true)}</>;
      },
    },
    {
      id: 'value',
      textAlign: 'end',
      label: t('paymentMethods.collected'),
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(Number(row.value))}</>;
      },
    },
  ];

  const columnsToFilter = ['quantity'];

  return (
    <>
      <Box sx={{ py: 4 }}>
        <CustomTable
          greyLastRow={true}
          fields={fields}
          setFields={setFields}
          filter={true}
          columnsToFilter={columnsToFilter}
          fixLastRow={true}
          fixedFirstColumn={true}
          maxWidthFirstColumn={'200px'}
          config={PaymentMethodConfig}
          data={PaymentMethodData}
          alignLastColumnRight={false}
          enableInfoModal={true}
          renderModalContent={rowData => (
            <ExtraDataPaymentModal
              extraData={{
                composedFilters,
                rawData,
                reportType,
                filters,
                rowData,
                formattedFilters,
              }}
            />
          )}
        />
      </Box>
    </>
  );
}
