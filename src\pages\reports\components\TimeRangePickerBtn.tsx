import { useMemo, useState } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  Popover,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { DesktopTimePicker } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

import type { ReportFiltersState } from './ReportFilters';
import type { DateRange } from '@mui/x-date-pickers-pro';
import type { MouseEvent } from 'react';

interface TimeRangePickerBtnProps {
  defaultValues: ReportFiltersState['timeRange'];
  setTimeRange: (timeRange: DateRange<Dayjs> | null) => void;
  disabled?: boolean;
}

enum TimeRangeValues {
  ALL_DAY,
  CUSTOM,
}

const defaultStart = dayjs().startOf('day').hour(9).set('minute', 0);
const defaultEnd = dayjs().startOf('day').hour(23).set('minute', 0);
// TODO: validation (start < end)
export default function TimeRangePickerBtn({
  defaultValues = {},
  setTimeRange,
  disabled,
}: TimeRangePickerBtnProps) {
  // Committed values (shown on button)
  const [committedRadioValue, setCommittedRadioValue] =
    useState<TimeRangeValues>(
      defaultValues.allDay ? TimeRangeValues.ALL_DAY : TimeRangeValues.CUSTOM
    );
  const [committedTimeRange, setCommittedTimeRange] =
    useState<DateRange<Dayjs> | null>(
      defaultValues.start && defaultValues.end
        ? [defaultValues.start, defaultValues.end]
        : null
    );

  // Local editing values (used while dropdown is open)
  const [radioGroupValue, setRadioGroupValue] = useState<TimeRangeValues>(
    defaultValues.allDay ? TimeRangeValues.ALL_DAY : TimeRangeValues.CUSTOM
  );
  const [localTimeRange, setLocalTimeRange] = useState<DateRange<Dayjs> | null>(
    defaultValues.start && defaultValues.end
      ? [defaultValues.start, defaultValues.end]
      : null
  );
  const [startOpen, setStartOpen] = useState(false);
  const [endOpen, setEndOpen] = useState(false);

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);
  const id = open ? 'time-range-popover' : undefined;
  const { t } = useTranslation();

  const label = useMemo(() => {
    if (committedRadioValue == TimeRangeValues.ALL_DAY) {
      return t('reportFilters.allDay');
    } else {
      return `${committedTimeRange?.[0]?.format('HH:mm') ?? ''} - ${committedTimeRange?.[1]?.format('HH:mm') ?? ''}`;
    }
  }, [committedTimeRange, committedRadioValue]);

  const openDropdown = (event: MouseEvent<HTMLButtonElement>) => {
    // Reset local values to committed values when opening
    setRadioGroupValue(committedRadioValue);
    setLocalTimeRange(committedTimeRange);
    setAnchorEl(event.currentTarget);
  };

  const closeDropdown = () => {
    // Only apply changes if not already on All Day (which was applied immediately)
    if (radioGroupValue !== TimeRangeValues.ALL_DAY) {
      if (
        localTimeRange !== null &&
        (!localTimeRange[0] || !localTimeRange[1])
      ) {
        const newTimeRange: [Dayjs, Dayjs] = [
          localTimeRange?.[0] ?? defaultStart,
          localTimeRange?.[1] ?? defaultEnd,
        ];
        setLocalTimeRange(newTimeRange);
        setCommittedTimeRange(newTimeRange);
        setCommittedRadioValue(TimeRangeValues.CUSTOM);
        setTimeRange(newTimeRange);
      } else {
        // Check if time range actually changed before updating
        const hasChanged =
          !committedTimeRange ||
          !localTimeRange ||
          committedTimeRange[0]?.format('HH:mm') !==
            localTimeRange[0]?.format('HH:mm') ||
          committedTimeRange[1]?.format('HH:mm') !==
            localTimeRange[1]?.format('HH:mm');

        if (hasChanged) {
          setCommittedTimeRange(localTimeRange);
          setCommittedRadioValue(TimeRangeValues.CUSTOM);
          setTimeRange(localTimeRange);
        }
      }
    }
    setAnchorEl(null);
  };

  const handleRadioGroupChange = (event: any) => {
    setRadioGroupValue(event.target.value);
    if (event.target.value == TimeRangeValues.ALL_DAY) {
      // All day: apply immediately and close dropdown
      setLocalTimeRange(null);
      setCommittedTimeRange(null);
      setCommittedRadioValue(TimeRangeValues.ALL_DAY);
      setTimeRange(null);
      setAnchorEl(null);
    } else {
      // Custom: only update local state, don't apply yet
      if (!localTimeRange) {
        setLocalTimeRange([defaultStart, defaultEnd]);
      }
    }
  };

  return (
    <>
      <Button
        //@ts-ignore
        variant="contained-light"
        disabled={disabled}
        aria-describedby={id}
        onClick={openDropdown}
      >
        {/* @ts-ignore */}
        <Typography variant="label" fontWeight={500} color="custom.gray800">
          {label}
        </Typography>

        {!disabled && <KeyboardArrowDownIcon color="disabled" />}
      </Button>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeDropdown}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box sx={{ minWidth: 280 }}>
          <Box p={2}>
            <FormControl sx={{ width: '100%' }}>
              <RadioGroup
                name="radio-btns-time-range"
                value={radioGroupValue}
                onChange={handleRadioGroupChange}
              >
                <FormControlLabel
                  value={TimeRangeValues.ALL_DAY}
                  label={
                    <Box mt={0} display="flex" flexDirection="column">
                      {/* @ts-ignore */}
                      <Typography variant="label" fontWeight={300}>
                        {t('reportFilters.allDay')}
                      </Typography>
                      <Typography variant="caption" color="custom.gray600">
                        {t('reportFilters.untilNextDay')}
                      </Typography>
                    </Box>
                  }
                  control={<Radio />}
                />
                <Divider sx={{ my: 1.5 }} />
                <FormControlLabel
                  value={TimeRangeValues.CUSTOM}
                  label={
                    // @ts-ignore
                    <Typography variant="label" fontWeight={300}>
                      {t('reportFilters.custom')}
                    </Typography>
                  }
                  control={<Radio />}
                />
              </RadioGroup>
            </FormControl>

            {radioGroupValue == TimeRangeValues.CUSTOM && (
              <Box
                width="100%"
                display="flex"
                flexDirection="column"
                gap={1}
                mt={0}
              >
                <DesktopTimePicker
                  ampm={false}
                  label={t('reportFilters.start')}
                  views={['hours']}
                  format="HH"
                  open={startOpen}
                  onOpen={() => setStartOpen(true)}
                  onClose={() => setStartOpen(false)}
                  onAccept={() => setStartOpen(false)}
                  value={localTimeRange?.[0]}
                  onChange={time => {
                    setLocalTimeRange([time, localTimeRange?.[1] ?? null]);
                    setStartOpen(false);
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      onClick: () => setStartOpen(true),
                    },
                    actionBar: {
                      actions: [],
                    },
                  }}
                />

                <DesktopTimePicker
                  ampm={false}
                  label={t('reportFilters.end')}
                  views={['hours']}
                  format="HH"
                  open={endOpen}
                  onOpen={() => setEndOpen(true)}
                  onClose={() => setEndOpen(false)}
                  onAccept={() => setEndOpen(false)}
                  value={localTimeRange?.[1]}
                  onChange={time => {
                    setLocalTimeRange([localTimeRange?.[0] ?? null, time]);
                    setEndOpen(false);
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      onClick: () => setEndOpen(true),
                    },
                    actionBar: {
                      actions: [],
                    },
                  }}
                />
              </Box>
            )}
          </Box>

          <Box
            display="flex"
            justifyContent="flex-end"
            gap={1}
            px={1}
            py={1}
            sx={{ borderTop: '1px solid rgba(0, 0, 0, 0.12)' }}
          >
            <Button onClick={closeDropdown}>OK</Button>
          </Box>
        </Box>
      </Popover>
    </>
  );
}
