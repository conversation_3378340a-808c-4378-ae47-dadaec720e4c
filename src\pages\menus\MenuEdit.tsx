import { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ListIcon from '@mui/icons-material/List';
import StayCurrentLandscapeIcon from '@mui/icons-material/StayCurrentLandscape';
import StayCurrentPortraitIcon from '@mui/icons-material/StayCurrentPortrait';
import { Box, Button, Theme, Typography, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { SaveButton, SimpleForm, useRedirect } from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import CloseWithConfirmationModal from '~/components/organisms/CloseWithConfirmationModal';
import AddItemModal from '~/components/organisms/menu-catalog-dnd/AddItemModal';
import EditOrCreateComboModal from '~/components/organisms/menu-catalog-dnd/CreateCombo/EditOrCreateComboModal';
import {
  ListModeModal,
  POSModeModal,
} from '~/components/organisms/menu-catalog-dnd/DisplayGroupCreateEditModal';
import { RealtimeEditConflictDetector } from '~/components/organisms/RealtimeEditConflictDetector';
import { useTheme } from '~/contexts';
import { useGranularConflictDetection } from '~/hooks/useGranularConflictDetection';
import { RESOURCES } from '~/providers/resources';
import isEqual from '~/providers/utils/isEqual';
import { formatNumber } from '~/utils/formatNumber';
import ModalHeader from '../../components/molecules/ModalHeader';
import CatalogContainer from '../../components/organisms/menu-catalog-dnd/CatalogContainer';
import CatalogTitleTree from '../../components/organisms/menu-catalog-dnd/CatalogTitleTree';
import { MenuItem as MenuItemT } from '../../components/organisms/menu-catalog-dnd/types';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import ConfigurableAccordion from './components/Accordion';
import { TableColumn } from './components/ConfigurableTable';
import { findNextAvailablePosition, getValueByPath } from './utils';

export const COLUMNS_WEB = 4;
const COLUMNS_MOBILE = 2;

type ScreenType = 'menu-list' | 'pos-layout';

type Item = {
  displayName: string;
  // locations: string[];
  modifiers?: string;
  price: number;
  image?: string;
};

type ComboModalPayload = {
  path: string;
  tileIndex: number | null;
  initialValues?: MenuItemT;
  create: boolean;
  edit: boolean;
  displayGroupColor?: string;
};

const columns: TableColumn<Item>[] = [
  {
    label: 'Item',
    render: row => {
      return (
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {((row as any).type === 'displayGroup' ||
            ((row as any).showColow && !row.image)) && (
            <Box
              sx={{
                width: '4px',
                height: '25px',
                bgcolor: (row as any).color,
                borderRadius: '4px',
              }}
            />
          )}
          {row.image && (
            <Box sx={{ display: { xs: 'none', md: 'block' } }}>
              <img style={{ width: '30px' }} src={row.image} alt="item" />
            </Box>
          )}
          {row.displayName}
          <Typography variant="caption" color="text.secondary" mt={0.5}>
            {(row as any).type === 'displayGroup' && '(GROUP)'}
            {(row as any).type === 'function' && '(FUNCTION)'}
          </Typography>
        </Box>
      );
    },
  },
  {
    label: 'Modifiers',
    render: row => (
      <Button variant="text" size="small" disabled>
        {row.modifiers || 'Select'}
      </Button>
    ),
    displayAfterSize: 'md',
  },
  {
    label: 'Price',
    render: row => (
      <Typography>
        {typeof row.price === 'number' || typeof row.price === 'string'
          ? formatNumber(row.price / 10000, 'RON')
          : '-'}
      </Typography>
    ),
  },
];

// Inner component needed in order to access form context.
function MenuEditInner({
  setShowCloseModal,
  isFormDirty,
  setIsFormDirty,
}: {
  setShowCloseModal: (show: boolean) => void;
  isFormDirty: boolean;
  setIsFormDirty: (isDirty: boolean) => void;
}) {
  const { setValue, getValues, watch } = useFormContext();
  const { t } = useTranslation('');
  const { theme } = useTheme();
  const redirect = useRedirect();

  const initialValuesRef = useRef(getValues());

  // Store remote data after "Keep My Changes and Merge" for modal consumption
  const [remoteDataAfterMerge, setRemoteDataAfterMerge] = useState<any>(null);
  // Store remote data after "Get Their Changes" for modal consumption
  const [remoteDataAfterGetTheirChanges, setRemoteDataAfterGetTheirChanges] =
    useState<any>(null);

  // Initialize granular conflict detection
  const {
    changeTracker,
    trackChange,
    hasChanges: hasGranularChanges,
  } = useGranularConflictDetection({ autoTrack: false });

  // Update initialValuesRef on mount with fresh data from DB
  useEffect(() => {
    initialValuesRef.current = getValues();
  }, [getValues]);

  const [landscapeView, setLandscapeView] = useState(true);
  const [record, setRecord] = useState(getValues());
  const [screen, setScreen] = useState<ScreenType>(
    record.type === 'pos' || !record.type ? 'pos-layout' : 'menu-list'
  );
  const [displayGroupTree, setDisplayGroupTree] = useState<Array<number>>([]);
  const [displayGroupPath, setDisplayGroupPath] = useState('');
  const [displayGroupColor, setDisplayGroupColor] = useState<string>();
  const [page, setPage] = useState(1);
  const [pageDisplayed, setPageDisplayed] = useState(1);
  const [currentTiles, setCurrentTiles] = useState<Array<MenuItemT>>([]);
  const [startIndex, setStartIndex] = useState(0);
  const [unselectTileTrigger, setUnselectTileTrigger] = useState(0);
  const [isAddingMenuGroup, setIsAddingMenuGroup] = useState(false);
  const [newItemPath, setNewItemPath] = useState('');
  const [editGroupPath, setEditGroupPath] = useState('');
  const [itemToEdit, setItemToEdit] = useState<number>();
  const [newGroupPath, setNewGroupPath] = useState('');
  const [expandedAccordion, setExpandedAccordion] = useState<string | null>(
    null
  );
  const [allDisplayGroups, setAllDisplayGroups] = useState<any[]>([]);
  const [comboModalPayload, setComboModalPayload] =
    useState<ComboModalPayload | null>(null);

  useEffect(() => {
    const allDisplayGroupsArray: any[] = [];
    Object.values(record.pages).forEach((page: any, pageIndex: number) => {
      page.forEach((item: any, itemIndex: number) => {
        if (item.type === 'displayGroup') {
          allDisplayGroupsArray.push({ pageIndex, itemIndex, item });
        }
      });
    });
    setAllDisplayGroups(allDisplayGroupsArray);
  }, [record.pages, record, watch]);

  // Update initialValuesRef when React Admin refetches data (only if no user changes)
  useEffect(() => {
    // Only update if there are no tracked changes (meaning it's a fresh load, not user edits)
    if (!hasGranularChanges && !changeTracker.hasChanges()) {
      const currentValues = getValues();
      if (!isEqual(currentValues, initialValuesRef.current)) {
        initialValuesRef.current = currentValues;
      }
    }
  }, [record, hasGranularChanges, changeTracker, getValues]);

  useEffect(() => {
    const newValues = watch();
    delete newValues._u;
    const oldValues = { ...initialValuesRef.current };
    delete oldValues._u;
    setIsFormDirty(!isEqual(newValues, oldValues));
  }, [watch, record]);

  const firstLevelItems = useMemo(() => {
    return {
      color: 'black',
      displayName: t('menu.posHomeScreen'),
      items: Object.values(record.pages).flatMap(
        (page: any, pageIndex: number) =>
          page
            .map((item: any, itemIndex: number) => ({
              ...item,
              pageIndex,
              itemIndex,
              showColow: true,
            }))
            .filter(
              (item: any) => ['product'].includes(item.type) || !item.type
            )
      ),
    };
  }, [record]);

  useEffect(() => {
    setItemsFromTree();
  }, [record]);

  useEffect(() => {
    const subscription = watch(value => {
      setRecord(value);
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    let path = `pages.${page - 1}`;
    for (const treeIndex of displayGroupTree) {
      path += `[${treeIndex}].items`;
    }

    setDisplayGroupPath(path);
    setItemsFromTree();

    if (displayGroupTree.length) {
      setDisplayGroupColor(
        getValueByPath(record, path.slice(0, -'.items'.length)).color
      );
    } else {
      setDisplayGroupColor(undefined);
    }
  }, [page, displayGroupTree]);

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  useEffect(() => {
    if (isXSmall) {
      setLandscapeView(false);
    }
    setPageDisplayed(
      landscapeView ? page : page * 2 - 1 + (startIndex ? 1 : 0)
    );

    if (displayGroupTree.length) setItemsFromTree();
  }, [page, startIndex, landscapeView, isXSmall]);

  const removeMenuTile = (index: number): void => {
    const tmp = [...currentTiles];
    tmp.splice(index, 1);

    if (displayGroupPath.includes('.items')) {
      const cleanedTiles = tmp.map(item => {
        const { position, ...rest } = item;
        return rest;
      });
      setValue(displayGroupPath, cleanedTiles, {
        shouldDirty: true,
      });
      trackChange(displayGroupPath, cleanedTiles);
    } else {
      setValue(displayGroupPath, tmp, {
        shouldDirty: true,
      });
      trackChange(displayGroupPath, tmp);
    }
  };

  const updateMenuTile = (
    index: number,
    menuTile: Partial<MenuItemT>
  ): void => {
    const newMenu = { ...currentTiles[index], ...menuTile };
    const itemPath = `${displayGroupPath}[${index}]`;

    setValue(itemPath, newMenu, {
      shouldDirty: true,
    });
    trackChange(itemPath, newMenu);
  };

  const setCurrentTree = (index: number) => {
    if (index === displayGroupTree.length) {
      return;
    }

    if (index === 0) {
      setDisplayGroupTree([]);
      return;
    }

    const newTree = displayGroupTree.slice(0, index);
    setDisplayGroupTree(newTree);
  };

  const setItemsFromTree = () => {
    let newItems: any = [...(record.pages[page - 1] || [])];
    let newColor = '';

    for (const treeIndex of displayGroupTree) {
      newColor = newItems[treeIndex].color;
      newItems = newItems[treeIndex].items;
    }

    // Always map items to ensure they have position property
    newItems = newItems.map((el: any, i: number) => {
      return {
        ...el,
        position: el.position || {
          startX: i % (landscapeView ? 4 : 2),
          startY: Math.floor(i / (landscapeView ? 4 : 2)),
          endX: (i % (landscapeView ? 4 : 2)) + 1,
          endY: Math.floor(i / (landscapeView ? 4 : 2)) + 1,
        },
        color:
          displayGroupTree.length &&
          (!el.type || el.type === 'product' || !el.color)
            ? newColor
            : el.color,
      };
    });

    setCurrentTiles(newItems);
  };

  const addNewPage = () => {
    const tmp = [...record.pages, []];
    setPage(tmp.length);
    setValue('pages', tmp, {
      shouldDirty: true,
    });
    trackChange('pages', tmp);
  };

  const deleteCurrentPage = () => {
    const tmp = [...record.pages];
    const length = tmp.length;
    if (length <= 1) return;

    tmp.splice(page - 1, 1);

    setPage(page === length ? page - 1 : page);
    setValue('pages', tmp, {
      shouldDirty: true,
    });
    trackChange('pages', tmp);
  };

  const setPageHelper = (page: number) => {
    setDisplayGroupTree([]);

    if (landscapeView) {
      setPage(page);
      return;
    }

    setPage(Math.floor((page + 1) / 2));
    setStartIndex(page % 2 == 0 ? 2 : 0);
  };

  const handleChangeScreen = (screen: ScreenType) => {
    if (!screen) return;

    setScreen(screen);
  };

  const removeSelectedItems = (selectedRows: number[], path: string) => {
    const tmp = [...getValueByPath(record, path)];
    const result = tmp.filter((_, index) => !selectedRows.includes(index));

    setValue(path, result, {
      shouldDirty: true,
    });
    trackChange(path, result);
  };

  const removeItemsFromPages = (
    selectedItems: { itemIndex: number; pageIndex?: number }[]
  ) => {
    const tmp = record.pages.map((page: any, pageIndex: number) =>
      page.filter(
        (_: any, itemIndex: number) =>
          !selectedItems.some(
            selected =>
              selected.pageIndex === pageIndex &&
              selected.itemIndex === itemIndex
          )
      )
    );

    setValue('pages', tmp, { shouldDirty: true });
    trackChange('pages', tmp);
  };

  if (!record) return <Typography>No menu found</Typography>;

  const handleDragEnd = (result: any) => {
    const { source, destination } = result;
    if (!destination) return;

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const sourceItem = allDisplayGroups[source.index];
    const destinationItem = allDisplayGroups[destination.index];

    if (!sourceItem || !destinationItem) return;

    // Create a copy of pages to modify
    const newPages = [...record.pages];

    // Remove the item from source
    newPages[sourceItem.pageIndex] = newPages[sourceItem.pageIndex].filter(
      (_: any, index: number) => index !== sourceItem.itemIndex
    );

    // Insert the item at destination
    newPages[destinationItem.pageIndex].splice(
      destinationItem.itemIndex,
      0,
      sourceItem.item
    );

    // Update the form
    setValue('pages', newPages, { shouldDirty: true });
    trackChange('pages', newPages);
  };

  const saveReorderedRows = (
    rows: any,
    result: any,
    itemId: string,
    pageIndex: number
  ) => {
    // Create a deep copy of the pages array to avoid mutations
    const newPages = record.pages.map((page: any, pIndex: number) => {
      if (pIndex === pageIndex) {
        return page.map((item: any) => {
          if (item.id === itemId) {
            return { ...item, items: rows };
          }
          return item;
        });
      }
      return page;
    });

    setValue('pages', newPages, { shouldDirty: true });
    trackChange('pages', newPages);

    // Immediately update the record state to ensure UI reflects changes
    setRecord(prevRecord => ({
      ...prevRecord,
      pages: newPages,
    }));
  };

  return (
    <Box sx={{ width: '100%' }}>
      <ModalHeader
        handleClose={() => {
          if (isFormDirty) {
            setShowCloseModal(true);
          } else {
            redirect('list', RESOURCES.HOSPITALITY_CATALOGS);
          }
        }}
        title={
          <CatalogTitleTree
            id={record.id}
            name={record.name}
            items={record.pages[page - 1]}
            tree={displayGroupTree}
            setCurrentTree={setCurrentTree}
            isPosLayout={screen === 'pos-layout'}
          />
        }
      >
        {/* <Button
          sx={{ mr: 2, gap: 1, whiteSpace: 'nowrap' }}
          variant="outlined"
          onClick={() => {
            if (screen === 'menu-list') {
              handleChangeScreen('pos-layout');
            } else {
              handleChangeScreen('menu-list');
              setCurrentTree(0);
            }
          }}
        >
          {screen === 'menu-list' ? (
            <img
              style={{ width: '23px' }}
              src="/assets/monitor-dashboard.svg"
            />
          ) : (
            <ListIcon />
          )}
          {screen === 'menu-list'
            ? `${t('menu.editPOSLayout')}`
            : `${t('menu.menuList')}`}
        </Button> */}
        <SaveButton
          type="button"
          icon={<></>}
          label={t('shared.save')}
          alwaysEnable={isFormDirty}
        />
      </ModalHeader>

      <Box
        sx={{
          display: 'flex',
          width: '100%',
          flexDirection: 'column',
          maxWidth: '1250px',
          marginLeft: 'auto',
          marginRight: 'auto',
        }}
      >
        {screen === 'menu-list' ? (
          <Box
            p={3}
            sx={{
              width: '100%',
              display: 'flex',
              gap: 2,
              flexDirection: 'column',
            }}
          >
            {/* <ConfigurableAccordion
              id="first-level"
              expanded={expandedAccordion === 'first-level'}
              onExpand={id =>
                setExpandedAccordion(id === expandedAccordion ? null : id)
              }
              editItem={(index, pageIndex) => {
                setItemToEdit(index);
                const path = `pages.${pageIndex}`;
                setNewItemPath(path);
              }}
              removeSelectedItems={removeItemsFromPages}
              section={firstLevelItems}
              columns={columns}
              renderAddItem={() => (
                <Button
                  fullWidth
                  onClick={() => {
                    setNewItemPath('pages.0');
                  }}
                  sx={{
                    gap: 1,
                    justifyContent: 'start',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      backgroundColor:
                        theme.palette.mode === 'dark' ? '#343439' : '#F2F2F2',
                      px: 0.7,
                      py: 0.4,
                      borderRadius: '5px',
                    }}
                  >
                    <AddRoundedIcon sx={{ width: '18px' }} />
                  </Box>
                  <Typography sx={{ fontWeight: 500, fontSize: '14px' }}>
                    {t('menu.addItem')}
                  </Typography>
                </Button>
              )}
            /> */}
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="droppable">
                {provided => (
                  <Box
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}
                  >
                    {allDisplayGroups.map(
                      ({ pageIndex, itemIndex, item }, globalIndex) => (
                        <Draggable
                          key={`${pageIndex}-${itemIndex}`}
                          draggableId={`${pageIndex}-${itemIndex}`}
                          index={globalIndex}
                          isDragDisabled={
                            expandedAccordion === `${pageIndex}-${itemIndex}`
                          }
                        >
                          {(provided, snapshot) => (
                            <Box
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <ConfigurableAccordion
                                itemId={item.id}
                                pageIndex={pageIndex}
                                id={`${pageIndex}-${itemIndex}`}
                                expanded={
                                  expandedAccordion ===
                                  `${pageIndex}-${itemIndex}`
                                }
                                onExpand={id =>
                                  setExpandedAccordion(
                                    id === expandedAccordion ? null : id
                                  )
                                }
                                saveReorderedRows={saveReorderedRows}
                                showAddGroup
                                editItem={index => {
                                  const path = `pages.${pageIndex}[${itemIndex}].items`;
                                  const items =
                                    getValueByPath(record, path) || [];
                                  const selectedItem = items[index];

                                  if (selectedItem?.type === 'productCombo') {
                                    const groupPath = path.endsWith('.items')
                                      ? path.slice(0, -'.items'.length)
                                      : '';
                                    const displayGroup =
                                      groupPath.length > 0
                                        ? getValueByPath(record, groupPath)
                                        : undefined;

                                    setItemToEdit(undefined);
                                    setNewItemPath(path);
                                    setComboModalPayload({
                                      path,
                                      tileIndex: index,
                                      initialValues: selectedItem,
                                      create: false,
                                      edit: true,
                                      displayGroupColor: displayGroup?.color,
                                    });
                                    return;
                                  }

                                  setItemToEdit(index);
                                  setComboModalPayload(null);
                                  setNewItemPath(path);
                                }}
                                removeSelectedItems={selectedRows =>
                                  removeSelectedItems(
                                    selectedRows.map(item => item.itemIndex),
                                    `pages.${pageIndex}[${itemIndex}].items`
                                  )
                                }
                                section={item}
                                columns={columns}
                                addGroup={() => {
                                  setIsAddingMenuGroup(true);
                                  setNewGroupPath(
                                    `pages.${pageIndex}[${itemIndex}].items`
                                  );
                                }}
                                editGroup={() => {
                                  setEditGroupPath(
                                    `pages.${pageIndex}[${itemIndex}]`
                                  );
                                }}
                                removeGroup={() => {
                                  removeSelectedItems(
                                    [itemIndex],
                                    `pages.${pageIndex}`
                                  );
                                }}
                                renderAddItem={() => (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      flexDirection: {
                                        xs: 'column',
                                        sm: 'row',
                                      },
                                      gap: 1,
                                      width: '100%',
                                    }}
                                  >
                                    <Button
                                      onClick={() => {
                                        setComboModalPayload(null);
                                        setNewItemPath(
                                          `pages.${pageIndex}[${itemIndex}].items`
                                        );
                                      }}
                                      sx={{
                                        gap: 1,
                                        justifyContent: 'start',
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          backgroundColor:
                                            theme.palette.mode === 'dark'
                                              ? '#26262B'
                                              : '#F2F2F2',
                                          px: 0.7,
                                          py: 0.4,
                                          borderRadius: '5px',
                                        }}
                                      >
                                        <AddRoundedIcon
                                          sx={{
                                            width: '18px',
                                            color: '#4c82ff',
                                          }}
                                        />
                                      </Box>
                                      <Typography
                                        sx={{
                                          fontWeight: 500,
                                          fontSize: '14px',
                                          color: '#4c82ff',
                                        }}
                                      >
                                        {t('menu.addItem')}
                                      </Typography>
                                    </Button>

                                    <Button
                                      onClick={() => {
                                        const path = `pages.${pageIndex}[${itemIndex}].items`;
                                        const groupPath = path.endsWith(
                                          '.items'
                                        )
                                          ? path.slice(0, -'.items'.length)
                                          : '';
                                        const displayGroup =
                                          groupPath.length > 0
                                            ? getValueByPath(record, groupPath)
                                            : undefined;

                                        setItemToEdit(undefined);
                                        setNewItemPath(path);
                                        setComboModalPayload({
                                          path,
                                          tileIndex: null,
                                          create: true,
                                          edit: false,
                                          displayGroupColor:
                                            displayGroup?.color,
                                        });
                                      }}
                                      sx={{
                                        gap: 1,
                                        justifyContent: 'start',
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          backgroundColor:
                                            theme.palette.mode === 'dark'
                                              ? '#26262B'
                                              : '#F2F2F2',
                                          px: 0.7,
                                          py: 0.4,
                                          borderRadius: '5px',
                                        }}
                                      >
                                        <AddRoundedIcon
                                          sx={{
                                            width: '18px',
                                            color: '#4c82ff',
                                          }}
                                        />
                                      </Box>
                                      <Typography
                                        sx={{
                                          fontWeight: 500,
                                          fontSize: '14px',
                                          color: '#4c82ff',
                                        }}
                                      >
                                        Add Combo Product
                                      </Typography>
                                    </Button>
                                  </Box>
                                )}
                              />
                            </Box>
                          )}
                        </Draggable>
                      )
                    )}
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            </DragDropContext>

            <Button
              onClick={() => setIsAddingMenuGroup(true)}
              variant={'outlined'}
              sx={{
                borderRadius: '10px',
                alignItems: 'center',
                justifyContent: 'start',
                gap: 1,
              }}
            >
              <AddRoundedIcon sx={{ width: '18px' }} />
              <Typography sx={{ fontWeight: 500, fontSize: '14px' }}>
                {t('menu.addMenuGroup')}
              </Typography>
            </Button>
          </Box>
        ) : (
          <Box
            sx={{
              p: 3,
              width: '100%',
              maxWidth: '1300px',
              display: { xs: 'flex', md: 'grid' },
              gridTemplateColumns: '3fr 1fr',
              flexDirection: { xs: 'column', md: 'row' },
              alignItems: { xs: 'center', md: 'flex-start' },
              justifyContent: 'center',
              mx: 'auto',
            }}
          >
            <Box
              onClick={() => setUnselectTileTrigger(prev => prev + 1)}
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                width: '100%',
                height: '100%',
              }}
            />
            <CatalogContainer
              columns={landscapeView ? COLUMNS_WEB : COLUMNS_MOBILE}
              data={currentTiles}
              path={displayGroupPath}
              updateMenuTile={updateMenuTile}
              removeMenuTile={removeMenuTile}
              openDisplayGroup={index => {
                setDisplayGroupTree(prev => [...prev, index]);
              }}
              startIndex={startIndex}
              unselectTileTrigger={unselectTileTrigger}
              displayGroupColor={displayGroupColor}
              recordType={record.type}
            />
            <Box sx={sideMenuStyles}>
              <Typography
                variant="caption"
                color="custom.gray600"
                sx={{ userSelect: 'none' }}
              >
                {t('menu.orientation')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
                <Button
                  //  @ts-ignore
                  variant="contained"
                  onClick={() => {
                    setStartIndex(0);
                    setLandscapeView(true);
                  }}
                  sx={{
                    width: '100%',
                    bgcolor: landscapeView
                      ? 'primary.main'
                      : 'rgba(0, 0, 0, 0.02) !important',
                    color: landscapeView ? 'white' : 'primary.main',
                  }}
                >
                  <StayCurrentLandscapeIcon sx={{ mr: 1 }} /> {t('menu.tablet')}
                </Button>
                <Button
                  //  @ts-ignore
                  variant="contained"
                  onClick={() => {
                    setStartIndex(0);
                    setLandscapeView(false);
                  }}
                  sx={{
                    width: '100%',
                    bgcolor: !landscapeView
                      ? 'primary.main'
                      : 'rgba(0, 0, 0, 0.02) !important',
                    color: !landscapeView ? 'white' : 'primary.main',
                  }}
                >
                  <StayCurrentPortraitIcon sx={{ mr: 1 }} /> {t('menu.phone')}
                </Button>
              </Box>

              {!displayGroupTree.length && (
                <>
                  <Typography
                    variant="caption"
                    mt={2}
                    color="custom.gray600"
                    sx={{ userSelect: 'none' }}
                  >
                    {t('menu.page')}
                  </Typography>

                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Button
                      //  @ts-ignore
                      variant="contained-light"
                      onClick={() => setPageHelper(pageDisplayed - 1)}
                      disabled={pageDisplayed === 1}
                    >
                      <ExpandLessIcon />
                    </Button>

                    <Box sx={pageSelectorStyle}>
                      <Typography
                        variant="body2"
                        color="custom.gray600"
                        fontWeight={100}
                        fontSize="12px"
                      >
                        {t('menu.page')}
                      </Typography>
                      <Typography variant="body2">
                        {pageDisplayed} /{' '}
                        {record.pages.length *
                          (COLUMNS_WEB /
                            (landscapeView ? COLUMNS_WEB : COLUMNS_MOBILE))}
                      </Typography>
                    </Box>
                    <Button
                      //  @ts-ignore
                      variant="contained-light"
                      onClick={() => setPageHelper(pageDisplayed + 1)}
                      disabled={
                        pageDisplayed ===
                        record.pages.length *
                          (COLUMNS_WEB /
                            (landscapeView ? COLUMNS_WEB : COLUMNS_MOBILE))
                      }
                    >
                      <ExpandMoreIcon />
                    </Button>
                  </Box>

                  <Button
                    //  @ts-ignore
                    variant="contained-light"
                    sx={{ width: '100%' }}
                    onClick={addNewPage}
                  >
                    {t('menu.addPage')}
                  </Button>

                  <Button
                    //  @ts-ignore
                    variant="contained-light"
                    sx={{ width: '100%' }}
                    color="error"
                    onClick={deleteCurrentPage}
                    disabled={record.pages.length <= 1}
                  >
                    {t('menu.removePage')}
                  </Button>
                </>
              )}
            </Box>
          </Box>
        )}
      </Box>
      {isAddingMenuGroup &&
        (record.type === 'list' ? (
          <ListModeModal
            mode="create"
            data={
              !!newGroupPath
                ? getValueByPath(record, newGroupPath)
                : record.pages[0]
            }
            path={newGroupPath || 'pages.0'}
            onClose={() => {
              setIsAddingMenuGroup(false);
              setNewGroupPath('');
            }}
            position={
              !!newGroupPath
                ? findNextAvailablePosition(
                    getValueByPath(record, newGroupPath)
                  )
                : findNextAvailablePosition(record.pages[0])
            }
          />
        ) : (
          <POSModeModal
            mode="create"
            data={
              !!newGroupPath
                ? getValueByPath(record, newGroupPath)
                : record.pages[0]
            }
            path={newGroupPath || 'pages.0'}
            onClose={() => {
              setIsAddingMenuGroup(false);
              setNewGroupPath('');
            }}
            position={
              !!newGroupPath
                ? findNextAvailablePosition(
                    getValueByPath(record, newGroupPath)
                  )
                : findNextAvailablePosition(record.pages[0])
            }
          />
        ))}
      {!!newItemPath && !comboModalPayload && (
        <AddItemModal
          recordType={record.type}
          initialValues={
            itemToEdit !== undefined
              ? getValueByPath(record, newItemPath)[itemToEdit]
              : undefined
          }
          displayGroupColor={
            newItemPath.endsWith('.items') &&
            getValueByPath(record, newItemPath.slice(0, -'.items'.length)).color
          }
          path={
            itemToEdit !== undefined
              ? `${newItemPath}[${itemToEdit}]`
              : newItemPath
          }
          onClose={() => {
            setNewItemPath('');
            setItemToEdit(undefined);
          }}
          position={
            !itemToEdit
              ? findNextAvailablePosition(getValueByPath(record, newItemPath))
              : { startX: 0, startY: 0, endX: 1, endY: 1 }
          }
          trackParentChange={trackChange}
          remoteDataAfterMerge={remoteDataAfterMerge}
          remoteDataAfterGetTheirChanges={remoteDataAfterGetTheirChanges}
        />
      )}

      {editGroupPath &&
        (record.type === 'list' ? (
          <ListModeModal
            mode="edit"
            path={editGroupPath}
            onClose={() => setEditGroupPath('')}
          />
        ) : (
          <POSModeModal
            mode="edit"
            path={editGroupPath}
            initialColorValue={getValueByPath(record, editGroupPath).color}
            onClose={() => setEditGroupPath('')}
          />
        ))}

      {comboModalPayload && (
        <EditOrCreateComboModal
          path={comboModalPayload.path}
          data={getValueByPath(record, comboModalPayload.path) || []}
          tileIndex={comboModalPayload.tileIndex}
          initialValues={comboModalPayload.initialValues}
          create={comboModalPayload.create}
          edit={comboModalPayload.edit}
          displayGroupColor={comboModalPayload.displayGroupColor}
          onClose={() => {
            setComboModalPayload(null);
            setNewItemPath('');
            setItemToEdit(undefined);
          }}
          trackParentChange={trackChange}
          remoteDataAfterMerge={remoteDataAfterMerge}
          remoteDataAfterGetTheirChanges={remoteDataAfterGetTheirChanges}
        />
      )}

      {/* Realtime Granular Conflict Detection */}
      <RealtimeEditConflictDetector
        translationNamespace="shared"
        changeTracker={changeTracker}
        onGetTheirChanges={() => {
          // Update initial values reference when accepting remote changes
          // This ensures the save button becomes disabled after accepting their changes
          const currentData = getValues();
          initialValuesRef.current = currentData;
          // Store remote data for modals to update their baseline
          setRemoteDataAfterGetTheirChanges(currentData);
          // Clear after a short delay
          setTimeout(() => setRemoteDataAfterGetTheirChanges(null), 100);
        }}
        onKeepMyChangesAndMerge={remoteData => {
          // DON'T update initialValuesRef here because user still has unsaved changes
          // The save button should remain active until they save or discard

          // When "Keep My Changes and Merge" is chosen, store the remote data
          // so open modals can update their remote baseline for proper cancel behavior
          setRemoteDataAfterMerge(remoteData);
          // Clear after a short delay to ensure modals have processed it
          setTimeout(() => setRemoteDataAfterMerge(null), 100);
        }}
      />
    </Box>
  );
}

export default function MenuEdit() {
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [isFormDirty, setIsFormDirty] = useState(false);
  const redirect = useRedirect();

  return (
    <EditDialog
      onClose={() => {
        if (isFormDirty) {
          setShowCloseModal(true);
        } else {
          redirect('list', RESOURCES.HOSPITALITY_CATALOGS);
        }
      }}
      {...getFullscreenModalProps()}
      mutationMode="pessimistic"
    >
      <SimpleForm
        toolbar={false}
        sx={{ p: 0, height: '100vh', '> div': { height: '100%' } }}
      >
        <MenuEditInner
          setShowCloseModal={setShowCloseModal}
          isFormDirty={isFormDirty}
          setIsFormDirty={setIsFormDirty}
        />
        <CloseWithConfirmationModal
          open={showCloseModal}
          onClose={() => setShowCloseModal(false)}
          onDiscard={() => {
            setShowCloseModal(false);
            redirect('list', RESOURCES.HOSPITALITY_CATALOGS);
          }}
          saveButton={
            <SaveButton
              type="button"
              icon={<></>}
              alwaysEnable
              // disabled={!isDirty}
              onClick={() => setShowCloseModal(false)}
            />
          }
        />
      </SimpleForm>
    </EditDialog>
  );
}

const pageSelectorStyle = {
  py: 1,
  px: 2,
  height: '48px',
  display: 'flex',
  alignItems: 'center',
  border: '1px solid',
  borderRadius: '6px',
  borderColor: 'custom.gray400',
  gap: '4px',
  userSelect: 'none',
};

const sideMenuStyles = {
  display: 'flex',
  flexDirection: 'column',
  gap: { xs: 1, md: 1.5 },
  width: '100%',
  maxWidth: { xs: '400px', md: '220px' },
  mx: 'auto',
  px: 1,
};
