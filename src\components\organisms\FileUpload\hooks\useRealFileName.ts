import { useCallback, useEffect, useMemo, useState } from 'react';

import { PrivateFileContext, UploadedFile } from '../types/fileUpload';
import {
  getRealFileName,
  getRealFileNameWithExtension,
  preloadFilenames,
} from '../utils/fileMetadataResolver';

interface UseRealFileNameResult {
  filename: string | null;
  loading: boolean;
  error: Error | null;
}

interface UseRealFileNamesResult {
  filenames: Map<string, string>;
  loading: boolean;
  errors: Map<string, Error>;
}

/**
 * Hook to get the real filename for a single file
 * Optimized for the immutable nature of uploaded files
 */
export const useRealFileName = (
  file: UploadedFile | null,
  context?: PrivateFileContext,
  options?: {
    withExtension?: boolean;
    immediate?: boolean;
  }
): UseRealFileNameResult => {
  const { withExtension = false, immediate = true } = options || {};

  const [filename, setFilename] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchFilename = useCallback(async () => {
    if (!file) {
      setFilename(null);
      setLoading(false);
      setError(null);
      return;
    }

    // For temporary files with rn field, return immediately (no loading state needed)
    if (file.x && file.rn) {
      const result = withExtension ? `${file.rn}.${file.e}` : file.rn;
      setFilename(result);
      setLoading(false);
      setError(null);
      return;
    }

    // For permanent files, show loading state and fetch from metadata
    setLoading(true);
    setError(null);

    try {
      const result = withExtension
        ? await getRealFileNameWithExtension(file, context)
        : await getRealFileName(file, context);

      setFilename(result);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err : new Error('Failed to fetch filename');
      setError(errorMessage);
      // Fallback to file.f
      setFilename(withExtension ? `${file.f}.${file.e}` : file.f);
    } finally {
      setLoading(false);
    }
  }, [file, context, withExtension]);

  useEffect(() => {
    if (immediate) {
      fetchFilename();
    }
  }, [fetchFilename, immediate]);

  return { filename, loading, error };
};

/**
 * Hook to get real filenames for multiple files with batch optimization
 * Leverages the immutable nature of files for efficient caching
 */
export const useRealFileNames = (
  files: UploadedFile[],
  context?: PrivateFileContext,
  options?: {
    withExtension?: boolean;
    immediate?: boolean;
  }
): UseRealFileNamesResult => {
  const { withExtension = false, immediate = true } = options || {};

  const [filenames, setFilenames] = useState<Map<string, string>>(new Map());
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Map<string, Error>>(new Map());

  // Create a stable key for files array to prevent unnecessary re-renders
  const filesKey = useMemo(() => {
    return files.map(f => `${f.f}.${f.e}-${f.x ? 'temp' : 'perm'}`).join(',');
  }, [files]);

  const fetchFilenames = useCallback(async () => {
    if (files.length === 0) {
      setFilenames(new Map());
      setLoading(false);
      setErrors(new Map());
      return;
    }

    setLoading(true);
    setErrors(new Map());

    const newFilenames = new Map<string, string>();
    const newErrors = new Map<string, Error>();

    try {
      // Separate temporary and permanent files
      const tempFiles = files.filter(f => f.x && f.rn);
      const permanentFiles = files.filter(f => !f.x || !f.rn);

      // Handle temporary files immediately (no async needed)
      tempFiles.forEach(file => {
        if (file.rn) {
          const key = `${file.f}.${file.e}`;
          const result = withExtension ? `${file.rn}.${file.e}` : file.rn;
          newFilenames.set(key, result);
        }
      });

      // Process permanent files with batch optimization
      if (permanentFiles.length > 0) {
        // Try to use batch preloading first
        try {
          const batchResults = await preloadFilenames(permanentFiles, context);

          for (const [key, cachedFilename] of batchResults.entries()) {
            if (withExtension) {
              // If we need extension and cached result doesn't have it, add it
              const file = permanentFiles.find(f => `${f.f}.${f.e}` === key);
              if (file && !cachedFilename.includes('.')) {
                newFilenames.set(key, `${cachedFilename}.${file.e}`);
              } else {
                newFilenames.set(key, cachedFilename);
              }
            } else {
              newFilenames.set(key, cachedFilename);
            }
          }
        } catch (batchError) {
          // Fallback to individual processing if batch fails
          console.warn(
            'Batch filename loading failed, falling back to individual requests:',
            batchError
          );

          const individualPromises = permanentFiles.map(async file => {
            const key = `${file.f}.${file.e}`;
            try {
              const result = withExtension
                ? await getRealFileNameWithExtension(file, context)
                : await getRealFileName(file, context);

              return { key, filename: result, error: null };
            } catch (err) {
              const errorMessage =
                err instanceof Error
                  ? err
                  : new Error('Failed to fetch filename');
              const fallback = withExtension ? `${file.f}.${file.e}` : file.f;
              return { key, filename: fallback, error: errorMessage };
            }
          });

          const results = await Promise.all(individualPromises);

          results.forEach(({ key, filename, error }) => {
            newFilenames.set(key, filename);
            if (error) {
              newErrors.set(key, error);
            }
          });
        }
      }

      setFilenames(newFilenames);
      setErrors(newErrors);
    } catch (err) {
      console.error('Failed to fetch filenames:', err);
      // Set fallback filenames for all files
      files.forEach(file => {
        const key = `${file.f}.${file.e}`;
        const fallback = withExtension ? `${file.f}.${file.e}` : file.f;
        newFilenames.set(key, fallback);
      });
      setFilenames(newFilenames);
    } finally {
      setLoading(false);
    }
  }, [files, context, withExtension, filesKey]);

  useEffect(() => {
    if (immediate) {
      fetchFilenames();
    }
  }, [fetchFilenames, immediate]);

  return { filenames, loading, errors };
};

/**
 * Hook to get a single filename from a batch result
 * Useful when you have a batch result and want to extract a specific file's name
 */
export const useFileNameFromBatch = (
  file: UploadedFile | null,
  batchResult: UseRealFileNamesResult
): UseRealFileNameResult => {
  const key = file ? `${file.f}.${file.e}` : null;

  return {
    filename: key ? batchResult.filenames.get(key) || null : null,
    loading: batchResult.loading,
    error: key ? batchResult.errors.get(key) || null : null,
  };
};

/**
 * Utility hook for getting display filename with fallback
 * Returns either the real filename or the unique filename as fallback
 */
export const useDisplayFileName = (
  file: UploadedFile | null,
  context?: PrivateFileContext,
  options?: { withExtension?: boolean }
): string => {
  // Always call the hook but pass null when we don't need it
  // This ensures hooks are called in the same order every time
  const shouldFetchFromMetadata = file && !file.x && !file.rn;
  const { filename, loading } = useRealFileName(
    shouldFetchFromMetadata ? file : null,
    context,
    options
  );

  if (!file) return '';

  // For temporary files, return immediately (no metadata request needed)
  if (file.x && file.rn) {
    return options?.withExtension ? `${file.rn}.${file.e}` : file.rn;
  }

  // For permanent files, if we still have the rn field, use it directly
  // This handles files that were recently uploaded and still have rn data
  if (!file.x && file.rn) {
    return options?.withExtension ? `${file.rn}.${file.e}` : file.rn;
  }

  // For permanent files without rn field, use the fetched filename
  if (shouldFetchFromMetadata) {
    // Show loading state while fetching from metadata
    if (loading) return 'Loading...';

    // Return fetched filename or fallback to unique filename
    const fallback = options?.withExtension ? `${file.f}.${file.e}` : file.f;
    return filename || fallback;
  }

  // Fallback case (shouldn't reach here normally)
  const fallback = options?.withExtension ? `${file.f}.${file.e}` : file.f;
  return fallback;
};
