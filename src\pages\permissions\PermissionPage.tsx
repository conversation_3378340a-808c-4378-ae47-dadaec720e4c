import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '../../components/molecules/PageTitle';
import PermissionCreate from './PermissionCreate';
import PermissionEdit from './PermissionEdit';
import PermissionList from './PermissionList';

export default function PermissionPage() {
  const { t } = useTranslation();

  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
          mt: 2,
        }}
        title={t('permissions.title')}
        description={
          <>
            {t('permissions.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <PermissionList />
      <PermissionEdit />
      <PermissionCreate />
    </Box>
  );
}
