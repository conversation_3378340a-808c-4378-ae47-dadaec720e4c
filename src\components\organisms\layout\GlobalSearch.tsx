import React, { useEffect, useMemo, useRef, useState } from 'react';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import EastRoundedIcon from '@mui/icons-material/EastRounded';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Inventory2OutlinedIcon from '@mui/icons-material/Inventory2Outlined';
import SearchIcon from '@mui/icons-material/Search';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  Divider,
  IconButton,
  InputAdornment,
  ListItemIcon,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useSearch } from '@react-admin/ra-search';
import debounce from 'lodash/debounce';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { RESOURCES } from '~/providers/resources';
import { useTheme } from '../../../contexts';
import menuConfig from '../../../data/menu-items';

function GlobalSearchInner({ handleClose, inputRef }: any) {
  const [query, setQuery] = useState<string>('');
  const navigate = useNavigate();
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { theme } = useTheme();
  const [search, { data: raSearchResults = [], isPending: raLoading }] =
    useSearch();

  const { t } = useTranslation();

  type MenuItemI = {
    label: string;
    href?: string;
    icon?: React.ReactNode;
    items?: MenuItemI[];
    searchable?: boolean;
  };

  function removeDiacritics(str: string): string {
    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }

  function searchMenuItems(searchWord: string): MenuItemI[] {
    const results: MenuItemI[] = [];

    menuConfig.forEach(menuItem => {
      const matchingItems = findMatchingItems(menuItem, searchWord);
      if (matchingItems.length > 0) {
        results.push(...matchingItems);
      }
    });

    return results;
  }

  function findMatchingItems(
    menuItem: MenuItemI,
    searchWord: string
  ): MenuItemI[] {
    const results: MenuItemI[] = [];
    const translatedWord = t(`menu.${menuItem.label}`);
    const normalizedTranslatedWord = removeDiacritics(
      translatedWord.toLowerCase()
    );
    const normalizedSearchWord = removeDiacritics(searchWord.toLowerCase());

    if (
      normalizedTranslatedWord.includes(normalizedSearchWord) &&
      menuItem.href &&
      menuItem.searchable
    ) {
      results.push(menuItem);
    }

    if (menuItem.items) {
      menuItem.items.forEach(subMenuItem => {
        const matchingItems = findMatchingItems(subMenuItem, searchWord);
        if (matchingItems.length > 0) {
          results.push(...matchingItems);
        }
      });
    }

    return results;
  }

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setIsLoading(true);
        const menuResults = searchMenuItems(value);
        setSearchResults(menuResults);
        search(value);
        setIsLoading(false);
      }, 500),
    [search]
  );

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setQuery(event.target.value);
    debouncedSearch(event.target.value);
  };

  const handleClick = (el: any) => {
    navigate(el.href);
    handleClose();
  };

  const RaResultsIcons: Record<string, React.ReactNode> = useMemo(
    () => ({
      [RESOURCES.DEVICES]: (
        <img
          src="/assets/monitor-dashboard.svg"
          alt="devices"
          style={{
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.TEAM_MEMBERS]: (
        <img
          src="/assets/menu-icons/team.svg"
          alt="teamMembers"
          style={{
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.TEAM_MEMBERS_WORKING_AREA]: (
        <img
          src="/assets/menu-icons/team.svg"
          alt="teamMembersWorkingArea"
          style={{
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.HOSPITALITY_CATEGORIES]: (
        <img
          src="/assets/icons/category-icon.svg"
          alt="hospitalityCategories"
          style={{
            maxWidth: '24px',
            maxHeight: '24px',
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.HOSPITALITY_CATALOGS]: (
        <img
          src="/assets/menu-icons/menus.svg"
          alt="floorPlans"
          style={{
            maxWidth: '24px',
            maxHeight: '24px',
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.LOCATIONS]: (
        <img
          src="/assets/icons/storefront.svg"
          alt="posDevices"
          style={{
            maxWidth: '24px',
            maxHeight: '24px',
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.PERMISSIONS]: (
        <img
          src="/assets/menu-icons/role.svg"
          alt="posDevices"
          style={{
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
      [RESOURCES.PREP_STATIONS]: (
        <img
          src="/assets/icons/prepStation-icon.svg"
          alt="posDevices"
          style={{
            maxWidth: '24px',
            maxHeight: '24px',
            filter:
              theme.palette.mode === 'light'
                ? 'brightness(0) saturate(100%) invert(37%) sepia(0%) saturate(1192%) hue-rotate(137deg) brightness(89%) contrast(85%)'
                : 'brightness(0) saturate(100%) invert(85%) sepia(1%) saturate(238%) hue-rotate(326deg) brightness(89%) contrast(86%)',
          }}
        />
      ),
    }),
    []
  );

  const RaResultsCategories: Record<string, string> = useMemo(
    () => ({
      [RESOURCES.DEVICES]: t('globalSearch.devices'),
      [RESOURCES.TEAM_MEMBERS]: t('globalSearch.members'),
      [RESOURCES.HOSPITALITY_CATEGORIES]: t('globalSearch.groups'),
      [RESOURCES.HOSPITALITY_CATALOGS]: t('globalSearch.catalogs'),
      [RESOURCES.LOCATIONS]: t('globalSearch.sellPoints'),
      [RESOURCES.PERMISSIONS]: t('globalSearch.permissions'),
      [RESOURCES.PREP_STATIONS]: t('globalSearch.prepStations'),
    }),
    []
  );

  return (
    <>
      <Box
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: 1,
          backgroundColor: theme.palette.background.paper,
          borderBottom: '1px solid #dbdbdb',
        }}
      >
        {' '}
        <Box sx={{ display: 'flex' }}>
          <TextField
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ fontSize: 25 }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="start">
                  <CloseRoundedIcon
                    onClick={() => {
                      setQuery('');
                    }}
                    sx={{
                      fontSize: 25,
                      cursor: 'pointer',
                    }}
                  />
                </InputAdornment>
              ),
            }}
            inputRef={inputRef}
            value={query}
            onChange={handleChange}
            placeholder={t('dashboard.headerSearchPlaceholder')}
            autoFocus
            sx={{
              m: 0,
              flex: 1,
              position: 'sticky !important',
              top: 0,
              borderBottom: '1.5px solid ',
              borderColor: '#dbdbdb',

              '& fieldset': {
                borderRadius: '0 !important',
                width: '100%',
                border: 'none',
              },
              '& .MuiInputBase-root': {
                height: '55px',
                width: '100%',
              },
            }}
          />
          <Button
            sx={{
              borderRadius: 0,
              borderColor: '#dbdbdb !important',
              backgroundColor:
                theme.palette.mode === 'light' ? '#efefef' : '#3d3d3d',
              borderBottom: '1.5px solid #dbdbdb',
              width: '100px',
              '&:hover': {
                backgroundColor:
                  theme.palette.mode === 'light' ? '' : '#2b2b2b',
              },
            }}
            onClick={handleClose}
          >
            {t('dashboard.headerSearchClose')}
          </Button>
        </Box>
      </Box>
      {query ? (
        <Box>
          <Typography
            variant="body1"
            sx={{ pt: 3, px: 3, pb: 1, fontSize: 14 }}
          >
            <span style={{ color: '#757575' }}>
              {t('dashboard.headerSearchResults')}
            </span>{' '}
            {query}
          </Typography>
          {isLoading ? (
            <Box
              sx={{
                display: 'flex',
                width: '100%',
                alignItems: 'center',
                justifyContent: 'center',

                marginBottom: '30px',
              }}
            >
              <CircularProgress sx={{ color: 'black' }} size={30} />
            </Box>
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                px: 2,
                pb: 2,
              }}
            >
              {searchResults?.map((el: any, index: number) => {
                return (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      transition: 'all 0.2s ease',
                      borderColor: '#F2F2F2',
                      cursor: 'pointer',
                      py: 2,
                      px: 2,
                      borderBottom:
                        index !== searchResults.length - 1
                          ? '1px solid rgba(139, 139, 139, 0.14)'
                          : '',
                      '&:hover': {
                        backgroundColor:
                          theme.palette.mode === 'light'
                            ? '#E6F0FF'
                            : '#2B2B30',
                      },
                    }}
                    onClick={() => {
                      handleClick(el);
                    }}
                  >
                    <EastRoundedIcon sx={{ mr: 2 }} />
                    <Box sx={{ mr: 2 }}>
                      <Typography variant="body1">
                        {t(`menu.${el.label}`)}
                      </Typography>
                      <Typography variant="subtitle2" sx={{ textTransform: 'capitalize', color: el.href.toString().split('/').join('-').split('-').includes('report') ? 'primary.main' : '#bab8b8' }}> 
                        {el.href.toString().split('/').pop()}
                      </Typography>
                    </Box>
                    {el.icon && (
                      <ListItemIcon
                        sx={{
                          filter:
                            theme.palette.mode === 'light'
                              ? ''
                              : 'invert(1) !important',
                        }}
                      >
                        {el.icon}
                      </ListItemIcon>
                    )}
                  </Box>
                );
              })}
              <Box>
                <Divider
                  sx={{
                    borderColor:
                      theme.palette.mode === 'light' ? '#474747' : 'white',
                    marginY: '20px',
                  }}
                />
                {raSearchResults.map((result, index) => (
                  <Box
                    key={`ra-${index}`}
                    onClick={() => {
                      navigate(result.url);
                      handleClose();
                    }}
                    sx={{
                      cursor: 'pointer',
                      px: 2,
                      py: 2,
                      display: 'flex',
                      width: '100%',
                      gap: 4,
                      borderBottom: '1px solid rgba(139, 139, 139, 0.14)',
                      '&:hover': {
                        backgroundColor:
                          theme.palette.mode === 'light'
                            ? '#E6F0FF'
                            : '#2B2B30',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {RaResultsIcons[result.type]}
                      <Typography
                        variant="body1"
                        sx={{
                          color:
                            theme.palette.mode === 'light'
                              ? '#5b5b5b'
                              : '#bab8b8',
                        }}
                      >
                        {result.content.label}
                      </Typography>
                    </Box>

                    <Typography
                      variant="body1"
                      sx={{
                        color:
                          theme.palette.mode === 'light'
                            ? '#5b5b5b'
                            : '#bab8b8',
                        fontSize: '12px',
                      }}
                    >
                      {RaResultsCategories[result.type]}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </Box>
      ) : (
        <Box sx={{ display: 'flex', alignItems: 'center', p: 4 }}>
          <InfoOutlinedIcon sx={{ fontSize: 21, color: '#0168FF' }} />
          <Typography
            sx={{ color: '#0168FF', fontSize: 14 }}
            variant="body2"
            ml={1.5}
            fontWeight={300}
          >
            {t('dashboard.headerSearchPrompt')}
          </Typography>
        </Box>
      )}
    </>
  );
}

export default function GlobalSearch() {
  const [open, setOpen] = useState<boolean>(false);
  const textFieldRef = useRef<HTMLInputElement>(null);
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (open && textFieldRef.current) {
      setTimeout(() => {
        textFieldRef.current?.focus();
      }, 100);
    }
  }, [open]);

  return (
    <>
      <IconButton onClick={() => setOpen(true)}>
        <SearchOutlinedIcon />
      </IconButton>

      <Dialog
        open={open}
        onClose={handleClose}
        scroll="paper"
        PaperProps={{
          sx: { mt: '0px', borderTopLeftRadius: 0, borderTopRightRadius: 0 },
        }}
        sx={{
          '& .MuiDialog-container': {
            alignItems: 'flex-start',
            '& .MuiPaper-root': {
              width: isXSmall ? '100%' : '70%',
              maxWidth: '1300px',
              margin: 0,
            },
          },
        }}
      >
        <GlobalSearchInner handleClose={handleClose} inputRef={textFieldRef} />
      </Dialog>
    </>
  );
}
