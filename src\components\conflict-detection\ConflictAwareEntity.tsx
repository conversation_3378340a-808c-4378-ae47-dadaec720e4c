/**
 * ConflictAwareEntity - Wrapper for visual elements with entity-level conflict detection
 *
 * Wraps any visual element (tables, cards, etc.) to provide remote update highlighting
 * and local changes indication. Similar to ConflictAwareInput but for entity-level tracking.
 *
 * For draggable/absolutely positioned elements, use the useEntityConflictStatus hook
 * and EntityConflictIndicator component instead.
 */

import React from 'react';
import EditIcon from '@mui/icons-material/Edit';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import UpdateIcon from '@mui/icons-material/Update';
import WarningIcon from '@mui/icons-material/Warning';
import { Box, SxProps, Theme, Tooltip, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useConflictDetectionOptional } from '../../hooks/useConflictDetection';
import {
  deepEqual,
  getValueAtPath,
  pathMatchesAnyPattern,
} from '../../hooks/useConflictDetection/entityRegistry';
import { buildFullPath } from '../../hooks/useConflictDetection/pathUtils';
import { useResolveUserName } from '../../hooks/useResolveUserName';

// ═══════════════════════════════════════════════════════════════════════════════
// Types
// ═══════════════════════════════════════════════════════════════════════════════

export interface EntityConflictStatus {
  /** Whether entity was updated remotely */
  wasUpdatedRemotely: boolean;
  /** Whether entity has local changes */
  isDirty: boolean;
  /** Whether entity has conflicts */
  hasConflict: boolean;
  /** Should show conflict indicator (red) */
  showConflict: boolean;
  /** Should show mixed changes indicator (purple) */
  showMixed: boolean;
  /** Should show local changes indicator (orange) */
  showLocalChanges: boolean;
  /** Should show remote update indicator (blue) */
  showRemoteUpdate: boolean;
  /** Whether any indicator should be shown */
  hasIndicator: boolean;
  /** Highlight color for styling */
  highlightColor: string | null;
  /** Box shadow style string to apply to the element */
  boxShadowStyle: string | null;
  /** Remote change info */
  remoteInfo: { userId?: string; timestamp?: number } | null;
  /** Resolved user name */
  resolvedUserName: string | null;
}

export interface ConflictAwareEntityProps {
  /**
   * The visual element to wrap (table, card, etc.)
   */
  children: React.ReactNode;
  /**
   * The entity ID to track (e.g., "number:20" for floor plan tables)
   */
  entityId: string;
  /**
   * Whether to show conflict indicator (red)
   * @default true
   */
  showConflictIndicator?: boolean;
  /**
   * Whether to show remote update indicator (blue)
   * @default true
   */
  showRemoteUpdateIndicator?: boolean;
  /**
   * Whether to show local changes indicator (orange) - when entity has local changes AND remote changes but no conflict
   * @default true
   */
  showLocalChangesIndicator?: boolean;
  /**
   * Custom styling for the wrapper
   */
  sx?: SxProps<Theme>;
  /**
   * Label for the entity (shown in tooltips)
   */
  entityLabel?: string;
}

export interface EntityConflictIndicatorProps {
  /** Entity ID to track */
  entityId: string;
  /** Label for tooltips */
  entityLabel?: string;
  /** Position style for the indicator */
  style?: React.CSSProperties;
  /** Whether to show conflict indicator */
  showConflictIndicator?: boolean;
  /** Whether to show remote update indicator */
  showRemoteUpdateIndicator?: boolean;
  /** Whether to show local changes indicator */
  showLocalChangesIndicator?: boolean;
}

// ═══════════════════════════════════════════════════════════════════════════════
// Hook: useEntityConflictStatus
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * Hook to get conflict status for an entity.
 * Use this for custom rendering (e.g., draggable elements).
 *
 * @example
 * ```tsx
 * const status = useEntityConflictStatus(`number:${table.number}`);
 * // Apply status.boxShadowStyle to your element
 * // Render EntityConflictIndicator separately
 * ```
 */
export function useEntityConflictStatus(
  entityId: string,
  options?: {
    showConflictIndicator?: boolean;
    showRemoteUpdateIndicator?: boolean;
    showLocalChangesIndicator?: boolean;
  }
): EntityConflictStatus {
  const {
    showConflictIndicator = true,
    showRemoteUpdateIndicator = true,
    showLocalChangesIndicator = true,
  } = options ?? {};

  const conflictCtx = useConflictDetectionOptional();

  // Debug: log available entities and remote changes
  // console.log(`[useEntityConflictStatus] entityId=${entityId}, hasCtx=${!!conflictCtx}, entities:`, conflictCtx?.state?.entities?.keys?.() ? Array.from(conflictCtx.state.entities.keys()) : 'N/A');

  // Check entity status
  const wasUpdatedRemotely =
    conflictCtx?.wasEntityUpdatedRemotely(entityId) ?? false;
  const isDirty = conflictCtx?.isEntityDirty(entityId) ?? false;
  const remoteInfo = conflictCtx?.getEntityRemoteInfo(entityId) ?? null;

  // Check if entity has any conflicts (direct or inside a conflicted array)
  const hasConflict = (() => {
    if (!conflictCtx) return false;

    const isAncestorOf = (ancestorId: string, childId: string): boolean => {
      let currentId: string | null = childId;
      const visited = new Set<string>();

      while (currentId && !visited.has(currentId)) {
        visited.add(currentId);
        const node = conflictCtx.state.entities.get(currentId);
        if (!node) return false;

        for (const [, occurrence] of node.occurrences) {
          const parentId = occurrence.parentEntityId;
          if (!parentId) continue;
          if (parentId === ancestorId) return true;
          currentId = parentId;
        }

        if (node.occurrences.size === 0) return false;
      }

      return false;
    };

    // Check direct conflict on this entity or any descendant
    const directConflict = conflictCtx.conflicts.some(
      c =>
        (c.type === 'field_conflict' || c.type === 'entity_deleted') &&
        (c.entityId === entityId || isAncestorOf(entityId, c.entityId))
    );
    if (directConflict) return true;

    // Check if entity is inside a conflicted array field AND actually differs (path-aware)
    for (const conflict of conflictCtx.conflicts) {
      if (conflict.type === 'field_conflict') {
        const entity = conflictCtx.state.entities.get(entityId);
        const basePath = buildFullPath(
          conflict.entityPath ?? null,
          conflict.fieldPath
        );

        if (
          pathMatchesAnyPattern(basePath, conflictCtx.state.referenceArrayPaths)
        ) {
          continue;
        }

        if (
          entity &&
          Array.isArray(conflict.localValue) &&
          Array.isArray(conflict.remoteValue)
        ) {
          const getRelativePath = (fullPath: string): string | null => {
            if (!basePath) return fullPath;
            if (fullPath === basePath) return '';
            if (fullPath.startsWith(basePath)) {
              let rel = fullPath.slice(basePath.length);
              if (rel.startsWith('.')) rel = rel.slice(1);
              return rel;
            }
            return null;
          };

          for (const [, occurrence] of entity.occurrences) {
            const relPath = getRelativePath(occurrence.path);
            if (relPath === null) continue;

            const localEntity = getValueAtPath(conflict.localValue, relPath);
            const remoteEntity = getValueAtPath(conflict.remoteValue, relPath);

            if (
              (!localEntity && remoteEntity) ||
              (localEntity && !remoteEntity)
            ) {
              return true;
            }
            if (
              localEntity &&
              remoteEntity &&
              !deepEqual(localEntity, remoteEntity)
            ) {
              return true;
            }
          }
        }
      }
    }

    return false;
  })();

  // Resolve user name
  const resolvedUserName = useResolveUserName(remoteInfo?.userId);

  const getDescendantStatus = () => {
    if (!conflictCtx) {
      return { hasLocal: false, hasRemote: false };
    }

    const isAncestorOf = (ancestorId: string, childId: string): boolean => {
      let currentId: string | null = childId;
      const visited = new Set<string>();

      while (currentId && !visited.has(currentId)) {
        visited.add(currentId);
        const node = conflictCtx.state.entities.get(currentId);
        if (!node) return false;

        for (const [, occurrence] of node.occurrences) {
          const parentId = occurrence.parentEntityId;
          if (!parentId) continue;
          if (parentId === ancestorId) return true;
          currentId = parentId;
        }

        if (node.occurrences.size === 0) return false;
      }

      return false;
    };

    let hasLocal = false;
    let hasRemote = false;

    for (const [childId] of conflictCtx.state.entities) {
      if (childId === entityId) continue;
      if (!isAncestorOf(entityId, childId)) continue;

      if (!hasLocal && conflictCtx.isEntityDirty(childId)) {
        hasLocal = true;
      }
      if (!hasRemote && conflictCtx.wasEntityUpdatedRemotely(childId)) {
        hasRemote = true;
      }

      if (hasLocal && hasRemote) break;
    }

    return { hasLocal, hasRemote };
  };

  // Determine what indicators to show
  const showConflict = showConflictIndicator && hasConflict;
  const descendantStatus = getDescendantStatus();
  const showMixed =
    !showConflict && descendantStatus.hasLocal && descendantStatus.hasRemote;
  const showLocalChanges =
    showLocalChangesIndicator &&
    isDirty &&
    wasUpdatedRemotely &&
    !hasConflict &&
    !showMixed;
  const showRemoteUpdate =
    showRemoteUpdateIndicator &&
    wasUpdatedRemotely &&
    !hasConflict &&
    !isDirty &&
    !showMixed;

  const hasIndicator =
    showConflict || showMixed || showLocalChanges || showRemoteUpdate;

  // Highlight color
  const highlightColor = hasIndicator
    ? showConflict
      ? 'rgba(244, 67, 54, 0.7)' // red
      : showMixed
        ? 'rgba(156, 39, 176, 0.7)' // purple
        : showLocalChanges
          ? 'rgba(255, 152, 0, 0.7)' // orange
          : 'rgba(33, 150, 243, 0.7)' // blue
    : null;

  const boxShadowStyle = highlightColor ? `0 0 0 2px ${highlightColor}` : null;

  return {
    wasUpdatedRemotely,
    isDirty,
    hasConflict,
    showMixed,
    showConflict,
    showLocalChanges,
    showRemoteUpdate,
    hasIndicator,
    highlightColor,
    boxShadowStyle,
    remoteInfo,
    resolvedUserName,
  };
}

// ═══════════════════════════════════════════════════════════════════════════════
// Component: EntityConflictIndicator (standalone icon)
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * Standalone conflict indicator icon for an entity.
 * Position this absolutely relative to your element.
 *
 * @example
 * ```tsx
 * <div style={{ position: 'relative' }}>
 *   <MyDraggableElement style={{ boxShadow: status.boxShadowStyle }} />
 *   <EntityConflictIndicator
 *     entityId={`number:${table.number}`}
 *     entityLabel={`Table ${table.number}`}
 *     style={{ position: 'absolute', top: -10, right: -10 }}
 *   />
 * </div>
 * ```
 */
export function EntityConflictIndicator({
  entityId,
  entityLabel,
  style,
  showConflictIndicator = true,
  showRemoteUpdateIndicator = true,
  showLocalChangesIndicator = true,
}: EntityConflictIndicatorProps) {
  const { t } = useTranslation();
  const status = useEntityConflictStatus(entityId, {
    showConflictIndicator,
    showRemoteUpdateIndicator,
    showLocalChangesIndicator,
  });

  if (!status.hasIndicator) {
    return null;
  }

  // Format timestamp for display
  const formatTime = (time?: number) => {
    if (!time) return null;
    const date = new Date(time);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const {
    resolvedUserName,
    remoteInfo,
    showConflict,
    showMixed,
    showLocalChanges,
    showRemoteUpdate,
  } = status;

  // Build tooltip content for conflict (red)
  const conflictTooltipContent = (
    <Box sx={{ p: 0.5 }}>
      <Typography
        variant="subtitle2"
        sx={{ fontWeight: 'bold', color: 'error.light' }}
      >
        ⚠️ {t('shared.conflictDetected')}
      </Typography>
      {entityLabel && (
        <Typography variant="body2" sx={{ mt: 0.5, fontWeight: 500 }}>
          {entityLabel}
        </Typography>
      )}
      <Typography variant="body2" sx={{ mt: 0.5 }}>
        {t('shared.entityHasConflict')}
      </Typography>
      {(resolvedUserName || remoteInfo?.timestamp) && (
        <Box
          sx={{
            mt: 1,
            p: 1,
            bgcolor: 'rgba(0,0,0,0.25)',
            borderRadius: 1,
            border: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.4)',
          }}
        >
          {resolvedUserName && (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.85 }}
            >
              👤 {t('shared.changedBy')} <strong>{resolvedUserName}</strong>
            </Typography>
          )}
          {remoteInfo?.timestamp && (
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                opacity: 0.85,
                mt: resolvedUserName ? 0.5 : 0,
              }}
            >
              🕒 {t('shared.at')}: {formatTime(remoteInfo.timestamp)}
            </Typography>
          )}
        </Box>
      )}
      <Typography
        variant="caption"
        sx={{
          display: 'block',
          mt: 1,
          pt: 1,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          fontStyle: 'italic',
          opacity: 0.9,
        }}
      >
        {t('shared.yourChangesConflict')}
      </Typography>
    </Box>
  );

  // Build tooltip content for remote update
  const updateTooltipContent = (
    <Box sx={{ p: 0.5 }}>
      <Typography
        variant="subtitle2"
        sx={{ fontWeight: 'bold', color: 'info.light' }}
      >
        🔄 {t('shared.remoteUpdate')}
      </Typography>
      {entityLabel && (
        <Typography variant="body2" sx={{ mt: 0.5, fontWeight: 500 }}>
          {entityLabel}
        </Typography>
      )}
      <Typography variant="body2" sx={{ mt: 0.5, color: 'info.contrastText' }}>
        {t('shared.entityUpdatedByAnotherUser')}
      </Typography>
      {(resolvedUserName || remoteInfo?.timestamp) && (
        <Box
          sx={{
            mt: 1,
            p: 1,
            bgcolor: 'rgba(0,0,0,0.25)',
            borderRadius: 1,
            border: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.4)',
          }}
        >
          {resolvedUserName && (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.85 }}
            >
              👤 {t('shared.changedBy')} <strong>{resolvedUserName}</strong>
            </Typography>
          )}
          {remoteInfo?.timestamp && (
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                opacity: 0.85,
                mt: resolvedUserName ? 0.5 : 0,
              }}
            >
              🕒 {t('shared.at')}: {formatTime(remoteInfo.timestamp)}
            </Typography>
          )}
        </Box>
      )}
      <Typography
        variant="caption"
        sx={{
          display: 'block',
          mt: 1,
          pt: 1,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          fontStyle: 'italic',
          opacity: 0.9,
        }}
      >
        ✅ {t('shared.changeAppliedAutomatically')}
      </Typography>
    </Box>
  );

  // Build tooltip content for local changes
  const localChangesTooltipContent = (
    <Box sx={{ p: 0.5 }}>
      <Typography
        variant="subtitle2"
        sx={{ fontWeight: 'bold', color: 'warning.light' }}
      >
        ✏️ {t('shared.youHaveUnsavedChanges')}
      </Typography>
      {entityLabel && (
        <Typography variant="body2" sx={{ mt: 0.5, fontWeight: 500 }}>
          {entityLabel}
        </Typography>
      )}
      <Typography
        variant="body2"
        sx={{ mt: 0.5, color: 'warning.contrastText' }}
      >
        {t('shared.entityContainsYourEdits')}
      </Typography>
      {(resolvedUserName || remoteInfo?.timestamp) && (
        <Box
          sx={{
            mt: 1,
            p: 1,
            bgcolor: 'rgba(0,0,0,0.25)',
            borderRadius: 1,
            border: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.4)',
          }}
        >
          <Typography
            variant="caption"
            sx={{
              display: 'block',
              opacity: 0.9,
              fontStyle: 'italic',
              mb: 0.5,
            }}
          >
            {t('shared.meanwhileAnotherUserUpdatedEntity')}
          </Typography>
          {resolvedUserName && (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.85 }}
            >
              👤 {t('shared.changedBy')} <strong>{resolvedUserName}</strong>
            </Typography>
          )}
          {remoteInfo?.timestamp && (
            <Typography
              variant="caption"
              sx={{ display: 'block', opacity: 0.85 }}
            >
              🕒 {t('shared.at')}: {formatTime(remoteInfo.timestamp)}
            </Typography>
          )}
        </Box>
      )}
      <Typography
        variant="caption"
        sx={{
          display: 'block',
          mt: 1,
          pt: 1,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          fontStyle: 'italic',
          opacity: 0.9,
        }}
      >
        💾 {t('shared.yourVersionWillBeSaved')}
      </Typography>
    </Box>
  );

  // Select tooltip content and icon based on state
  let tooltipContent: React.ReactNode;
  let icon: React.ReactNode;
  let tooltipBgColor: string;

  if (showConflict) {
    tooltipContent = conflictTooltipContent;
    tooltipBgColor = 'error.dark';
    icon = (
      <WarningIcon
        sx={{
          color: 'error.main',
          fontSize: 20,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))',
        }}
      />
    );
  } else if (showMixed) {
    tooltipContent = (
      <Box sx={{ p: 0.5 }}>
        <Typography
          variant="subtitle2"
          sx={{ fontWeight: 'bold', color: '#ce93d8' }}
        >
          ⚠️ {t('shared.mixedChanges')}
        </Typography>
        {entityLabel && (
          <Typography variant="body2" sx={{ mt: 0.5, fontWeight: 500 }}>
            {entityLabel}
          </Typography>
        )}
        <Typography variant="body2" sx={{ mt: 0.5 }}>
          {t('shared.entityHasMixedChanges')}
        </Typography>
      </Box>
    );
    tooltipBgColor = '#6a1b9a';
    icon = (
      <PriorityHighIcon
        sx={{
          color: '#ce93d8',
          fontSize: 20,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))',
        }}
      />
    );
  } else if (showLocalChanges) {
    tooltipContent = localChangesTooltipContent;
    tooltipBgColor = '#e65100';
    icon = (
      <EditIcon
        sx={{
          color: 'rgba(255, 152, 0, 0.7)',
          fontSize: 20,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))',
        }}
      />
    );
  } else {
    tooltipContent = updateTooltipContent;
    tooltipBgColor = '#1565c0';
    icon = (
      <UpdateIcon
        sx={{
          color: 'info.main',
          fontSize: 22,
          cursor: 'help',
          filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))',
        }}
      />
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 24,
        height: 24,
        backgroundColor: 'background.paper',
        borderRadius: '50%',
        boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
        zIndex: 100,
        ...style,
      }}
    >
      <Tooltip
        title={tooltipContent}
        placement="top"
        arrow
        componentsProps={{
          tooltip: {
            sx: {
              bgcolor: tooltipBgColor,
              '& .MuiTooltip-arrow': { color: tooltipBgColor },
              maxWidth: 320,
              boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
            },
          },
        }}
      >
        {icon}
      </Tooltip>
    </Box>
  );
}

// ═══════════════════════════════════════════════════════════════════════════════
// Component: ConflictAwareEntity (wrapper - for non-draggable elements)
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * Wraps a visual element with entity-level conflict detection awareness.
 * Shows visual indicators when entities are updated remotely or have local changes.
 *
 * NOTE: For draggable/absolutely positioned elements, use useEntityConflictStatus hook
 * and EntityConflictIndicator component instead.
 *
 * @example
 * ```tsx
 * <ConflictAwareEntity entityId={`number:${table.number}`} entityLabel={`Table ${table.number}`}>
 *   <MyCard {...cardProps} />
 * </ConflictAwareEntity>
 * ```
 */
export function ConflictAwareEntity({
  children,
  entityId,
  showConflictIndicator = true,
  showRemoteUpdateIndicator = true,
  showLocalChangesIndicator = true,
  sx,
  entityLabel,
}: ConflictAwareEntityProps) {
  const status = useEntityConflictStatus(entityId, {
    showConflictIndicator,
    showRemoteUpdateIndicator,
    showLocalChangesIndicator,
  });

  // If no indicator needed, return children unchanged
  if (!status.hasIndicator) {
    return <>{children}</>;
  }

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'inline-block',
        // Apply inner box shadow to the first child element
        '& > *:first-of-type': {
          boxShadow: status.boxShadowStyle,
        },
        ...sx,
      }}
    >
      {/* Indicator icon positioned at top-right */}
      <EntityConflictIndicator
        entityId={entityId}
        entityLabel={entityLabel}
        showConflictIndicator={showConflictIndicator}
        showRemoteUpdateIndicator={showRemoteUpdateIndicator}
        showLocalChangesIndicator={showLocalChangesIndicator}
        style={{ position: 'absolute', top: -10, right: -10 }}
      />

      {/* Original element */}
      {children}
    </Box>
  );
}

export default ConflictAwareEntity;
