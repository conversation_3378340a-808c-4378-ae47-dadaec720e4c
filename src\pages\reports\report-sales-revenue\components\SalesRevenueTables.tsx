import { useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { Link } from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { groupReport } from '~/fake-provider/reports/groupReport';
import parseTableProperty from '~/utils/formatAndDivideNumber';

type TableRow = {
  item: string;
  value: string;
  items?: TableRow[];
  subItems?: TableRow[];
};

export default function SalesRevenueTables({
  tableData,
}: {
  tableData: ReturnType<typeof groupReport>[number]['report'][number];
}) {
  const { t } = useTranslation();
  const { paymentsSubItems } = useMemo(() => {
    if (!tableData || !('payments' in tableData)) {
      return { paymentsSubItems: [], totalCollected: 0 };
    }

    const paymentTranslations: Record<string, string> = {
      cash: t('paymentMethods.cash'),
      card: t('paymentMethods.card'),
      giftCard: t('paymentMethods.giftCard'),
      mealTicket: t('paymentMethods.mealTicket'),
      online: t('paymentMethods.online'),
      valueTicket: t('paymentMethods.valueTicket'),
      voucher: t('paymentMethods.voucher'),
      wireTransfer: t('paymentMethods.wireTransfer'),
      cashless: t('paymentMethods.cashless'),
      '3rdParty': t('paymentMethods.3rdParty'),
    };

    const paymentsSubItems = Object.keys(tableData.payments || {}).map(key => ({
      item:
        paymentTranslations[key] || key.charAt(0).toUpperCase() + key.slice(1),
      value: parseTableProperty(
        (tableData?.payments as Record<string, number>)[key]
      ),
    }));

    return { paymentsSubItems };
  }, [tableData]);

  const paymentsData = [
    {
      item: t('shared.payments'),
      value: '7.980,89',
      subItems: paymentsSubItems,
    },
    {
      item: t('transactionsPage.totalCollected'),
      isMain: true,
      value: parseTableProperty(tableData, 'totalValue'),
    },
  ];

  const salesRevenueData = [
    {
      item: t('dashboard.grossSales'),
      value: parseTableProperty(tableData, 'value'),
      subItems: [
        {
          item: t('shared.items', { context: 'capitalize' }),
          value: parseTableProperty(tableData, 'itemsValue'),
        },
        {
          item: t('menu.modifiers'),
          value: parseTableProperty(tableData, 'modifiersValue'),
        },
        {
          item: t('menu.giftCards'),
          value: parseTableProperty(tableData, 'giftCardsValue'),
        },
        {
          item: t('menu.extraCharges'),
          value: parseTableProperty(tableData, 'extraChargesValue'),
        },
      ],
      items: [
        {
          item: t('menu.discounts'),
          value: '- ' + parseTableProperty(tableData, 'discountsValue'),
        },
        {
          item: t('menu.coupons'),
          value: '- ' + parseTableProperty(tableData, 'couponsValue'),
        },
        {
          item: t('menu.promotions'),
          value: '- ' + parseTableProperty(tableData, 'promotionsValue'),
        },
      ],
    },
    {
      item: t('shared.netSales'),
      value: parseTableProperty(tableData, 'netValue'),
      items: [
        {
          item: t('menu.tips'),
          isMain: true,
          value: parseTableProperty(tableData, 'tipsValue'),
          subItems: [
            {
              item: t('shared.cash'),
              value: parseTableProperty(tableData, 'tipsCashValue'),
            },
            {
              item: t('shared.nonCash'),
              value: parseTableProperty(tableData, 'tipsNonCashValue'),
            },
          ],
        },
      ],
    },
    {
      item: t('menu.totalSales'),
      value: parseTableProperty(tableData, 'totalValue'),
      isMain: true,
    },
  ];

  const salesRevenueConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'item',
      label: 'Item',
      textAlign: 'start',
    },
    {
      id: 'value',
      label: 'Value',
    },
  ];

  const paymentsConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'item',
      label: 'Item',
      textAlign: 'start',
    },
    {
      id: 'value',
      label: 'Value',
      render: row => {
        if (row.item === t('shared.payments')) {
          return (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Link to="/report-payment-methods" className="do-not-print">
                <Box
                  sx={{
                    '&:hover': {
                      opacity: 0.8,
                    },
                    color: '#0063EF',
                    fontWeight: 500,
                    cursor: 'pointer',
                    width: 'fit-content',
                    textAlign: 'right',
                  }}
                >
                  {t('transactionsPage.seePaymentMethods')}
                </Box>
              </Link>
            </Box>
          );
        }
        return row.value;
      },
    },
  ];

  const [fields, setFields] = useState<FieldOption[]>(
    salesRevenueConfig.map(col => ({
      value: col.id as string,
      isChecked: true,
    }))
  );

  const [fieldsPayments, setFieldsPayments] = useState<FieldOption[]>(
    paymentsConfig.map(col => ({
      value: col.id as string,
      isChecked: true,
    }))
  );

  return (
    <>
      <Box sx={{ pt: 5 }}>
        <CustomTable
          filter={false}
          fields={fields}
          setFields={setFields}
          searchBar={false}
          config={salesRevenueConfig}
          data={salesRevenueData}
          alignLastColumnRight={true}
          hideHeader={true}
        />
      </Box>

      <Box
        className="do-not-print"
        sx={{
          pb: 2,
          margin: '20px auto',
          fontFamily: 'Arial, sans-serif',
          color: '#666',
        }}
      >
        <p
          style={{
            fontSize: '13px',
            marginBottom: '10px',
            lineHeight: '1.5',
          }}
        >
          {t('transactionsPage.salesPie')}
          <span role="img" aria-label="pie">
            🥧
          </span>
        </p>
        <ul style={{ paddingLeft: '20px', margin: '0 0 10px 0' }}>
          <li
            style={{
              fontSize: '13px',
              marginBottom: '8px',
              lineHeight: '1.5',
            }}
          >
            <b>{t('dashboard.grossSales2')}</b>{' '}
            {t('transactionsPage.salesPie1')}
          </li>
          <li
            style={{
              fontSize: '13px',
              marginBottom: '8px',
              lineHeight: '1.5',
            }}
          >
            <b>{t('shared.netSales')}</b> {t('transactionsPage.salesPie2')}
          </li>
        </ul>
        <p
          style={{
            fontSize: '13px',
            marginBottom: '10px',
            lineHeight: '1.5',
          }}
        >
          <b>{t('transactionsPage.totalSales')}</b> ={' '}
          {t('transactionsPage.salesPie3')}
          <span role="img" aria-label="money bag">
            💰
          </span>
        </p>
      </Box>
      <Box sx={{ pb: 7, '@media print': { pt: { xs: 3, md: 0 } } }}>
        <CustomTable
          config={paymentsConfig}
          data={paymentsData}
          searchBar={false}
          fields={fieldsPayments}
          setFields={setFieldsPayments}
          alignLastColumnRight={true}
          filter={false}
          hideHeader={true}
        />
      </Box>
    </>
  );
}
