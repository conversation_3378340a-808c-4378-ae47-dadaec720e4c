import { useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import {
  ColumnConfig,
  FieldOption,
} from '~/components/organisms/CustomTable/types/globals';
import { groupReport } from '~/fake-provider/reports/groupReport';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';

type TableRow = {
  vat: number | string;
  price: number;
  quantity: number;
  value: number;
  tipsCashValue: number;
  member: string;
  tipsNonCashValue: number;
};

const fieldsConstant = [
  { isChecked: true, value: 'member' },
  { isChecked: true, value: 'value' },
  { isChecked: true, value: 'tipsCashValue' },
  { isChecked: true, value: 'tipsNonCashValue' },
];

export default function ReportTipsTable({
  tableData,
}: {
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
}) {
  const [fields, setFields] = useState<FieldOption[]>(fieldsConstant);

  const reportTipsData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.vat = 'Total';
      totalItemsData.member = 'Total';

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }

    return [];
  }, [tableData]);

  const { t } = useTranslation();

  const reportTipsConfig: ColumnConfig<TableRow>[] = useMemo(
    () => [
      {
        id: 'member',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                // @ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {row.member}
            </div>
          );
        },
        label: t('tips.teamMember'),
        textAlign: 'start',
      },

      {
        id: 'tipsNonCashValue',
        label: t('tips.nonCashValue'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(row.tipsNonCashValue)}</>;
        },
      },
      {
        id: 'tipsCashValue',
        label: t('tips.cashValue'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(row.tipsCashValue)}</>;
        },
      },
      {
        id: 'value',
        label: t('tips.totalAmount'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(row.value)}</>;
        },
      },
    ],
    [fields]
  );

  const columnsToFilter = ['member', 'value', 'tipsCashValue'];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <CustomTable
          greyLastRow={true}
          fields={fields}
          setFields={setFields}
          fixedFirstColumn={true}
          maxWidthFirstColumn="200px"
          columnsToFilter={columnsToFilter}
          config={reportTipsConfig}
          data={reportTipsData}
          fixLastRow={true}
          alignLastColumnRight={false}
        />
      </Box>
    </>
  );
}
