import { useCallback, useEffect, useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { FieldOption } from '~/components/organisms/CustomTable/types/globals';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import {
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
} from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import cleanStringArond from '~/utils/cleanStringArond';
import ItemSalesTable from './components/ItemSalesTable';

const REPORT_TYPE = 'compedItems';

const fieldsConstant = [
  { isChecked: true, value: 'groupId' },
  { isChecked: true, value: 'id' },
  { isChecked: true, value: 'measureUnit' },
  { isChecked: true, value: 'quantity' },
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'couponsValue' },
  { isChecked: true, value: 'discountsValue' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'prepStation' },
  { isChecked: true, value: 'netValue' },
  { isChecked: true, value: 'value' },
  { isChecked: true, value: 'reason' },
];

export default function ItemSales({
  updateCompsData,
  filters,
  rawData,
  commonFields,
}: {
  updateCompsData?: any;
  filters: any;
  rawData: any;
  commonFields: any;
}) {
  const { t } = useTranslation();
  const [tableFields, setTableFields] = useState<FieldOption[]>(fieldsConstant);
  const [groupingItems, setGroupingItems] = useState<string[]>(['reason']);
  const [enrichedData, setEnrichedData] = useState<any[]>([]);
  const { data: itemsLibrary } = useGetListHospitalityItemsLive();
  const { data: categories } = useGetListHospitalityCategoriesLive();

  const { tableData, composedFilters } = useMemo(() => {
    if (!rawData || !filters) return { tableData: [], composedFilters: [] };

    const appliedFilters = composeFilters(filters, REPORT_TYPE);
    const filteredData = filterReport(REPORT_TYPE, rawData, appliedFilters, []);

    const availableFields = tableFields.filter(field =>
      reportSpecificFields.compedItems.some(
        f => cleanStringArond(field.value) === cleanStringArond(f)
      )
    );

    const rawGroupedData = groupReport(
      REPORT_TYPE,
      filteredData,
      [],
      availableFields.map(item => item.value)
    );

    const hierarchicalData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        rawGroupedData,
        groupingItems as []
      )[0]?.report || [];

    return {
      tableData: hierarchicalData,
      composedFilters: appliedFilters,
    };
  }, [filters, rawData, groupingItems, tableFields]);

  const fetchEnrichedData = useCallback(async () => {
    if (!tableData) return;

    const abortController = new AbortController();

    const collectIds = (
      items: any[]
    ): { leafIds: string[]; groupIds: Set<string> } => {
      let leafIds: string[] = [];
      let groupIds: Set<string> = new Set();

      for (const item of items) {
        if (!item.subReport || item.subReport.length === 0) {
          leafIds.push(item.id);
          groupIds.add(item.groupId);
        } else {
          groupIds.add(item.groupId);
          const { leafIds: childLeafIds, groupIds: childGroupIds } = collectIds(
            item.subReport
          );
          leafIds = [...leafIds, ...childLeafIds];
          childGroupIds.forEach(id => groupIds.add(id));
        }
      }
      return { leafIds, groupIds };
    };

    try {
      const { leafIds, groupIds } = collectIds(tableData);
      const itemNameMap: Record<string, string> = {};

      leafIds.map(async id => {
        try {
          const itemFound = itemsLibrary?.find(item => item.id === id);
          itemNameMap[id] = itemFound?.name;
        } catch (error) {
          console.error(`Failed to fetch item with id: ${id}`, error);
          itemNameMap[id] = id;
        }
      });

      const categoryNameMap: Record<string, string> = {};
      Array.from(groupIds).map(async groupId => {
        try {
          const categoryFound = categories?.find(
            category => category.id === groupId
          );
          categoryNameMap[groupId] = categoryFound?.name;
        } catch (error) {
          console.error(`Failed to fetch category with id: ${groupId}`, error);
          categoryNameMap[groupId] = groupId;
        }
      });

      const enrich = (items: any[]): any[] =>
        items.map(item => {
          if (item.groupedBy) {
            const groupField = item.groupedBy.field;
            const groupValue = item.groupedBy.value;
            return {
              ...item,
              name:
                groupField === 'groupId'
                  ? categoryNameMap[groupValue] || groupValue
                  : `${capitalize(groupField)}: ${groupValue}`,
              groupName:
                groupField === 'groupId' && item.groupedBy
                  ? categoryNameMap[groupValue] || groupValue
                  : '',
              subReport: item.subReport ? enrich(item.subReport) : undefined,
            };
          } else {
            const enrichedItem = {
              ...item,
              name: itemNameMap[item.id] || item.id,
              groupName: '',
            };
            if (item.subReport && item.subReport.length > 0) {
              enrichedItem.subReport = enrich(item.subReport);
            }
            return enrichedItem;
          }
        });

      const enriched = enrich(tableData);
      setEnrichedData(enriched);
    } catch (error: any) {
      if (error.name !== 'AbortError')
        console.error('Error fetching enriched data:', error);
    }
    return () => abortController.abort();
  }, [tableData, itemsLibrary, categories]);

  const sortDataRecursively = (items: any[]): any[] => {
    if (!items || items.length === 0) return [];

    return items
      .map(item => ({
        ...item,
        subReport: item.subReport ? sortDataRecursively(item.subReport) : [],
      }))
      .sort((a, b) => {
        if (groupingItems.length > 0) {
          const quantityA = a.quantity ?? 0;
          const quantityB = b.quantity ?? 0;
          if (quantityA !== quantityB) return quantityB - quantityA;
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  useEffect(() => {
    fetchEnrichedData();
  }, [fetchEnrichedData]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const finalTableData = useMemo(() => {
    const sortedData = sortData(enrichedData);
    return remapReports(sortedData, 'id');
  }, [enrichedData]);

  const onChangeGrouping = (items: string[]) => setGroupingItems(items);

  useEffect(() => {
    if (!tableData || (tableData && tableData.length === 0)) {
      updateCompsData('totalItems', {});
    }
  }, [tableData]);

  const formattedFilters = useMemo(() => {
    if (!filters) return {};

    const getLabel = (field: string, value: string) => {
      const options = commonFields[field];
      const found = options?.find((opt: any) => opt.value === value);
      return found?.label || value;
    };

    return {
      sellpoint: filters.sellpointId,
      dateRange: filters.dateRange.map((d: any) => d?.format('YYYY-MM-DD')),
      timeRange: filters.timeRange,
      member: getLabel('member', filters.member ?? ''),
      floor: getLabel('floor', filters.floor ?? ''),
      serviceType: getLabel('serviceType', filters.diningOption),
      source: getLabel('sources', filters.source),
    };
  }, [filters, commonFields]);

  return (
    <Box sx={{ width: '100%' }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('giftCards.Comped Items')}
        hideBorder
        doNotPrint
      />
      <ItemSalesTable
        reportType={REPORT_TYPE}
        formattedFilters={formattedFilters}
        rawData={rawData}
        fields={tableFields}
        setFields={setTableFields}
        composedFilters={composedFilters}
        filters={filters}
        updateCompsData={updateCompsData}
        tableData={finalTableData || []}
        groupingItems={groupingItems}
        onChangeGrouping={onChangeGrouping}
      />
    </Box>
  );
}
