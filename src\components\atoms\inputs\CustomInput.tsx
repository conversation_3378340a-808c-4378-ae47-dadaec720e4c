import { useMemo, useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import {
  Box,
  IconButton,
  Switch,
  Theme,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import {
  AutocompleteArrayInput,
  AutocompleteInput,
  BooleanInput,
  DateInput,
  InputHelperText,
  NumberInput,
  PasswordInput,
  SelectArrayInput,
  SelectInput,
  SelectInputProps,
  TextInput,
  useChoicesContext,
  useInput,
} from 'react-admin';

import { useTheme } from '../../../contexts';
import CodeInput from './CodeInput';
import PhoneNumberInput from './PhoneNumberInput';

interface CustomInputI extends SelectInputProps {
  inputStyle?: any;
  inputText?: any;
  matchSuggestion?: any;
  selectOnFocus?: boolean;
  options?: any;
  locale?: string;
  helperText?: string;
  isRequired?: boolean;
  uppercase?: boolean;
  digits?: number; // Add this for CodeInput
  type?:
    | 'text'
    | 'number'
    | 'select'
    | 'select-array'
    | 'autocomplete-select-array'
    | 'autocomplete-select'
    | 'password'
    | 'date'
    | 'switch'
    | 'phone'
    | 'code';
}

const selectArrayStyle = {
  m: 0,
  '> div, :hover > div': {
    background: 'transparent !important',
  },
  '& p, label': {
    display: 'none',
  },
  '& .MuiInputBase-input': {
    padding: '11px',
  },
  '> div::before': {
    borderBottom: 'none !important',
  },
};

export default function CustomInput({
  source: sourceProp,
  label,
  placeholder = '',
  type = 'text',
  inputStyle = {},
  error,
  options = {},
  locale,
  helperText,
  isRequired: isRequiredProp,
  validate,
  digits = 6, // Default to 6 digits for code input
  uppercase = false,
  ...props
}: CustomInputI) {
  const [focused, setFocused] = useState<boolean>(false);
  const { theme } = useTheme();
  const custom = theme.palette.custom;
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const isDisabled = props.disabled || props.readOnly;
  const disabledBg =
    theme.palette.mode == 'light'
      ? custom.fieldBg
      : theme.palette.background.tinted;

  const { source, allChoices, selectedChoices } = useChoicesContext({
    source: sourceProp,
  });

  if (source === undefined) {
    throw new Error(
      `If you're not wrapping the SelectInput inside a ReferenceInput, you must provide the source prop`
    );
  }

  const {
    fieldState,
    field,
    isRequired: isRequiredFromHook,
  } = useInput({
    source,
    validate,
    ...props,
  });

  // Check if the field is required based on:
  // 1. The isRequired prop passed directly
  // 2. The isRequired flag from useInput hook
  // 3. Check if validate contains required() function (as an array or single function)
  const hasRequiredValidator = Array.isArray(validate)
    ? validate.some(validator => validator.name === 'required')
    : validate?.name === 'required';

  const isFieldRequired =
    isRequiredProp || isRequiredFromHook || hasRequiredValidator;

  // Determine tooltip content - show error if invalid, otherwise show helperText
  const tooltipContent = fieldState.invalid ? (
    <InputHelperText error={fieldState.error?.message} helperText={''} />
  ) : (
    <InputHelperText error={''} helperText={helperText} />
  );

  const InputComponent = useMemo(() => {
    switch (type) {
      case 'text':
        return TextInput;
      case 'number':
        return NumberInput;
      case 'select':
        return SelectInput;
      case 'select-array':
        return SelectArrayInput;
      case 'date':
        return DateInput;
      case 'password':
        return PasswordInput;
      case 'autocomplete-select':
        return AutocompleteInput;
      case 'autocomplete-select-array':
        return AutocompleteArrayInput;
      case 'phone':
        return PhoneNumberInput;
      case 'switch':
        return BooleanInput;
      case 'code':
        return CodeInput;
      default:
        return <></>;
    }
  }, [type]);

  return (
    <Box
      sx={{
        width: '100%',
        position: 'relative',
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        border: `1px solid ${custom.gray400}`,
        borderBottom: focused
          ? `1px solid ${theme.palette.primary.main}`
          : `1px solid ${custom.gray400}`,
        zIndex: focused ? 2 : 0,
        marginTop: '-1px',
        bgcolor:
          isDisabled && theme.palette.mode === 'light'
            ? 'custom.gray400'
            : 'custom.fieldBg',
        backgroundImage: isDisabled
          ? `repeating-linear-gradient(135deg,transparent,transparent 1px,${disabledBg} 1px,${disabledBg} 5px,transparent 5px,transparent 6px,${disabledBg} 6px,${disabledBg} 10px)`
          : 'none',
        backgroundSize: '7px 7px',
      }}
    >
      <Box
        sx={{
          width: isXSmall ? '100%' : '200px',
          minWidth: isXSmall ? '100%' : '200px',
          height: isXSmall ? '35px' : '45px',
          bgcolor: !!fieldState.error
            ? 'error.light'
            : focused
              ? 'primary.light'
              : 'background.tinted',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: 2,
          flexShrink: 0,
        }}
      >
        <Typography
          // @ts-ignore
          variant="label"
          color={!!fieldState.error || error ? 'error.main' : 'inherit'}
        >
          {label}
          {isFieldRequired ? ' *' : ''}
        </Typography>

        {(helperText || fieldState.invalid) && (
          <Tooltip title={tooltipContent} arrow placement="top">
            <IconButton size="small" sx={{ ml: 1, p: 0 }}>
              <InfoIcon
                fontSize="small"
                color={fieldState.invalid ? 'error' : 'disabled'}
              />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      {/* @ts-ignore */}
      <InputComponent
        sx={{
          marginTop: type === 'switch' ? '0px' : '',
          padding: type === 'switch' ? '0px 016px' : '',
          flex: 1,
          '.MuiOutlinedInput-notchedOutline': {
            border: 0,
            borderColor: 'transparent !important',
          },
          '.MuiFormControl-root': {
            m: 0,
          },
          '.MuiFormHelperText-root': {
            display: type === 'switch' ? 'none' : '',
          },
          ...inputStyle,
          ...(type === 'select-array' && selectArrayStyle),
        }}
        inputProps={{
          sx: {
            textTransform: uppercase ? 'uppercase' : 'none',
          },
        }}
        className="custom-outlined"
        source={source}
        label=""
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        helperText={false} // Hide default helper text since we're using tooltip
        validate={validate}
        placeholder={type === 'phone' ? placeholder : undefined}
        digits={type === 'code' ? digits : undefined}
        {...props}
      />

      {/* placeholder - this was necessary because placeholder doesnt work consistently in all input types */}
      {!focused && !field.value && type !== 'phone' && type !== 'code' && (
        <Typography
          fontSize="14px"
          color="custom.fadedText"
          sx={{
            position: 'absolute',
            left: isXSmall ? '15px' : '214px',
            top: isXSmall ? 'calc(50% + 15px)' : '50%',
            transform: 'translateY(-50%)',
            zIndex: -1,
          }}
        >
          {placeholder}
        </Typography>
      )}
    </Box>
  );
}
