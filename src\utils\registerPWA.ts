/**
 * PWA Registration Utility
 *
 * This module handles Progressive Web App (PWA) functionality for authenticated users only.
 * It registers the service worker and injects the manifest link dynamically when the main app loads.
 *
 * Key Features:
 * - Only runs for authenticated users (after gateway auth check)
 * - Injects manifest link dynamically (not present in index.html)
 * - Registers service worker manually
 * - Manages install prompt for better UX
 */

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

let deferredPrompt: BeforeInstallPromptEvent | null = null;

/**
 * Inject the PWA manifest link into the document head
 * This ensures the manifest is only available for authenticated users
 */
function injectManifestLink(): void {
  // Check if manifest link already exists
  if (document.querySelector('link[rel="manifest"]')) {
    console.log('[PWA] Manifest link already exists');
    return;
  }

  const manifestLink = document.createElement('link');
  manifestLink.rel = 'manifest';
  manifestLink.href = '/manifest.webmanifest';
  document.head.appendChild(manifestLink);

  console.log('[PWA] Manifest link injected');
}

/**
 * Register the service worker for PWA functionality
 * This is called manually after authentication, not automatically on page load
 */
async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if (!('serviceWorker' in navigator)) {
    console.warn('[PWA] Service workers not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    console.log('[PWA] Service worker registered:', registration.scope);

    // Check for updates periodically
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      console.log('[PWA] New service worker found, installing...');

      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (
            newWorker.state === 'installed' &&
            navigator.serviceWorker.controller
          ) {
            console.log('[PWA] New service worker installed, update available');
            // Optionally show a notification to the user about the update
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('[PWA] Service worker registration failed:', error);
    return null;
  }
}

/**
 * Setup the beforeinstallprompt event listener
 * This captures the browser's install prompt so we can show it at the right time
 */
function setupInstallPromptListener(): void {
  window.addEventListener('beforeinstallprompt', (e: Event) => {
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault();

    // Store the event so it can be triggered later
    deferredPrompt = e as BeforeInstallPromptEvent;

    console.log(
      '[PWA] Install prompt captured, ready to show when appropriate'
    );

    // Dispatch a custom event that the app can listen to
    window.dispatchEvent(new Event('pwa-install-available'));
  });

  // Listen for the app being installed
  window.addEventListener('appinstalled', () => {
    console.log('[PWA] App was installed');
    deferredPrompt = null;

    // Dispatch a custom event
    window.dispatchEvent(new Event('pwa-installed'));
  });
}

/**
 * Show the install prompt to the user
 * Call this function when you want to prompt the user to install the app
 *
 * @returns Promise resolving to true if user accepted, false if dismissed, null if no prompt available
 */
export async function showInstallPrompt(): Promise<boolean | null> {
  if (!deferredPrompt) {
    console.log('[PWA] No install prompt available');
    return null;
  }

  try {
    // Show the install prompt
    await deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const choiceResult = await deferredPrompt.userChoice;

    console.log(
      `[PWA] User ${choiceResult.outcome === 'accepted' ? 'accepted' : 'dismissed'} the install prompt`
    );

    // Clear the deferred prompt
    deferredPrompt = null;

    return choiceResult.outcome === 'accepted';
  } catch (error) {
    console.error('[PWA] Error showing install prompt:', error);
    return null;
  }
}

/**
 * Check if the app is currently running as an installed PWA
 */
export function isInstalledPWA(): boolean {
  // Check if running in standalone mode (installed PWA)
  const isStandalone =
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true ||
    document.referrer.includes('android-app://');

  return isStandalone;
}

/**
 * Check if the install prompt is available
 */
export function isInstallPromptAvailable(): boolean {
  return deferredPrompt !== null;
}

/**
 * Main function to initialize PWA functionality
 * Call this once when the authenticated main app loads
 */
export async function initializePWA(): Promise<void> {
  console.log('[PWA] ========================================');
  console.log('[PWA] Initializing PWA for authenticated user...');
  console.log('[PWA] Current URL:', window.location.href);
  console.log('[PWA] User agent:', navigator.userAgent);

  // Check if already installed
  const alreadyInstalled = isInstalledPWA();
  console.log('[PWA] Is already installed?', alreadyInstalled);
  if (alreadyInstalled) {
    console.log('[PWA] App is running as installed PWA');
  } else {
    console.log('[PWA] App is running in browser (not installed)');
  }

  // Step 1: Inject manifest link
  console.log('[PWA] Step 1: Injecting manifest link...');
  injectManifestLink();

  // Step 2: Setup install prompt listener (must be before SW registration)
  console.log('[PWA] Step 2: Setting up install prompt listener...');
  setupInstallPromptListener();

  // Step 3: Register service worker
  console.log('[PWA] Step 3: Registering service worker...');
  await registerServiceWorker();

  console.log('[PWA] ✅ PWA initialization complete');
  console.log('[PWA] ========================================');

  // Check if prompt event was captured
  setTimeout(() => {
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    if (isSafari) {
      console.log(
        '[PWA] ℹ️ Safari browser - Install via "Add to Home Screen" in share menu'
      );
    } else if (deferredPrompt) {
      console.log('[PWA] ✅ Install prompt is available and ready to show');
    } else {
      console.log('[PWA] ⚠️ Install prompt not captured yet (this is normal)');
      console.log(
        '[PWA] The beforeinstallprompt event may fire later or not at all if:'
      );
      console.log('[PWA]   - App is already installed');
      console.log(
        '[PWA]   - Browser criteria not met (e.g., needs HTTPS, manifest requirements)'
      );
      console.log('[PWA]   - User already dismissed the prompt previously');
      console.log(
        "[PWA]   - Browser doesn't support PWA installation (Safari, Firefox)"
      );
      console.log(
        '[PWA]   - Running in iOS/Safari (use "Add to Home Screen" from share menu)'
      );
    }
  }, 2000);
}

/**
 * Unregister the service worker (useful for testing or cleanup)
 */
export async function unregisterServiceWorker(): Promise<boolean> {
  if (!('serviceWorker' in navigator)) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const success = await registration.unregister();
      console.log(
        `[PWA] Service worker ${success ? 'unregistered' : 'failed to unregister'}`
      );
      return success;
    }
    return false;
  } catch (error) {
    console.error('[PWA] Error unregistering service worker:', error);
    return false;
  }
}
