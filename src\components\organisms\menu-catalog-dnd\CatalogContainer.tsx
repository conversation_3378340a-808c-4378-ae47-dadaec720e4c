import { useEffect, useMemo, useState } from 'react';
import {
  Dnd<PERSON>ontext,
  Mouse<PERSON>ensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToFirstScrollableAncestor } from '@dnd-kit/modifiers';
import { Box } from '@mui/material';

import { calculateClosestSnap } from '../../../utils/calculateClosestSnap';
import { elementsCollide } from '../../../utils/checkCollision';
import ActiveTileMenuDropdown from './ActiveTileMenuDropdown';
import AddFunctionModal from './AddFunctionModal';
import AddItemModal from './AddItemModal';
import CatalogBackgroundGrid from './CatalogBackgroundGrid';
import CatalogItem from './CatalogItem';
import EditOrCreateComboModal from './CreateCombo/EditOrCreateComboModal';
import { ListModeModal, POSModeModal } from './DisplayGroupCreateEditModal';
import { Coordinates, MenuGroup, MenuItem as MenuItemI } from './types';

export const MENU_TILE_WIDTH = 200;
export const MOBILE_MENU_TILE_WIDTH = 150;
export const MENU_TILE_HEIGHT = 70;
export const ROWS_NO = 8;

interface CatalogContainerProps {
  columns: number;
  data: Array<MenuItemI | MenuGroup>;
  updateMenuTile: any;
  path: string;
  startIndex: number;
  unselectTileTrigger: number;
  removeMenuTile: (index: number) => void;
  openDisplayGroup: (index: number) => void;
  displayGroupColor?: string;
  recordType?: string;
}

export interface TileActionI {
  position: Coordinates;
  type: 'product' | 'displayGroup' | 'function' | 'productCombo';
  create?: boolean;
  edit?: boolean;
  recordType?: string;
}

export default function CatalogContainer({
  columns,
  data,
  startIndex,
  updateMenuTile,
  path,
  unselectTileTrigger,
  removeMenuTile,
  openDisplayGroup,
  displayGroupColor,
  recordType,
}: CatalogContainerProps) {
  const [{ tileIndex, error }, setSelectedTile] = useState<{
    tileIndex: number | null;
    error: boolean;
  }>({ tileIndex: null, error: false });

  const [resetGridSelection, setResetGridSelection] = useState<number>(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [tileAction, setTileAction] = useState<TileActionI>();

  const dropdownOpen = Boolean(anchorEl);
  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor));
  const isMobile = columns === 2;

  const inDisplayGroup = useMemo(() => {
    return path.includes('items');
  }, [path]);

  useEffect(() => {
    setSelectedTile({ tileIndex: null, error: false });
    setResetGridSelection(prev => prev + 1);
  }, [path, unselectTileTrigger]);

  const getCoordFromDelta = (
    oldCoord: Coordinates,
    deltaX: number,
    deltaY: number
  ): Coordinates => {
    const x = calculateClosestSnap(
      deltaX,
      isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH
    );
    const y = calculateClosestSnap(deltaY, MENU_TILE_HEIGHT);

    // we calculate new coordinates but recalculate when needed
    // to keep them inside the grid
    const newSX = +oldCoord.startX + x;
    const startX = newSX < 0 ? 0 : newSX >= columns ? columns - 1 : newSX;

    const newSY = +oldCoord.startY + y;
    // check if tile is small or tall
    const height = +oldCoord.endY - +oldCoord.startY;
    const startY =
      newSY < 0 ? 0 : newSY > ROWS_NO - height ? ROWS_NO - height : newSY;

    return {
      startX,
      startY,
      endX: startX + 1,
      endY: startY + height,
    };
  };

  /**
   * Checks if a new set of coordinates for a tile collides with any other tiles on
   * the grid, or if it's beeing moved out of bounds.
   *
   * @returns A boolean value indicating whether a collision occurred between the `newCoord` and any other tiles on the grid.
   */
  const elementsCollideHelper = (
    newCoord: Coordinates,
    index: number
  ): boolean => {
    let collision = false;

    for (let i = 0; i < data.length; i++) {
      if (i === index) continue;
      if (elementsCollide(newCoord, data[i].position)) {
        collision = true;
        break;
      }
    }

    setSelectedTile(prev => {
      return { ...prev, ...{ error: collision } };
    });
    return collision;
  };

  const onDragEnd = (
    index: number,
    deltaX: number,
    deltaY: number,
    target: any
  ) => {
    // Check if event is mouse click and open menu.
    if (deltaX === 0 && deltaY === 0) {
      setAnchorEl(target);
      return;
    }

    // Else event was drag and drop, move tile.
    let newCoord = getCoordFromDelta(data[index].position, deltaX, deltaY);
    if (elementsCollideHelper(newCoord, index)) {
      setSelectedTile(prev => {
        return { ...prev, ...{ error: false } };
      });
      return;
    }

    updateMenuTile(index, { position: newCoord });
  };

  const canChangeTileSizeHelper = useMemo(() => {
    // if we are inside display group, we can't change tile size
    if (inDisplayGroup) return false;

    if (tileIndex === null || !data[tileIndex]) return true;

    const { position: currentItemPos } = data[tileIndex ?? 0];
    if (currentItemPos.endY - currentItemPos.startY == 2) return true;
    if (currentItemPos.endY === ROWS_NO) return false;

    const index = data.findIndex(
      el =>
        el.position.startX === currentItemPos.startX &&
        el.position.startY === currentItemPos.endY
    );

    return index < 0;
  }, [data, tileIndex]);

  return (
    <>
      <Box
        sx={{
          overflow: 'auto',
          mb: 3,
          mx: 'auto',
          maxWidth: '100%',
          bgcolor: 'custom.gray400',
          zIndex: 10,
          borderStyle: 'solid',
          borderWidth: columns === 2 ? '12px' : '20px 60px 20px 20px',
          borderColor: 'black',
          borderRadius: '20px',
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width:
              (isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH) * columns +
              1,
            minHeight: MENU_TILE_HEIGHT * 8 + 1,
            height: 'fit-content',
            overflow: inDisplayGroup ? 'unset' : 'auto',
          }}
        >
          <DndContext
            sensors={sensors}
            onDragStart={({ active }) => {
              setSelectedTile({
                tileIndex: Number(active.id) - 1,
                error: false,
              });
              setResetGridSelection(prev => prev + 1);
            }}
            onDragEnd={({ active, delta, activatorEvent }) => {
              onDragEnd(
                Number(active.id) - 1,
                delta.x,
                delta.y,
                activatorEvent.target
              );
            }}
            onDragMove={({ active, delta }) => {
              const newCoord = getCoordFromDelta(
                data[Number(active.id) - 1].position,
                delta.x,
                delta.y
              );
              elementsCollideHelper(newCoord, Number(active.id) - 1);
            }}
            modifiers={[restrictToFirstScrollableAncestor]}
          >
            {data.map((tile, index: number) => {
              // If we are on mobile, show only items in view
              if (
                columns === 4 ||
                (tile.position.startX >= startIndex &&
                  tile.position.startX < startIndex + 2)
              )
                return (
                  <CatalogItem
                    key={`${tile.id}-${index}`}
                    aria-controls={dropdownOpen ? 'menu-item-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={dropdownOpen ? 'true' : undefined}
                    {...tile}
                    id={index}
                    selected={tileIndex === index}
                    error={tileIndex === index && error}
                    openDisplayGroup={() => {
                      setSelectedTile({ tileIndex: null, error: false });
                      openDisplayGroup(index);
                    }}
                    startIndex={startIndex}
                    columns={columns}
                  />
                );
            })}

            <CatalogBackgroundGrid
              totalItems={data.length}
              setTileAction={setTileAction}
              columns={columns}
              startIndex={startIndex}
              resetSelectionChild={resetGridSelection}
              resetSelectionParent={() => {
                setSelectedTile({ tileIndex: null, error: false });
              }}
              recordType={recordType}
            />

            {dropdownOpen && (
              <ActiveTileMenuDropdown
                anchorEl={anchorEl}
                open={dropdownOpen}
                tile={data[tileIndex ?? 0]}
                updateTile={newTile => updateMenuTile(tileIndex, newTile)}
                removeTile={() => {
                  setAnchorEl(null);
                  removeMenuTile(tileIndex ?? 0);
                  setSelectedTile({ tileIndex: null, error: false });
                }}
                editTile={setTileAction}
                canChangeTileSize={canChangeTileSizeHelper}
                onClose={() => {
                  setAnchorEl(null);
                }}
              />
            )}

            {/* Modals for products or display groups */}
            {!!tileAction &&
              (tileAction.type === 'product' ||
                tileAction.type === 'displayGroup') &&
              (tileAction.edit ? (
                tileAction.type === 'displayGroup' ? (
                  // Edit modal for display group
                  ((tileAction.recordType || recordType) === 'list' ? (<ListModeModal
                    mode="edit"
                    path={`${path}[${tileIndex ?? 0}]`}
                    onClose={() => setTileAction(undefined)}
                  />) : (<POSModeModal
                    mode="edit"
                    path={`${path}[${tileIndex ?? 0}]`}
                    initialColorValue={data[tileIndex ?? 0].color ?? ''}
                    onClose={() => setTileAction(undefined)}
                  />))
                ) : (
                  <AddItemModal
                    path={`${path}[${tileIndex ?? 0}]`}
                    initialValues={data[tileIndex ?? 0] as MenuItemI}
                    onClose={() => setTileAction(undefined)}
                    recordType={tileAction.recordType || recordType || 'pos'}
                    position={tileAction.position}
                    type={tileAction.type}
                  />
                )
              ) : tileAction.type === 'displayGroup' ? (
                // Create modal for display group
                ((tileAction.recordType || recordType) === 'list' ? (<ListModeModal
                  mode="create"
                  data={data}
                  path={path}
                  position={tileAction.position}
                  onClose={() => setTileAction(undefined)}
                />) : (<POSModeModal
                  mode="create"
                  data={data}
                  path={path}
                  position={tileAction.position}
                  onClose={() => setTileAction(undefined)}
                />))
              ) : (
                <AddItemModal
                  path={path}
                  onClose={() => setTileAction(undefined)}
                  displayGroupColor={
                    inDisplayGroup ? displayGroupColor : undefined
                  }
                  recordType={tileAction.recordType || recordType || 'pos'}
                  position={tileAction.position}
                  type={tileAction.type}
                />
              ))}

            {/* Create function modal */}
            {!!tileAction && tileAction.type === 'function' && (
              <AddFunctionModal
                columns={columns}
                data={data}
                path={path}
                onClose={() => setTileAction(undefined)}
                {...tileAction}
              />
            )}

            {/* Add or edit combo modal */}
            {!!tileAction && tileAction.type === 'productCombo' && (
              <EditOrCreateComboModal
                data={data}
                path={path}
                tileIndex={tileIndex}
                onClose={() => setTileAction(undefined)}
                initialValues={data[tileIndex ?? 0] as MenuItemI}
                displayGroupColor={displayGroupColor}
                {...tileAction}
              />
            )}
          </DndContext>
        </Box>
      </Box>
    </>
  );
}
