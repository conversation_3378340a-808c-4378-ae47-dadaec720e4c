import React, { useState } from 'react';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
  Box,
  Button,
  Checkbox,
  Collapse,
  Fade,
  IconButton,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { useTranslation } from 'react-i18next';

import { FilePreviewInline } from '~/components/organisms/FileUpload';
import { UploadedFile } from '~/components/organisms/FileUpload/types/fileUpload';

export type TableColumn<T> = {
  label: string;
  image?: string;
  render: (row: T) => React.ReactNode;
  align?: 'left' | 'right' | 'center';
  displayAfterSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
};

// Component for displaying item thumbnail using new file hooks in table
const TableItemThumbnail = ({ images }: { images?: UploadedFile[] }) => {
  return (
    <FilePreviewInline
      files={images || []}
      borderRadius={1.5}
      imageVariant="thumbnail"
      fileType="images"
    />
  );
};
type ConfigurableTableProps<T> = {
  rows: T[];
  columns: TableColumn<T>[];
  renderAddItem: () => React.ReactNode;
  removeSelectedItems: (
    selectedRows: {
      itemIndex: number;
      pageIndex?: number;
    }[]
  ) => void;
  editItem: (index: number, pageIndex?: number) => void;
  saveReorderedRows: (
    rows: T[],
    result: any,
    itemId: string,
    pageIndex: number
  ) => void;
  itemId: string;
  pageIndex: number;
};
export default function ConfigurableTable<T>({
  rows,
  columns,
  renderAddItem,
  removeSelectedItems,
  editItem,
  saveReorderedRows,
  itemId,
  pageIndex,
}: ConfigurableTableProps<T>) {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const { t } = useTranslation('');

  const allSelected = selectedRows.length === rows.length;

  const toggleSelectAll = () => {
    if (allSelected) setSelectedRows([]);
    else setSelectedRows(rows.map((_, i) => i));
  };

  const toggleRow = (index: number) => {
    setSelectedRows(prev =>
      prev.includes(index) ? prev.filter(i => i !== index) : [...prev, index]
    );
  };

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    index: number
  ) => {
    setSelectedRowIndex(index);
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setSelectedRowIndex(null);
    setMenuAnchorEl(null);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) {
      return;
    }

    if (result.destination.index === result.source.index) {
      return;
    }

    const reorderedRows = Array.from(rows);
    const [removed] = reorderedRows.splice(result.source.index, 1);
    reorderedRows.splice(result.destination.index, 0, removed);

    saveReorderedRows(reorderedRows, result, itemId, pageIndex);
  };

  return (
    <Box>
      <Collapse in={selectedRows.length > 0}>
        {selectedRows.length && (
          <Box
            sx={{
              backgroundColor: '#F2F2F2',
              borderBottom: '1px solid #ddd',
              display: 'flex',
              width: '100%',
              justifyContent: 'flex-end',
              py: 1,
            }}
          >
            <Button
              variant="text"
              color="error"
              onClick={() => {
                const selectedItems = selectedRows.map(itemIndex => ({
                  itemIndex,
                  pageIndex: (
                    rows.find(
                      row => (row as any).itemIndex === itemIndex
                    ) as any
                  )?.pageIndex,
                }));
                removeSelectedItems(selectedItems);
                setSelectedRows([]);
              }}
            >
              Remove {selectedRows.length} item
              {selectedRows.length > 1 ? 's' : ''} selected
            </Button>
          </Box>
        )}
      </Collapse>

      {rows && rows.length === 0 ? (
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            py: 6,
            gap: 1,
          }}
        >
          <Typography variant="body1">{t('menu.emptyMenuGroup')}</Typography>{' '}
          <Typography variant="body2" sx={{ color: '#A0A0A0' }}>
            {t('menu.emptyMenuGroupDescription')}
          </Typography>
          <Box width="145px">{renderAddItem()}</Box>
        </Box>
      ) : (
        <Table size="small" sx={{ userSelect: 'none' }}>
          <TableHead>
            <TableRow sx={{ position: 'relative' }}>
              <TableCell
                sx={{
                  display: {
                    xs: 'none',
                    sm: 'table-cell',
                  },
                }}
              ></TableCell>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={allSelected}
                  onChange={toggleSelectAll}
                  inputProps={{ 'aria-label': 'select all rows' }}
                />
              </TableCell>
              <TableCell
                sx={{
                  display: {
                    xs: 'none',
                    sm: 'table-cell',
                  },
                }}
              ></TableCell>
              {columns.map((col, index) => (
                <TableCell
                  key={index}
                  align={col.align || 'left'}
                  sx={{
                    display: {
                      xs: 'none',
                      [col.displayAfterSize ?? 'xs']: 'table-cell',
                    },
                  }}
                >
                  {t(`menu.${col.label}`)}
                </TableCell>
              ))}
              <TableCell align="right">
                {t('permissions.groupTitles.actions')}
              </TableCell>
            </TableRow>
          </TableHead>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="droppable">
              {provided => (
                <TableBody ref={provided.innerRef} {...provided.droppableProps}>
                  {rows.map((row, rowIndex) => {
                    return (
                      <Draggable
                        key={rowIndex}
                        draggableId={rowIndex.toString()}
                        index={rowIndex}
                      >
                        {(provided, snapshot) => (
                          <TableRow
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            sx={{
                              cursor: 'pointer',
                              position: 'relative',
                              backgroundColor: 'white',
                              borderBottom: '1px solid #ddd',
                              boxShadow: snapshot.isDragging
                                ? '0 0 20px 0 rgba(0, 0, 0, 0.13)'
                                : 'none',
                            }}
                            key={rowIndex}
                            hover
                            onClick={() => {
                              if (
                                ['displayGroup', 'function'].includes(
                                  (row as any).type
                                )
                              ) {
                                return;
                              }

                              editItem(
                                (row as any).itemIndex ?? rowIndex,
                                (row as any).pageIndex ?? undefined
                              );
                            }}
                          >
                            <TableCell
                              padding="checkbox"
                              sx={{
                                display: {
                                  xs: 'none',
                                  sm: 'table-cell',
                                },
                              }}
                            >
                              <DragIndicatorIcon
                                sx={{
                                  color: 'text.secondary',
                                }}
                              />
                            </TableCell>
                            <TableCell padding="checkbox">
                              <Checkbox
                                checked={selectedRows.includes(
                                  (row as any).itemIndex ?? rowIndex
                                )}
                                onChange={() =>
                                  toggleRow((row as any).itemIndex ?? rowIndex)
                                }
                              />
                            </TableCell>
                            <TableCell
                              padding="checkbox"
                              sx={{
                                display: {
                                  xs: 'none',
                                  sm: 'table-cell',
                                },
                              }}
                            >
                              <TableItemThumbnail
                                images={(row as any).images}
                              />
                            </TableCell>
                            {columns.map((col, colIndex) => (
                              <TableCell
                                key={colIndex}
                                align={col.align || 'left'}
                                sx={{
                                  display: {
                                    xs: 'none',
                                    [col.displayAfterSize ?? 'xs']:
                                      'table-cell',
                                  },
                                }}
                              >
                                {col.render(row)}
                              </TableCell>
                            ))}
                            <TableCell align="right">
                              <Tooltip
                                title={t('permissions.groupTitles.actions')}
                              >
                                <IconButton
                                  onClick={e => handleMenuOpen(e, rowIndex)}
                                >
                                  <MoreVertIcon
                                    fontSize="small"
                                    sx={{
                                      transform: 'rotate(90deg)',
                                      color: '#0064F0',
                                    }}
                                  />
                                </IconButton>
                              </Tooltip>
                              <Menu
                                anchorEl={menuAnchorEl}
                                open={selectedRowIndex === rowIndex}
                                onClose={handleMenuClose}
                                TransitionComponent={Fade}
                                anchorOrigin={{
                                  vertical: 'bottom',
                                  horizontal: 'right',
                                }}
                                transformOrigin={{
                                  vertical: 'top',
                                  horizontal: 'right',
                                }}
                              >
                                {!['displayGroup', 'function'].includes(
                                  (row as any).type
                                ) && (
                                  <MenuItem
                                    onClick={() => {
                                      editItem(
                                        (row as any).itemIndex ?? rowIndex,
                                        (row as any).pageIndex ?? undefined
                                      );
                                      handleMenuClose();
                                    }}
                                  >
                                    {t('menu.editItem')}
                                  </MenuItem>
                                )}
                                <MenuItem
                                  sx={{ color: '#DC4437' }}
                                  onClick={() => {
                                    removeSelectedItems([
                                      {
                                        itemIndex:
                                          (row as any).itemIndex ?? rowIndex,
                                        pageIndex:
                                          (row as any).pageIndex ?? undefined,
                                      },
                                    ]);
                                    handleMenuClose();
                                  }}
                                >
                                  {t('menu.removeItem')}
                                </MenuItem>
                              </Menu>
                            </TableCell>
                          </TableRow>
                        )}
                      </Draggable>
                    );
                  })}
                  {provided.placeholder}
                  {renderAddItem && (
                    <TableRow>
                      <TableCell
                        sx={{ border: 0 }}
                        colSpan={columns.length + 3}
                      >
                        <Box display="flex" alignItems="start">
                          {renderAddItem()}
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              )}
            </Droppable>
          </DragDropContext>
        </Table>
      )}
    </Box>
  );
}
