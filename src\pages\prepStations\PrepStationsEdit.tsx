import { Box, Typography } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import {
  ConflictAwareInput,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import { useAvailableCategories } from '~/hooks';
import { validateName } from '~/utils/validateName';
import ModalHeader from '../../components/molecules/ModalHeader';

const PrepStationsEditInner = () => {
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');

  // Use the current prep station ID so its categories remain available
  const { availableCategories, isLoading } = useAvailableCategories(
    record?.id as string
  );

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('prepStations.editPrepStation')}
      >
        <SaveButton type="submit" label={t('shared.save')} icon={<></>} />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <ConflictAwareInput>
            <CustomInput
              type="text"
              ui="custom"
              sanitize="singleLine"
              source="name"
              label={t('shared.name')}
              validate={[required(), validateName]}
              roundedCorners="top"
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            {isLoading ? (
              <CustomInput
                source="groupIds"
                choices={[]}
                type="selectArray"
                ui="custom"
                label={t('prepStations.tags')}
                optionText="name"
                optionValue="id"
                placeholder={t('shared.loading')}
                validate={[required()]}
                disabled={true}
                roundedCorners="bottom"
              />
            ) : availableCategories.length > 0 ? (
              <CustomInput
                source="groupIds"
                choices={availableCategories}
                type="selectArray"
                ui="custom"
                label={t('prepStations.tags')}
                placeholder={t('shared.none', { context: 'female' })}
                optionText="name"
                optionValue="id"
                validate={[required()]}
                roundedCorners="bottom"
              />
            ) : (
              <>
                <CustomInput
                  source="groupIds"
                  choices={[]}
                  type="selectArray"
                  ui="custom"
                  label={t('prepStations.tags')}
                  optionText="name"
                  optionValue="id"
                  placeholder={t('prepStations.noAvailableCategories')}
                  validate={[required()]}
                  disabled={true}
                  roundedCorners="bottom"
                />
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: 'block' }}
                >
                  {t('prepStations.allCategoriesAssigned')}
                </Typography>
              </>
            )}
          </ConflictAwareInput>
        </Box>
      </Box>
    </>
  );
};

export const PrepStationEdit = () => {
  const transform = (data: any) => {
    return {
      ...data,
      groups: data.groupIds,
    };
  };
  return (
    <EditDialog
      maxWidth="sm"
      fullWidth
      transform={transform}
      mutationMode="pessimistic"
    >
      <ConflictAwareSimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        translationNamespace="shared"
      >
        <PrepStationsEditInner />
      </ConflictAwareSimpleForm>
    </EditDialog>
  );
};
