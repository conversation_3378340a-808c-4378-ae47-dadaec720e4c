<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>SELIO Manager</title>
    <meta name="description" content="SELIO Manager - POS Management System" />
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Geologica:wght@100;200;300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700&display=swap" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@3.0.1/dist/cookieconsent.css" />
    <link rel="stylesheet" href="/cookieConsent.css" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Shared loader styles -->
    <link rel="stylesheet" href="/loader.css" />

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="SELIO Manager" />
    <meta name="theme-color" content="#000000" />
    <meta name="application-name" content="SELIO Manager" />

    <style>
        #root {
            min-height: 100vh;
        }
    </style>
</head>

<body>
    <div id="initial-loader">
        <div class="loader">
            <div>
                <img src="/assets/logo/SELIO_LOGO_BLACK.svg" width="52px" class="spinner-logo" />
            </div>
            <div class="square" id="sq1"></div>
            <div class="square" id="sq2"></div>
            <div class="square" id="sq3"></div>
            <div class="square" id="sq4"></div>
            <div class="square" id="sq5"></div>
            <div class="square" id="sq6"></div>
            <div class="square" id="sq7"></div>
            <div class="square" id="sq8"></div>
            <div class="square" id="sq9"></div>
        </div>
    </div>
    <div id="draggable-portal"></div>
    <div id="root"></div>

    <!-- Shared Cookie Consent configuration -->
    <script src="/cookieConsent.js"></script>

    <script>
        window.global = window;
        window.APP_VERSION = '__APP_VERSION__';
    </script>

    <!-- Gateway loader - dynamically loads gateway script on interaction -->
    <script>
        // Interaction detector: loads gateway only after user interaction or timeout
        (function () {
            let loaded = false;
            let timeout;

            const loadGateway = function () {
                if (loaded) return;
                loaded = true;
                clearTimeout(timeout);
                console.log('[Detector] Loading gateway...');

                // Create script tag to load gateway module
                const script = document.createElement('script');
                script.type = 'module';
                script.src = '/assets/index-init-PLACEHOLDER.js'; // Replaced by build process
                script.onerror = function (err) {
                    console.error('[Detector] Failed to load gateway:', err);
                };
                document.body.appendChild(script);
            };

            // Listen for various interaction events
            const events = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart', 'touchmove'];
            events.forEach(function (event) {
                document.addEventListener(event, loadGateway, { once: true, passive: true });
            });

            // Fallback: Load after 2 seconds if no interaction detected
            timeout = setTimeout(function () {
                console.log('[Detector] Timeout (2s) reached, loading gateway...');
                loadGateway();
            }, 2000);
        })();
    </script>
</body>

</html>