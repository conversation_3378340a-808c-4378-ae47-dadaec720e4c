import { Box } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import {
  ConflictAwareInput,
  ConflictAwareSimpleForm,
} from '~/components/conflict-detection';
import { validateName } from '~/utils/validateName';
import ModalHeader from '../../components/molecules/ModalHeader';

const CategoryEditInner = () => {
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('categoryLibrary.editCategory')}
      >
        <SaveButton type="submit" label={t('shared.save')} icon={<></>} />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <ConflictAwareInput>
            <CustomInput
              type="text"
              ui="custom"
              sanitize="singleLine"
              source="name"
              label={t('shared.name')}
              validate={[required(), validateName]}
              roundedCorners="top"
            />
          </ConflictAwareInput>
          <ConflictAwareInput>
            <CustomInput
              source="straightFire"
              type="boolean"
              ui="custom"
              label="Straight fire"
              roundedCorners="bottom"
            />
          </ConflictAwareInput>
        </Box>
      </Box>
    </>
  );
};

export const CategoryEdit = () => {
  const transform = (data: any) => {
    return {
      ...data,
    };
  };

  return (
    <EditDialog
      maxWidth="sm"
      fullWidth
      transform={transform}
      mutationMode="pessimistic"
    >
      <ConflictAwareSimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        translationNamespace="shared"
      >
        <CategoryEditInner />
      </ConflictAwareSimpleForm>
    </EditDialog>
  );
};
