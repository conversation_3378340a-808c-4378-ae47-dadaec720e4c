/**
 * Shared <PERSON><PERSON>sent Styling
 * Single source of truth for cookie consent appearance across auth page and React-admin app
 */

#cc-main {
  --cc-btn-primary-bg: #0169ff;
  --cc-btn-primary-border-color: #0169ff;
  --cc-btn-primary-color: white;
  --cc-btn-primary-hover-bg: #004ab3;
  --cc-btn-primary-hover-color: white;
  --cc-btn-primary-hover-border-color: #004ab3;

  --cc-toggle-on-bg: var(--cc-btn-primary-bg);

  --cc-btn-border-radius: 10px;
}

/* ONLY IF WE WANT TO TARGET EACH COOKIE BUTTON */
/* POPUP REJECT BUTTON */
.cm__btn[data-role='necessary'] {
  background-color: white !important;
  border: 1.5px solid #0051c2 !important;
  color: #0051c2 !important;
}

.cm__btn[data-role='necessary']:hover {
  background-color: #ebebeb !important;
}

/* MODAL REJECT BUTTON */
.pm__btn[data-role='necessary'] {
  background-color: white !important;
  border: 1.5px solid #0051c2 !important;
  color: #0051c2 !important;
}

.pm__btn[data-role='necessary']:hover {
  background-color: #ebebeb !important;
}
