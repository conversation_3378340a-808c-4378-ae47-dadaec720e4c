import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import {
  Box,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Theme,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts/ThemeContext';
import FieldsMenu from './components/FieldsMenu';
import GroupingMenu from './components/GroupingMenu';
import InfoModal from './components/InfoModal';
import TableInput from './components/TableInput';
import styles from './styles.module.css';
import { ColumnConfig, FieldOption } from './types/globals';

type RowData = {
  [key: string]: any;
  items?: RowData[];
  subItems?: RowData[];
  extraData?: { [key: string]: any };
};

type CustomTableProps<T extends RowData> = {
  config: ColumnConfig<T>[];
  data: T[];
  fixedFirstColumn?: boolean;
  hideHeader?: boolean;
  modalTitle?: string;
  fixLastRow?: boolean;
  noMainRows?: boolean;
  alignLastColumnRight?: boolean;
  scrollable?: boolean;
  pagination?: boolean;
  expendRows?: boolean;
  fields: FieldOption[];
  searchBar?: boolean;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  enableInfoModal?: boolean;
  filter?: boolean;
  scrollHeight?: string;
  maxWidthFirstColumn?: string;
  columnsToFilter?: Array<string>;
  groupingOptions?: { label: string; value: string }[];
  groupingItems?: string[];
  extraDataFirstRow?: boolean;
  onChangeGrouping?: (items: any[]) => void;
  renderModalContent?: (rowData: T) => ReactNode;
  greyLastRow?: boolean;
  exportCSV?: boolean;
  handleExport?: () => void;
};

const CustomTable = <T extends RowData>({
  config,
  data,
  alignLastColumnRight,
  fixedFirstColumn,
  pagination,
  scrollable,
  hideHeader,
  expendRows,
  searchBar = true,
  fixLastRow,
  noMainRows,
  filter,
  columnsToFilter,
  enableInfoModal,
  renderModalContent,
  extraDataFirstRow,
  maxWidthFirstColumn,
  scrollHeight,
  fields,
  setFields,
  groupingOptions,
  greyLastRow,
  onChangeGrouping,
  groupingItems,
  exportCSV,
  handleExport,
}: CustomTableProps<T>) => {
  const [hoveredRowIndex, setHoveredRowIndex] = useState<number | null>(null);
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const { theme } = useTheme();
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [selectedRowData, setSelectedRowData] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');

  const [expandAll, setExpandAll] = useState(false);

  const toggleAllRowsExpansion = () => {
    setExpandAll(prev => !prev);
    setExpandedRows(prev => {
      const newState: Record<number, boolean> = {};
      data.forEach((_, index) => {
        newState[index] = !expandAll;
      });
      return newState;
    });
  };

  useEffect(() => {
    setExpandedRows(
      expendRows
        ? {}
        : Object.fromEntries(data.map((_, index) => [index, true]))
    );
  }, [expendRows, data]);

  const filteredData = useMemo(() => {
    if (!searchQuery.trim().length) return data;

    const normalizedQuery = searchQuery
      .trim()
      .replace(/\s+/g, ' ')
      .toLowerCase();

    const filtered = data.filter(row =>
      JSON.stringify(row).toLowerCase().includes(normalizedQuery)
    );

    return filtered;
  }, [searchQuery, data]);

  const handleChangePage = (newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const toggleRowExpansion = (rowIndex: number) => {
    setExpandedRows(prev => ({
      ...prev,
      [rowIndex]: !prev[rowIndex],
    }));
  };

  const handleRowClick = (row: T) => {
    if (enableInfoModal && row) {
      setSelectedRowData(row);
      setIsModalOpen(true);
    }
  };

  const closeModal = () => setIsModalOpen(false);

  const paginatedData = pagination
    ? filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : filteredData;

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        justifyContent: 'space-between',
        width: '100%',
      }}
    >
      <Box sx={{ width: '100%' }}>
        <Box
          sx={{
            width: '100%',
            alignItems: 'center',
            gap: 1,
            justifyContent: 'space-between',
            display: 'flex',
            mb: { xs: 1.5, sm: 1 },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            {searchBar && (
              <TableInput
                onChange={setSearchQuery}
                searchMaxWidth={isXSmall ? '100%' : '250px'}
              />
            )}
            <Box sx={{ display: 'flex', gap: 1 }}>
              {groupingOptions && groupingOptions?.length > 0 && (
                <GroupingMenu
                  groupingItemsState={groupingItems || []}
                  fields={fields}
                  groupingOptions={groupingOptions}
                  onChangeGrouping={onChangeGrouping}
                />
              )}
              {filter && (
                <FieldsMenu
                  fields={fields}
                  setFields={setFields}
                  config={config}
                  columnsToFilter={columnsToFilter}
                />
              )}
              {exportCSV && (
                <Button
                  className="do-not-print"
                  onClick={() => {
                    handleExport?.();
                  }}
                >
                  <FileDownloadIcon
                    color="primary"
                    sx={{ width: 20, height: 20 }}
                  />
                  {!isXSmall && 'Export'}
                </Button>
              )}
            </Box>
          </Box>
        </Box>

        <TableContainer
          className={styles.table}
          sx={{
            overflowY: scrollable ? 'visible' : 'visible',
            overflowX: 'auto !important',
            maxWidth: { xs: 'calc(100vw - 20px)' },
            '@media print': {
              overflowX: 'visible !important',
              maxWidth: {
                xs: '100%!important',
              },
            },
            maxHeight: scrollable
              ? {
                  sm: scrollHeight ? scrollHeight : 'calc(100vh - 400px)',
                  xs: 'calc(100vh - 370px)',
                }
              : 'none',
          }}
        >
          <Table stickyHeader>
            {!hideHeader && (
              <TableHead>
                <TableRow>
                  {config.map((column, index) => {
                    return fields.some(
                      f => f.value === column.id && f.isChecked
                    ) ? (
                      <TableCell
                        key={column.id as string}
                        sx={
                          fixedFirstColumn && index === 0
                            ? {
                                width: maxWidthFirstColumn
                                  ? maxWidthFirstColumn
                                  : '',
                                textAlign: column.textAlign
                                  ? column.textAlign
                                  : 'center',
                                position: 'sticky',
                                left: '-1px !important',
                                backgroundColor:
                                  theme.palette.mode == 'light'
                                    ? '#F2F2F2'
                                    : '#26262B',
                                '@media print': {
                                  backgroundColor: '#FFFFFF !important',
                                  color: 'black !important',
                                },

                                zIndex: 10,
                                borderRight: '1px #cecece dashed',
                                cursor: enableInfoModal ? 'pointer' : 'default',
                              }
                            : {
                                textTransform: 'capitalize',
                                '@media print': {
                                  backgroundColor: '#FFFFFF !important',
                                  color: 'black !important',
                                },
                                textAlign: column.textAlign
                                  ? column.textAlign
                                  : 'center',
                                backgroundColor:
                                  theme.palette.mode == 'light'
                                    ? '#F2F2F2'
                                    : '#26262B',
                                cursor: enableInfoModal ? 'pointer' : 'default',
                              }
                        }
                      >
                        {column.label}
                        <Box sx={{ position: 'relative', pr: 6 }}>
                          {expendRows &&
                            groupingItems &&
                            groupingItems.length > 0 &&
                            index === 0 && (
                              <IconButton
                                sx={{
                                  position: 'absolute',
                                  top: '-10px',
                                  transform: 'translateY(-50%)',
                                  right: '-10px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  cursor: 'pointer',
                                }}
                                onClick={toggleAllRowsExpansion}
                              >
                                {expandAll ? (
                                  <ExpandLessIcon />
                                ) : (
                                  <ExpandMoreIcon />
                                )}
                              </IconButton>
                            )}
                        </Box>
                      </TableCell>
                    ) : null;
                  })}
                </TableRow>
              </TableHead>
            )}
            {data.length === 0 || (fixLastRow && data.length === 1) ? (
              <TableRow>
                <TableCell colSpan={config.length} align="center">
                  <Box sx={{ width: '100%', textAlign: 'center' }}>
                    {t('reportsPage.noData')}
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              <TableBody>
                {paginatedData.map((row, rowIndex) => (
                  <React.Fragment key={rowIndex}>
                    {/* Main Row */}
                    <TableRow
                      onClick={() => handleRowClick(row)}
                      onMouseEnter={() => setHoveredRowIndex(rowIndex)}
                      onMouseLeave={() => setHoveredRowIndex(null)}
                      key={rowIndex}
                      sx={{
                        '&:hover':
                          (hoveredRowIndex === rowIndex && enableInfoModal) ||
                          ((row.subItems || row.items || row.isMain) &&
                            !expendRows &&
                            !noMainRows)
                            ? {
                                backgroundColor:
                                  theme.palette.mode == 'light'
                                    ? 'background.tinted'
                                    : '#0b0b0f',
                                '@media print': {
                                  backgroundColor: '#FFFFFF !important',
                                  color: 'black !important',
                                },
                                cursor: 'pointer',
                              }
                            : undefined,
                        backgroundColor:
                          (row.subItems || row.items || row.isMain) &&
                          !expendRows &&
                          !noMainRows
                            ? theme.palette.mode == 'light'
                              ? 'background.tinted'
                              : '#0b0b0f'
                            : extraDataFirstRow && rowIndex === 0
                              ? '#F4F8FF'
                              : theme.palette.mode == 'light'
                                ? 'white'
                                : 'transparent',
                        '@media print': {
                          backgroundColor: '#FFFFFF !important',
                          color: 'black !important',
                        },
                        ...(!searchQuery &&
                        fixLastRow &&
                        !expendRows &&
                        rowIndex === paginatedData.length - 1
                          ? {
                              fontWeight: '600 !important',
                              position: 'sticky',
                              bottom: '0.4px',
                              backgroundColor:
                                theme.palette.mode == 'light'
                                  ? '#F2F2F2'
                                  : '#26262B',
                              zIndex: 1,
                              '@media print': {
                                backgroundColor: '#FFFFFF !important',
                                color: 'black !important',
                              },
                            }
                          : {}),
                        ...(extraDataFirstRow && {
                          backgroundColor:
                            rowIndex === 0
                              ? theme.palette.mode == 'light'
                                ? '#F4F8FF'
                                : '#1E1E22'
                              : '',
                        }),
                      }}
                    >
                      {config.map((column, colIndex) =>
                        fields.some(
                          f => f.value === column.id && f.isChecked
                        ) ? (
                          <TableCell
                            key={String(column.id)}
                            sx={{
                              whiteSpace: expandedRows ? 'nowrap' : '',
                              backgroundColor:
                                hoveredRowIndex === rowIndex &&
                                (row.subItems || row.items || row.isMain) &&
                                !expendRows &&
                                !noMainRows
                                  ? theme.palette.mode == 'light'
                                    ? 'background.tinted'
                                    : '#0b0b0f'
                                  : undefined,
                              '@media print': {
                                backgroundColor: '#FFFFFF !important',
                                color: 'black !important',
                              },
                              cursor:
                                hoveredRowIndex === rowIndex &&
                                (row.subItems || row.items || row.isMain) &&
                                !expendRows &&
                                !noMainRows
                                  ? 'pointer'
                                  : undefined,
                              textAlign: column.textAlign
                                ? column.textAlign
                                : 'center',
                              fontWeight:
                                (row.subItems || row.items || row.isMain) &&
                                !expendRows &&
                                !noMainRows
                                  ? 'bold'
                                  : 'normal',
                              ...(fixedFirstColumn && colIndex === 0
                                ? {
                                    width: maxWidthFirstColumn
                                      ? maxWidthFirstColumn
                                      : '',
                                    position: 'sticky',
                                    left: '-1px !important',
                                    backgroundColor:
                                      (hoveredRowIndex === rowIndex &&
                                        enableInfoModal) ||
                                      ((row.subItems ||
                                        row.items ||
                                        row.isMain) &&
                                        !expendRows &&
                                        !noMainRows)
                                        ? theme.palette.mode == 'light'
                                          ? 'background.tinted'
                                          : '#0b0b0f'
                                        : extraDataFirstRow && rowIndex === 0
                                          ? theme.palette.mode == 'light'
                                            ? '#F4F8FF'
                                            : '#1E1E22'
                                          : theme.palette.mode == 'light'
                                            ? 'white'
                                            : '#0C0C12',
                                    zIndex: 10,
                                    borderRight: '1px #cecece dashed',
                                  }
                                : {}),
                              ...(alignLastColumnRight &&
                              colIndex === config.length - 1
                                ? { textAlign: 'right' }
                                : {}),
                              ...(!searchQuery &&
                              fixLastRow &&
                              !expendRows &&
                              rowIndex === paginatedData.length - 1
                                ? {
                                    fontWeight: '600 !important',
                                    backgroundColor: greyLastRow
                                      ? 'background.tinted'
                                      : theme.palette.mode == 'light'
                                        ? 'white'
                                        : '#0C0C12',
                                    // borderTop: '1px solid #CECECE',
                                  }
                                : {}),
                            }}
                          >
                            {expendRows &&
                              row?.subItems &&
                              row?.subItems.length > 0 &&
                              colIndex === 0 && (
                                <IconButton
                                  sx={{ p: 0.5, mr: 0.5 }}
                                  onClick={e => {
                                    e.stopPropagation();
                                    toggleRowExpansion(rowIndex);
                                  }}
                                >
                                  {expandedRows[rowIndex] ? (
                                    <ExpandLessIcon />
                                  ) : (
                                    <ExpandMoreIcon />
                                  )}
                                </IconButton>
                              )}
                            {column.render
                              ? column.render(row as T, rowIndex, colIndex)
                              : (() => {
                                  const cellValue =
                                    row[column.id as keyof RowData];

                                  return typeof cellValue === 'object' &&
                                    cellValue !== null &&
                                    'title' in cellValue
                                    ? (cellValue as { title: string }).title
                                    : typeof cellValue === 'string' ||
                                        typeof cellValue === 'number'
                                      ? cellValue
                                      : '';
                                })()}
                          </TableCell>
                        ) : null
                      )}
                    </TableRow>
                    {/* SubItems */}
                    {expandedRows[rowIndex] &&
                      row.subItems &&
                      row.subItems.map((subItem, subIndex) => (
                        <TableRow key={`${rowIndex}-${subIndex}`}>
                          {config.map((column, colIndex) =>
                            fields.some(
                              f => f.value === column.id && f.isChecked
                            ) ? (
                              <TableCell
                                key={`${String(column.id)}-${subIndex}`}
                                sx={{
                                  textAlign: column.textAlign
                                    ? column.textAlign
                                    : 'center',
                                  paddingLeft:
                                    colIndex === 0 ? (!expendRows ? 5 : 7) : '',
                                  color: 'gray',
                                  ...(fixedFirstColumn && colIndex === 0
                                    ? {
                                        width: maxWidthFirstColumn
                                          ? maxWidthFirstColumn
                                          : '',
                                        position: 'sticky',
                                        left: '-1px !important',
                                        backgroundColor:
                                          (hoveredRowIndex === rowIndex &&
                                            enableInfoModal) ||
                                          ((row.subItems ||
                                            row.items ||
                                            row.isMain) &&
                                            !expendRows &&
                                            !noMainRows)
                                            ? theme.palette.mode == 'light'
                                              ? 'background.tinted'
                                              : '#0b0b0f'
                                            : extraDataFirstRow &&
                                                rowIndex === 0
                                              ? '#F4F8FF'
                                              : 'white',
                                        zIndex: 1,
                                        borderRight: '1px #cecece dashed',
                                      }
                                    : {}),
                                  ...(alignLastColumnRight &&
                                  colIndex === config.length - 1
                                    ? { textAlign: 'right' }
                                    : {}),
                                }}
                              >
                                {column.render
                                  ? column.render(
                                      subItem as T,
                                      subIndex,
                                      colIndex
                                    )
                                  : (() => {
                                      const cellValue =
                                        subItem[column.id as keyof RowData];

                                      return typeof cellValue === 'object' &&
                                        cellValue !== null &&
                                        'title' in cellValue
                                        ? (cellValue as { title: string }).title
                                        : typeof cellValue === 'string' ||
                                            typeof cellValue === 'number'
                                          ? cellValue
                                          : '';
                                    })()}
                              </TableCell>
                            ) : null
                          )}
                        </TableRow>
                      ))}
                    {/* Items */}
                    {row.items &&
                      row.items.map((item, itemIndex) => (
                        <React.Fragment key={`${rowIndex}-${itemIndex}`}>
                          <TableRow>
                            {config.map((column, colIndex) =>
                              fields.some(
                                f => f.value === column.id && f.isChecked
                              ) ? (
                                <TableCell
                                  key={`${String(column.id)}-${itemIndex}`}
                                  sx={{
                                    textAlign: column.textAlign
                                      ? column.textAlign
                                      : 'center',
                                    ...(fixedFirstColumn && colIndex === 0
                                      ? {
                                          width: maxWidthFirstColumn
                                            ? maxWidthFirstColumn
                                            : '',
                                          position: 'sticky',
                                          left: '-1px !important',
                                          backgroundColor:
                                            (hoveredRowIndex === rowIndex &&
                                              enableInfoModal) ||
                                            ((row.subItems ||
                                              row.items ||
                                              row.isMain) &&
                                              !expendRows &&
                                              !noMainRows)
                                              ? theme.palette.mode == 'light'
                                                ? 'background.tinted'
                                                : '#0b0b0f'
                                              : extraDataFirstRow &&
                                                  rowIndex === 0
                                                ? '#F4F8FF'
                                                : 'white',
                                          zIndex: 1,
                                          borderRight: '1px #cecece dashed',
                                        }
                                      : {}),
                                    ...(alignLastColumnRight &&
                                    colIndex === config.length - 1
                                      ? { textAlign: 'right' }
                                      : {}),
                                  }}
                                >
                                  {column.render
                                    ? column.render(
                                        item as T,
                                        itemIndex,
                                        colIndex
                                      )
                                    : (() => {
                                        const cellValue =
                                          item[column.id as keyof RowData];

                                        return typeof cellValue === 'object' &&
                                          cellValue !== null &&
                                          'title' in cellValue
                                          ? (cellValue as { title: string })
                                              .title
                                          : typeof cellValue === 'string' ||
                                              typeof cellValue === 'number'
                                            ? cellValue
                                            : '';
                                      })()}
                                </TableCell>
                              ) : null
                            )}
                          </TableRow>

                          {/* SubItems of Items */}
                          {item.subItems &&
                            item.subItems.map((subItem, subItemIndex) => (
                              <TableRow
                                key={`${rowIndex}-${itemIndex}-${subItemIndex}`}
                              >
                                {config.map((column, colIndex) =>
                                  fields.some(
                                    f => f.value === column.id && f.isChecked
                                  ) ? (
                                    <TableCell
                                      key={`${String(column.id)}-${subItemIndex}`}
                                      sx={{
                                        textAlign: column.textAlign
                                          ? column.textAlign
                                          : 'center',
                                        paddingLeft: colIndex === 0 ? 5 : '',
                                        color: 'gray',
                                        ...(fixedFirstColumn && colIndex === 0
                                          ? {
                                              width: maxWidthFirstColumn
                                                ? maxWidthFirstColumn
                                                : '',
                                              position: 'sticky',
                                              left: '-1px !important',
                                              backgroundColor:
                                                (hoveredRowIndex === rowIndex &&
                                                  enableInfoModal) ||
                                                ((row.subItems ||
                                                  row.items ||
                                                  row.isMain) &&
                                                  !expendRows &&
                                                  !noMainRows)
                                                  ? theme.palette.mode ==
                                                    'light'
                                                    ? 'background.tinted'
                                                    : '#0b0b0f'
                                                  : extraDataFirstRow &&
                                                      rowIndex === 0
                                                    ? '#F4F8FF'
                                                    : 'white',
                                              zIndex: 1,
                                              borderRight: '1px #cecece dashed',
                                            }
                                          : {}),
                                        ...(alignLastColumnRight &&
                                        colIndex === config.length - 1
                                          ? { textAlign: 'right' }
                                          : {}),
                                      }}
                                    >
                                      {column.render
                                        ? column.render(
                                            subItem as T,
                                            rowIndex,
                                            colIndex
                                          )
                                        : (() => {
                                            const cellValue =
                                              subItem[
                                                column.id as keyof RowData
                                              ];

                                            return typeof cellValue ===
                                              'object' &&
                                              cellValue !== null &&
                                              'title' in cellValue
                                              ? (cellValue as { title: string })
                                                  .title
                                              : typeof cellValue === 'string' ||
                                                  typeof cellValue === 'number'
                                                ? cellValue
                                                : '';
                                          })()}
                                    </TableCell>
                                  ) : null
                                )}
                              </TableRow>
                            ))}
                        </React.Fragment>
                      ))}
                  </React.Fragment>
                ))}
              </TableBody>
            )}
          </Table>
        </TableContainer>
        {pagination && (
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            className={styles.pagination}
            count={data.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => handleChangePage(newPage)}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        )}
        {enableInfoModal && selectedRowData && (
          <InfoModal
            open={isModalOpen}
            extraData={selectedRowData}
            onClose={closeModal}
          >
            {renderModalContent?.(selectedRowData)}
          </InfoModal>
        )}
      </Box>
    </Box>
  );
};

export default CustomTable;
