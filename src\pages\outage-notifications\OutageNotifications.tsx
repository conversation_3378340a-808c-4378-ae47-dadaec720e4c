import React, { useEffect, useState } from 'react';
import { Box, Button, MenuItem, TextField, Typography } from '@mui/material';
import { ReferenceInput, SimpleForm, useDataProvider } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '../../components/atoms/inputs/CustomInput/CustomInput';
import CustomModal from '../../components/molecules/CustomModal';
import Subsection from '../../components/molecules/Subsection';

interface OutageNotifications {
  email: string;
  phone?: {
    number: string;
    prefix: string;
  };
}

const OutageNotifications: React.FC = () => {
  const { t } = useTranslation();
  const dataProvider = useDataProvider();
  const [outageNotifications, setOutageNotifications] =
    useState<OutageNotifications>({} as OutageNotifications);
  const [emailState, setEmailState] = useState('');
  const [emailModal, setEmailModal] = useState(false);

  const [phoneState, setPhoneState] = useState('');
  const [phoneModal, setPhoneModal] = useState(false);
  const [countryCode, setCountryCode] = useState('+40');

  const [countryCodes] = useState([
    { code: '+40', flag: '🇷🇴' },
    { code: '+44', flag: '🇬🇧' },
    { code: '+33', flag: '🇫🇷' },
    { code: '+34', flag: '🇪🇸' },
  ]);

  useEffect(() => {
    const fetchOutageNotifications = async () => {
      try {
        const { data } = await dataProvider.getOne('outageNotifications', {
          id: '1',
        });
        setOutageNotifications(data);
        setEmailState(data.email);
        setPhoneState(data.phone?.number || '');
        setCountryCode(data.phone?.prefix || '+40');
      } catch (error: any) {
        console.log(error.message);
      }
    };

    fetchOutageNotifications();
  }, [dataProvider]);

  const updateOutageNotifications = async (
    updatedInfo: OutageNotifications
  ) => {
    try {
      await dataProvider.update('outageNotifications', {
        id: '1',
        data: updatedInfo,
        previousData: outageNotifications,
      });
      setOutageNotifications(updatedInfo);
      closeAllModals();
    } catch (error: any) {
      console.log(error.message);
    }
  };

  const closeAllModals = () => {
    handleCloseEmail();
    handleClosePhone();
  };

  const handleClosePhone = () => {
    setPhoneModal(false);
    resetPhoneState();
  };

  const openPhoneModal = () => {
    resetPhoneState();
    setPhoneModal(true);
  };

  const openEmailModal = () => {
    resetEmailState();
    setEmailModal(true);
  };

  const resetEmailState = () => {
    setEmailState(outageNotifications.email || '');
  };

  const resetPhoneState = () => {
    setPhoneState(outageNotifications.phone?.number || '');
    setCountryCode(outageNotifications.phone?.prefix || '+40');
  };

  const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneState(event.target.value);
  };

  const handleCountryCodeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCountryCode(event.target.value);
  };

  const handleCloseEmail = () => {
    setEmailModal(false);
    setEmailState(outageNotifications.email);
  };

  const handleEmailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEmailState(event.target.value);
  };

  return (
    <>
      <Box
        p={2}
        sx={{
          mt: 3,
          width: '100%',
          maxWidth: '600px',
          display: 'flex',
          flexDirection: 'column',
          gap: 4,
        }}
      >
        <Subsection
          titleSx={{ fontSize: 25 }}
          title={t('menu.outageNotifications')}
          subtitle={t('outageNotifications.description')}
        >
          <SimpleForm toolbar={false} sx={{ p: 0 }}>
            <Box
              sx={{
                position: 'relative',
                display: 'flex',
                width: { xs: '100%', sm: 'fit-content' },
              }}
            >
              <ReferenceInput source="id2" reference="outageNotifications">
                <CustomInput
                  type="text"
                  label={t('outageNotifications.emailAddress')}
                  source="outageNotifications"
                  InputProps={{
                    sx: {
                      pr: { xs: 6, sm: 8 },
                    },
                  }}
                  disabled
                  defaultValue={
                    outageNotifications.email ? outageNotifications.email : '-'
                  }
                />
              </ReferenceInput>
              <Button
                onClick={openEmailModal}
                sx={{
                  position: 'absolute',
                  right: 0,
                  top: { xs: 38, sm: 0 },
                  zIndex: 10,
                  borderRadius: 0,
                }}
              >
                {t('shared.edit')}
              </Button>
            </Box>
            <Box
              sx={{
                position: 'relative',
                display: 'flex',
                width: { xs: '100%', sm: 'fit-content' },
              }}
            >
              <ReferenceInput source="id" reference="outageNotifications">
                <CustomInput
                  type="text"
                  label={t('signInSecurity.phoneNumber')}
                  source="outageNotifications"
                  InputProps={{
                    sx: {
                      pr: { xs: 6, sm: 8 },
                    },
                  }}
                  disabled
                  defaultValue={
                    outageNotifications?.phone?.number
                      ? outageNotifications?.phone?.prefix +
                        outageNotifications?.phone?.number
                      : '-'
                  }
                />
              </ReferenceInput>
              <Button
                onClick={openPhoneModal}
                sx={{
                  position: 'absolute',
                  right: 0,
                  top: { xs: 38, sm: 0 },
                  zIndex: 10,
                  borderRadius: 0,
                }}
              >
                {t('shared.edit')}
              </Button>
            </Box>
          </SimpleForm>
        </Subsection>
      </Box>
      <CustomModal
        modalTextButton="Confirm"
        onSubmit={() =>
          updateOutageNotifications({
            ...outageNotifications,
            phone: { number: phoneState, prefix: countryCode },
          })
        }
        modalHeaderTile={
          phoneState
            ? t('outageNotifications.changePhoneNumber')
            : t('outageNotifications.addPhoneNumber')
        }
        open={phoneModal}
        handleClose={handleClosePhone}
        confirmDisabled={!phoneState || !countryCode}
        fullWidth
        maxWidth="sm"
      >
        <Box sx={{ p: 2 }}>
          <Typography
            sx={{
              fontWeight: 300,
              lineHeight: { xs: 1.5 },
              mb: { xs: 1 },
            }}
          >
            {t('outageNotifications.addPhoneNumberDescription')}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              select
              sx={{ width: { xs: 140, md: 120 } }}
              value={countryCode}
              onChange={handleCountryCodeChange}
              margin="normal"
            >
              {countryCodes.map(country => (
                <MenuItem key={country.code} value={country.code}>
                  {country.flag} {country.code}
                </MenuItem>
              ))}
            </TextField>
            <TextField
              label={t('outageNotifications.phoneNumber')}
              type="number"
              value={phoneState}
              onChange={handlePhoneChange}
              fullWidth
              margin="normal"
            />
          </Box>
        </Box>
      </CustomModal>

      <CustomModal
        modalTextButton="Confirm"
        onSubmit={() =>
          updateOutageNotifications({
            ...outageNotifications,
            email: emailState,
          })
        }
        modalHeaderTile={
          !outageNotifications.email
            ? t('outageNotifications.addEmailAddress')
            : t('outageNotifications.updateEmailAddress')
        }
        open={emailModal}
        handleClose={handleCloseEmail}
        confirmDisabled={!emailState}
        fullWidth
        maxWidth="sm"
      >
        <Box sx={{ p: 2 }}>
          <Typography sx={{ fontWeight: 300, lineHeight: 2, ml: 0.5 }}>
            {t('outageNotifications.addEmailAddressDescription')}
          </Typography>
          <TextField
            label={t('outageNotifications.emailAddress')}
            type="email"
            value={emailState}
            onChange={handleEmailChange}
            margin="normal"
          />
        </Box>
      </CustomModal>
    </>
  );
};

export default OutageNotifications;
