/**
 * Conflict Detection UI Components
 *
 * Components for building conflict-aware forms in react-admin.
 *
 * @example Basic usage with ConflictAwareSimpleForm
 * ```tsx
 * import {
 *   ConflictAwareSimpleForm,
 *   ConflictAwareInput,
 * } from '@/components/conflict-detection';
 *
 * function MyResourceEdit() {
 *   return (
 *     <EditDialog>
 *       <ConflictAwareSimpleForm translationNamespace="myResource">
 *         <ConflictAwareInput>
 *           <TextInput source="name" />
 *         </ConflictAwareInput>
 *       </ConflictAwareSimpleForm>
 *     </EditDialog>
 *   );
 * }
 * ```
 */

// SimpleForm replacement with built-in conflict detection
export {
  ConflictAwareSimpleForm,
  type ConflictAwareSimpleFormProps,
} from './ConflictAwareSimpleForm';

// Input wrappers with visual indicators
export {
  ConflictAwareInput,
  withConflictAwareness,
  type ConflictAwareInputProps,
} from './ConflictAwareInput';

// Entity wrappers for non-input visual elements (tables, cards, etc.)
export {
  ConflictAwareEntity,
  EntityConflictIndicator,
  useEntityConflictStatus,
  type ConflictAwareEntityProps,
  type EntityConflictIndicatorProps,
  type EntityConflictStatus,
} from './ConflictAwareEntity';

// Specialized wrapper for DraggableTable with conflict detection
export { ConflictAwareDraggableTable } from './ConflictAwareDraggableTable';

// Specialized wrapper for CatalogItem with conflict detection (menu tiles)
export { ConflictAwareCatalogItem } from './ConflictAwareCatalogItem';
