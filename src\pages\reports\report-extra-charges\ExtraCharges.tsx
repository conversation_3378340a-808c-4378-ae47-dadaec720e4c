import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, capitalize, useMediaQuery } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { FieldOption } from '~/components/organisms/CustomTable/types/globals';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import { useGetListLocationsLive } from '~/providers/resources';
import cleanStringArond from '~/utils/cleanStringArond';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import ExtraChargesGraph from './components/ExtraChargesGraph';
import ExtraChargesTable from './components/ExtraChargesTable';

const REPORT_TYPE = 'extraCharges';

const fieldsConstant = [
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'quantity' },
  { isChecked: true, value: 'price' },
  { isChecked: true, value: 'name' },
  { isChecked: true, value: 'value' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'promotionName' },
  { isChecked: true, value: 'netValue' },
];

export default function ExtraCharges() {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [rawData, setRawData] = useState<any>();
  const [currency, setCurrency] = useState<string>('RON');
  const [tableFields, setTableFields] = useState<FieldOption[]>(fieldsConstant);
  const [groupingItems, setGroupingItems] = useState<string[]>([]);
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = (key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  const contentRef = useRef<HTMLDivElement>(null);

  //TODO: asta nu mai e ok la dining si la source
  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });

        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });

        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        });

        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });

        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const { tableData, graphData } = useMemo(() => {
    if (!rawData || !filters) return { tableData: [], graphData: {} };

    if (rawData.length) {
      setCurrency(rawData[0].currency);
    }

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const highestValueProductIds = (
      groupReport(REPORT_TYPE, rawDataFiltered, [], ['id'])[0]?.report ?? []
    )
      .sort((a, b) => (b.value ?? 0) - (a.value ?? 0))
      .slice(0, 5)
      .map(el => el.id);

    const filteredByIds = filterReport(REPORT_TYPE, rawData, composedFilters, [
      {
        field: 'id',
        operator: 'in',
        value: highestValueProductIds,
      },
    ]);

    const groupedByHour = groupReport(
      REPORT_TYPE,
      filteredByIds,
      ['hourOfDay'],
      ['id', 'name']
    );

    const labels = groupedByHour?.map(el => el.hourOfDay.toString());

    const idToNameMap: Record<string, string> = {};
    groupedByHour.forEach(({ report: items }) => {
      items.forEach(item => {
        if (item.id && item.name) {
          idToNameMap[item.id] = item.name;
        }
      });
    });

    const datasets: { label: string; data: number[] }[] =
      highestValueProductIds.map(id => ({
        label: idToNameMap[id] || id,
        data: [],
      }));

    groupedByHour.forEach(({ report: items }) => {
      datasets.forEach(el => {
        const item = items.find(
          i =>
            i.id ===
            highestValueProductIds.find(
              pid => idToNameMap[pid] === el.label || pid === el.label
            )
        );

        if (item) {
          const itemsValue = item.value || 0;
          const formattedValue = Math.round((itemsValue / 10000) * 10) / 10;
          el.data.push(formattedValue);
        } else {
          el.data.push(0);
        }
      });
    });

    const graphData = {
      datasets,
      labels,
    };

    const filteredFields = tableFields.filter((field: any) => {
      return (
        field.isChecked &&
        reportSpecificFields.extraCharges.some(
          discountField =>
            cleanStringArond(field.value) === cleanStringArond(discountField)
        )
      );
    });

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      filteredFields.map((item: FieldOption) => item.value)
    );

    if (!groupedTableData) return { tableData: [] };

    const groupedByItemsTableData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        groupedTableData,
        groupingItems as []
      )[0]?.report || [];

    const tableData = remapReports(
      sortData(groupedByItemsTableData) || [],
      'name'
    );

    return { tableData, graphData };
  }, [filters, rawData, groupingItems, tableFields]);

  const onChangeGrouping = (items: string[]) => {
    setGroupingItems(items);
  };

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report extra charges';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      ['Name', 'VAT', 'Price', 'Quantity', 'Gross Sales', 'Net Sales'].join(
        ','
      ),
      ...tableData.map(el =>
        [
          el.name,
          el.vat,
          el.price / 10000 || 0,
          el.quantity / 1000 || 0,
          el.value / 10000 || 0,
          el.netValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        '',
        tableData.reduce((acc, el) => acc + el.price, 0) / 10000,
        tableData.reduce((acc, el) => acc + el.quantity, 0) / 1000,
        tableData.reduce((acc, el) => acc + (el.value || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.netValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'extraCharges');
  };

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('md'));

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('reports.extraCharges')}
        description={
          <>
            {t('extraCharges.description')}
            <a href="https://selio.io/support-center" target="_blank">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={
          <ExportMenuButton
            contentRef={contentRef}
            handleExport={handleExport}
          />
        }
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />
      <ReportDateTitle />
      {isSmall ? null : (
        <ExtraChargesGraph
          data={graphData ?? {}}
          currency={currency as 'RON' | 'USD'}
        />
      )}
      <Box sx={{ pb: 3 }}>
        <ExtraChargesTable
          fields={tableFields}
          setFields={setTableFields}
          tableData={tableData || []}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
        />
      </Box>
    </Box>
  );
}
