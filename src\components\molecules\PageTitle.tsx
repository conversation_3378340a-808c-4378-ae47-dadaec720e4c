import { ReactElement, ReactNode } from 'react';
import { Box, SxProps, Theme, Typography } from '@mui/material';

interface PageTitleProps {
  title: string;
  description?: string | ReactElement;
  hideBorder?: boolean;
  sx?: SxProps<Theme>;
  doNotPrint?: boolean;
  action?: ReactNode;
}
export default function PageTitle({
  title,
  description,
  hideBorder,
  doNotPrint = false,
  action,
  sx = {},
}: PageTitleProps) {
  return (
    <Box
      sx={{
        ...{
          borderBottom: hideBorder ? 'none' : '1px solid rgba(0,0,0,.1)',
          mt: 1,
          mb: 2,
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        },
        ...sx,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          minHeight: { xs: '36px', sm: '48px' }, // Reserve space for action button even when not present
        }}
      >
        <Typography
          sx={{
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
            flex: 1,
          }}
          variant="h3"
        >
          {title}
        </Typography>
        {action && (
          <Box className="do-not-print" sx={{ flexShrink: 0, mr: -2 }}>
            {action}
          </Box>
        )}
      </Box>
      {description && (
        <Typography
          className={doNotPrint ? 'do-not-print' : ''}
          variant="subtitle2"
          mt={0}
        >
          {description}
        </Typography>
      )}
    </Box>
  );
}
