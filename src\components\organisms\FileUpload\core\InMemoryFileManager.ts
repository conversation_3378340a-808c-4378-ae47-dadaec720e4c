/**
 * In-Memory File Manager
 * Replaces temp bucket storage with client-side blob storage
 * Handles blob URL generation, memory tracking, and cleanup
 */

import {
  IN_MEMORY_FILE_CONFIG,
  InMemoryFileData,
  UploadedFile,
} from '../types/fileUpload';

/**
 * Memory usage statistics
 */
export interface MemoryUsageStats {
  totalSize: number; // Total memory usage in bytes
  fileCount: number; // Number of files in memory
  blobUrlCount: number; // Number of active blob URLs
  largestFile: number; // Size of largest file in bytes
  oldestFile: number; // Timestamp of oldest file
}

/**
 * Blob URL cache entry
 */
interface BlobUrlCacheEntry {
  url: string;
  fileId: string;
  variant: string;
  createdAt: number;
  size: number;
}

/**
 * In-Memory File Manager
 * Singleton class for managing in-memory files and blob URLs
 */
export class InMemoryFileManager {
  private static instance: InMemoryFileManager;

  // Memory tracking
  private memoryUsage = 0;
  private fileCount = 0;

  // Blob URL management
  private blobUrls = new Map<string, BlobUrlCacheEntry>();
  private fileToUrls = new Map<string, Set<string>>(); // fileId -> Set of cache keys

  // Active file protection - prevents cleanup of URLs for files currently in use
  private activeFiles = new Set<string>();

  // Cleanup management
  private cleanupInterval: NodeJS.Timeout | null = null;
  private lastCleanup = Date.now();

  private constructor() {
    this.startCleanupInterval();
    this.setupMemoryWarnings();
  }

  static getInstance(): InMemoryFileManager {
    if (!InMemoryFileManager.instance) {
      InMemoryFileManager.instance = new InMemoryFileManager();
    }
    return InMemoryFileManager.instance;
  }

  /**
   * Get blob URL for a file variant
   */
  async getUrl(file: UploadedFile, variant?: string): Promise<string> {
    console.log('🔍 [InMemoryFileManager] getUrl called:', {
      fileName: file.f,
      requestedVariant: variant || 'original',
      hasInMemoryData: !!file.inMemoryData,
      isActive: this.activeFiles.has(file.f),
      availableVariants: file.inMemoryData?.variants
        ? Array.from(file.inMemoryData.variants.keys())
        : [],
    });

    if (!file.inMemoryData) {
      console.error(
        '🚨 [InMemoryFileManager] File missing inMemoryData - this should not happen for temp files:',
        { fileName: file.f, fileType: file.t, isTemp: file.x }
      );
      throw new Error(
        `Temporary file ${file.f}.${file.e} missing in-memory data. This indicates a system error.`
      );
    }

    const cacheKey = this.getCacheKey(file.f, variant || 'original');
    console.log('🔍 [InMemoryFileManager] Cache key:', cacheKey);

    // Check if we have a cached URL that's still valid
    const cachedEntry = this.blobUrls.get(cacheKey);
    if (cachedEntry && this.isUrlValid(cachedEntry)) {
      console.log(
        '🔍 [InMemoryFileManager] Using cached URL for:',
        cacheKey,
        cachedEntry.url
      );
      return cachedEntry.url;
    }

    // Clean up old URL if it exists but is invalid
    if (cachedEntry) {
      console.log(
        '🔍 [InMemoryFileManager] Cleaning up invalid cached URL for:',
        cacheKey
      );
      URL.revokeObjectURL(cachedEntry.url);
      this.blobUrls.delete(cacheKey);

      // Update file mapping
      const fileUrls = this.fileToUrls.get(file.f);
      if (fileUrls) {
        fileUrls.delete(cacheKey);
      }
    }

    // Get the appropriate blob
    let blob: Blob;
    if (!variant || variant === 'original') {
      blob = file.inMemoryData.original;
      console.log(
        '🔍 [InMemoryFileManager] Using original blob, size:',
        blob.size
      );
    } else if (file.inMemoryData.variants?.has(variant)) {
      blob = file.inMemoryData.variants.get(variant)!;
      console.log(
        '🔍 [InMemoryFileManager] Using variant blob for',
        variant,
        'size:',
        blob.size
      );
    } else {
      // Variant doesn't exist - this should trigger variant generation or return error
      console.error(
        '� [InMemoryFileManager] Requested variant does not exist:',
        {
          fileName: file.f,
          requestedVariant: variant,
          availableVariants: Array.from(
            file.inMemoryData.variants?.keys() || []
          ),
        }
      );
      throw new Error(
        `Variant '${variant}' not found for file ${file.f}.${file.e}. Available variants: ${Array.from(file.inMemoryData.variants?.keys() || []).join(', ')}`
      );
    }

    // Create new blob URL
    const blobUrl = URL.createObjectURL(blob);
    console.log(
      '🔍 [InMemoryFileManager] Created new blob URL:',
      blobUrl,
      'for variant:',
      variant || 'original'
    );

    // Cache the URL
    const entry: BlobUrlCacheEntry = {
      url: blobUrl,
      fileId: file.f,
      variant: variant || 'original',
      createdAt: Date.now(),
      size: blob.size,
    };

    this.blobUrls.set(cacheKey, entry);

    // Track file -> URLs mapping
    if (!this.fileToUrls.has(file.f)) {
      this.fileToUrls.set(file.f, new Set());
    }
    this.fileToUrls.get(file.f)!.add(cacheKey);

    // Enforce URL limits
    this.enforceUrlLimits();

    console.log('🔍 [InMemoryFileManager] Returning new blob URL:', blobUrl);
    return blobUrl;
  }

  /**
   * Mark a file as actively being used (protects its URLs from cleanup)
   */
  markFileAsActive(fileId: string): void {
    this.activeFiles.add(fileId);
    console.log('🔒 [InMemoryFileManager] Marked file as active:', fileId);
  }

  /**
   * Unmark a file as active (allows its URLs to be cleaned up)
   */
  unmarkFileAsActive(fileId: string): void {
    this.activeFiles.delete(fileId);
    console.log('🔓 [InMemoryFileManager] Unmarked file as active:', fileId);
  }

  /**
   * Mark multiple files as active (e.g., current file list)
   */
  markFilesAsActive(fileIds: string[]): void {
    fileIds.forEach(fileId => this.markFileAsActive(fileId));
  }

  /**
   * Clear all active file markings
   */
  clearActiveFiles(): void {
    this.activeFiles.clear();
    console.log('🔓 [InMemoryFileManager] Cleared all active file markings');
  }

  /**
   * Handle blob URL errors by regenerating the URL
   */
  async handleBlobError(file: UploadedFile, variant?: string): Promise<string> {
    console.log(
      '🔍 [InMemoryFileManager] Handling blob error, regenerating URL for:',
      {
        fileName: file.f,
        variant: variant || 'original',
      }
    );

    const cacheKey = this.getCacheKey(file.f, variant || 'original');

    // Remove the invalid cached URL
    const cached = this.blobUrls.get(cacheKey);
    if (cached) {
      URL.revokeObjectURL(cached.url);
      this.blobUrls.delete(cacheKey);
    }

    // Regenerate the URL
    return this.getUrl(file, variant);
  }

  /**
   * Track memory usage when a file is added
   */
  trackFile(file: UploadedFile): void {
    if (!file.inMemoryData) return;

    let size = file.inMemoryData.original.size;
    if (file.inMemoryData.variants) {
      for (const variant of file.inMemoryData.variants.values()) {
        size += variant.size;
      }
    }

    this.memoryUsage += size;
    this.fileCount++;

    // Check memory limits
    this.checkMemoryLimits();
  }

  /**
   * Clean up all resources for a file
   */
  cleanupFile(file: UploadedFile): void {
    if (!file.inMemoryData) return;

    // Revoke all blob URLs for this file
    const fileUrls = this.fileToUrls.get(file.f);
    if (fileUrls) {
      for (const cacheKey of fileUrls) {
        const entry = this.blobUrls.get(cacheKey);
        if (entry) {
          URL.revokeObjectURL(entry.url);
          this.blobUrls.delete(cacheKey);
        }
      }
      this.fileToUrls.delete(file.f);
    }

    // Update memory usage
    let size = file.inMemoryData.original.size;
    if (file.inMemoryData.variants) {
      for (const variant of file.inMemoryData.variants.values()) {
        size += variant.size;
      }
    }
    this.memoryUsage -= size;
    this.fileCount--;

    // Clear in-memory data
    delete file.inMemoryData;
  }

  /**
   * Get current memory usage statistics
   */
  getMemoryUsage(): MemoryUsageStats {
    let largestFile = 0;
    let oldestFile = Date.now();

    for (const entry of this.blobUrls.values()) {
      if (entry.size > largestFile) {
        largestFile = entry.size;
      }
      if (entry.createdAt < oldestFile) {
        oldestFile = entry.createdAt;
      }
    }

    return {
      totalSize: this.memoryUsage,
      fileCount: this.fileCount,
      blobUrlCount: this.blobUrls.size,
      largestFile,
      oldestFile,
    };
  }

  /**
   * Force cleanup of old or excess blob URLs
   */
  forceCleanup(): void {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    // Clean up old URLs
    for (const [cacheKey, entry] of this.blobUrls.entries()) {
      if (now - entry.createdAt > maxAge) {
        URL.revokeObjectURL(entry.url);
        this.blobUrls.delete(cacheKey);

        // Update file mapping
        const fileUrls = this.fileToUrls.get(entry.fileId);
        if (fileUrls) {
          fileUrls.delete(cacheKey);
          if (fileUrls.size === 0) {
            this.fileToUrls.delete(entry.fileId);
          }
        }
      }
    }

    this.lastCleanup = now;
  }

  /**
   * Clean up all resources (call on app shutdown)
   */
  destroy(): void {
    // Stop cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Revoke all blob URLs
    for (const entry of this.blobUrls.values()) {
      URL.revokeObjectURL(entry.url);
    }

    // Clear all maps
    this.blobUrls.clear();
    this.fileToUrls.clear();

    // Reset counters
    this.memoryUsage = 0;
    this.fileCount = 0;
  }

  // Private helper methods

  private getCacheKey(fileId: string, variant: string): string {
    return `${fileId}_${variant}`;
  }

  private isUrlValid(entry: BlobUrlCacheEntry): boolean {
    // First check timestamp - URLs are valid for 30 minutes
    const maxAge = 30 * 60 * 1000;
    const age = Date.now() - entry.createdAt;
    if (age >= maxAge) {
      console.log(
        '🔍 [InMemoryFileManager] URL expired by timestamp:',
        entry.url,
        'age:',
        Math.round(age / 1000),
        'seconds'
      );
      return false;
    }

    // For blob URLs, we can't easily test if they're still valid without making a request
    // But we can check if the URL format is still correct
    if (!entry.url.startsWith('blob:')) {
      console.log('🔍 [InMemoryFileManager] Invalid URL format:', entry.url);
      return false;
    }

    // URL appears valid (we'll rely on error handling if it's actually revoked)
    console.log(
      '🔍 [InMemoryFileManager] URL is valid:',
      entry.url,
      'age:',
      Math.round(age / 1000),
      'seconds'
    );
    return true;
  }

  private enforceUrlLimits(): void {
    if (this.blobUrls.size <= IN_MEMORY_FILE_CONFIG.maxBlobUrls) {
      return;
    }

    console.log('🧹 [InMemoryFileManager] Enforcing URL limits:', {
      currentUrls: this.blobUrls.size,
      maxUrls: IN_MEMORY_FILE_CONFIG.maxBlobUrls,
      activeFiles: Array.from(this.activeFiles),
    });

    // Separate URLs into active and inactive files
    const activeEntries: Array<[string, BlobUrlCacheEntry]> = [];
    const inactiveEntries: Array<[string, BlobUrlCacheEntry]> = [];

    for (const [cacheKey, entry] of this.blobUrls.entries()) {
      if (this.activeFiles.has(entry.fileId)) {
        activeEntries.push([cacheKey, entry]);
      } else {
        inactiveEntries.push([cacheKey, entry]);
      }
    }

    // Sort inactive entries by age (oldest first)
    inactiveEntries.sort(([, a], [, b]) => a.createdAt - b.createdAt);

    // Only remove URLs from inactive files first
    const targetRemovalCount =
      this.blobUrls.size - IN_MEMORY_FILE_CONFIG.maxBlobUrls;
    const inactiveRemovalCount = Math.min(
      targetRemovalCount,
      inactiveEntries.length
    );

    console.log('🧹 [InMemoryFileManager] URL cleanup strategy:', {
      totalUrls: this.blobUrls.size,
      activeUrls: activeEntries.length,
      inactiveUrls: inactiveEntries.length,
      targetRemoval: targetRemovalCount,
      inactiveRemoval: inactiveRemovalCount,
    });

    // Remove URLs from inactive files
    for (let i = 0; i < inactiveRemovalCount; i++) {
      const [cacheKey, entry] = inactiveEntries[i];
      console.log(
        '🗑️ [InMemoryFileManager] Removing URL for inactive file:',
        entry.fileId,
        entry.url
      );
      URL.revokeObjectURL(entry.url);
      this.blobUrls.delete(cacheKey);

      // Update file mapping
      const fileUrls = this.fileToUrls.get(entry.fileId);
      if (fileUrls) {
        fileUrls.delete(cacheKey);
        if (fileUrls.size === 0) {
          this.fileToUrls.delete(entry.fileId);
        }
      }
    }

    // If we still need to remove more URLs and only active files remain,
    // remove oldest active URLs but log a warning
    const remainingRemoval = targetRemovalCount - inactiveRemovalCount;
    if (remainingRemoval > 0) {
      console.warn(
        '⚠️ [InMemoryFileManager] Removing URLs from active files - this may cause display issues:',
        remainingRemoval
      );

      // Sort active entries by age and remove oldest
      activeEntries.sort(([, a], [, b]) => a.createdAt - b.createdAt);

      for (
        let i = 0;
        i < Math.min(remainingRemoval, activeEntries.length);
        i++
      ) {
        const [cacheKey, entry] = activeEntries[i];
        console.log(
          '🗑️ [InMemoryFileManager] Removing URL for active file (forced):',
          entry.fileId,
          entry.url
        );
        URL.revokeObjectURL(entry.url);
        this.blobUrls.delete(cacheKey);

        // Update file mapping
        const fileUrls = this.fileToUrls.get(entry.fileId);
        if (fileUrls) {
          fileUrls.delete(cacheKey);
          if (fileUrls.size === 0) {
            this.fileToUrls.delete(entry.fileId);
          }
        }
      }
    }
  }

  private checkMemoryLimits(): void {
    if (this.memoryUsage > IN_MEMORY_FILE_CONFIG.memoryWarningThreshold) {
      console.warn(
        `In-memory file storage approaching limit: ${(this.memoryUsage / 1024 / 1024).toFixed(1)}MB / ${(IN_MEMORY_FILE_CONFIG.maxMemoryUsage / 1024 / 1024).toFixed(1)}MB`
      );
    }

    if (this.fileCount > IN_MEMORY_FILE_CONFIG.fileCountWarningThreshold) {
      console.warn(
        `High number of in-memory files: ${this.fileCount} / ${IN_MEMORY_FILE_CONFIG.maxConcurrentFiles}`
      );
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.forceCleanup();
    }, IN_MEMORY_FILE_CONFIG.cleanupInterval);
  }

  private setupMemoryWarnings(): void {
    // Monitor memory usage and warn if approaching limits
    if (
      typeof window !== 'undefined' &&
      'performance' in window &&
      'memory' in (window.performance as any)
    ) {
      setInterval(() => {
        const memory = (window.performance as any).memory;
        if (memory && memory.usedJSHeapSize > 100 * 1024 * 1024) {
          // 100MB
          console.warn('High JavaScript memory usage detected:', {
            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB`,
            total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(1)}MB`,
            inMemoryFiles: this.getMemoryUsage(),
          });
        }
      }, 60000); // Check every minute
    }
  }
}

// Export singleton instance
export const inMemoryFileManager = InMemoryFileManager.getInstance();
