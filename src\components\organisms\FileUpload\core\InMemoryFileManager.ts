/**
 * In-Memory File Manager
 * Production-ready blob storage manager for client-side file handling
 */

import {
  IN_MEMORY_FILE_CONFIG,
  InMemoryFileData,
  UploadedFile,
} from '../types/fileUpload';

/**
 * Memory usage statistics
 */
export interface MemoryUsageStats {
  totalSize: number; // Total memory usage in bytes
  fileCount: number; // Number of files in memory
  blobUrlCount: number; // Number of active blob URLs
}

/**
 * Blob URL cache entry
 */
interface BlobUrlCacheEntry {
  url: string;
  fileId: string;
  variant: string;
  createdAt: number;
  size: number;
}

/**
 * In-Memory File Manager
 * Singleton class for managing in-memory files and blob URLs
 */
export class InMemoryFileManager {
  private static instance: InMemoryFileManager;

  // Memory tracking
  private memoryUsage = 0;
  private fileCount = 0;

  // Blob URL management
  private blobUrls = new Map<string, BlobUrlCacheEntry>();
  private fileToUrls = new Map<string, Set<string>>(); // fileId -> Set of cache keys

  // Active file protection - prevents cleanup of URLs for files currently in use
  private activeFiles = new Set<string>();

  // Pending URL generation (prevents race conditions)
  private pendingUrls = new Map<string, Promise<string>>();

  // Cleanup management
  private cleanupInterval: NodeJS.Timeout | null = null;
  private destroyed = false;

  private constructor() {
    this.startCleanupInterval();
  }

  static getInstance(): InMemoryFileManager {
    if (!InMemoryFileManager.instance) {
      InMemoryFileManager.instance = new InMemoryFileManager();
    }
    return InMemoryFileManager.instance;
  }

  /**
   * Get blob URL for a file variant
   */
  async getUrl(file: UploadedFile, variant?: string): Promise<string> {
    if (!file.inMemoryData) {
      throw new Error(
        `Temporary file ${file.f}.${file.e} missing in-memory data. This indicates a system error.`
      );
    }

    const cacheKey = this.getCacheKey(file.f, variant || 'original');

    // Check if we have a cached URL that's still valid
    const cachedEntry = this.blobUrls.get(cacheKey);
    if (cachedEntry && this.isUrlValid(cachedEntry)) {
      return cachedEntry.url;
    }

    // Check if URL generation already in progress (prevents race condition)
    const pending = this.pendingUrls.get(cacheKey);
    if (pending) {
      return pending;
    }

    // Create new promise for this cache key
    const promise = (async () => {
      try {
        // Clean up old URL if it exists but is invalid
        if (cachedEntry) {
          URL.revokeObjectURL(cachedEntry.url);
          this.blobUrls.delete(cacheKey);

          // Update file mapping
          const fileUrls = this.fileToUrls.get(file.f);
          if (fileUrls) {
            fileUrls.delete(cacheKey);
          }
        }

        // Get the appropriate blob
        let blob: Blob;
        if (!variant || variant === 'original') {
          blob = file.inMemoryData!.original;
        } else if (file.inMemoryData!.variants?.has(variant)) {
          blob = file.inMemoryData!.variants.get(variant)!;
        } else {
          throw new Error(
            `Variant '${variant}' not found for file ${file.f}.${file.e}. Available variants: ${Array.from(file.inMemoryData!.variants?.keys() || []).join(', ')}`
          );
        }

        // Create new blob URL
        const blobUrl = URL.createObjectURL(blob);

        // Cache the URL
        const entry: BlobUrlCacheEntry = {
          url: blobUrl,
          fileId: file.f,
          variant: variant || 'original',
          createdAt: Date.now(),
          size: blob.size,
        };

        this.blobUrls.set(cacheKey, entry);

        // Track file -> URLs mapping
        if (!this.fileToUrls.has(file.f)) {
          this.fileToUrls.set(file.f, new Set());
        }
        this.fileToUrls.get(file.f)!.add(cacheKey);

        // Enforce URL limits
        this.enforceUrlLimits();

        return blobUrl;
      } finally {
        this.pendingUrls.delete(cacheKey);
      }
    })();

    this.pendingUrls.set(cacheKey, promise);
    return promise;
  }

  /**
   * Mark a file as actively being used (protects its URLs from cleanup)
   */
  markFileAsActive(fileId: string): void {
    this.activeFiles.add(fileId);
  }

  /**
   * Unmark a file as active (allows its URLs to be cleaned up)
   */
  unmarkFileAsActive(fileId: string): void {
    this.activeFiles.delete(fileId);
  }

  /**
   * Mark multiple files as active (e.g., current file list)
   */
  markFilesAsActive(fileIds: string[]): void {
    fileIds.forEach(fileId => this.markFileAsActive(fileId));
  }

  /**
   * Clear all active file markings
   */
  clearActiveFiles(): void {
    this.activeFiles.clear();
  }

  /**
   * Handle blob URL errors by regenerating the URL
   */
  async handleBlobError(file: UploadedFile, variant?: string): Promise<string> {
    const cacheKey = this.getCacheKey(file.f, variant || 'original');

    // Remove the invalid cached URL
    const cached = this.blobUrls.get(cacheKey);
    if (cached) {
      try {
        URL.revokeObjectURL(cached.url);
      } catch (error) {
        // Ignore revoke errors for already-revoked URLs
      }
      this.blobUrls.delete(cacheKey);
    }

    // Regenerate the URL
    return this.getUrl(file, variant);
  }

  /**
   * Track memory usage when a file is added
   */
  trackFile(file: UploadedFile): void {
    if (!file.inMemoryData) return;

    let size = file.inMemoryData.original.size;
    if (file.inMemoryData.variants) {
      for (const variant of file.inMemoryData.variants.values()) {
        size += variant.size;
      }
    }

    this.memoryUsage += size;
    this.fileCount++;
  }

  /**
   * Clean up all resources for a file
   */
  cleanupFile(file: UploadedFile): void {
    if (!file.inMemoryData) return;

    // Revoke all blob URLs for this file
    const fileUrls = this.fileToUrls.get(file.f);
    if (fileUrls) {
      for (const cacheKey of fileUrls) {
        const entry = this.blobUrls.get(cacheKey);
        if (entry) {
          try {
            URL.revokeObjectURL(entry.url);
          } catch (error) {
            // Ignore revoke errors for already-revoked URLs
          }
          this.blobUrls.delete(cacheKey);
        }
      }
      this.fileToUrls.delete(file.f);
    }

    // Update memory usage
    let size = file.inMemoryData.original.size;
    if (file.inMemoryData.variants) {
      for (const variant of file.inMemoryData.variants.values()) {
        size += variant.size;
      }
    }
    this.memoryUsage -= size;
    this.fileCount--;

    // Explicitly clear blob references to help GC
    if (file.inMemoryData.variants) {
      file.inMemoryData.variants.clear();
      file.inMemoryData.variants = undefined;
    }
    file.inMemoryData.original = undefined as any;

    // Clear in-memory data
    delete file.inMemoryData;
  }

  /**
   * Get current memory usage statistics
   */
  getMemoryUsage(): MemoryUsageStats {
    return {
      totalSize: this.memoryUsage,
      fileCount: this.fileCount,
      blobUrlCount: this.blobUrls.size,
    };
  }

  /**
   * Force cleanup of old or excess blob URLs
   */
  forceCleanup(): void {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    // Clean up old URLs
    for (const [cacheKey, entry] of this.blobUrls.entries()) {
      if (now - entry.createdAt > maxAge) {
        try {
          URL.revokeObjectURL(entry.url);
        } catch (error) {
          // Ignore revoke errors
        }
        this.blobUrls.delete(cacheKey);

        // Update file mapping
        const fileUrls = this.fileToUrls.get(entry.fileId);
        if (fileUrls) {
          fileUrls.delete(cacheKey);
          if (fileUrls.size === 0) {
            this.fileToUrls.delete(entry.fileId);
          }
        }
      }
    }
  }

  /**
   * Clean up all resources (call on app shutdown)
   */
  destroy(): void {
    if (this.destroyed) return;
    this.destroyed = true;

    // Stop cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Revoke all blob URLs
    for (const entry of this.blobUrls.values()) {
      try {
        URL.revokeObjectURL(entry.url);
      } catch (error) {
        // Ignore revoke errors for already-revoked URLs
      }
    }

    // Clear all maps
    this.blobUrls.clear();
    this.fileToUrls.clear();
    this.pendingUrls.clear();
    this.activeFiles.clear();

    // Reset counters
    this.memoryUsage = 0;
    this.fileCount = 0;
  }

  // Private helper methods

  private getCacheKey(fileId: string, variant: string): string {
    return `${fileId}_${variant}`;
  }

  private isUrlValid(entry: BlobUrlCacheEntry): boolean {
    const maxAge = 30 * 60 * 1000; // 30 minutes
    return Date.now() - entry.createdAt < maxAge;
  }

  private enforceUrlLimits(): void {
    if (this.blobUrls.size <= IN_MEMORY_FILE_CONFIG.maxBlobUrls) {
      return;
    }

    // Separate URLs into active and inactive files
    const activeEntries: Array<[string, BlobUrlCacheEntry]> = [];
    const inactiveEntries: Array<[string, BlobUrlCacheEntry]> = [];

    for (const [cacheKey, entry] of this.blobUrls.entries()) {
      if (this.activeFiles.has(entry.fileId)) {
        activeEntries.push([cacheKey, entry]);
      } else {
        inactiveEntries.push([cacheKey, entry]);
      }
    }

    // Sort inactive entries by age (oldest first)
    inactiveEntries.sort(([, a], [, b]) => a.createdAt - b.createdAt);

    // Only remove URLs from inactive files first
    const targetRemovalCount =
      this.blobUrls.size - IN_MEMORY_FILE_CONFIG.maxBlobUrls;
    const inactiveRemovalCount = Math.min(
      targetRemovalCount,
      inactiveEntries.length
    );

    // Remove URLs from inactive files
    for (let i = 0; i < inactiveRemovalCount; i++) {
      const [cacheKey, entry] = inactiveEntries[i];
      try {
        URL.revokeObjectURL(entry.url);
      } catch (error) {
        // Ignore revoke errors
      }
      this.blobUrls.delete(cacheKey);

      // Update file mapping
      const fileUrls = this.fileToUrls.get(entry.fileId);
      if (fileUrls) {
        fileUrls.delete(cacheKey);
        if (fileUrls.size === 0) {
          this.fileToUrls.delete(entry.fileId);
        }
      }
    }

    // If we still need to remove more URLs and only active files remain,
    // remove oldest active URLs but log a warning
    const remainingRemoval = targetRemovalCount - inactiveRemovalCount;
    if (remainingRemoval > 0) {
      console.warn(
        `[InMemoryFileManager] Removing ${remainingRemoval} URLs from active files - this may cause display issues`
      );

      // Sort active entries by age and remove oldest
      activeEntries.sort(([, a], [, b]) => a.createdAt - b.createdAt);

      for (
        let i = 0;
        i < Math.min(remainingRemoval, activeEntries.length);
        i++
      ) {
        const [cacheKey, entry] = activeEntries[i];
        try {
          URL.revokeObjectURL(entry.url);
        } catch (error) {
          // Ignore revoke errors
        }
        this.blobUrls.delete(cacheKey);

        // Update file mapping
        const fileUrls = this.fileToUrls.get(entry.fileId);
        if (fileUrls) {
          fileUrls.delete(cacheKey);
          if (fileUrls.size === 0) {
            this.fileToUrls.delete(entry.fileId);
          }
        }
      }
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.forceCleanup();
    }, IN_MEMORY_FILE_CONFIG.cleanupInterval);
  }
}

// Export singleton instance
export const inMemoryFileManager = InMemoryFileManager.getInstance();
