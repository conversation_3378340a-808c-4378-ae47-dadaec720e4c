/**
 * Sanitized Input Components
 *
 * This module provides pre-wrapped versions of common input components
 * with automatic text sanitization to prevent issues from copy-pasted
 * content containing invisible characters.
 *
 * ## Usage
 *
 * Simply import the sanitized version of the component you need:
 *
 * ### MUI Components
 * ```tsx
 * import { SanitizedTextField, SanitizedAutocomplete } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedTextField
 *   label="Name"
 *   value={name}
 *   onChange={(e) => setName(e.target.value)}
 * />
 * ```
 *
 * ### React-Admin Components
 * ```tsx
 * import { SanitizedTextInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedTextInput source="name" label="Name" />
 * </SimpleForm>
 * ```
 *
 * ### Custom Components
 * ```tsx
 * import { SanitizedCustomInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SimpleForm>
 *   <SanitizedCustomInput source="name" label="Name" type="text" />
 * </SimpleForm>
 * ```
 *
 * ## What Gets Sanitized?
 *
 * - ✅ Leading and trailing whitespace (trimmed)
 * - ✅ Zero-width spaces (U+200B, U+200C, U+200D)
 * - ✅ Byte order marks (U+FEFF)
 * - ✅ Non-breaking spaces (converted to regular spaces)
 * - ✅ Control characters
 * - ✅ Multiple consecutive spaces (normalized to single space)
 *
 * ## Special Variants
 *
 * ### Multiline Text
 * Preserves intentional line breaks while sanitizing each line:
 * ```tsx
 * import { SanitizedTextFieldMultiline } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedTextFieldMultiline
 *   label="Description"
 *   multiline
 *   rows={4}
 * />
 * ```
 *
 * ### Identifiers (SKU, Codes)
 * Automatically converts to uppercase:
 * ```tsx
 * import { SanitizedTextFieldIdentifier } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedTextFieldIdentifier
 *   label="SKU"
 *   value={sku}
 * />
 * ```
 *
 * @module sanitized
 */

// Re-export all MUI components
export * from './mui';

// Re-export all React-Admin components
export * from './react-admin';

// Re-export all Custom components
export * from './custom';

// Re-export utilities for advanced usage
export { withSanitization, createSanitizedComponent } from '~/utils/withSanitization';
export {
    sanitizeTextInput,
    SINGLE_LINE_PRESET,
    MULTI_LINE_PRESET,
    IDENTIFIER_PRESET,
    NO_SANITIZATION,
} from '~/utils/sanitizeTextInput';
export type { SanitizationOptions } from '~/utils/sanitizeTextInput';
export type { WithSanitizationConfig } from '~/utils/withSanitization';
