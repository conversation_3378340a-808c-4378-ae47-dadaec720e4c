import { httpsCallable } from 'firebase/functions';
import { ResourceCallbacks } from 'react-admin';

import { auth, functions } from '~/configs/firebaseConfig';
import { RESOURCES, resourcesInfo } from '../resources';

const fnIncrementAndGetResourceVersion = httpsCallable(
  functions,
  'callables-incrementAndGetResourceVersion'
);

/**
 * Get member ID and name from params.meta or current auth user
 */
const getMemberInfo = (meta?: {
  [key: string]: any;
}): { memberId: string; memberName: string } => {
  if (meta?.memberId && meta?.memberName) {
    return {
      memberId: meta.memberId,
      memberName: meta.memberName,
    };
  }

  // Fallback to current user
  const user = auth.currentUser;
  if (!user) {
    throw new Error('No authenticated user and no member info in params.meta');
  }

  return {
    memberId: user.uid,
    memberName: user.displayName || 'Unknown User',
  };
};

/**
 * Call cloud function to increment and get resource version
 */
const incrementResourceVersion = async (
  accountId: string,
  resource: string,
  memberId: string,
  memberName: string
): Promise<number> => {
  const result = await fnIncrementAndGetResourceVersion({
    accountId,
    resource,
    memberId,
    memberName,
  });

  const data = result.data as { value: number };
  return data.value;
};

/**
 * Create version increment handler for a specific resource
 */
const createVersionIncrementHandler = (resource: string): ResourceCallbacks => {
  return {
    resource,

    beforeCreate: async (params, dataProvider) => {
      console.log(`Incrementing version for ${resource} beforeCreate`);

      const { memberId, memberName } = getMemberInfo(params.meta);
      const accountId = dataProvider.getAccountId();

      const version = await incrementResourceVersion(
        accountId,
        resource,
        memberId,
        memberName
      );

      // Update params.data with the new version
      return {
        ...params,
        data: {
          ...params.data,
          _v: version,
        },
      };
    },

    beforeUpdate: async (params, dataProvider) => {
      console.log(`Incrementing version for ${resource} beforeUpdate`);

      const { memberId, memberName } = getMemberInfo(params.meta);
      const accountId = dataProvider.getAccountId();

      const version = await incrementResourceVersion(
        accountId,
        resource,
        memberId,
        memberName
      );

      // Update params.data with the new version
      return {
        ...params,
        data: {
          ...params.data,
          _v: version,
        },
      };
    },

    beforeDelete: async (params, dataProvider) => {
      console.log(`Incrementing version for ${resource} beforeDelete`);

      const { memberId, memberName } = getMemberInfo(params.meta);
      const accountId = dataProvider.getAccountId();

      const version = await incrementResourceVersion(
        accountId,
        resource,
        memberId,
        memberName
      );

      // For soft delete, we need to add _v to meta so it can be used during the update
      // The hybrid data provider will handle adding this to the actual data during soft delete
      return {
        ...params,
        meta: {
          ...params.meta,
          _v: version,
        },
      };
    },
  };
};

/**
 * Create version increment handlers for all collection resources with versioning enabled
 */
export const createGenericVersionIncrementHandlers =
  (): ResourceCallbacks[] => {
    const handlers: ResourceCallbacks[] = [];

    Object.entries(resourcesInfo).forEach(([resourceKey, resourceInfo]) => {
      // Only apply to collection resources with live listeners and versioning enabled
      if (
        resourceInfo.dataProvider === 'hybrid' &&
        resourceInfo.type === 'collection' &&
        resourceInfo.hasLiveListener === true &&
        resourceInfo.useVersioning === true
      ) {
        handlers.push(createVersionIncrementHandler(resourceKey));
      }
    });

    return handlers;
  };
