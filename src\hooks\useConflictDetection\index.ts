/**
 * Resource-Agnostic Conflict Detection System
 *
 * A unified system for detecting and resolving concurrent edit conflicts
 * in react-admin forms with complex nested data structures.
 *
 * @example Basic usage with ConflictAwareSimpleForm
 * ```tsx
 * import { ConflictAwareSimpleForm, ConflictAwareInput } from '@/components/conflict-detection';
 *
 * function MyResourceEdit() {
 *   return (
 *     <EditDialog>
 *       <ConflictAwareSimpleForm translationNamespace="myResource">
 *         <ConflictAwareInput>
 *           <TextInput source="name" />
 *         </ConflictAwareInput>
 *       </ConflictAwareSimpleForm>
 *     </EditDialog>
 *   );
 * }
 * ```
 *
 * @example Using hooks directly
 * ```tsx
 * import { useTrackedField, useEntityViewport } from '@/hooks/useConflictDetection';
 *
 * function MyField({ path }: { path: string }) {
 *   const { value, hasConflict, wasUpdatedRemotely, setValue } = useTrackedField(path);
 *   // ...
 * }
 *
 * function ItemModal({ entityId }: { entityId: string }) {
 *   const { path, isDeleted, hasConflicts } = useEntityViewport(entityId);
 *   // ...
 * }
 * ```
 */

// Types
export type {
  ConflictDetectionConfig,
  EntityOccurrence,
  EntityRecord,
  NoIdArrayRecord,
  LocalChange,
  RemoteChange,
  FieldConflict,
  NoIdArrayConflict,
  EntityDeletedConflict,
  Conflict,
  TrackerState,
  ViewportCallbacks,
  ProcessUpdateResult,
  ConflictDetectionContextValue,
} from './types';

// Context and Provider
export {
  ConflictDetectionContext,
  ConflictDetectionProvider,
  useConflictDetection,
  useConflictDetectionOptional,
} from './ConflictDetectionContext';

// Consumer Hooks
export {
  useTrackedField,
  useEntityViewport,
  useEntityConflicts,
  useEntityWasUpdated,
  useFieldPath,
  useOptionalTrackedField,
} from './hooks';

// Utility Functions
export {
  buildEntityRegistry,
  findEntityById,
  findAllEntityPaths,
  computeArraySignature,
  extractEntityOwnFields,
} from './entityRegistry';

export { parsePathToEntity, buildFullPath } from './pathUtils';

export {
  processRemoteUpdate,
  resolveConflict,
  hasLocalChangesForEntity,
  hasLocalChangesForNoIdArray,
  getLocalChangesForEntity,
} from './conflictResolver';
