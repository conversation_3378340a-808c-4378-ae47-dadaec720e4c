import CryptoJS from 'crypto-js';
import localforage from 'localforage';

import { ResourcesInfo, resourcesInfo } from '../resources';

interface StoredValue {
  data: string;
  timestamp: number | null;
}

export const createEncryptedInstance = (
  accountId: string,
  options: LocalForageOptions
): LocalForage => {
  if (!accountId) {
    throw new Error('Account ID is required for encrypted localforage.');
  }

  // Create the base instance first
  const baseInstance = localforage.createInstance(options);

  function getEncryptionKey(
    resource: string,
    timestamp: number | null
  ): string {
    const version =
      resourcesInfo[resource as keyof ResourcesInfo]?.version ?? 1;
    return `${accountId}-${resource}-v${version}-t${timestamp ?? ''}`;
  }

  function encrypt(
    value: unknown,
    resource: string,
    timestamp: number | null
  ): string {
    const key = getEncryptionKey(resource, timestamp);
    return CryptoJS.AES.encrypt(JSON.stringify(value), key).toString();
  }

  function decrypt(
    encrypted: any,
    resource: string,
    timestamp: number | null
  ): unknown {
    if (typeof encrypted !== 'string') {
      throw new Error('Invalid encrypted data format');
    }
    const key = getEncryptionKey(resource, timestamp);
    const bytes = CryptoJS.AES.decrypt(encrypted, key);
    const decrypted = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decrypted);
  }

  function getMaxTimestamp(value: unknown): number | null {
    if (!Array.isArray(value)) {
      return null;
    }

    let maxTimestamp: number | null = null;
    for (const item of value) {
      if (item && typeof item === 'object' && '_u' in item) {
        const itemTimestamp = (item as { _u: number | null })._u;
        if (typeof itemTimestamp === 'number') {
          maxTimestamp = Math.max(maxTimestamp ?? 0, itemTimestamp);
        }
      }
    }
    return maxTimestamp;
  }

  // Create a proxy that wraps the base instance
  const encryptedInstance = new Proxy(baseInstance, {
    get(target, prop) {
      if (prop === 'setItem') {
        return async function <T>(
          key: string,
          value: T,
          callback?: (err: any, value: T) => void
        ): Promise<T> {
          try {
            const timestamp = getMaxTimestamp(value);
            const storedValue: StoredValue = {
              data: encrypt(value, key, timestamp),
              timestamp,
            };
            await target.setItem(key, storedValue);
            callback?.(null, value);
            return value;
          } catch (err) {
            callback?.(err, value);
            throw err;
          }
        };
      }

      if (prop === 'getItem') {
        return async function <T>(
          key: string,
          callback?: (err: any, value: T | null) => void
        ): Promise<T | null> {
          try {
            const storedValue = await target.getItem<StoredValue>(key);
            if (!storedValue) {
              callback?.(null, null);
              return null;
            }

            try {
              const value = decrypt(
                storedValue.data,
                key,
                storedValue.timestamp
              ) as T;
              callback?.(null, value);
              return value;
            } catch (err) {
              // If decryption fails for any reason, remove the item and return null
              console.warn(`Decryption failed for ${key}, removing item:`, err);
              await target.removeItem(key);
              callback?.(null, null);
              return null;
            }
          } catch (err) {
            callback?.(err, null);
            throw err;
          }
        };
      }

      if (prop === 'getLastUpdateTimestamp') {
        return async function (key: string): Promise<number | null> {
          const storedValue = await target.getItem<StoredValue>(key);
          if (!storedValue) {
            return null;
          }

          try {
            // Attempt to decrypt the data to verify integrity
            decrypt(storedValue.data, key, storedValue.timestamp);
            return storedValue.timestamp;
          } catch (err) {
            // If decryption fails, remove corrupted data and return null
            console.warn(`Decryption failed for ${key}, removing item:`, err);
            await target.removeItem(key);
            return null;
          }
        };
      }

      if (prop === 'getMaxVersion') {
        return async function (key: string): Promise<number | null> {
          const storedValue = await target.getItem<StoredValue>(key);
          if (!storedValue) {
            return null;
          }

          try {
            // Decrypt the data to get the array of records
            // decrypt() already returns parsed JSON, not a string
            const records = decrypt(
              storedValue.data,
              key,
              storedValue.timestamp
            );

            if (!Array.isArray(records) || records.length === 0) {
              return null;
            }

            // Find the maximum _v value in the cached records
            const maxVersion = records.reduce((max, record) => {
              if (typeof record._v === 'number' && record._v > max) {
                return record._v;
              }
              return max;
            }, 0);

            return maxVersion > 0 ? maxVersion : null;
          } catch (err) {
            // If decryption or parsing fails, remove corrupted data and return null
            console.warn(
              `Error getting max version for ${key}, removing item:`,
              err
            );
            await target.removeItem(key);
            return null;
          }
        };
      }

      // For all other methods, return the original method bound to the target
      const value = target[prop as keyof LocalForage];
      return typeof value === 'function' ? value.bind(target) : value;
    },
  });

  return encryptedInstance;
};
