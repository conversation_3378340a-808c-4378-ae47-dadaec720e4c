import React, { useCallback, useMemo } from 'react';
import { useInput, useNotify } from 'react-admin';

import FileUploadComponent from './FileUploadComponent';
import { RaFileUploadComponentProps } from './types';
import { UploadedFile } from './types/fileUpload';
import { mergeWithDefaults } from './utils/fileUploadConfig';

import type { FileUploadConfig } from './types';

/**
 * React-Admin wrapper for FileUploadComponent
 *
 * Accepts both flat props (like standard React-Admin inputs) and config object.
 * Flat props provide simple API, config object provides advanced control.
 * Config object takes precedence over flat props when both are provided.
 */
const RaFileUploadComponent: React.FC<RaFileUploadComponentProps> = ({
  source,
  config: providedConfig,
  label,
  helperText,
  infoText,
  validate,
  required,

  // Flat props for simple usage
  disabled,
  readOnly,
  placeholder,
  fileType = 'images',
  multiple = false,
  maxFiles,
  maxSize,
  acceptedTypes,
  variant,
  imageConfig,
  validationConfig,
  callbacks,
  privateContext,

  ...props
}) => {
  const notify = useNotify();

  const {
    field: { onChange, value },
    fieldState: { error, invalid },
    formState: { isSubmitting },
  } = useInput({
    source,
    validate,
    ...props,
  });

  // Get current files from form value (not record)
  const currentFiles: UploadedFile[] = Array.isArray(value)
    ? value
    : value
      ? [value]
      : [];

  // Build config from flat props
  const configFromProps: Partial<FileUploadConfig> = useMemo(() => {
    // Only include properties that were explicitly provided (not undefined)
    const config: Partial<FileUploadConfig> = {
      fileType,
      multiple,
    };

    if (maxFiles !== undefined) config.maxFiles = maxFiles;
    if (maxSize !== undefined) config.maxSize = maxSize;
    if (acceptedTypes !== undefined) config.acceptedTypes = acceptedTypes;
    if (imageConfig !== undefined) config.imageConfig = imageConfig;
    if (validationConfig !== undefined) config.validation = validationConfig;
    if (callbacks !== undefined) config.callbacks = callbacks;
    if (privateContext !== undefined) config.privateContext = privateContext;

    // Build UI config from flat props
    const uiConfig: any = {};
    if (variant !== undefined) uiConfig.variant = variant;
    if (disabled !== undefined) uiConfig.disabled = disabled;
    if (readOnly !== undefined) uiConfig.readOnly = readOnly;
    if (placeholder !== undefined) uiConfig.placeholder = placeholder;

    if (Object.keys(uiConfig).length > 0) {
      config.ui = uiConfig;
    }

    return config;
  }, [
    fileType,
    multiple,
    maxFiles,
    maxSize,
    acceptedTypes,
    variant,
    imageConfig,
    validationConfig,
    callbacks,
    privateContext,
    disabled,
    readOnly,
    placeholder,
  ]);

  // Merge configs: providedConfig takes precedence over flat props
  const mergedConfig = useMemo(() => {
    const merged: Partial<FileUploadConfig> = {
      ...configFromProps,
      ...providedConfig,
      ui: {
        ...configFromProps.ui,
        ...providedConfig?.ui,
      },
    };
    return merged;
  }, [configFromProps, providedConfig]);

  // Merge with defaults and get final config
  const config = useMemo(() => mergeWithDefaults(mergedConfig), [mergedConfig]);

  // Handle files change
  const handleFilesChange = useCallback(
    (files: UploadedFile[]) => {
      console.log('🔄 [RaFileUploadComponent] Files changed:', {
        source,
        newFilesCount: files.length,
        files: files.map(f => ({
          f: f.f,
          e: f.e,
          t: f.t,
          x: f.x,
          hasUrl: !!f.url,
        })),
      });
      onChange(files);
    },
    [onChange, source]
  );

  // Handle file uploaded callback
  const handleFileUploaded = useCallback(
    (file: UploadedFile) => {
      // The main FileUploadComponent handles adding files to the list
      // We just need to call the original callback if provided
      config.callbacks?.onFileUploaded?.(file);
    },
    [config.callbacks]
  );

  // Handle upload error
  const handleUploadError = useCallback(
    (uploadError: any) => {
      let errorMessage = 'Failed to upload file. ';

      if (uploadError?.message) {
        errorMessage += uploadError.message;
      } else {
        errorMessage +=
          'Please try again or contact support if the problem persists.';
      }

      notify(errorMessage, { type: 'error' });
    },
    [notify]
  );

  // Handle validation error
  const handleValidationError = useCallback(
    (errors: any[]) => {
      const message =
        errors.length === 1
          ? errors[0].message
          : `${errors.length} validation errors occurred`;
      notify(message, { type: 'warning' });
    },
    [notify]
  );

  // Handle upload success
  const handleUploadSuccess = useCallback(
    (files: UploadedFile[], context?: { isImageEdit?: boolean }) => {
      const count = files.length;
      let message: string;

      if (context?.isImageEdit) {
        message =
          count === 1
            ? 'File edited successfully'
            : `${count} files edited successfully`;
      } else {
        message =
          count === 1
            ? 'File uploaded successfully'
            : `${count} files uploaded successfully`;
      }

      notify(message, { type: 'success' });
    },
    [notify]
  );

  // Enhanced config with callbacks
  const enhancedConfig = React.useMemo(
    () => ({
      ...config,
      callbacks: {
        ...config.callbacks,
        onFileUploaded: handleFileUploaded,
        onUploadError: handleUploadError,
        onValidationError: handleValidationError,
        onUploadSuccess: handleUploadSuccess,
      },
      ui: {
        ...config.ui,
        disabled: isSubmitting || config.ui?.disabled,
      },
    }),
    [
      config,
      handleFileUploaded,
      handleUploadError,
      handleValidationError,
      handleUploadSuccess,
      isSubmitting,
    ]
  );

  return (
    <FileUploadComponent
      value={currentFiles}
      onChange={handleFilesChange}
      config={enhancedConfig}
      label={label}
      helperText={helperText}
      error={invalid}
      errorText={error?.message}
      sx={props.sx}
    />
  );
};

export default RaFileUploadComponent;
