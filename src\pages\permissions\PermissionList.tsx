import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  CreateButton,
  Datagrid,
  FunctionField,
  List,
  TextField,
  TopToolbar,
  WrapperField,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { useTheme } from '~/contexts';
import { RESOURCES } from '~/providers/resources';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { resourcesInfo } from '../../providers/resources';
import { PermissionPages, permissionsTree } from './components/constants';

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ padding: '48px 0', textAlign: 'center' }}>
      <img src="/assets/menu-icons/role.svg" width="45px" />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('permissions.noPermissionSetsYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('permissions.noPermissionSetsYetDescription')}
      </Typography>
      <CreateButton
        variant="contained"
        label={t('permissions.createPermissionSet')}
      />
    </div>
  );
};

const PostListActions = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { t } = useTranslation('');
  return (
    <TopToolbar>
      <CreateButton
        variant="contained"
        label={t('permissions.createPermissionSet')}
        {...(isXSmall ? {} : { icon: <></> })}
      />
    </TopToolbar>
  );
};

export default function PermissionList() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const filters = [
    <CustomSearchInput
      placeholder={t('permissions.filterPermissions')}
      key="search-input"
      source="q"
      alwaysOn
    />,
  ];

  return (
    <List
      empty={<CustomEmpty />}
      component="div"
      resource={RESOURCES.PERMISSIONS}
      sort={resourcesInfo[RESOURCES.PERMISSIONS].defaultSort}
      filters={filters}
      pagination={false}
      perPage={Number.MAX_SAFE_INTEGER}
      actions={<PostListActions />}
      sx={{
        '& .RaDatagrid-tableWrapper': {
          marginTop: '10px',
        },
      }}
    >
      {isXSmall ? (
        <MobileGrid>
          <MobileCard actions={true} cardClick="edit">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('permissions.access')}
              </Typography>
              <FunctionField
                render={record => (
                  <AccessField permissions={record.permissions} />
                )}
              />
            </Box>
          </MobileCard>
        </MobileGrid>
      ) : (
        <Datagrid
          rowClick="edit"
          sx={{
            '& .RaDatagrid-tableWrapper': {
              marginTop: '10px',
            },
            '& .RaDatagrid-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
          }}
          bulkActionButtons={false}
        >
          <TextField source="name" label={t('shared.name')} />
          <FunctionField
            label={t('permissions.access')}
            textAlign="right"
            render={record => <AccessField permissions={record.permissions} />}
          />
          <WrapperField label={t('prepStations.actions')} textAlign="right">
            <ActionsField
              textAlign="right"
              hasEdit={true}
              hasDelete={true}
              deleteMutationMode="pessimistic"
            />
          </WrapperField>
        </Datagrid>
      )}
      <ListLiveUpdate />
    </List>
  );
}

const AccessField = ({ permissions }: { permissions: number[] }) => {
  if (
    Array.isArray(permissions) &&
    permissions.length === 1 &&
    permissions[0] === 1
  ) {
    return <Typography variant="body2">Full Access</Typography>;
  }

  const pagesWithActivePermissions: PermissionPages[] = [];
  Object.keys(permissionsTree).forEach(pageKey => {
    const pageGroups = permissionsTree[pageKey as PermissionPages];
    const hasActivePermissions = pageGroups.some(group =>
      group.possiblePermissions.some(perm => permissions?.includes(perm))
    );

    if (hasActivePermissions) {
      pagesWithActivePermissions.push(pageKey as PermissionPages);
    }
  });

  return (
    <Typography variant="body2">
      {pagesWithActivePermissions.length
        ? pagesWithActivePermissions
            .map(item => item.charAt(0).toUpperCase() + item.slice(1))
            .join(', ')
        : '-'}
    </Typography>
  );
};
