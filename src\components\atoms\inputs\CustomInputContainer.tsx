import { ReactNode } from 'react';
import { Box, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';

type RoundedCorners = 'top' | 'bottom' | 'both' | 'none';

interface CustomInputContainerProps {
  title: string;
  children: ReactNode;
  roundedCorners?: RoundedCorners;
  sx?: any;
}

/**
 * CustomInputContainer - Container for inputs with a title header
 *
 * Looks like CustomTranslatableInputs but with a title instead of tabs.
 * Supports optional rounded corners via roundedCorners prop.
 *
 * @example
 * <CustomInputContainer title="Details" roundedCorners="both">
 *   <CustomInput type="text" source="name" ui="custom" />
 * </CustomInputContainer>
 */
export const CustomInputContainer = ({
  title,
  children,
  roundedCorners,
  sx,
}: CustomInputContainerProps) => {
  const theme = useTheme();
  const custom = (theme.palette as any).custom;

  // Calculate border radius based on roundedCorners prop
  const getHeaderBorderRadius = () => {
    if (!roundedCorners) return {};
    const radius = '6px';

    switch (roundedCorners) {
      case 'top':
      case 'both':
        return {
          borderTopLeftRadius: radius,
          borderTopRightRadius: radius,
        };
      default:
        return {};
    }
  };

  const getContainerBorderRadius = () => {
    if (!roundedCorners) return {};
    const radius = '6px';

    switch (roundedCorners) {
      case 'top':
        return {
          borderTopLeftRadius: radius,
          borderTopRightRadius: radius,
        };
      case 'bottom':
        return {
          borderBottomLeftRadius: radius,
          borderBottomRightRadius: radius,
        };
      case 'both':
        return {
          borderRadius: radius,
        };
      case 'none':
      default:
        return {};
    }
  };

  return (
    <Box
      sx={{
        // Changed from overflow: 'hidden' to allow conflict badges to be visible
        // Rounded corners are still clipped via border-radius on child elements
        overflow: 'visible',
        ...getContainerBorderRadius(),
        ...sx,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          minHeight: '45px',
          display: 'flex',
          alignItems: 'center',
          px: 2,
          backgroundColor: custom.modalHeader,
          border: `1px solid ${custom.gray400}`,
          borderBottom: 'none',
          ...getHeaderBorderRadius(),
        }}
      >
        <Typography
          variant="body2"
          sx={{
            fontWeight: 500,
            color: theme.palette.text.primary,
          }}
        >
          {title}
        </Typography>
      </Box>

      {/* Content */}
      <Box
        sx={{
          '& > div:first-of-type': {
            marginTop: '-1px',
          },
        }}
      >
        {children}
      </Box>
    </Box>
  );
};
