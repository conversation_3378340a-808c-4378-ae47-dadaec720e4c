import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// Import cache clearing functions for proper cleanup
import { clearFilePreviewCacheForFile } from '../components/FilePreviewInline';
import { FileListState } from '../types';
import { UploadedFile } from '../types/fileUpload';
import { clearFileMetadataCache } from '../utils/fileMetadataResolver';
import { isValidUploadedFile } from '../utils/fileUploadHelpers';
import { clearFileUrlCache } from './useFileUrl';

/**
 * Hook for managing file list state and operations
 */
export const useFileList = (
  initialFiles: UploadedFile[] = [],
  onChange?: (files: UploadedFile[]) => void
) => {
  const [state, setState] = useState<FileListState>({
    files: initialFiles.filter(isValidUploadedFile),
    draggedIndex: null,
    hoveredIndex: null,
  });

  const isInitialRender = useRef(true);
  const onChangeRef = useRef(onChange);

  // Update the ref when onChange changes
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Update files when external value changes
  const updateFiles = useCallback(
    (newFiles: UploadedFile[]) => {
      console.log('📝 [useFileList] updateFiles called:', {
        newCount: newFiles.length,
        newFiles: newFiles.map(
          (f, i) => `${i}: ${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
        ),
      });

      const validFiles = newFiles.filter(isValidUploadedFile);
      setState(prev => {
        // Only update if files actually changed to prevent unnecessary re-renders
        const currentFilesKey = prev.files
          .map(f => `${f.f}.${f.e}-${f.x ? 'temp' : 'perm'}`)
          .join(',');
        const newFilesKey = validFiles
          .map(f => `${f.f}.${f.e}-${f.x ? 'temp' : 'perm'}`)
          .join(',');

        if (currentFilesKey === newFilesKey) {
          console.log('📝 [useFileList] Files unchanged, skipping update');
          return prev;
        }

        console.log('📝 [useFileList] Files changed, updating state:', {
          from: currentFilesKey,
          to: newFilesKey,
        });

        // Clear caches for files that are being removed
        const newFileIds = new Set(validFiles.map(f => `${f.f}.${f.e}`));

        prev.files.forEach(file => {
          const fileId = `${file.f}.${file.e}`;
          if (!newFileIds.has(fileId)) {
            console.log(
              '🧹 [useFileList] Clearing caches for removed file:',
              fileId
            );
            // Clear all caches for this file to prevent stale data
            clearFileUrlCache(file);
            clearFilePreviewCacheForFile(file);
            clearFileMetadataCache(file);
          }
        });

        return { ...prev, files: validFiles };
      });
    },
    [] // Remove state.files dependency to avoid stale closures
  );

  // Call onChange when files change (but not on initial render)
  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }
    onChangeRef.current?.(state.files);
  }, [state.files]);

  // Sync with external value changes (critical for react-admin form integration)
  useEffect(() => {
    console.log('📝 [useFileList] External initialFiles changed, syncing:', {
      initialFilesCount: initialFiles.length,
      currentFilesCount: state.files.length,
      initialFiles: initialFiles.map(
        (f, i) => `${i}: ${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
      ),
    });

    // Only update if we're not in the initial render phase
    if (!isInitialRender.current) {
      updateFiles(initialFiles);
    }
  }, [initialFiles, updateFiles]);

  // Add files to the list
  const addFiles = useCallback((newFiles: UploadedFile[]) => {
    const validFiles = newFiles.filter(isValidUploadedFile);
    setState(prev => {
      const updatedFiles = [...prev.files, ...validFiles];
      return { ...prev, files: updatedFiles };
    });
  }, []);

  // Add a single file
  const addFile = useCallback(
    (file: UploadedFile) => {
      if (isValidUploadedFile(file)) {
        addFiles([file]);
      }
    },
    [addFiles]
  );

  // Remove file by index
  const removeFile = useCallback((index: number) => {
    setState(prev => {
      const fileToRemove = prev.files[index];
      const updatedFiles = prev.files.filter((_, i) => i !== index);

      // Clear caches for the removed file
      if (fileToRemove) {
        console.log(
          '🧹 [useFileList] Clearing caches for removed file (by index):',
          `${fileToRemove.f}.${fileToRemove.e}`
        );
        clearFileUrlCache(fileToRemove);
        clearFilePreviewCacheForFile(fileToRemove);
        clearFileMetadataCache(fileToRemove);
      }

      return { ...prev, files: updatedFiles };
    });
  }, []);

  // Remove file by filename
  const removeFileByName = useCallback((filename: string) => {
    setState(prev => {
      const filesToRemove = prev.files.filter(file => file.f === filename);
      const updatedFiles = prev.files.filter(file => file.f !== filename);

      // Clear caches for removed files
      filesToRemove.forEach(fileToRemove => {
        console.log(
          '🧹 [useFileList] Clearing caches for removed file (by name):',
          `${fileToRemove.f}.${fileToRemove.e}`
        );
        clearFileUrlCache(fileToRemove);
        clearFilePreviewCacheForFile(fileToRemove);
        clearFileMetadataCache(fileToRemove);
      });

      return { ...prev, files: updatedFiles };
    });
  }, []);

  // Remove file by unique identifier (more robust than index-based removal)
  const removeFileByIdentifier = useCallback((file: UploadedFile) => {
    setState(prev => {
      const updatedFiles = prev.files.filter(f => {
        // Use multiple properties to ensure unique identification
        const sameBasic = f.f === file.f && f.e === file.e && f.t === file.t;
        const sameState = Boolean(f.x) === Boolean(file.x);

        // For temporary files, also check URL if available
        if (file.x && file.url && f.url) {
          return !(sameBasic && sameState && f.url === file.url);
        }

        return !(sameBasic && sameState);
      });

      // Clear caches for the removed file
      console.log(
        '🧹 [useFileList] Clearing caches for removed file (by identifier):',
        `${file.f}.${file.e}`
      );
      clearFileUrlCache(file);
      clearFilePreviewCacheForFile(file);
      clearFileMetadataCache(file);

      return { ...prev, files: updatedFiles };
    });
  }, []);

  // Replace file at index
  const replaceFile = useCallback((index: number, newFile: UploadedFile) => {
    if (!isValidUploadedFile(newFile)) return;

    setState(prev => {
      const updatedFiles = [...prev.files];
      updatedFiles[index] = newFile;
      return { ...prev, files: updatedFiles };
    });
  }, []);

  // Reorder files (for drag and drop)
  const reorderFiles = useCallback(
    (fromIndex: number, toIndex: number) => {
      setState(prev => {
        console.log('🔄 [useFileList] Reordering files:', {
          fromIndex,
          toIndex,
          filesBefore: prev.files.map(
            (f, i) => `${i}: ${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
          ),
        });

        const updatedFiles = [...prev.files];
        const [movedFile] = updatedFiles.splice(fromIndex, 1);
        updatedFiles.splice(toIndex, 0, movedFile);

        console.log('🔄 [useFileList] Files after reorder:', {
          filesAfter: updatedFiles.map(
            (f, i) => `${i}: ${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
          ),
        });

        return { ...prev, files: updatedFiles };
      });
    },
    [] // Remove state.files dependency to avoid stale closures
  );

  // Clear all files
  const clearFiles = useCallback(() => {
    setState(prev => {
      return { ...prev, files: [] };
    });
  }, []);

  // Set dragged index for drag and drop
  const setDraggedIndex = useCallback((index: number | null) => {
    setState(prev => ({ ...prev, draggedIndex: index }));
  }, []);

  // Set hovered index for drag and drop
  const setHoveredIndex = useCallback((index: number | null) => {
    setState(prev => ({ ...prev, hoveredIndex: index }));
  }, []);

  // Get file by index
  const getFile = useCallback(
    (index: number): UploadedFile | undefined => {
      return state.files[index];
    },
    [state.files]
  );

  // Get file by filename
  const getFileByName = useCallback(
    (filename: string): UploadedFile | undefined => {
      return state.files.find(file => file.f === filename);
    },
    [state.files]
  );

  // Check if file exists
  const hasFile = useCallback(
    (filename: string): boolean => {
      return state.files.some(file => file.f === filename);
    },
    [state.files]
  );

  // Get files by type
  const getFilesByType = useCallback(
    (type: 'i' | 'v' | 's' | 'p'): UploadedFile[] => {
      return state.files.filter(file => file.t === type);
    },
    [state.files]
  );

  // Get temporary files
  const getTempFiles = useCallback((): UploadedFile[] => {
    return state.files.filter(file => file.x === true);
  }, [state.files]);

  // Get permanent files
  const getPermanentFiles = useCallback((): UploadedFile[] => {
    return state.files.filter(file => !file.x);
  }, [state.files]);

  // Computed values
  const fileCount = useMemo(() => state.files.length, [state.files]);
  const isEmpty = useMemo(() => fileCount === 0, [fileCount]);
  const hasFiles = useMemo(() => fileCount > 0, [fileCount]);
  const tempFileCount = useMemo(() => getTempFiles().length, [getTempFiles]);
  const permanentFileCount = useMemo(
    () => getPermanentFiles().length,
    [getPermanentFiles]
  );

  // Get total size of all files (estimated for uploaded files)
  const totalSize = useMemo(() => {
    // Note: UploadedFile doesn't have size property, so this is an estimation
    // In a real implementation, you might want to fetch sizes asynchronously
    return state.files.length * 1024 * 1024; // Rough estimate
  }, [state.files]);

  return {
    // State
    files: state.files,
    draggedIndex: state.draggedIndex,
    hoveredIndex: state.hoveredIndex,

    // Actions
    updateFiles,
    addFiles,
    addFile,
    removeFile,
    removeFileByName,
    removeFileByIdentifier,
    replaceFile,
    reorderFiles,
    clearFiles,
    setDraggedIndex,
    setHoveredIndex,

    // Getters
    getFile,
    getFileByName,
    hasFile,
    getFilesByType,
    getTempFiles,
    getPermanentFiles,

    // Computed values
    fileCount,
    isEmpty,
    hasFiles,
    tempFileCount,
    permanentFileCount,
    totalSize,
  };
};
