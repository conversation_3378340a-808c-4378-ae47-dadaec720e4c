/**
 * GranularChangeTracker
 *
 * Tracks changes at specific paths in nested objects to enable granular conflict detection.
 * This allows detecting conflicts only when the same property path is modified by multiple users,
 * rather than treating any change to the document as a conflict.
 */

import isEqual from 'lodash/isEqual';
import get from 'lodash/get';
import set from 'lodash/set';
import cloneDeep from 'lodash/cloneDeep';

export interface TrackedChange {
    path: string;
    originalValue: any;
    currentValue: any;
    timestamp: number;
}

export interface ConflictInfo {
    path: string;
    myValue: any;
    theirValue: any;
    originalValue: any;
}

export class GranularChangeTracker {
    private trackedChanges: Map<string, TrackedChange> = new Map();
    private initialSnapshot: any = null;

    /**
     * Initialize tracking with the initial state
     */
    initialize(initialData: any): void {
        this.initialSnapshot = cloneDeep(initialData);
        this.trackedChanges.clear();
    }

    /**
     * Track a change at a specific path
     */
    trackChange(path: string, newValue: any): void {
        if (!this.initialSnapshot) {
            console.warn('[GranularChangeTracker] Cannot track change, not initialized');
            return;
        }

        const originalValue = get(this.initialSnapshot, path);

        // If the value hasn't actually changed from original, remove tracking
        if (isEqual(originalValue, newValue)) {
            console.log('[GranularChangeTracker] Value unchanged, removing tracking for:', path);
            this.trackedChanges.delete(path);
            return;
        }

        console.log('[GranularChangeTracker] Tracking change at path:', path, {
            originalValue,
            newValue,
            totalTracked: this.trackedChanges.size + 1
        });

        // Track or update the change
        this.trackedChanges.set(path, {
            path,
            originalValue,
            currentValue: newValue,
            timestamp: Date.now(),
        });
    }

    /**
     * Get all tracked changes
     */
    getTrackedChanges(): TrackedChange[] {
        return Array.from(this.trackedChanges.values());
    }

    /**
     * Check if there are any tracked changes
     */
    hasChanges(): boolean {
        return this.trackedChanges.size > 0;
    }

    /**
     * Clear all tracked changes
     */
    clearChanges(): void {
        this.trackedChanges.clear();
    }

    /**
     * Detect conflicts between local changes and remote changes
     * Returns array of paths where conflicts exist
     */
    detectConflicts(remoteData: any): ConflictInfo[] {
        if (!this.initialSnapshot) {
            return [];
        }

        const conflicts: ConflictInfo[] = [];

        // Check each tracked change for conflicts
        for (const [path, change] of this.trackedChanges) {
            const remoteValue = get(remoteData, path);
            const originalValue = change.originalValue;
            const myValue = change.currentValue;

            // Conflict exists if:
            // 1. Remote value differs from original value (they changed it)
            // 2. My value differs from remote value (we have different changes)
            const remoteChanged = !isEqual(remoteValue, originalValue);
            const valuesConflict = !isEqual(myValue, remoteValue);

            if (remoteChanged && valuesConflict) {
                conflicts.push({
                    path,
                    myValue,
                    theirValue: remoteValue,
                    originalValue,
                });
            }
        }

        return conflicts;
    }

    /**
     * Merge remote changes with local changes, preserving non-conflicting local changes
     * Returns merged data and list of conflicts
     */
    mergeWithRemote(remoteData: any): {
        mergedData: any;
        conflicts: ConflictInfo[];
    } {
        if (!this.initialSnapshot) {
            return { mergedData: remoteData, conflicts: [] };
        }

        const conflicts = this.detectConflicts(remoteData);
        const mergedData = cloneDeep(remoteData);

        // Apply non-conflicting local changes to merged data
        for (const [path, change] of this.trackedChanges) {
            const hasConflict = conflicts.some(c => c.path === path);

            if (!hasConflict) {
                // No conflict, apply local change
                set(mergedData, path, change.currentValue);
            }
        }

        return { mergedData, conflicts };
    }

    /**
     * Update initial snapshot after merging changes
     * This should be called after successfully saving or after resolving conflicts
     */
    updateSnapshot(newData: any): void {
        this.initialSnapshot = cloneDeep(newData);
        this.trackedChanges.clear();
    }

    /**
     * Update the snapshot while preserving tracked changes
     * Used after auto-merge to update the baseline without losing track of user edits
     */
    updateSnapshotKeepingChanges(newSnapshot: any, currentData: any): void {
        this.initialSnapshot = cloneDeep(newSnapshot);

        // Update each tracked change's originalValue to the new snapshot value
        // but keep currentValue pointing to what the user has
        const updatedChanges = new Map<string, TrackedChange>();

        for (const [path, change] of this.trackedChanges) {
            const newOriginalValue = get(newSnapshot, path);
            const userCurrentValue = get(currentData, path);

            // Only keep tracking if the user's value differs from the new snapshot
            if (!isEqual(userCurrentValue, newOriginalValue)) {
                updatedChanges.set(path, {
                    path,
                    originalValue: newOriginalValue,
                    currentValue: userCurrentValue,
                    timestamp: change.timestamp,
                });
            }
        }

        this.trackedChanges = updatedChanges;
    }

    /**
     * Apply conflict resolution: keep my changes for specified paths
     */
    keepMyChanges(conflictPaths: string[], currentData: any): any {
        const resolvedData = cloneDeep(currentData);

        for (const path of conflictPaths) {
            const change = this.trackedChanges.get(path);
            if (change) {
                set(resolvedData, path, change.currentValue);
            }
        }

        return resolvedData;
    }

    /**
     * Apply conflict resolution: accept their changes for specified paths
     */
    acceptTheirChanges(conflictPaths: string[], remoteData: any): any {
        // Remove tracked changes for resolved paths
        for (const path of conflictPaths) {
            this.trackedChanges.delete(path);
        }

        return remoteData;
    }

    /**
     * Helper to check if a specific path or any parent path has changes
     */
    hasChangesInPath(checkPath: string): boolean {
        for (const [path] of this.trackedChanges) {
            // Check if paths match or if one is a parent of the other
            if (path === checkPath || path.startsWith(checkPath + '.') || checkPath.startsWith(path + '.')) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get a summary of what paths have been changed (for debugging/logging)
     */
    getChangeSummary(): string[] {
        return Array.from(this.trackedChanges.keys());
    }
}

/**
 * Helper function to normalize paths for nested form structures
 * Converts array indices and object keys to consistent format
 */
export function normalizePath(path: string): string {
    return path.replace(/\[(\d+)\]/g, '.$1');
}

/**
 * Helper to get all nested paths from an object
 */
export function getAllPaths(obj: any, prefix = ''): string[] {
    const paths: string[] = [];

    if (obj === null || obj === undefined || typeof obj !== 'object') {
        return prefix ? [prefix] : [];
    }

    if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
            const currentPath = prefix ? `${prefix}[${index}]` : `[${index}]`;
            paths.push(...getAllPaths(item, currentPath));
        });
    } else {
        Object.keys(obj).forEach(key => {
            const currentPath = prefix ? `${prefix}.${key}` : key;
            paths.push(currentPath);
            paths.push(...getAllPaths(obj[key], currentPath));
        });
    }

    return paths;
}
