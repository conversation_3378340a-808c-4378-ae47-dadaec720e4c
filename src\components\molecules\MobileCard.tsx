import { MoreHorizRounded } from '@mui/icons-material';
import { Box, Card, CardContent, CardHeader, IconButton } from '@mui/material';
import {
  TextField,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';

import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { FilePreviewInline } from '~/components/organisms/FileUpload';
import { useTheme } from '~/contexts';

const MobileCard = ({
  children,
  actions = true,
  hasImage = true,
  imageUrl,
  titleSource = 'name',
  cardClick = '',
  onClickFunction,
  titleRender,
}: {
  children: React.ReactNode;
  actions?: boolean;
  imageUrl?: any;
  hasImage?: boolean;
  cardClick?: string;
  titleSource?: string;
  onClickFunction?: () => void;
  titleRender?: (record: any) => React.ReactNode;
}) => {
  const { theme } = useTheme();
  const record = useRecordContext();
  const redirect = useRedirect();
  const resource = useResourceContext();
  console.log(record);

  const renderTitle = () => {
    if (titleRender && record) {
      return titleRender(record);
    }
    return <TextField source={titleSource} variant="body1" />;
  };

  return (
    <Card
      sx={{
        border: `1px solid ${theme.palette.mode === 'light' ? '#E0E0E0' : '#2b2b2b'}`,
        borderRadius: '6px',
        p: 0,
        marginBottom: '16px',
        bgcolor: theme.palette.mode === 'light' ? 'white' : '#13131A',
        boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.2)',
      }}
      onClick={() => {
        if (cardClick) {
          redirect(cardClick, resource, record!.id, record, {
            _scrollToTop: false,
          });
        }
      }}
    >
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            {hasImage && (
              <FilePreviewInline
                files={record?.images || []}
                size={35}
                borderRadius={1.5}
                imageVariant="thumbnail"
                fileType="images"
              />
            )}
            {renderTitle()}
          </Box>
        }
        action={
          actions ? (
            <ActionsField hasEdit hasDelete deleteMutationMode="pessimistic" />
          ) : (
            <IconButton sx={{ color: '#0064F0' }}>
              <MoreHorizRounded />
            </IconButton>
          )
        }
      />
      <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {children}
      </CardContent>
    </Card>
  );
};

export default MobileCard;
