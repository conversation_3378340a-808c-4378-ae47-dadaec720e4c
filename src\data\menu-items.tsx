import MoveUpIcon from '@mui/icons-material/MoveUp';

import { RESOURCES } from '~/providers/resources';
import { MenuItemI } from '../components/molecules/DropdownMenuItem';

const menuConfig: MenuItemI[] = [
  {
    label: 'itemsAndOrders',
    items: [
      {
        label: 'menus',
        href: `/${RESOURCES.HOSPITALITY_CATALOGS}`,
        icon: <img src="/assets/menu-icons/menus.svg" alt="menu-icon-menus" />,
        searchable: true,
      },
      {
        label: 'items',
        href: `/${RESOURCES.HOSPITALITY_ITEMS}`,
        icon: <img src="/assets/menu-icons/items.svg" alt="menu-icon-items" />,
        items: [
          {
            label: 'itemLibrary',
            href: `/${RESOURCES.HOSPITALITY_ITEMS}`,
            searchable: true,
          },
          {
            label: 'categories',
            href: `/${RESOURCES.HOSPITALITY_CATEGORIES}`,
            searchable: true,
          },
          {
            label: 'menus',
            href: `/${RESOURCES.HOSPITALITY_CATALOGS}`,
          },
          {
            label: 'prepStations',
            href: `/${RESOURCES.PREP_STATIONS}`,
            searchable: true,
          },
          {
            label: 'modifiers',
            href: '/modifiers',
            searchable: true,
          },
          {
            label: 'units',
            href: `/${RESOURCES.MEASURE_UNITS}`,
            searchable: true,
          },
          {
            label: 'extraCharges',
            href: `/${RESOURCES.EXTRA_CHARGES}`,
            searchable: true,
          },
        ],
      },
      {
        label: 'orders',
        href: '/orders',
        searchable: true,
        icon: (
          <img src="/assets/menu-icons/orders.svg" alt="menu-icon-orders" />
        ),
        items: [
          {
            label: 'overview',
            href: '/orders-overview',
          },
          {
            label: 'orderPartners',
            href: '/orders-partners',
          },
        ],
      },
    ],
  },
  {
    label: 'payments',
    items: [
      {
        label: 'invoices',
        href: '/invoices',
        searchable: true,
        icon: (
          <img src="/assets/menu-icons/invoice.svg" alt="menu-icon-invoice" />
        ),
        items: [
          {
            label: 'overview',
            href: '/invoices-overview',
          },
          {
            label: 'invoices',
            href: '/invoices',
          },
          {
            label: 'settings',
            href: '/invoices-settings',
          },
        ],
      },
      {
        label: 'virtualTerminal',
        href: '/virtual-terminal',
        searchable: true,
        icon: (
          <img src="/assets/menu-icons/virtual.svg" alt="menu-icon-virtual" />
        ),
      },
      {
        label: 'giftCards',
        href: '/gift-cards-overview',
        searchable: true,
        icon: <img src="/assets/menu-icons/gift.svg" alt="menu-icon-gift" />,
        items: [
          {
            label: 'overview',
            href: '/gift-cards-overview',
          },
          {
            label: 'settings',
            href: '/gift-cards-settings',
          },
        ],
      },
    ],
  },
  {
    label: 'reporting',
    searchable: true,
    items: [
      {
        label: 'reports',
        searchable: true,
        icon: (
          <img src="/assets/menu-icons/reports.svg" alt="menu-icon-reports" />
        ),
        href: '/report-sales-revenue',
        items: [
          {
            label: 'salesRevenue',
            href: '/report-sales-revenue',
            searchable: true,
          },
          {
            label: 'paymentMethods',
            href: '/report-payment-methods',
            searchable: true,
          },
          {
            label: 'categorySales',
            href: '/report-category-sales',
            searchable: true,
          },
          {
            label: 'itemSales',
            href: '/report-item-sales',
            searchable: true,
          },
          {
            label: 'modifierSales',
            href: '/report-modifier-sales',
            searchable: true,
          },
          {
            label: 'giftCards',
            href: '/report-gift-cards',
            searchable: true,
          },
          {
            label: 'extraCharges',
            href: '/report-extra-charges',
            searchable: true,
          },
          {
            label: 'discounts',
            href: '/report-discounts',
            searchable: true,
          },
          {
            label: 'coupons',
            href: '/report-coupons',
            searchable: true,
          },
          {
            label: 'promotions',
            href: '/report-promotions',
            searchable: true,
          },
          {
            label: 'tips',
            href: '/report-tips',
            searchable: true,
          },
          {
            label: 'teamRevenue',
            href: '/report-team-revenue',
            searchable: true,
          },
          {
            label: 'comps',
            href: '/report-comps',
            searchable: true,
          },
          {
            label: 'voids',
            href: '/report-voids',
            searchable: true,
          },
          {
            label: 'vat',
            href: '/report-vat',
            searchable: true,
          },
          // {
          //   label: 'kitchenPerformance',
          //   href: '/kitchen-performance',
          // },
        ],
      },
      {
        label: 'transactions',
        icon: (
          <img
            src="/assets/menu-icons/transactions.svg"
            alt="menu-icon-transactions"
          />
        ),
        href: '/transactions',
        searchable: true,
      },
    ],
  },
  {
    label: 'loyalty',
    items: [
      {
        label: 'customers',
        href: '/coming-soon',
        icon: (
          <img
            src="/assets/menu-icons/customers.svg"
            alt="menu-icon-customers"
          />
        ),
        searchable: true,
        items: [
          {
            label: 'directory',
            href: '/directory',
          },
          {
            label: 'feedback',
            href: '/feedback',
          },
          {
            label: 'insights',
            href: '/insights',
          },
        ],
      },
      // {
      //   label: 'giftCards',
      //   href: '/gift-cards-overview',
      //   icon: <img src='/assets/menu-icons/gift.svg' alt='menu-icon-gift' />,
      //   items: [
      //     {
      //       label: 'overview',
      //       href: '/gift-cards-overview',
      //     },
      //     {
      //       label: 'settings',
      //       href: '/gift-cards-settings',
      //     },
      //   ],
      // },
      // {
      //   label: 'houseAccounts',
      //   href: '/house-accounts',
      //   icon: <img src='/assets/menu-icons/house.svg' alt='menu-icon-house' />,
      // },
      {
        label: 'loyalty',
        icon: <img src="/assets/menu-icons/loyalty.svg" alt="menu-icon-home" />,
        href: '/loyalty',
        searchable: true,
        // items: [
        //   {
        //     label: 'overview',
        //     href: '/overview',
        //   },
        //   {
        //     label: 'reports',
        //     items: [
        //       {
        //         label: 'visits',
        //         href: '/visits',
        //       },
        //       {
        //         label: 'sales',
        //         href: '/sales',
        //       },
        //       {
        //         label: 'topCustomers',
        //         href: '/top-customers',
        //       },
        //     ],
        //   },
        //   {
        //     label: 'activity',
        //     items: [
        //       {
        //         label: 'allActivity',
        //         href: '/all-activity',
        //       },
        //       {
        //         label: 'suspiciousActivity',
        //         href: '/suspicious-activity',
        //       },
        //     ],
        //   },
        //   {
        //     label: 'marketing',
        //     href: '/marketing',
        //   },
        //   {
        //     label: 'loyaltyPromotions',
        //     href: '/loyalty-promotions',
        //   },
        //   {
        //     label: 'settings',
        //     href: '/settings',
        //   },
        // ],
      },
      // {
      //   label: 'marketing',
      //   href: '/marketing',
      //   icon: (
      //     <img
      //       src='/assets/menu-icons/marketing.svg'
      //       alt='menu-icon-marketing'
      //     />
      //   ),
      // },
    ],
  },
  {
    label: 'staff',
    items: [
      // {
      //   label: 'shifts',
      //   href: '/shifts',
      //   icon: (
      //     <img src='/assets/menu-icons/shifts.svg' alt='menu-icon-shifts' />
      //   ),
      // },
      {
        label: 'permissions',
        href: `/${RESOURCES.PERMISSIONS}`,
        searchable: true,
        icon: <img src="/assets/menu-icons/role.svg" alt="menu-icon-role" />,
        items: [
          {
            label: 'permissions',
            href: '/permissions',
          },
          {
            label: 'teamMembers',
            href: `/${RESOURCES.TEAM_MEMBERS}`,
          },
          {
            label: 'teamMembersWorkingArea',
            href: `/${RESOURCES.TEAM_MEMBERS_WORKING_AREA}`,
          },
        ],
      },
      {
        label: 'teamMembers',
        href: `/${RESOURCES.TEAM_MEMBERS}`,
        searchable: true,
        icon: <img src="/assets/menu-icons/team.svg" alt="menu-icon-team" />,
        items: [
          {
            label: 'permissions',
            href: '/permissions',
          },
          {
            label: 'teamMembers',
            href: `/${RESOURCES.TEAM_MEMBERS}`,
          },
          {
            label: 'teamMembersWorkingArea',
            href: `/${RESOURCES.TEAM_MEMBERS_WORKING_AREA}`,
          },
        ],
      },
      {
        label: 'teamMembersWorkingArea',
        href: `/${RESOURCES.TEAM_MEMBERS_WORKING_AREA}`,
        searchable: true,
        icon: (
          <img
            src="/assets/icons/working-area-assignment.svg"
            alt="menu-icon-team"
          />
        ),
        items: [
          {
            label: 'permissions',
            href: '/permissions',
          },
          {
            label: 'teamMembers',
            href: `/${RESOURCES.TEAM_MEMBERS}`,
          },
          {
            label: 'teamMembersWorkingArea',
            href: `/${RESOURCES.TEAM_MEMBERS_WORKING_AREA}`,
          },
        ],
      },
    ],
  },
  {
    label: 'settings',
    items: [
      {
        label: 'accountAndSettings',
        href: '/sign-in-security',
        searchable: true,
        icon: (
          <img src="/assets/menu-icons/account.svg" alt="menu-icon-account" />
        ),
        items: [
          {
            label: 'personalInformation',
            href: '/sign-in-security',
            searchable: true,
            items: [
              {
                label: 'signInAndSecurity',
                href: '/sign-in-security',
                searchable: true,
              },
              {
                label: 'preferences',
                href: '/preferences',
                searchable: true,
              },
            ],
          },
          {
            label: 'businessInformation',
            items: [
              {
                label: 'aboutMyBusiness',
                href: '/about-my-business',
                searchable: true,
              },
              {
                label: 'emailNotifications',
                href: '/email-notifications',
                searchable: true,
              },
              {
                label: 'outageNotifications',
                href: '/outage-notifications',
                searchable: true,
              },
              {
                label: 'sellpoints',
                href: `/${RESOURCES.LOCATIONS}`,
                searchable: true,
              },
              {
                label: 'pricingAndSubscriptions',
                href: '/pricing-subscriptions',
                searchable: true,
              },
              {
                label: 'bankAccounts',
                href: '/bank-accounts',
                searchable: true,
              },
              {
                label: 'salesTaxes',
                href: '/sales-taxes',
                searchable: true,
              },
            ],
          },
          {
            label: 'selioHardware',
            href: 'https://selio.io/shop-main/',
            externalLink: true,
            searchable: true,
          },
        ],
      },
      {
        label: 'devicesManagement',
        href: `/${RESOURCES.DEVICES}`,
        searchable: true,
        icon: (
          <img src="/assets/menu-icons/device.svg" alt="menu-icon-device" />
        ),
        items: [
          {
            label: 'pointOfSales',
            items: [
              {
                label: 'posDevices',
                href: `/${RESOURCES.DEVICES}`,
                searchable: false,
              },
              {
                label: 'floorPlans',
                href: `/${RESOURCES.FLOOR_PLANS}`,
                searchable: true,
              },
            ],
          },
          {
            label: 'serviceSettings',
            items: [
              {
                label: 'menuBehaviour',
                href: '/menu-behaviour',
                searchable: true,
              },
              {
                label: 'courses',
                href: '/courses',
                searchable: true,
              },
              {
                label: 'tips',
                href: `/${RESOURCES.TIPS}`,
                searchable: true,
              },
              {
                label: 'discounts',
                href: `/${RESOURCES.DISCOUNTS}`,
                searchable: true,
              },
              {
                label: 'compAndVoid',
                href: '/comp-and-void',
                searchable: true,
              },
            ],
          },
          {
            label: 'kitchenDisplays',
            href: '/kitchen-displays',
            searchable: true,
          },
        ],
      },
      {
        label: 'appIntegrations',
        href: '/partner-integrations',
        icon: (
          <img
            src="/assets/menu-icons/partner-integrations.svg"
            alt="menu-icon-partner-integrations"
            style={{ width: '24px', height: '24px' }}
          />
        ),
        searchable: true,
      },
    ],
  },
];

export default menuConfig;
