import { useEffect, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import { TextField } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useDebounce } from '~/hooks';

const TableInput = ({
  onChange,
  searchPlaceHolder,
  searchMaxWidth,
}: {
  onChange: (text: string) => void;
  searchMaxWidth?: number | string;
  searchPlaceHolder?: string;
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  useEffect(() => {
    onChange(debouncedSearchQuery);
  }, [debouncedSearchQuery, onChange]);

  return (
    <TextField
      variant="outlined"
      id="outlined-size-small"
      size="small"
      placeholder={
        searchPlaceHolder
          ? searchPlaceHolder
          : t('dashboard.headerSearchPlaceholder')
      }
      value={searchQuery}
      onChange={e => setSearchQuery(e.target.value)}
      className="do-not-print"
      slotProps={{
        input: {
          startAdornment: <SearchIcon sx={{ mx: 1, color: 'gray' }} />,
        },
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          padding: '2px!important',
        },
        backgroundColor: 'background.paper',
        borderRadius: 2,
        maxWidth: searchMaxWidth || 250,
      }}
    />
  );
};

export default TableInput;
