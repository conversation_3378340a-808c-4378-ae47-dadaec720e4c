/**
 * Sanitized MUI Components
 *
 * Pre-wrapped Material-UI components with automatic text sanitization.
 * These components remove invisible characters and normalize whitespace
 * from user input to prevent issues with copy-pasted content.
 *
 * @module sanitized/mui
 */

import {
    Autocomplete,
    TextField,
    OutlinedInput,
    Input,
    FilledInput,
} from '@mui/material';
import {
    withSanitization,
    createSanitizedComponent,
} from '~/utils/withSanitization';
import { MULTI_LINE_PRESET, IDENTIFIER_PRESET } from '~/utils/sanitizeTextInput';

/**
 * Sanitized TextField component
 *
 * @example
 * ```tsx
 * import { SanitizedTextField } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedTextField
 *   label="Display Name"
 *   value={name}
 *   onChange={(e) => setName(e.target.value)}
 * />
 * ```
 */
export const SanitizedTextField = createSanitizedComponent.singleLine(TextField);

/**
 * Sanitized TextField for multiline text (textarea)
 *
 * @example
 * ```tsx
 * import { SanitizedTextFieldMultiline } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedTextFieldMultiline
 *   label="Description"
 *   multiline
 *   rows={4}
 *   value={description}
 *   onChange={(e) => setDescription(e.target.value)}
 * />
 * ```
 */
export const SanitizedTextFieldMultiline = withSanitization(TextField, {
    ...MULTI_LINE_PRESET,
    handlers: ['onChange', 'onBlur'],
});

/**
 * Sanitized TextField for identifiers (SKU, codes, etc.)
 * Automatically converts to uppercase
 *
 * @example
 * ```tsx
 * import { SanitizedTextFieldIdentifier } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedTextFieldIdentifier
 *   label="SKU"
 *   value={sku}
 *   onChange={(e) => setSku(e.target.value)}
 * />
 * ```
 */
export const SanitizedTextFieldIdentifier = withSanitization(TextField, {
    ...IDENTIFIER_PRESET,
    handlers: ['onChange', 'onBlur'],
});

/**
 * Sanitized Autocomplete component
 *
 * @example
 * ```tsx
 * import { SanitizedAutocomplete } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedAutocomplete
 *   options={items}
 *   value={selectedItem}
 *   onChange={(e, value) => setSelectedItem(value)}
 *   onInputChange={(e, value) => setInputValue(value)}
 *   renderInput={(params) => <TextField {...params} label="Item" />}
 * />
 * ```
 */
export const SanitizedAutocomplete = createSanitizedComponent.autocomplete(
    Autocomplete
);

/**
 * Sanitized OutlinedInput component
 *
 * @example
 * ```tsx
 * import { SanitizedOutlinedInput } from '~/components/atoms/inputs/sanitized';
 *
 * <SanitizedOutlinedInput
 *   value={value}
 *   onChange={(e) => setValue(e.target.value)}
 * />
 * ```
 */
export const SanitizedOutlinedInput =
    createSanitizedComponent.singleLine(OutlinedInput);

/**
 * Sanitized Input component
 */
export const SanitizedInput = createSanitizedComponent.singleLine(Input);

/**
 * Sanitized FilledInput component
 */
export const SanitizedFilledInput =
    createSanitizedComponent.singleLine(FilledInput);
