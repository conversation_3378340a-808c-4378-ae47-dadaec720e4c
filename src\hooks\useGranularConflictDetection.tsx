/**
 * useGranularConflictDetection Hook
 *
 * A reusable hook that provides granular conflict detection for forms,
 * especially useful for nested structures like menus with display groups and items.
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { GranularChangeTracker } from '../utils/granularChangeTracker';

interface UseGranularConflictDetectionOptions {
  /**
   * Enable automatic change tracking on form value changes
   * @default true
   */
  autoTrack?: boolean;

  /**
   * Callback when changes are detected
   */
  onChangeDetected?: (path: string, value: any) => void;

  /**
   * Paths to exclude from tracking
   */
  excludePaths?: string[];

  /**
   * Custom function to determine if tracking should be enabled
   */
  shouldTrack?: (path: string, value: any) => boolean;
}

export interface GranularConflictDetectionControls {
  /**
   * The change tracker instance
   */
  changeTracker: GranularChangeTracker;

  /**
   * Track a specific change manually
   */
  trackChange: (path: string, value: any) => void;

  /**
   * Check if there are any tracked changes
   */
  hasChanges: boolean;

  /**
   * Get all tracked change paths
   */
  changedPaths: string[];

  /**
   * Clear all tracked changes
   */
  clearChanges: () => void;

  /**
   * Re-initialize with current form values
   */
  reinitialize: () => void;

  /**
   * Check if a specific path has changes
   */
  hasChangesInPath: (path: string) => boolean;
}

/**
 * Hook for granular conflict detection in forms
 *
 * @example
 * ```tsx
 * function MyFormComponent() {
 *   const { changeTracker, trackChange, hasChanges } = useGranularConflictDetection();
 *
 *   const handleFieldChange = (path: string, value: any) => {
 *     setValue(path, value);
 *     trackChange(path, value);
 *   };
 *
 *   return (
 *     <Box>
 *       <TextField onChange={(e) => handleFieldChange('name', e.target.value)} />
 *       <RealtimeEditConflictDetector changeTracker={changeTracker} />
 *     </Box>
 *   );
 * }
 * ```
 */
export function useGranularConflictDetection(
  options: UseGranularConflictDetectionOptions = {}
): GranularConflictDetectionControls {
  const {
    autoTrack = true,
    onChangeDetected,
    excludePaths = [],
    shouldTrack,
  } = options;

  const { watch, getValues } = useFormContext();
  const changeTrackerRef = useRef(new GranularChangeTracker());
  const [hasChanges, setHasChanges] = useState(false);
  const [changedPaths, setChangedPaths] = useState<string[]>([]);
  const initializedRef = useRef(false);

  // Initialize the tracker with current form values
  const reinitialize = useCallback(() => {
    const currentValues = getValues();
    changeTrackerRef.current.initialize(currentValues);
    initializedRef.current = true;
    setHasChanges(false);
    setChangedPaths([]);
  }, [getValues]);

  // Initialize on mount
  useEffect(() => {
    if (!initializedRef.current) {
      reinitialize();
    }
  }, [reinitialize]);

  // Track a change manually
  const trackChange = useCallback(
    (path: string, value: any) => {
      // Check if path should be excluded
      if (excludePaths.some(excludePath => path.startsWith(excludePath))) {
        return;
      }

      // Check custom shouldTrack function
      if (shouldTrack && !shouldTrack(path, value)) {
        return;
      }

      changeTrackerRef.current.trackChange(path, value);
      setHasChanges(changeTrackerRef.current.hasChanges());
      setChangedPaths(changeTrackerRef.current.getChangeSummary());

      onChangeDetected?.(path, value);
    },
    [excludePaths, shouldTrack, onChangeDetected]
  );

  // Auto-track changes if enabled
  useEffect(() => {
    if (!autoTrack || !initializedRef.current) return;

    const subscription = watch((value, { name }) => {
      if (name) {
        trackChange(name, value[name]);
      }
    });

    return () => subscription.unsubscribe();
  }, [autoTrack, watch, trackChange]);

  // Clear all changes
  const clearChanges = useCallback(() => {
    changeTrackerRef.current.clearChanges();
    setHasChanges(false);
    setChangedPaths([]);
  }, []);

  // Check if a specific path has changes
  const hasChangesInPath = useCallback((path: string) => {
    return changeTrackerRef.current.hasChangesInPath(path);
  }, []);

  return {
    changeTracker: changeTrackerRef.current,
    trackChange,
    hasChanges,
    changedPaths,
    clearChanges,
    reinitialize,
    hasChangesInPath,
  };
}

/**
 * Hook specifically for modal forms that maintain local state
 * and only update the parent form on submit
 */
export function useModalGranularConflictDetection<T = any>(
  initialValues: T | undefined,
  path: string
): {
  changeTracker: GranularChangeTracker;
  trackLocalChange: (field: keyof T, value: any) => void;
  hasLocalChanges: boolean;
  getLocalChangedPaths: () => string[];
  commitChangesToPath: (
    finalValues: T,
    formSetValue: (path: string, value: any) => void
  ) => void;
} {
  const changeTrackerRef = useRef(new GranularChangeTracker());
  const [hasLocalChanges, setHasLocalChanges] = useState(false);

  // Initialize with initial values
  useEffect(() => {
    if (initialValues) {
      changeTrackerRef.current.initialize(initialValues);
    }
  }, [initialValues]);

  // Track local state changes
  const trackLocalChange = useCallback((field: keyof T, value: any) => {
    const fieldPath = `${field as string}`;
    changeTrackerRef.current.trackChange(fieldPath, value);
    setHasLocalChanges(changeTrackerRef.current.hasChanges());
  }, []);

  // Get changed paths
  const getLocalChangedPaths = useCallback(() => {
    return changeTrackerRef.current.getChangeSummary();
  }, []);

  // Commit changes to the parent form
  const commitChangesToPath = useCallback(
    (finalValues: T, formSetValue: (path: string, value: any) => void) => {
      // When committing, we need to track changes at the parent path level
      // For example, if path is "pages.0[2]", we track the entire item change
      formSetValue(path, finalValues);

      // Clear local tracking after commit since changes are now in parent form
      changeTrackerRef.current.clearChanges();
      setHasLocalChanges(false);
    },
    [path]
  );

  return {
    changeTracker: changeTrackerRef.current,
    trackLocalChange,
    hasLocalChanges,
    getLocalChangedPaths,
    commitChangesToPath,
  };
}

/**
 * Hook for tracking changes in nested arrays/objects within a form
 * Useful for menu items, display groups, etc.
 */
export function useNestedPathTracking(basePath: string) {
  const { changeTracker, trackChange, hasChangesInPath } =
    useGranularConflictDetection({
      autoTrack: false, // We'll track manually for nested paths
    });

  const trackNestedChange = useCallback(
    (nestedPath: string, value: any) => {
      const fullPath = basePath ? `${basePath}.${nestedPath}` : nestedPath;
      trackChange(fullPath, value);
    },
    [basePath, trackChange]
  );

  const hasNestedChanges = useCallback(
    (nestedPath?: string) => {
      const checkPath = nestedPath ? `${basePath}.${nestedPath}` : basePath;
      return hasChangesInPath(checkPath);
    },
    [basePath, hasChangesInPath]
  );

  return {
    changeTracker,
    trackNestedChange,
    hasNestedChanges,
  };
}
