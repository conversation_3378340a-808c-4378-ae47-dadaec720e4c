import { useCallback, useEffect, useState } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  InputAdornment,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { isEqual } from 'lodash';
import {
  FormDataConsumer,
  ReferenceInput,
  required,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextInput,
  TimeInput,
  useRedirect,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { RESOURCES } from '~/providers/resources';
import CustomImageInput from '../../components/atoms/inputs/CustomImageInput';
import CustomDeleteWithConfirmButton from '../../components/molecules/CustomDeleteWithConfirmButton';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import InfoPopup from './components/InfoPopup';
import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';

export const weekKeys = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
];

const SellpointEditInner = () => {
  const [closedDays, setClosedDays] = useState<string[]>([]);
  const [openInfoDialog, setOpenInfoDialog] = useState(false);
  const [showBusinessHoursExceptions, setShowBusinessHoursExceptions] =
    useState(false);
  const [prevBusinessHours, setPrevBusinessHours] = useState({});

  const redirect = useRedirect();
  const isMobile = useMediaQuery('(max-width:600px)', { noSsr: true });
  const { t } = useTranslation();
  const { getValues, setValue } = useFormContext();
  const { businessHoursSchedule } = getValues();

  useEffect(() => {
    const record = getValues();
    if (record) {
      const dailyHours = record.businessHoursSchedule ?? {};
      const exceptions = record.businessHoursExceptions ?? {};
      const businessHours = Array(7).fill(dailyHours);

      const closed: string[] = [];
      for (let key in exceptions) {
        const idx = parseInt(key);
        businessHours[idx] = exceptions[key];

        if (!exceptions[key].startAt && !exceptions[key].endAt) {
          closed.push(weekKeys[idx]);
        }
      }

      setPrevBusinessHours(dailyHours);
      setValue('businessHours', businessHours);
      setClosedDays(closed);
    }
  }, [businessHoursSchedule.startAt, businessHoursSchedule.endAt]);

  const toggleClosedBusinessDay = (key: string) => {
    const index = closedDays.indexOf(key);
    const newDays = [...closedDays];
    if (index === -1) {
      newDays.push(key);
      setValue(`businessHours.${weekKeys.indexOf(key)}`, {
        startAt: null,
        endAt: null,
      });
    } else {
      newDays.splice(index, 1);
      const daily = getValues('businessHoursSchedule');
      setValue(`businessHours.${weekKeys.indexOf(key)}`, daily);
    }

    setClosedDays(newDays);
  };

  const onDailyHoursChange = (e: any, field: 'startAt' | 'endAt') => {
    const newHours = { ...prevBusinessHours, ...{ [field]: e.target.value } };
    const businessHours = getValues('businessHours');
    weekKeys.forEach((_, idx) => {
      if (isEqual(prevBusinessHours, businessHours[idx])) {
        setValue(`businessHours.${idx}`, newHours);
      }
    });

    setPrevBusinessHours(newHours);
  };

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.LOCATIONS);
  }, [redirect]);

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('sellPointsPage.editSellpoint')}
      >
        <>
          <CustomDeleteWithConfirmButton
            icon={isMobile ? <DeleteIcon /> : <></>}
            sx={{ mr: 2, background: 'rgba(0,0,0,.05)' }}
            field="displayName"
            label={t('shared.delete')}
          />
          <SaveButton type="button" icon={<></>} label={t('shared.save')} />
        </>
      </ModalHeader>
      {/* Body */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '600px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 6,
        }}
      >
        <Subsection
          title={t('sellPointsPage.basicInformation')}
          subtitle={t('sellPointsPage.basicInformationDescription')}
        >
          <CustomInput
            type="text"
            sanitize="singleLine"
            ui="original"
            isRequired
            source="name"
            label={t('sellPointsPage.locationBusinessName')}
            validate={required()}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  onClick={() => setOpenInfoDialog(true)}
                  position="end"
                  style={{
                    cursor: 'pointer',
                  }}
                >
                  <Typography variant="body2" color="primary" fontWeight={500}>
                    {t('sellPointsPage.whatsThis')}
                  </Typography>
                </InputAdornment>
              ),
            }}
          />
          <Typography variant="caption" color="custom.gray600">
            {t('sellPointsPage.locationBusinessNameDescription')}
          </Typography>
          <Box height="24px" />
          <CustomInput
            type="text"
            sanitize="multiLine"
            ui="original"
            source="description"
            label={t('sellPointsPage.businessDescription')}
            multiline
            inputProps={{ maxLength: 1024 }}
          />
          <FormDataConsumer>
            {({ formData }) => (
              <Typography variant="caption" color="custom.gray600">
                {formData.description?.length ?? 0} / 1024
              </Typography>
            )}
          </FormDataConsumer>
        </Subsection>
        <CustomDivider />
        <Subsection title={t('sellPointsPage.businessAddress')}>
          <ReferenceInput source="type" reference="locationTypes">
            <CustomInput
              type="select"
              source="type"
              sanitize="singleLine"
              ui="original"
              defaultValue={'mobile'}
              label={t('sellPointsPage.locationType')}
              optionText="name"
              optionValue="id"
              validate={required()}
            />
          </ReferenceInput>
          <CustomInput
            type="text"
            sanitize="singleLine"
            ui="original"
            isRequired
            source="address.line1"
            label={t('sellPointsPage.addressLine1')}
            validate={required()}
          />
          <Grid container columnSpacing={2}>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                source="address.line2"
                label={t('sellPointsPage.addressLine2')}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                isRequired
                source="address.city"
                label={t('sellPointsPage.city')}
                validate={required()}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                isRequired
                source="address.county"
                label={t('sellPointsPage.county')}
                validate={required()}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                source="address.zipCode"
                label={t('sellPointsPage.zipCode')}
              />
            </Grid>
          </Grid>
        </Subsection>
        <CustomDivider />
        <Subsection title={t('sellPointsPage.contactInformation')}>
          <Grid container columnSpacing={2}>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                source="contactInfo.email"
                label={t('sellPointsPage.emailAddress')}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                source="contactInfo.phoneNumber"
                label={t('sellPointsPage.phoneNumber')}
              />
            </Grid>
          </Grid>
        </Subsection>
        <Subsection title={t('sellPointsPage.socialContact')}>
          <CustomInput
            type="text"
            sanitize="singleLine"
            ui="original"
            source="contactInfo.website"
            label="Website"
          />
          <Grid container columnSpacing={2}>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                source="contactInfo.facebook"
                label="Facebook"
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                sm: 6
              }}>
              <CustomInput
                type="text"
                sanitize="singleLine"
                ui="original"
                source="contactInfo.instagram"
                label="Instagram"
              />
            </Grid>
          </Grid>
          <CustomInput
            type="text"
            sanitize="singleLine"
            ui="original"
            source="contactInfo.youtube"
            label="Youtube"
          />
        </Subsection>
        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.branding')}
          subtitle={t('sellPointsPage.brandingDescription')}
        >
          <Typography variant="h4" mb={2}>
            Logo
          </Typography>
          <CustomImageInput source="logoURL" />
        </Subsection>
        <CustomDivider />

        {/* businessHours */}
        <Subsection
          title={t('sellPointsPage.businessHours')}
          subtitle={t('sellPointsPage.businessHoursDescription')}
        >
          <FormDataConsumer>
            {({ formData }) => (
              <>
                <Grid container columnSpacing={2} mb={1}>
                  <Grid
                    size={{
                      xs: 12,
                      sm: 4
                    }}>
                    <Typography variant="body2" mt={2}>
                      {t('sellpoints.daily')}
                    </Typography>
                  </Grid>
                  <Grid
                    size={{
                      xs: 12,
                      sm: 4
                    }}>
                    <TimeInput
                      label={t('sellPointsPage.startAt')}
                      source="businessHoursSchedule.startAt"
                      // no idea why this is neccessary but it doesn't work without it
                      parse={(value: string) => value}
                      onChange={e => onDailyHoursChange(e, 'startAt')}
                    />
                  </Grid>
                  <Grid
                    size={{
                      xs: 12,
                      sm: 4
                    }}>
                    <TimeInput
                      label={t('sellPointsPage.endAt')}
                      source="businessHoursSchedule.endAt"
                      parse={(value: string) => value}
                      sx={{ marginBottom: 0 }}
                      onChange={e => onDailyHoursChange(e, 'endAt')}
                    />
                    <Typography
                      variant="caption"
                      color="custom.gray600"
                      sx={{
                        opacity:
                          formData.businessHoursSchedule?.endAt?.split(':')[0] <
                          formData.businessHoursSchedule?.startAt?.split(':')[0]
                            ? 1
                            : 0,
                      }}
                    >
                      {t('sellPointsPage.nextDay')}
                    </Typography>
                  </Grid>
                  <Grid size={12}>
                    <Typography
                      // @ts-ignore
                      variant="label"
                      color="primary"
                      sx={{ cursor: 'pointer' }}
                      onClick={() =>
                        setShowBusinessHoursExceptions(prev => !prev)
                      }
                    >
                      {showBusinessHoursExceptions
                        ? t('sellPointsPage.hideExceptions')
                        : t('sellPointsPage.addExceptions')}
                    </Typography>
                  </Grid>
                </Grid>
                {showBusinessHoursExceptions &&
                  weekKeys.map((key, idx) => (
                    <Grid key={key} container columnSpacing={2} mb={1}>
                      <Grid
                        size={{
                          xs: 12,
                          sm: 4
                        }}>
                        <Typography variant="body2">
                          {t(`sellpoints.${key}`)}
                        </Typography>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={closedDays.includes(key)}
                              onChange={() => toggleClosedBusinessDay(key)}
                              inputProps={{ 'aria-label': 'controlled' }}
                            />
                          }
                          label={
                            <Typography variant="body2">
                              {t('sellPointsPage.closed')}
                            </Typography>
                          }
                        />
                      </Grid>
                      <Grid
                        size={{
                          xs: 12,
                          sm: 4
                        }}>
                        <TimeInput
                          label={t('sellPointsPage.startAt')}
                          source={`businessHours.${idx}.startAt`}
                          parse={(value: string) => value}
                          disabled={closedDays.includes(key)}
                        />
                      </Grid>
                      <Grid
                        size={{
                          xs: 12,
                          sm: 4
                        }}>
                        <TimeInput
                          label={t('sellPointsPage.endAt')}
                          source={`businessHours.${idx}.endAt`}
                          parse={(value: string) => value}
                          sx={{ marginBottom: 0 }}
                          disabled={closedDays.includes(key)}
                        />
                        <Typography
                          variant="caption"
                          color="custom.gray600"
                          sx={{
                            opacity:
                              formData.businessHours?.[idx]?.endAt?.split(
                                ':'
                              )[0] <
                              formData.businessHours?.[idx]?.startAt?.split(
                                ':'
                              )[0]
                                ? 1
                                : 0,
                          }}
                        >
                          Next day
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
              </>
            )}
          </FormDataConsumer>
        </Subsection>

        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.bankInformation')}
          subtitle={t('sellPointsPage.bankInformationDescription')}
        >
          <ReferenceInput source="bankAccountId" reference="bankAccounts">
            <CustomInput
              type="select"
              source="bankAccountId"
              sanitize="singleLine"
              ui="original"
              label={t('sellPointsPage.transferAccount')}
              optionText="name"
              optionValue="id"
              validate={required()}
            />
          </ReferenceInput>
        </Subsection>
        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.preferredLanguage')}
          subtitle={t('sellPointsPage.preferredLanguageDescription')}
        >
          <ReferenceInput source="localization" reference="languages">
            <CustomInput
              type="select"
              source="localization"
              sanitize="singleLine"
              ui="original"
              label={t('sellPointsPage.selectLanguage')}
              optionText="name"
              optionValue="id"
              validate={required()}
            />
          </ReferenceInput>
        </Subsection>
      </Box>
      <InfoPopup
        open={openInfoDialog}
        onClose={() => setOpenInfoDialog(false)}
      />
    </>
  );
};

export const SellpointEdit = () => {
  const transform = (data: any) => {
    const exceptions: any = {};
    data.businessHours.forEach((hours: any, idx: number) => {
      if (!isEqual(hours, data.businessHoursSchedule)) {
        exceptions[idx] = hours;
      }
    });
    return {
      ...data,
      businessHoursExceptions: exceptions,
    };
  };

  return (
    <EditDialog {...getFullscreenModalProps()} transform={transform}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <SellpointEditInner />
      </SimpleForm>
    </EditDialog>
  );
};
