import {
  Box,
  Divider,
  Grid,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Title } from 'react-admin';
import { useTranslation } from 'react-i18next';

import Categories from './components/organisms/dashboard/Categories';
import CustomersStats from './components/organisms/dashboard/CustomersStats';
import FrequentActions from './components/organisms/dashboard/FrequentActions';
import LocationAndDateSelectors from './components/organisms/dashboard/LocationAndDateSelectors';
import OpenSalesPending from './components/organisms/dashboard/OpenSalesPending';
import PaymentTypes from './components/organisms/dashboard/PaymentTypes';
import RecommendedProducts from './components/organisms/dashboard/RecommendedProducts';
import SetupProgressBar from './components/organisms/dashboard/SetupProgressBar';
import ShareSelioBanner from './components/organisms/dashboard/ShareSelioBanner';
import SignUpCard from './components/organisms/dashboard/SignUpCard';
import TopCategoriesBySales from './components/organisms/dashboard/TopCategoriesBySales';
import TopItemsBySales from './components/organisms/dashboard/TopItemsBySales';
import TopModifiers from './components/organisms/dashboard/TopModifiers';
import TotalSales from './components/organisms/dashboard/TotalSales';
import UpgradePlanCard from './components/organisms/dashboard/UpgradePlanCard';
import WhatsNewBanner from './components/organisms/dashboard/WhatsNewBanner';
import { useFirebase } from './contexts/FirebaseContext';
import useEnv from './hooks/useEnv';

export default () => {
  const { t } = useTranslation('');
  const { NODE_ENV } = useEnv();
  const { details: fbDetails } = useFirebase();

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const dashItems = [
    {
      component: (
        <>
          <Typography variant="h2" mb={2}>
            {t('dashboard.welcome')},{' '}
            {fbDetails.user?.displayName?.split(' ')[0]}!
          </Typography>
          <FrequentActions />
        </>
      ),
    },
    {
      component: NODE_ENV === 'dev' && <SetupProgressBar />,
    },
    {
      component: <OpenSalesPending />,
    },
    {
      component: <TotalSales />,
    },

    {
      component: <PaymentTypes />,
      lg: 4,
    },
    {
      component: <Categories />,
      lg: 4,
    },
    {
      // we randomly show one card or the other
      component: Math.round(Math.random())
        ? NODE_ENV === 'dev' && <SignUpCard />
        : NODE_ENV === 'dev' && <UpgradePlanCard />,
      lg: 4,
    },
    {
      component: <Divider sx={{ marginY: 3 }} />,
    },
    {
      component: <TopCategoriesBySales />,
      lg: 4,
    },
    {
      component: <TopItemsBySales />,
      lg: 4,
    },
    {
      component: <TopModifiers />,
      lg: 4,
    },
    {
      component: NODE_ENV === 'dev' && <CustomersStats />,
      lg: 4,
    },
    {
      component: <Divider sx={{ marginY: 3 }} />,
    },
    {
      component: NODE_ENV === 'dev' && <RecommendedProducts />,
    },
  ];

  return (
    <Box
      sx={{
        pt: 2,
        pb: 5,
        px: 2,
        maxWidth: '1400px',
        margin: 'auto',
        width: '100%',
      }}
    >
      <Title title="Home" />
      {/* dashboard container */}
      <Grid container rowSpacing={3} columnSpacing={5}>
        <Grid sx={{ paddingTop: '0px !important' }} size={12}>
          {NODE_ENV === 'dev' && (
            <>
              <WhatsNewBanner />
              <ShareSelioBanner />
            </>
          )}
        </Grid>
        <Grid
          sx={{
            position: 'sticky',
            top: isXSmall ? '30px' : '35px',
            zIndex: 10,
          }}
          size={12}>
          <LocationAndDateSelectors />
        </Grid>

        {dashItems.map((item, idx) => (
          <Grid
            key={idx}
            size={{
              xs: 12,
              lg: item.lg ?? 12
            }}>
            {item.component}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};
