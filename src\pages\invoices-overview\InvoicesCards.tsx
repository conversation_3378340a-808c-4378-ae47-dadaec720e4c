import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import ExtraDataCard from '~/components/molecules/ExtraDataCard';
import { useTheme } from '~/contexts/ThemeContext';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';

type TableRow = {
  billName: string;
  closedAt: number;
  closedBy: string;
  closedWith: string;
  closedWithCompReason: string;
  id: string;
  items: Record<string, any>;
  name?: string;
  netValue?: number;
  openedAt: number;
  tipsValue: number;
  openedBy: string;
  owner: string;
  quantity?: number;
  section: string;
  totalValue: number;
  billPaymentsValue: number;
};

export default function InvoicesCards({
  tableData,
}: {
  tableData: TableRow[];
}) {
  const { theme } = useTheme();
  const { t } = useTranslation('');

  const completeTransactions =
    tableData?.filter(
      row => row.closedWith !== 'comp' && row.closedWith !== 'void'
    ) || [];

  const voidTransactions =
    tableData?.filter(row => row.closedWith == 'void') || [];

  const voidCount = voidTransactions.length;

  const compedTransactions =
    tableData?.filter(row => row.closedWith == 'comp') || [];

  const compedCount = compedTransactions.length;

  const completeCount = completeTransactions.length;

  const totalCollected = completeTransactions.reduce(
    (acc, row) => acc + (row.totalValue || 0),
    0
  );

  const totalNetSales = completeTransactions.reduce(
    (acc, row) => acc + (row.totalValue || 0) - (row.tipsValue || 0),
    0
  );

  const billPaymentsValue = tableData.reduce(
    (acc, row) => acc + row.billPaymentsValue,
    0
  );

  const formattedTotalCollected = formatAndDivideNumber(totalCollected);
  const formattedNetSales = formatAndDivideNumber(totalNetSales);

  const cardsConfig: { title: string; value: string | number; info: string }[] =
    [
      {
        title: t('shared.totalValue'),
        value: formatAndDivideNumber(billPaymentsValue),
        info: t('shared.netSales'),
      },
      {
         title: t('reports.completeInvoices'),
        value: completeCount,
        info: t('reports.completeInvoicesInfo'),
      },
      

    ];

  return (
    <Box
      sx={{
        width: '100%',
        gap: {
          xs: 1,
          md: 8,
        },
        flexWrap: 'wrap',
        p: 3,
        backgroundColor:
          theme.palette.mode === 'light'
            ? ' #F1F1F140'
            : ' rgba(28, 28, 28, 0.5)',
        borderRadius: 2,
        border:
          theme.palette.mode === 'light'
            ? '2px solid #F2F2F2'
            : '2px solid rgb(115, 115, 115)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: { xs: 'center', md: 'start' },
        '@media print': {
          backgroundColor: '#F2F2F2 !important',
          color: '#fff !important',
        },
      }}
    >
      {cardsConfig.map((item, index) => (
        <Box
          key={index}
          sx={{
            p: 2,
            height: '100%',
            '@media print': {
              backgroundColor: '#fff !important',
              color: '#fff !important',
            },
            backgroundColor: 'background.paper',
            borderRadius: 2,
            width: { xs: '100%', sm: 'auto' },
            border:
              theme.palette.mode === 'light'
                ? '2px solid #F2F2F2'
                : '2px solid rgb(115, 115, 115)',
            justifyContent: 'center',
          }}
        >
          <ExtraDataCard item={item} />
        </Box>
      ))}
    </Box>
  );
}
