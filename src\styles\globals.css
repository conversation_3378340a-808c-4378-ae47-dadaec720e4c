/* Geologica and Roboto fonts loaded in HTML files for early availability */
/* <PERSON><PERSON> consent CSS loaded via link tags in HTML files */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  appearance: textfield;
  -moz-appearance: textfield;
}

.MuiDialog-paper {
  overflow-x: hidden;
}

.RaLayout-appFrame {
  margin-top: 60px !important;
}

#edit-dialog-title {
  display: none;
}

.MuiList-root.MuiList-padding.RaMenu-closed {
  width: 0px;
  overflow: hidden;
}

.MuiList-root.MuiList-padding.RaMenu-open {
  /* width: 300px; */
  overflow: hidden;
}

.RaList-main .RaList-content {
  overflow: visible !important;
}

.RaList-main
  .RaList-content
  .RaBulkActionsToolbar-toolbar:not(.RaBulkActionsToolbar-collapsed) {
  min-height: 73px;
  transform: translateY(-73px);
}

.RaList-main {
  max-width: 100%;
  overflow: auto;
}

/* nu mai stiu de ce facusem asta, -bosti : Buna <PERSON>, este okay, nici eu nu-mi dau seama... :) */
/* #main-content {
  padding-left: 0;
} */

.MuiAutocomplete-paper {
  background-image: linear-gradient(
    rgba(255, 255, 255, 0.12),
    rgba(255, 255, 255, 0.12)
  ) !important;
}

@media screen and (max-width: 600px) {
  #main-content {
    width: 100vw;
    padding-left: 8px;
  }

  .MuiList-root.MuiList-padding.RaMenu-open,
  .MuiDrawer-paper.RaSidebar-paper {
    width: 100vw !important;
    overflow: hidden;
  }

  .filter-field,
  .MuiAutocomplete-root {
    width: 100%;
  }
}

/* styles for "Save query" and "Confirm delete" modals */
.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing {
  justify-content: space-between;
  padding: 0px 24px 15px;
  width: calc(100vw - 112px);
  max-width: 350px;
}

.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing {
  justify-content: space-between;
  padding: 0px 24px 15px;
}

.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing
  button:last-of-type,
.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing
  button:last-of-type {
  background: #0069ff;
  color: white;
}

.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing
  button
  > span,
.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing
  button
  > span {
  display: none;
}

.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing
  button:first-of-type,
.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing
  button:first-of-type {
  border: 1px solid #0069ff;
}

.MuiTableRow-root {
  height: 51px;
}

.MuiToolbar-root > form {
  width: 100%;
}

.MuiMenu-list > .Mui-selected {
  background-color: rgba(0, 105, 255, 0.28) !important;
}

.RaCreateButton-floating {
  bottom: 20px !important;
}

@media only screen and (max-width: 900px) {
  .MuiToolbar-regular {
    min-height: auto !important;
  }
}

/* sparkline chart */
.sparkline--cursor {
  stroke: rgba(0, 0, 0, 0.2);
}

.sparkline--spot {
  stroke: transparent;
}

.RaAppBar-menuButton {
  display: none !important;
}

/* Time picker styles */
.MuiMultiSectionDigitalClockSection-root {
  width: auto !important;
  &:after {
    height: 0 !important;
  }

  > li {
    width: 70px;
  }
}
/* End of time picker styles */

@media print {
  /* Hide navigation and UI elements */
  .RaLayout-appFrame {
    margin-top: 0 !important;
  }

  .RaAppBar-root,
  .RaSidebar-root,
  .MuiDrawer-root,
  nav,
  header {
    display: none !important;
  }

  .do-not-print {
    display: none !important;
  }

  /* Ensure content is visible */
  body {
    background: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Reset problematic positions */
  .RaLayout-content,
  #main-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}

.print-body {
  .do-not-print {
    display: none;
  }
}
@media screen and (max-width: 600px) {
  .MuiPickersLayout-contentWrapper {
    display: none !important;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fade-in {
  animation: fadeIn 0.4s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-in-out;
}

.loading-screen.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.app-content-loaded {
  animation: fadeIn 0.5s ease-in-out;
}

#root {
  min-height: 100vh;
}

body {
  background-color: #f5f5f5;
}

body.dark-mode {
  background-color: #121212;
}
