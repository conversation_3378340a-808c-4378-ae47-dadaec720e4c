/**
 * Utility function to generate obfuscated tokens for OrderNow URLs
 * This can be run in the browser console for testing purposes
 *
 * Usage:
 * 1. Copy this entire file content
 * 2. Paste it in the browser console
 * 3. Call: generateObfuscatedToken('accountPartial', 'sellPointPartial', floorPlanIndex, itemNumber, 'secret')
 *
 * Example:
 * generateObfuscatedToken('********9012', '********', 0, 1, 'your-secret-key')
 */

// Helper functions for encoding - Simple base64 with URL-safe characters
const encodeToCustomBase = (data) => {
    // Convert to standard base64
    let binary = '';
    for (let i = 0; i < data.length; i++) {
        binary += String.fromCharCode(data[i]);
    }

    // Create base64 string and make it URL-safe
    const base64 = btoa(binary)
        .replace(/\+/g, '-') // Replace + with -
        .replace(/\//g, '_') // Replace / with _
        .replace(/=+$/, ''); // Remove padding =

    return base64;
};

const decodeFromCustomBase = (encoded) => {
    try {
        // Convert back from URL-safe base64
        let base64 = encoded
            .replace(/-/g, '+') // Replace - with +
            .replace(/_/g, '/'); // Replace _ with /

        // Add padding if needed
        while (base64.length % 4 !== 0) {
            base64 += '=';
        }

        // Decode from base64
        const binary = atob(base64);
        const result = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
            result[i] = binary.charCodeAt(i);
        }
        return result;
    } catch (error) {
        console.error('Failed to decode base64:', error, 'Input:', encoded);
        throw new Error('Invalid token format');
    }
};

// Simple checksum function
const calculateChecksum = (data) => {
    let checksum = 0;
    for (let i = 0; i < data.length; i++) {
        checksum = (checksum + data.charCodeAt(i)) & 0xff;
    }
    return checksum;
};

// Simple XOR obfuscation - not for security, just basic obfuscation
const simpleXorObfuscate = (data, key) => {
    if (!data || !key) {
        throw new Error('Data and key must not be empty');
    }

    // Add a simple checksum at the beginning for validation
    const checksum = calculateChecksum(data);
    const fullData = String.fromCharCode(checksum) + data;

    const result = new Uint8Array(fullData.length);

    console.log('XOR obfuscate debug:', {
        originalData: data,
        dataLength: data.length,
        keyLength: key.length,
        checksum: checksum,
        fullDataLength: fullData.length,
    });

    for (let i = 0; i < fullData.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        result[i] = fullData.charCodeAt(i) ^ keyChar;
    }

    console.log('XOR obfuscate result:', {
        resultLength: result.length,
        result: Array.from(result),
    });

    return result;
};

const simpleXorDeobfuscate = (data, key) => {
    if (data.length < 2) {
        console.warn('XOR deobfuscate: data too short', data.length);
        return null;
    }

    if (!key) {
        console.warn('XOR deobfuscate: key is empty');
        return null;
    }

    // Deobfuscate the data
    const deobfuscated = [];
    for (let i = 0; i < data.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        deobfuscated.push(data[i] ^ keyChar);
    }

    const fullString = String.fromCharCode(...deobfuscated);

    // Extract checksum and data
    const storedChecksum = fullString.charCodeAt(0);
    const originalData = fullString.substring(1);
    const calculatedChecksum = calculateChecksum(originalData);

    console.log('XOR deobfuscate debug:', {
        dataLength: data.length,
        deobfuscatedData: Array.from(deobfuscated),
        fullStringLength: fullString.length,
        storedChecksum: storedChecksum,
        calculatedChecksum: calculatedChecksum,
        originalData: originalData,
        checksumMatch: storedChecksum === calculatedChecksum,
    });

    // Verify checksum
    if (storedChecksum !== calculatedChecksum) {
        console.warn('XOR deobfuscate: checksum mismatch', {
            storedChecksum,
            calculatedChecksum,
        });
        return null;
    }

    return originalData;
};

/**
 * Generate an obfuscated token from account and sellpoint partials
 * @param {string} accountPartial - First 12 characters of account ID
 * @param {string} sellPointPartial - First 8 characters of sellpoint ID
 * @param {string} secret - Secret key for obfuscation
 * @param {number|null} [floorPlanIndex=null] - Floor plan index (0-99, optional)
 * @param {number|null} [itemNumber=null] - Item number (0-9999, optional)
 * @returns {string} The obfuscated token
 */
function generateObfuscatedToken(accountPartial, sellPointPartial, secret, floorPlanIndex = null, itemNumber = null) {
    try {
        if (!secret) {
            throw new Error('Secret key is required');
        }

        // Validate and format inputs
        const accountPart = accountPartial.substring(0, 12).padEnd(12, '0'); // Ensure 12 chars
        const sellPointPart = sellPointPartial.substring(0, 8).padEnd(8, '0'); // Ensure 8 chars

        // Create token string based on provided parameters
        let tokenString = accountPart + sellPointPart; // Base: 12+8 = 20 characters

        // Add floor and item parts only if provided
        if (floorPlanIndex !== null && itemNumber !== null) {
            const floorIndex = Math.min(Math.max(floorPlanIndex, 0), 99); // 0-99
            const itemNum = Math.min(Math.max(itemNumber, 0), 9999); // 0-9999
            const floorPadded = floorIndex.toString().padStart(2, '0');
            const itemPadded = itemNum.toString().padStart(4, '0');
            tokenString += floorPadded + itemPadded; // Total: 12+8+2+4 = 26 characters
        }

        console.log('Token generation debug:', {
            accountPartial: accountPart,
            sellPointPartial: sellPointPart,
            floorPlanIndex: floorPlanIndex,
            itemNumber: itemNumber,
            tokenFormat: floorPlanIndex !== null && itemNumber !== null ? 'full (26 chars)' : 'short (20 chars)',
            tokenString: tokenString,
            tokenStringLength: tokenString.length,
        });

        // Obfuscate with simple XOR
        const obfuscatedData = simpleXorObfuscate(tokenString, secret);

        // Encode with URL-safe base64
        const encodedToken = encodeToCustomBase(obfuscatedData);

        console.log('Generated token:', encodedToken);
        console.log('Token length:', encodedToken.length);

        return encodedToken;
    } catch (error) {
        console.error('Failed to generate token:', error);
        throw error;
    }
}

/**
 * Decode an obfuscated token back to its components
 * @param {string} token - The obfuscated token to decode
 * @param {string} secret - Secret key for deobfuscation
 * @returns {object|null} Decoded components or null if failed
 */
function decodeObfuscatedToken(token, secret) {
    try {
        if (!secret) {
            throw new Error('Secret key is required');
        }

        console.log('Decoding token:', token);

        // Decode from URL-safe base64
        const obfuscatedData = decodeFromCustomBase(token);

        // Deobfuscate with simple XOR
        const decryptedString = simpleXorDeobfuscate(obfuscatedData, secret);

        if (!decryptedString) {
            throw new Error('Failed to deobfuscate token - may be corrupted or invalid');
        }

        // Parse token based on length
        let result;

        if (decryptedString.length === 20) {
            // Short format: account(12) + sellpoint(8)
            const accountPartial = decryptedString.substring(0, 12); // chars 0-11
            const sellPointPartial = decryptedString.substring(12, 20); // chars 12-19

            result = {
                accountPartial,
                sellPointPartial,
                floorPlanIndex: null,
                itemNumber: null,
            };
        } else if (decryptedString.length === 26) {
            // Full format: account(12) + sellpoint(8) + floor(2) + item(4)
            const accountPartial = decryptedString.substring(0, 12); // chars 0-11
            const sellPointPartial = decryptedString.substring(12, 20); // chars 12-19
            const floorPlanIndex = parseInt(decryptedString.substring(20, 22)); // chars 20-21
            const itemNumber = parseInt(decryptedString.substring(22, 26)); // chars 22-25

            // Validate extracted numbers
            if (isNaN(floorPlanIndex) || isNaN(itemNumber) || floorPlanIndex < 0 || itemNumber < 0) {
                throw new Error('Invalid numeric values in token');
            }

            result = {
                accountPartial,
                sellPointPartial,
                floorPlanIndex,
                itemNumber,
            };
        } else {
            throw new Error(`Invalid token length: expected 20 or 26 chars, got ${decryptedString.length}`);
        }

        console.log('Decoded token:', result);
        return result;
    } catch (error) {
        console.error('Token decode error:', error);
        return null;
    }
}

// Export functions for console use
window.generateObfuscatedToken = generateObfuscatedToken;
window.decodeObfuscatedToken = decodeObfuscatedToken;

console.log('Token generator utility loaded!');
console.log('Available functions:');
console.log('- generateObfuscatedToken(accountPartial, sellPointPartial, secret, [floorPlanIndex], [itemNumber])');
console.log('- decodeObfuscatedToken(token, secret)');
console.log('');
console.log('Example usage:');
console.log('// Simple usage (short 20-char token with just account + sellpoint):');
console.log('generateObfuscatedToken("********9012", "********", "your-secret-key")');
console.log('');
console.log('// With optional parameters (full 26-char token):');
console.log('generateObfuscatedToken("********9012", "********", "your-secret-key", 2, 123)');
