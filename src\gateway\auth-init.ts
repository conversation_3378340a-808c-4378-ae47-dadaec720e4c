/**
 * Gateway Auth Init - Authentication Page
 *
 * Handles Google Sign-In on the auth.html page.
 * This module is loaded directly (not via interaction detector) since the auth page
 * is only accessed by real users who clicked "Sign In".
 */

import {
  getRedirectResult,
  GoogleAuthProvider,
  onAuthStateChanged,
  signInWithPopup,
  signInWithRedirect,
} from 'firebase/auth';

import { auth } from '../firebase-core';

const nodeEnv = import.meta.env.VITE_NODE_ENV;

// DOM elements
const loadingState = document.getElementById('loading-state');
const loginCard = document.getElementById('login-card');
const errorMessage = document.getElementById('error-message');
const signInBtn = document.getElementById('google-signin-btn');
const authFooter = document.getElementById('auth-footer');
const googleIcon = document.querySelector('.google-icon');

// Check if user is already authenticated
onAuthStateChanged(auth, user => {
  if (user) {
    // Already authenticated, redirect back to main app
    console.log('[Auth] User already authenticated:', user.email);
    const params = new URLSearchParams(window.location.search);
    const returnUrl = params.get('returnUrl');
    window.location.href = returnUrl || '/';
  } else {
    // Not authenticated - show login form
    if (loadingState) loadingState.style.display = 'none';
    if (loginCard) loginCard.style.display = 'flex';
    if (authFooter) authFooter.style.display = 'block';
  }
});

// Check for redirect result (production only)
// After signInWithRedirect, user is redirected back and we need to call getRedirectResult
if (nodeEnv === 'prod') {
  getRedirectResult(auth)
    .then(result => {
      if (result) {
        const params = new URLSearchParams(window.location.search);
        const returnUrl = params.get('returnUrl');
        window.location.href = returnUrl || '/';
      }
    })
    .catch(err => {
      console.error('[Auth] Redirect error:', err);
      if (errorMessage) {
        errorMessage.textContent = err.message;
        errorMessage.style.display = 'block';
      }
      if (loadingState) loadingState.style.display = 'none';
      if (loginCard) loginCard.style.display = 'flex';
      if (authFooter) authFooter.style.display = 'block';
    });
}

// Google Sign-In button handler
if (signInBtn) {
  signInBtn.addEventListener('click', async () => {
    try {
      // Clear previous errors
      if (errorMessage) {
        errorMessage.style.display = 'none';
        errorMessage.textContent = '';
      }

      // Disable button and show loading
      signInBtn.setAttribute('disabled', 'true');
      if (googleIcon) googleIcon.classList.add('spinning');

      const provider = new GoogleAuthProvider();
      provider.setCustomParameters({ prompt: 'select_account' });

      if (nodeEnv !== 'prod') {
        // Development: Use popup (easier debugging)
        console.log('[Auth] Using popup sign-in (development)');
        const result = await signInWithPopup(auth, provider);
        if (result.user) {
          const params = new URLSearchParams(window.location.search);
          const returnUrl = params.get('returnUrl');
          window.location.href = returnUrl || '/';
        }
      } else {
        // Production: Use redirect (better for mobile/PWA)
        console.log('[Auth] Using redirect sign-in (production)');
        await signInWithRedirect(auth, provider);
        // Page will redirect away, so execution stops here
      }
    } catch (err: any) {
      console.error('[Auth] Sign-in error:', err);

      // Remove loading state
      if (googleIcon) googleIcon.classList.remove('spinning');
      signInBtn.removeAttribute('disabled');

      // Display user-friendly error messages
      if (errorMessage) {
        let errorText = err.message || 'An error occurred during sign-in';

        // Customize common error messages
        if (err.code === 'auth/popup-blocked') {
          errorText =
            'Pop-up was blocked. Please allow pop-ups for this site and try again.';
        } else if (err.code === 'auth/network-request-failed') {
          errorText =
            'Network error. Please check your connection and try again.';
        } else if (err.code === 'auth/unauthorized-domain') {
          errorText = 'This domain is not authorized. Please contact support.';
        } else if (err.code === 'auth/popup-closed-by-user') {
          errorText = 'Sign-in cancelled. Please try again.';
        } else if (err.code === 'auth/cancelled-popup-request') {
          errorText = 'Sign-in cancelled. Please try again.';
        }

        errorMessage.textContent = errorText;
        errorMessage.style.display = 'block';
      }
    }
  });
}
