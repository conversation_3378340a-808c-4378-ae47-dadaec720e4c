import { useState } from 'react';
import { List, Theme, useMediaQuery } from '@mui/material';
import { useSidebarState } from 'react-admin';

import DropdownMenuItem, { MenuItemI } from './DropdownMenuItem';

export const DROPDOWN_MENU_WIDTH = 260;

export interface SecondMenuProps {
  items: Array<MenuItemI>;
}

export default function SecondMenu({ items }: SecondMenuProps) {
  const [initialOpen, setInitalOpen] = useState<number | null>(null);
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const [sidebarOpen] = useSidebarState();

  return (
    <List
      sx={{
        ...{
          position: 'relative',
          width: '100%',
          height: 'auto',
          bgcolor: 'background.default',
          py: 3,
          '@media print': {
            display: 'none',
          },
        },
      }}
      component="nav"
      aria-labelledby="nested-list-subheader"
    >
      {items.map((el: MenuItemI, index) => {
        return (
          <DropdownMenuItem
            key={el.label}
            menuItem={el}
            open={initialOpen === index}
            setOpen={() => setInitalOpen(index)}
          />
        );
      })}
    </List>
  );
}
