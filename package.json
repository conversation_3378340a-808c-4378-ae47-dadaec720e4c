{"name": "selio-manager", "private": true, "version": "0.9.81", "type": "module", "scripts": {"dev": "vite", "build": "node scripts/incrementVersion.js && tsc && vite build", "build:dev": "tsc && vite build", "build:analyze": "cross-env ANALYZE=true tsc && vite build", "build:profile": "tsc && vite build --profile", "preview": "vite preview", "format": "prettier --config .prettierrc --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --config .prettierrc --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fnando/sparkline": "^0.3.10", "@googlemaps/js-api-loader": "^2.0.2", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^7.3.7", "@mui/material": "^7.3.7", "@mui/x-date-pickers": "^8.27.0", "@mui/x-date-pickers-pro": "^8.27.0", "@mui/x-license": "^8.26.0", "@react-admin/ra-editable-datagrid": "^5.2.2", "@react-admin/ra-form-layout": "^6.1.0", "@react-admin/ra-navigation": "^7.0.1", "@react-admin/ra-realtime": "^5.2.0", "@react-admin/ra-relationships": "^5.7.1", "@react-admin/ra-search": "^6.0.0", "chart.js": "^4.5.1", "crypto-js": "^4.2.0", "date-fns-v4": "npm:date-fns@^4.1.0", "dayjs": "^1.11.19", "fakerest": "^4.2.0", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.3.4", "firebase": "^12.8.0", "i18next": "^25.8.1", "jsonexport": "^3.2.0", "jszip": "^3.10.1", "localforage": "^1.10.0", "lodash": "^4.17.23", "mui-tel-input": "^9.0.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "ra-data-fakerest": "^5.14.1", "react": "^18.3.1", "react-admin": "^5.14.1", "react-chartjs-2": "^5.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-i18next": "^16.5.4", "react-image-crop": "^11.0.10", "react-is": "^18.3.1", "react-router-dom": "^7.13.0", "react-to-print": "^3.2.0", "recharts": "^3.7.0", "remove-accents": "^0.5.0", "vanilla-cookieconsent": "^3.1.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.7.0", "@tanstack/react-query-devtools": "^5.91.3", "@types/crypto-js": "^4.2.2", "@types/fnando__sparkline": "^0.3.7", "@types/google.maps": "^3.58.1", "@types/jsonexport": "^3.0.5", "@types/lodash": "^4.17.23", "@types/node": "^25.2.0", "@types/qrcode": "^1.5.6", "@types/react": "^19.2.10", "@types/react-dom": "^19.2.3", "@types/react-helmet": "^6.1.11", "@types/recharts": "^2.0.1", "@vitejs/plugin-react": "^5.1.3", "cross-env": "^10.1.0", "prettier": "^3.8.1", "rollup-plugin-visualizer": "^6.0.5", "sharp": "^0.34.5", "type-fest": "^5.4.3", "typescript": "^5.9.3", "vite": "^7.3.1", "vite-plugin-pwa": "^1.2.0", "vite-plugin-svgr": "^4.5.0"}, "overrides": {"react-is": "^18.3.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.17.2"}}