/**
 * Conflict Resolver
 *
 * Logic for detecting conflicts between local and remote changes,
 * and resolving them.
 */

import {
  buildEntityRegistry,
  computeArraySignature,
  deepClone,
  deepEqual,
  extractEntityOwnFields,
  findEntityById,
} from './entityRegistry';
import {
  AutoMerge,
  Conflict,
  EntityDeletedConflict,
  FieldConflict,
  LocalChange,
  NoIdArrayConflict,
  ProcessUpdateResult,
  RemoteChange,
  TrackerState,
} from './types';

/**
 * Get all local changes for a specific entity.
 */
export function getLocalChangesForEntity(
  state: TrackerState,
  entityId: string
): LocalChange[] {
  const changes: LocalChange[] = [];
  for (const [key, change] of state.localChanges) {
    if (change.entityId === entityId) {
      changes.push(change);
    }
  }
  return changes;
}

/**
 * Check if there are any local changes for an entity.
 */
export function hasLocalChangesForEntity(
  state: TrackerState,
  entityId: string
): boolean {
  for (const [, change] of state.localChanges) {
    if (change.entityId === entityId) {
      return true;
    }
  }
  return false;
}

/**
 * Get local changes for a specific entity occurrence path.
 */
export function getLocalChangesForEntityPath(
  state: TrackerState,
  entityId: string,
  entityPath: string
): LocalChange[] {
  const changes: LocalChange[] = [];
  for (const [, change] of state.localChanges) {
    if (change.entityId === entityId && change.entityPath === entityPath) {
      changes.push(change);
    }
  }
  return changes;
}

/**
 * Check if there are any local changes for an entity occurrence path.
 */
export function hasLocalChangesForEntityPath(
  state: TrackerState,
  entityId: string,
  entityPath: string
): boolean {
  for (const [, change] of state.localChanges) {
    if (change.entityId === entityId && change.entityPath === entityPath) {
      return true;
    }
  }
  return false;
}

/**
 * Check if a locally modified array contains a new entity with the given ID.
 * This handles the case where user adds an entity by modifying the array directly
 * (tracked as ROOT:arrayPath) rather than individual entity fields.
 *
 * @param state The tracker state
 * @param entityId The entity ID to check for (e.g., "number:24")
 * @param idField Default ID field
 * @param arrayIdFields Path-specific ID field mappings
 * @returns The local entity data if found, or null
 */
export function findLocallyAddedEntityInArrayChanges(
  state: TrackerState,
  entityId: string,
  idField: string,
  arrayIdFields: Record<string, string>
): { localEntity: any; arrayPath: string } | null {
  // Parse the entityId to get the ID field and value
  // entityId format is typically "idField:value" (e.g., "number:24")
  let entityIdField = idField;
  let entityIdValue: any = entityId;

  if (entityId.includes(':') && !entityId.startsWith('http')) {
    const colonIndex = entityId.indexOf(':');
    entityIdField = entityId.substring(0, colonIndex);
    entityIdValue = entityId.substring(colonIndex + 1);
    // Try to parse as number if it looks like one
    if (/^\d+$/.test(entityIdValue)) {
      entityIdValue = parseInt(entityIdValue, 10);
    }
  }

  // Look for array changes that might contain the entity
  // This can be ROOT-level or entity-level (e.g., entityId='1' with fieldPath='items')
  for (const [key, change] of state.localChanges) {
    const currentValue = change.currentValue;
    const originalValue = change.originalValue;

    // Check if currentValue is an array
    if (!Array.isArray(currentValue)) {
      continue;
    }

    // Find entity in currentValue
    const localEntity = currentValue.find((item: any) => {
      if (!item || typeof item !== 'object') return false;
      return item[entityIdField] === entityIdValue;
    });

    if (!localEntity) {
      continue;
    }

    // Check if this entity existed in originalValue
    const existedInOriginal =
      Array.isArray(originalValue) &&
      originalValue.some((item: any) => {
        if (!item || typeof item !== 'object') return false;
        return item[entityIdField] === entityIdValue;
      });

    // If it's in currentValue but NOT in originalValue, it was locally added
    if (!existedInOriginal) {
      // Build the full array path
      const arrayPath =
        change.entityId === 'ROOT' ? change.fieldPath : change.fieldPath; // For entity-level, fieldPath is already the array name (e.g., 'items')
      return { localEntity, arrayPath };
    }
  }

  return null;
}

/**
 * Check if there are local changes affecting a no-ID array.
 * Returns true only if the user has made changes to the specific array path itself.
 */
export function hasLocalChangesForNoIdArray(
  state: TrackerState,
  arrayPath: string
): boolean {
  // Check if any tracked change is specifically for this array path
  // or is a nested path within this array (e.g., "groups[0].name" for "groups")
  for (const [key, change] of state.localChanges) {
    // For ROOT-level changes, the fieldPath is the full path
    if (change.entityId === 'ROOT') {
      const fullFieldPath = change.fieldPath;
      // Check if the changed field is the array itself or is inside the array
      if (
        fullFieldPath === arrayPath ||
        fullFieldPath.startsWith(arrayPath + '[') ||
        fullFieldPath.startsWith(arrayPath + '.')
      ) {
        return true;
      }
      continue;
    }

    // For entity-level changes, build the full path from entity occurrences
    const entity = state.entities.get(change.entityId);
    if (entity) {
      const occurrencePaths = change.entityPath
        ? [change.entityPath]
        : Array.from(entity.occurrences.values()).map(o => o.path);

      for (const occurrencePath of occurrencePaths) {
        // Determine the full path where this change occurs
        const fullFieldPath = occurrencePath
          ? `${occurrencePath}.${change.fieldPath}`
          : change.fieldPath;

        // Check if the changed field is the array itself or is inside the array
        if (
          fullFieldPath === arrayPath ||
          fullFieldPath.startsWith(arrayPath + '[') ||
          fullFieldPath.startsWith(arrayPath + '.')
        ) {
          return true;
        }
      }
    }
  }

  return false;
}

/**
 * Process a remote update and detect conflicts/auto-merges.
 */
export function processRemoteUpdate(
  newData: any,
  remoteUserId: string,
  state: TrackerState
): ProcessUpdateResult {
  const conflicts: Conflict[] = [];
  const autoMerges: AutoMerge[] = [];
  const remoteChanges: RemoteChange[] = [];

  // Build registry from new data (using arrayIdFields for path-specific ID fields)
  const { entities: newEntities, noIdArrays: newNoIdArrays } =
    buildEntityRegistry(
      newData,
      state.idField,
      state.excludeFields,
      state.arrayIdFields,
      state.referenceArrayPaths
    );

  // ═══════════════════════════════════════════════════════════════════════════
  // Check existing entities for changes
  // ═══════════════════════════════════════════════════════════════════════════

  for (const [entityId, oldRecord] of state.entities) {
    const newRecord = newEntities.get(entityId);

    const entityIdField =
      entityId.includes(':') && !entityId.startsWith('http')
        ? entityId.substring(0, entityId.indexOf(':'))
        : state.idField;

    const oldOccurrencePaths = new Set(
      Array.from(oldRecord.occurrences.values()).map(o => o.path)
    );

    const newOccurrencePaths = new Set(
      newRecord
        ? Array.from(newRecord.occurrences.values()).map(o => o.path)
        : []
    );

    const findAffectedLocalChanges = (
      entId: string,
      entPath: string,
      fName: string
    ): LocalChange[] => {
      const affected: LocalChange[] = [];
      for (const [, change] of state.localChanges) {
        if (change.entityId !== entId) continue;
        if (change.entityPath && change.entityPath !== entPath) continue;
        if (
          change.fieldPath === fName ||
          change.fieldPath.startsWith(fName + '.')
        ) {
          affected.push(change);
        }
      }
      return affected;
    };

    // Compare existing occurrences
    for (const occurrencePath of oldOccurrencePaths) {
      const oldEntity = getValueAtPath(state.formOpenSnapshot, occurrencePath);
      const newEntity = getValueAtPath(newData, occurrencePath);

      if (!newEntity) {
        if (hasLocalChangesForEntityPath(state, entityId, occurrencePath)) {
          const conflict: EntityDeletedConflict = {
            type: 'entity_deleted',
            entityId,
            entityPath: occurrencePath,
            localChanges: getLocalChangesForEntityPath(
              state,
              entityId,
              occurrencePath
            ),
          };
          conflicts.push(conflict);
        }
        continue;
      }

      const oldFields = oldEntity
        ? extractEntityOwnFields(
            oldEntity,
            entityIdField,
            state.excludeFields,
            state.arrayIdFields,
            state.referenceArrayPaths,
            occurrencePath
          )
        : {};

      const newFields = extractEntityOwnFields(
        newEntity,
        entityIdField,
        state.excludeFields,
        state.arrayIdFields,
        state.referenceArrayPaths,
        occurrencePath
      );

      // Check each field in the new data
      for (const [fieldName, newValue] of Object.entries(newFields)) {
        const oldValue = oldFields[fieldName];

        if (!deepEqual(oldValue, newValue)) {
          const affectedLocalChanges = findAffectedLocalChanges(
            entityId,
            occurrencePath,
            fieldName
          );

          if (affectedLocalChanges.length > 0) {
            // Check if any of the affected local changes conflict with the new value
            let hasConflict = false;
            for (const localChange of affectedLocalChanges) {
              if (localChange.fieldPath === fieldName) {
                if (!deepEqual(localChange.currentValue, newValue)) {
                  hasConflict = true;
                  break;
                }
              } else {
                const nestedPath = localChange.fieldPath.slice(
                  fieldName.length + 1
                );
                let remoteNestedValue = newValue;
                for (const part of nestedPath.split('.')) {
                  remoteNestedValue = remoteNestedValue?.[part];
                }
                if (!deepEqual(localChange.currentValue, remoteNestedValue)) {
                  hasConflict = true;
                  break;
                }
              }
            }

            if (hasConflict) {
              const conflict: FieldConflict = {
                type: 'field_conflict',
                entityId,
                entityPath: occurrencePath,
                fieldPath: fieldName,
                localValue: affectedLocalChanges[0].currentValue,
                remoteValue: newValue,
                originalValue: oldValue,
              };
              conflicts.push(conflict);
            }
          } else {
            // Auto-merge: Only remote changed this field
            autoMerges.push({
              entityId,
              entityPath: occurrencePath,
              fieldPath: fieldName,
              newValue,
              oldValue,
            });
          }

          remoteChanges.push({
            entityId,
            entityPath: occurrencePath,
            fieldPath: fieldName,
            oldValue,
            newValue,
            userId: remoteUserId,
            timestamp: Date.now(),
          });
        }
      }

      // Check for fields that were removed remotely
      for (const fieldName of Object.keys(oldFields)) {
        if (!(fieldName in newFields)) {
          const affectedLocalChanges = findAffectedLocalChanges(
            entityId,
            occurrencePath,
            fieldName
          );

          if (affectedLocalChanges.length > 0) {
            const conflict: FieldConflict = {
              type: 'field_conflict',
              entityId,
              entityPath: occurrencePath,
              fieldPath: fieldName,
              localValue: affectedLocalChanges[0].currentValue,
              remoteValue: undefined,
              originalValue: oldFields[fieldName],
            };
            conflicts.push(conflict);
          } else {
            autoMerges.push({
              entityId,
              entityPath: occurrencePath,
              fieldPath: fieldName,
              newValue: undefined,
              oldValue: oldFields[fieldName],
            });
          }

          remoteChanges.push({
            entityId,
            entityPath: occurrencePath,
            fieldPath: fieldName,
            oldValue: oldFields[fieldName],
            newValue: undefined,
            userId: remoteUserId,
            timestamp: Date.now(),
          });
        }
      }
    }

    // Handle new occurrences added remotely
    for (const occurrencePath of newOccurrencePaths) {
      if (oldOccurrencePaths.has(occurrencePath)) continue;
      const newEntity = getValueAtPath(newData, occurrencePath);
      if (!newEntity) continue;

      const newFields = extractEntityOwnFields(
        newEntity,
        entityIdField,
        state.excludeFields,
        state.arrayIdFields,
        state.referenceArrayPaths,
        occurrencePath
      );

      for (const [fieldName, newValue] of Object.entries(newFields)) {
        autoMerges.push({
          entityId,
          entityPath: occurrencePath,
          fieldPath: fieldName,
          newValue,
          oldValue: undefined,
        });

        remoteChanges.push({
          entityId,
          entityPath: occurrencePath,
          fieldPath: fieldName,
          oldValue: undefined,
          newValue,
          userId: remoteUserId,
          timestamp: Date.now(),
        });
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // Check for NEW entities added remotely that conflict with locally added entities
  // This handles the case where both users add an item with the same ID
  // ═══════════════════════════════════════════════════════════════════════════

  for (const [entityId, newRecord] of newEntities) {
    // Skip if entity already existed at form open (already handled above)
    if (state.entities.has(entityId)) {
      continue;
    }

    // This is a NEW entity added remotely
    // Check two scenarios:
    // 1. Direct entity-level local changes (entityId matches)
    // 2. Array-level local changes that contain a new entity with this ID
    const hasDirectLocalChanges = hasLocalChangesForEntity(state, entityId);
    const locallyAddedInArray = findLocallyAddedEntityInArrayChanges(
      state,
      entityId,
      state.idField,
      state.arrayIdFields
    );

    if (hasDirectLocalChanges || locallyAddedInArray) {
      // Both users added an entity with the same ID - this is a conflict!

      // Get the remote entity's data
      const remoteEntity = findEntityById(
        newData,
        entityId,
        state.idField,
        state.arrayIdFields
      );

      const entityIdField =
        entityId.includes(':') && !entityId.startsWith('http')
          ? entityId.substring(0, entityId.indexOf(':'))
          : state.idField;

      const entityPath =
        newRecord.occurrences.values().next().value?.path || '';

      const remoteFields = remoteEntity
        ? extractEntityOwnFields(
            remoteEntity,
            entityIdField,
            state.excludeFields,
            state.arrayIdFields,
            state.referenceArrayPaths,
            entityPath
          )
        : {};

      if (hasDirectLocalChanges) {
        // Handle direct entity-level changes
        const localChanges = getLocalChangesForEntity(state, entityId);

        // Create conflicts for each field where local and remote differ
        for (const localChange of localChanges) {
          const fieldName = localChange.fieldPath;
          const remoteValue = remoteFields[fieldName];
          const localValue = localChange.currentValue;

          // If the values are different, it's a conflict
          if (!deepEqual(localValue, remoteValue)) {
            const conflict: FieldConflict = {
              type: 'field_conflict',
              entityId,
              fieldPath: fieldName,
              localValue,
              remoteValue,
              originalValue: undefined, // Entity didn't exist before
            };
            conflicts.push(conflict);
          }

          // Track as remote change regardless
          remoteChanges.push({
            entityId,
            fieldPath: fieldName,
            oldValue: undefined,
            newValue: remoteValue,
            userId: remoteUserId,
            timestamp: Date.now(),
          });
        }

        // Also check for fields that exist in remote but weren't locally changed
        for (const [fieldName, remoteValue] of Object.entries(remoteFields)) {
          const hasLocalChangeForField = localChanges.some(
            lc => lc.fieldPath === fieldName
          );
          if (!hasLocalChangeForField) {
            autoMerges.push({
              entityId,
              fieldPath: fieldName,
              newValue: remoteValue,
              oldValue: undefined,
            });

            remoteChanges.push({
              entityId,
              fieldPath: fieldName,
              oldValue: undefined,
              newValue: remoteValue,
              userId: remoteUserId,
              timestamp: Date.now(),
            });
          }
        }
      } else if (locallyAddedInArray) {
        // Handle array-level changes where entity was added via array modification
        const { localEntity } = locallyAddedInArray;

        // Extract local entity fields
        const localFields = extractEntityOwnFields(
          localEntity,
          entityIdField,
          state.excludeFields,
          state.arrayIdFields,
          state.referenceArrayPaths,
          entityPath
        );

        // Compare all fields between local and remote entity
        const allFieldNames = new Set([
          ...Object.keys(localFields),
          ...Object.keys(remoteFields),
        ]);

        for (const fieldName of allFieldNames) {
          const localValue = localFields[fieldName];
          const remoteValue = remoteFields[fieldName];

          if (!deepEqual(localValue, remoteValue)) {
            // Values differ - this is a conflict
            const conflict: FieldConflict = {
              type: 'field_conflict',
              entityId,
              fieldPath: fieldName,
              localValue,
              remoteValue,
              originalValue: undefined, // Entity didn't exist before
            };
            conflicts.push(conflict);
          }

          // Track as remote change
          remoteChanges.push({
            entityId,
            fieldPath: fieldName,
            oldValue: undefined,
            newValue: remoteValue,
            userId: remoteUserId,
            timestamp: Date.now(),
          });
        }
      }
    } else {
      // New entity added remotely, no local changes for it
      // This is purely an auto-merge (new entity should be added)
      const newEntity = findEntityById(
        newData,
        entityId,
        state.idField,
        state.arrayIdFields
      );

      const entityIdField =
        entityId.includes(':') && !entityId.startsWith('http')
          ? entityId.substring(0, entityId.indexOf(':'))
          : state.idField;

      const entityPath =
        newRecord.occurrences.values().next().value?.path || '';

      const newFields = newEntity
        ? extractEntityOwnFields(
            newEntity,
            entityIdField,
            state.excludeFields,
            state.arrayIdFields,
            state.referenceArrayPaths,
            entityPath
          )
        : {};

      for (const [fieldName, remoteValue] of Object.entries(newFields)) {
        autoMerges.push({
          entityId,
          fieldPath: fieldName,
          newValue: remoteValue,
          oldValue: undefined,
        });

        remoteChanges.push({
          entityId,
          fieldPath: fieldName,
          oldValue: undefined,
          newValue: remoteValue,
          userId: remoteUserId,
          timestamp: Date.now(),
        });
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // Check no-ID arrays for structural changes
  // ═══════════════════════════════════════════════════════════════════════════

  for (const [path, oldArrayRecord] of state.noIdArrays) {
    const newArrayRecord = newNoIdArrays.get(path);

    // Check if array structure changed
    const oldSignature = oldArrayRecord.signatureAtFormOpen;
    const newSignature = newArrayRecord?.signatureAtFormOpen;

    const structureChanged = !newArrayRecord || newSignature !== oldSignature;

    if (structureChanged && hasLocalChangesForNoIdArray(state, path)) {
      const conflict: NoIdArrayConflict = {
        type: 'no_id_array_conflict',
        path,
        parentEntityId: oldArrayRecord.parentEntityId,
        description: `Array at "${path}" was modified by another user`,
      };
      conflicts.push(conflict);
    }
  }

  return { conflicts, autoMerges, remoteChanges };
}

/**
 * Apply a conflict resolution to the form.
 */
export function resolveConflict(
  conflict: Conflict,
  resolution: 'local' | 'remote',
  state: TrackerState,
  setValue: (path: string, value: any, options?: any) => void,
  getValues: () => any
): void {
  if (conflict.type === 'field_conflict') {
    if (resolution === 'remote') {
      // Apply remote value to form
      const entity = state.entities.get(conflict.entityId);

      if (entity) {
        // Apply to all occurrences
        for (const [, occurrence] of entity.occurrences) {
          const fullPath = occurrence.path
            ? `${occurrence.path}.${conflict.fieldPath}`
            : conflict.fieldPath;
          setValue(fullPath, conflict.remoteValue, { shouldDirty: false });
        }
      } else if (conflict.entityId === 'ROOT') {
        // Root-level field
        setValue(conflict.fieldPath, conflict.remoteValue, {
          shouldDirty: false,
        });
      }

      // NOTE: Local changes are cleared by the CLEAR_CONFLICT reducer action
      // Don't mutate state directly here

      // Local changes for nested entities are also cleared by the CLEAR_CONFLICT reducer
      // which handles the array case immutably
    }
    // If 'local', do nothing - keep the current form value as-is
    // (Don't use conflict.localValue as it may be stale)
  } else if (conflict.type === 'entity_deleted') {
    if (resolution === 'remote') {
      // Accept deletion - remove entity from form
      // This is complex and depends on the structure
      // For now, just clear local changes
      for (const change of conflict.localChanges) {
        state.localChanges.delete(`${change.entityId}:${change.fieldPath}`);
      }
    }
    // If 'local', the entity should be restored - this requires adding it back
    // which is handled by keeping the form as-is
  } else if (conflict.type === 'no_id_array_conflict') {
    if (resolution === 'remote') {
      // Replace with remote array
      const remoteValue = state.latestServerSnapshot
        ? getValueAtPath(state.latestServerSnapshot, conflict.path)
        : undefined;
      if (remoteValue !== undefined) {
        setValue(conflict.path, remoteValue, { shouldDirty: false });
      }
    }
    // If 'local', keep the current form value - do nothing
  }
}

/**
 * Get value at a path from an object.
 */
function getValueAtPath(obj: any, path: string): any {
  const segments = path.split(/\.|\[|\]/).filter(Boolean);
  let current = obj;

  for (const segment of segments) {
    if (current === null || current === undefined) return undefined;
    current = current[segment];
  }

  return current;
}

/**
 * Create a merged data object that applies auto-merges.
 */
export function applyAutoMerges(
  formData: any,
  autoMerges: AutoMerge[],
  state: TrackerState
): any {
  const merged = deepClone(formData);

  for (const merge of autoMerges) {
    const entity = state.entities.get(merge.entityId);

    if (entity) {
      // Apply to all occurrences
      for (const [, occurrence] of entity.occurrences) {
        const fullPath = occurrence.path
          ? `${occurrence.path}.${merge.fieldPath}`
          : merge.fieldPath;
        setValueAtPath(merged, fullPath, merge.newValue);
      }
    } else if (merge.entityId === 'ROOT') {
      setValueAtPath(merged, merge.fieldPath, merge.newValue);
    }
  }

  return merged;
}

/**
 * Set value at a path in an object (mutates).
 */
function setValueAtPath(obj: any, path: string, value: any): void {
  const segments = path.split(/\.|\[|\]/).filter(Boolean);
  let current = obj;

  for (let i = 0; i < segments.length - 1; i++) {
    const segment = segments[i];
    if (current[segment] === undefined) {
      current[segment] = /^\d+$/.test(segments[i + 1]) ? [] : {};
    }
    current = current[segment];
  }

  const lastSegment = segments[segments.length - 1];
  current[lastSegment] = value;
}
