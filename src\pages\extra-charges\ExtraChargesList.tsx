import { Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  CreateButton,
  DataTable,
  FunctionField,
  List,
  TopToolbar,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import {
  RESOURCES,
  resourcesInfo,
  useGetListExtraChargesLive,
} from '~/providers/resources';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import { useTheme } from '../../contexts';

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ textAlign: 'center' }}>
      <LocationAndDateSelectors isDate={false} hideShadow />
      <img
        src="/assets/icons/extra-charges.svg"
        width="75px"
        style={{ marginTop: '32px' }}
      />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('extraCharges.noExtraChargesYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('extraCharges.noExtraChargesYetDescription')}
      </Typography>
      <CreateButton
        variant="contained"
        label={t('extraCharges.createExtraCharge')}
      />
    </div>
  );
};

export default function ExtraChargesList(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { t } = useTranslation();
  const { theme } = useTheme();

  if (!sellPointId) {
    return null;
  }

  const { data: extraCharges } = useGetListExtraChargesLive({
    meta: { sellPointId: sellPointId },
  });
  const isSmall = useMediaQuery(theme => theme.breakpoints.down('sm'));

  return (
    <>
      <List
        resource={RESOURCES.EXTRA_CHARGES}
        sort={resourcesInfo[RESOURCES.EXTRA_CHARGES].defaultSort}
        pagination={false}
        perPage={Number.MAX_SAFE_INTEGER}
        component="div"
        exporter={false}
        actions={false}
        empty={<CustomEmpty />}
        queryOptions={{ meta: { sellPointId: sellPointId } }}
        sx={{
          '& .RaFilterFormInput-spacer': {
            display: { xs: 'none', md: 'block' },
          },
        }}
      >
        <TopToolbar
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 0,
          }}
        >
          <LocationAndDateSelectors isDate={false} hideShadow />
          <CreateButton
            variant="contained"
            label={t('extraCharges.createExtraCharge')}
            {...(isXSmall ? {} : { icon: <></> })}
          />
        </TopToolbar>
        {isSmall ? (
          <MobileGrid>
            <MobileCard hasImage={false}>
              <DataTable.Col
                source="charge.fixed.value"
                label={t('shared.price')}
              >
                <FunctionField
                  textAlign="right"
                  render={record => {
                    return (
                      <>{formatAndDivideNumber(record?.charge?.fixed?.value)}</>
                    );
                  }}
                />
              </DataTable.Col>

              <DataTable.Col source="charge.fixed.vat" label={t('shared.tva')}>
                <FunctionField
                  textAlign="right"
                  render={record => {
                    return <>{record?.charge?.fixed?.vat}%</>;
                  }}
                />
              </DataTable.Col>
            </MobileCard>
          </MobileGrid>
        ) : (
          <DataTable
            rowClick={(_, __, row) => {
              redirect('edit', RESOURCES.EXTRA_CHARGES, row.id, row, {
                _scrollToTop: false,
              });
              return false;
            }}
            bulkActionButtons={false}
            sx={{
              marginTop: '10px',
              '& .RaDataTable-headerCell': {
                backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
                borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
              },
              '& .MuiTableCell-root:last-of-type': {
                textAlign: 'right',
                '& button': {
                  visibility: 'visible',
                },
              },
            }}
          >
            <DataTable.Col source="name" label={t('shared.name')} />

            <DataTable.Col
              source="charge.fixed.value"
              label={t('shared.price')}
            >
              <FunctionField
                textAlign="right"
                render={record => {
                  return (
                    <>{formatAndDivideNumber(record?.charge?.fixed?.value)}</>
                  );
                }}
              />
            </DataTable.Col>

            <DataTable.Col source="charge.fixed.vat" label={t('shared.tva')}>
              <FunctionField
                textAlign="right"
                render={record => {
                  return <>{record?.charge?.fixed?.vat}%</>;
                }}
              />
            </DataTable.Col>

            <DataTable.Col label={t('prepStations.actions')} align="right">
              <ActionsField
                hasEdit
                hasDelete
                deleteMutationMode="pessimistic"
                deleteMutationOptions={{ meta: { sellPointId: sellPointId } }}
              />
            </DataTable.Col>
          </DataTable>
        )}

        <ListLiveUpdate />
      </List>
    </>
  );
}
