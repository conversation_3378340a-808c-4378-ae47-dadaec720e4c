/**
 * Text Input Sanitization Utility
 *
 * Removes invisible characters and normalizes whitespace to prevent
 * issues with copy-pasted content from PDFs, Excel, Word, etc.
 *
 * @module sanitizeTextInput
 */

export interface SanitizationOptions {
    /**
     * Remove leading and trailing whitespace
     * @default true
     */
    trimWhitespace?: boolean;

    /**
     * Remove invisible Unicode characters (zero-width spaces, etc.)
     * @default true
     */
    removeInvisibleChars?: boolean;

    /**
     * Normalize multiple consecutive spaces to single space
     * @default true
     */
    normalizeSpaces?: boolean;

    /**
     * Preserve line breaks in multiline text
     * @default false
     */
    preserveLineBreaks?: boolean;

    /**
     * Convert to uppercase after sanitization
     * @default false
     */
    uppercase?: boolean;

    /**
     * Convert to lowercase after sanitization
     * @default false
     */
    lowercase?: boolean;
}

/**
 * Default sanitization options
 */
const DEFAULT_OPTIONS: Required<SanitizationOptions> = {
    trimWhitespace: true,
    removeInvisibleChars: true,
    normalizeSpaces: true,
    preserveLineBreaks: false,
    uppercase: false,
    lowercase: false,
};

/**
 * Sanitizes text input by removing invisible characters and normalizing whitespace
 *
 * This function is designed to handle text pasted from PDFs, Excel, Word, HTML, and
 * other external sources that may contain invisible Unicode characters while preserving
 * all visible characters including emojis, accents, and international scripts.
 *
 * @param value - The input value to sanitize (only strings are processed)
 * @param options - Sanitization options
 * @returns Sanitized string or original value if not a string
 *
 * @example
 * // Basic usage - removes invisible characters and trims
 * sanitizeTextInput('  Hello\u200BWorld  '); // 'Hello World'
 *
 * @example
 * // Preserves emojis
 * sanitizeTextInput('Hello 😀 World'); // 'Hello 😀 World'
 *
 * @example
 * // Preserves accents
 * sanitizeTextInput('Café Münchën'); // 'Café Münchën'
 *
 * @example
 * // Preserves international characters
 * sanitizeTextInput('Здравей สวัสดี Γεια'); // 'Здравей สวัสดี Γεια'
 *
 * @example
 * // Removes PDF artifacts
 * sanitizeTextInput('Prod\u00ADuct\u200BName'); // 'ProductName'
 *
 * @example
 * // Uppercase option
 * sanitizeTextInput('  hello  world  ', { uppercase: true }); // 'HELLO WORLD'
 *
 * @example
 * // Multiline text
 * sanitizeTextInput('Line 1\nLine 2', { preserveLineBreaks: true }); // 'Line 1\nLine 2'
 *
 * @example
 * // Real-world PDF paste example
 * sanitizeTextInput('  Product\u200BName\u00AD\u200E  '); // 'ProductName'
 */
export function sanitizeTextInput(
    value: any,
    options: SanitizationOptions = {}
): any {
    // Only sanitize strings - return other types as-is
    if (typeof value !== 'string') {
        return value;
    }

    // If empty string, return as-is
    if (value === '') {
        return value;
    }

    const opts = { ...DEFAULT_OPTIONS, ...options };
    let sanitized = value;

    // Step 1: Remove invisible characters
    if (opts.removeInvisibleChars) {
        sanitized = sanitized
            // Control characters (C0 and C1 controls, except tab/newline if preserving)
            // Removes: null, backspace, etc. - DOES NOT affect emojis or international chars
            .replace(
                opts.preserveLineBreaks
                    ? /[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g
                    : /[\u0000-\u0008\u000B-\u001F\u007F-\u009F]/g,
                ''
            )
            // Non-breaking space → regular space
            .replace(/\u00A0/g, ' ')
            // Soft hyphen (invisible hyphen suggestion)
            .replace(/\u00AD/g, '')
            // Mongolian vowel separator (deprecated but still in use)
            .replace(/\u180E/g, '')
            // Zero-width characters
            .replace(/[\u200B-\u200D]/g, '') // Zero-width space, non-joiner, joiner
            .replace(/\u200E/g, '') // Left-to-right mark
            .replace(/\u200F/g, '') // Right-to-left mark
            // Line and paragraph separators
            .replace(/[\u2028\u2029]/g, opts.preserveLineBreaks ? '\n' : ' ')
            // Directional formatting (can cause display issues)
            .replace(/[\u202A-\u202E]/g, '') // Embedding and override marks
            // Word joiner (like zero-width non-breaking space)
            .replace(/\u2060/g, '')
            // Invisible math operators
            .replace(/[\u2061-\u2064]/g, '')
            // Deprecated format characters
            .replace(/[\u206A-\u206F]/g, '')
            // Various Unicode spaces → regular space
            // Includes: en quad, em quad, thin space, hair space, ideographic space, etc.
            .replace(/[\u1680\u2000-\u200A\u202F\u205F\u3000]/g, ' ')
            // Byte order mark (BOM) - can appear at start of pasted content
            .replace(/\uFEFF/g, '');
    }

    // Step 2: Normalize internal spaces
    if (opts.normalizeSpaces) {
        if (opts.preserveLineBreaks) {
            // Normalize spaces on each line separately
            sanitized = sanitized
                .split(/\r?\n/)
                .map(line => line.replace(/\s+/g, ' '))
                .join('\n');
        } else {
            // Normalize all whitespace to single spaces
            sanitized = sanitized.replace(/\s+/g, ' ');
        }
    }

    // Step 3: Trim leading/trailing whitespace
    if (opts.trimWhitespace) {
        if (opts.preserveLineBreaks) {
            // Trim each line separately
            sanitized = sanitized
                .split(/\r?\n/)
                .map(line => line.trim())
                .join('\n');
            // Also trim the entire string
            sanitized = sanitized.trim();
        } else {
            sanitized = sanitized.trim();
        }
    }

    // Step 4: Apply case transformations
    if (opts.uppercase) {
        sanitized = sanitized.toUpperCase();
    } else if (opts.lowercase) {
        sanitized = sanitized.toLowerCase();
    }

    return sanitized;
}

/**
 * Sanitization preset for single-line inputs (default)
 */
export const SINGLE_LINE_PRESET: SanitizationOptions = {
    trimWhitespace: false,
    removeInvisibleChars: true,
    normalizeSpaces: true,
    preserveLineBreaks: false,
};

/**
 * Sanitization preset for multi-line inputs (textarea, descriptions)
 */
export const MULTI_LINE_PRESET: SanitizationOptions = {
    trimWhitespace: true,
    removeInvisibleChars: true,
    normalizeSpaces: true,
    preserveLineBreaks: true,
};

/**
 * Sanitization preset for code/SKU/identifiers (uppercase, strict)
 */
export const IDENTIFIER_PRESET: SanitizationOptions = {
    trimWhitespace: true,
    removeInvisibleChars: true,
    normalizeSpaces: true,
    preserveLineBreaks: false,
    uppercase: true,
};

/**
 * No sanitization (pass-through)
 */
export const NO_SANITIZATION: SanitizationOptions = {
    trimWhitespace: false,
    removeInvisibleChars: false,
    normalizeSpaces: false,
    preserveLineBreaks: false,
};
