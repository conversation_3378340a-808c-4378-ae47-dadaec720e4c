import { FileUploadConfig } from '../types';
import { UploadedFile } from '../types/fileUpload';
import { formatFileSize } from './fileSizeUtils';

// Re-export for convenience
export { formatFileSize };

/**
 * Generate a unique ID for files
 */
export const generateFileId = (file: File): string => {
    return `${file.name}-${file.size}-${file.lastModified}`;
};

/**
 * Check if a file is an image
 */
export const isImageFile = (file: File): boolean => {
    return file?.type?.startsWith('image/') || false;
};

/**
 * Check if a file is a video
 */
export const isVideoFile = (file: File): boolean => {
    return file?.type?.startsWith('video/') || false;
};

/**
 * Check if a file can be previewed
 */
export const canPreviewFile = (file: File): boolean => {
    return (
        isImageFile(file) || isVideoFile(file) || file.type === 'application/pdf'
    );
};

/**
 * Get file extension from filename
 */
export const getFileExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
};

// formatFileSize moved to fileSizeUtils.ts to avoid duplication

/**
 * Convert file type to internal code
 */
export const mapFileTypeToCode = (
    fileType: FileUploadConfig['fileType']
): 'i' | 'v' | 's' | 'p' => {
    const mapping = {
        images: 'i' as const,
        videos: 'v' as const,
        public: 's' as const,
        private: 'p' as const,
    };

    return mapping[fileType];
};

/**
 * Get MIME type from file extension
 */
export const getMimeTypeFromExtension = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
        // Images
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        webp: 'image/webp',
        bmp: 'image/bmp',
        svg: 'image/svg+xml',

        // Videos
        mp4: 'video/mp4',
        webm: 'video/webm',
        mov: 'video/quicktime',
        avi: 'video/x-msvideo',
        mkv: 'video/x-matroska',

        // Documents
        pdf: 'application/pdf',
        doc: 'application/msword',
        docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        xls: 'application/vnd.ms-excel',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ppt: 'application/vnd.ms-powerpoint',
        pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        txt: 'text/plain',
        csv: 'text/csv',

        // Archives
        zip: 'application/zip',
        rar: 'application/x-rar-compressed',
        '7z': 'application/x-7z-compressed',
        tar: 'application/x-tar',
        gz: 'application/gzip',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
};

/**
 * Get file extensions from MIME types
 * Shared between FileDropzone and FileUploadComponent for consistent messaging
 */
export const getFileExtensions = (acceptedTypes: string[]): string[] => {
    if (acceptedTypes.length === 0) return [];

    const mimeToExtension: Record<string, string[]> = {
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/webp': ['.webp'],
        'image/gif': ['.gif'],
        'image/svg+xml': ['.svg'],
        'image/bmp': ['.bmp'],
        'image/tiff': ['.tiff', '.tif'],
        'application/pdf': ['.pdf'],
        'text/plain': ['.txt'],
        'text/csv': ['.csv'],
        'application/json': ['.json'],
        'application/xml': ['.xml'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            ['.docx'],
        'application/vnd.ms-excel': ['.xls'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
            '.xlsx',
        ],
        'video/mp4': ['.mp4'],
        'video/webm': ['.webm'],
        'video/ogg': ['.ogv'],
        'audio/mp3': ['.mp3'],
        'audio/wav': ['.wav'],
        'audio/ogg': ['.ogg'],
    };

    const extensions: string[] = [];
    acceptedTypes.forEach(mimeType => {
        const exts = mimeToExtension[mimeType];
        if (exts) {
            extensions.push(...exts);
        }
    });

    return [...new Set(extensions)]; // Remove duplicates
};

/**
 * Check if file type is accepted
 */
export const isFileTypeAccepted = (
    file: File,
    acceptedTypes: string[]
): boolean => {
    if (!file?.type) return false;
    return (
        acceptedTypes.includes(file.type) ||
        acceptedTypes.some(
            type => type.endsWith('/*') && file.type.startsWith(type.slice(0, -1))
        )
    );
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};

/**
 * Validate uploaded file structure
 */
export const isValidUploadedFile = (file: any): file is UploadedFile => {
    return (
        file &&
        typeof file === 'object' &&
        typeof file.f === 'string' &&
        typeof file.e === 'string' &&
        typeof file.t === 'string' &&
        ['i', 'v', 's', 'p'].includes(file.t) &&
        (file.rn === undefined || typeof file.rn === 'string') &&
        (file.x === undefined || typeof file.x === 'boolean') &&
        (file.url === undefined || typeof file.url === 'string')
    );
};
