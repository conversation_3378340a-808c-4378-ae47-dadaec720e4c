import path from 'path';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig, loadEnv } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';
import svgr from 'vite-plugin-svgr';

import { APP_VERSION } from './version';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode, isSsrBuild, isPreview }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '');

  // Only enable bundle analyzer when explicitly requested via ANALYZE env var
  const shouldAnalyze = process.env.ANALYZE === 'true';

  return {
    plugins: [
      svgr(),
      react(),
      VitePWA({
        registerType: 'prompt', // Changed from 'autoUpdate' - manual control for authenticated users only
        injectRegister: false, // Don't inject registerSW.js - we'll register manually in main.tsx
        includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'mask-icon.svg'],
        manifest: {
          name: 'SELIO Manager',
          short_name: 'SELIO',
          description: 'SELIO Manager - POS Management System',
          theme_color: '#000000',
          background_color: '#ffffff',
          display: 'standalone', // This removes the browser UI
          orientation: 'portrait',
          scope: '/',
          start_url: '/',
          icons: [
            // Light theme icons (default)
            {
              src: 'pwa-192x192.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'any',
            },
            {
              src: 'pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any',
            },
            {
              src: 'pwa-maskable-192x192.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'maskable',
            },
            {
              src: 'pwa-maskable-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'maskable',
            },
            // Dark theme icons
            {
              src: 'pwa-192x192-dark.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'any',
            },
            {
              src: 'pwa-512x512-dark.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any',
            },
            {
              src: 'pwa-maskable-192x192-dark.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'maskable',
            },
            {
              src: 'pwa-maskable-512x512-dark.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'maskable',
            },
          ],
        },
        workbox: {
          // MINIMAL precaching - only what's absolutely necessary for initial page load
          // Gateway pattern means authenticated content loads on-demand

          // Manual control: specify exactly what to precache
          globPatterns: ['**/*.{html,css,webmanifest}'], // Only HTML, CSS, manifest

          // Exclude everything else from precaching
          globIgnores: [
            '**/*.js', // No JavaScript - loads on demand
            '**/*.map', // No source maps
            '**/*.svg', // No SVG icons - loads on demand with app
            '**/*.png', // No PNG images - loads on demand
            '**/*.jpg', // No JPG images
            '**/*.jpeg', // No JPEG images
            '**/*.woff', // No fonts - loads on demand
            '**/*.woff2', // No fonts - loads on demand
            '**/*.ico', // No favicon - browser handles this
          ],

          // Don't create an offline fallback page since the app requires internet
          navigateFallback: undefined,
          runtimeCaching: [
            {
              // Cache Google Fonts for performance
              urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
              handler: 'StaleWhileRevalidate', // Faster response, still checks for updates
              options: {
                cacheName: 'google-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              // Cache CDN resources for performance
              urlPattern: /^https:\/\/cdn\.jsdelivr\.net\/.*/i,
              handler: 'StaleWhileRevalidate', // Faster response, still checks for updates
              options: {
                cacheName: 'cdn-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            // Explicitly NOT caching Firebase or API calls
            // App will always require internet for functionality
          ],
        },
        devOptions: {
          enabled: false, // Disable in dev for faster development
        },
      }),
      {
        name: 'html-transform',
        transformIndexHtml: {
          order: 'post',
          handler(html, ctx) {
            // Add app version meta tag
            html = html.replace(
              /<\/head>/,
              `  <meta name="app-version" content="${APP_VERSION}" />\n  </head>`
            );

            // Remove manifest link - injected dynamically for authenticated users only
            html = html.replace(
              /<link rel="manifest"[^>]*>/g,
              '<!-- Manifest injected dynamically for authenticated users only -->'
            );

            // Inject gateway script path into interaction detector
            if (ctx.bundle) {
              const gatewayFile = Object.keys(ctx.bundle).find(
                file => file.includes('index-init') && file.endsWith('.js')
              );

              if (gatewayFile) {
                html = html.replace(
                  /\/assets\/index-init-PLACEHOLDER\.js/g,
                  `/${gatewayFile}`
                );
              }
            }

            return html;
          },
        },
      },
      {
        name: 'gateway-main-path-injection',
        generateBundle(options, bundle) {
          // Inject actual main bundle path into gateway script
          const mainFile = Object.keys(bundle).find(
            file =>
              file.startsWith('assets/main-') &&
              file.endsWith('.js') &&
              !file.includes('init')
          );

          const gatewayFile = Object.keys(bundle).find(
            file => file.includes('index-init') && file.endsWith('.js')
          );

          if (mainFile && gatewayFile) {
            const gateway = bundle[gatewayFile];
            if (gateway.type === 'chunk') {
              gateway.code = gateway.code.replace(
                /\/assets\/main-PLACEHOLDER\.js/g,
                `/${mainFile}`
              );
            }
          }
        },
      },
      // Conditionally add visualizer plugin
      ...(shouldAnalyze
        ? [
            visualizer({
              open: true,
              gzipSize: true,
              brotliSize: true,
              filename: 'dist/stats.html',
            }),
          ]
        : []),
    ],
    define: {
      //      'process.env': process.env,
      __APP_VERSION__: JSON.stringify(APP_VERSION),
    },
    esbuild: {
      keepNames: true,
    },
    build: {
      chunkSizeWarningLimit: 2000,
      modulePreload: {
        polyfill: false,
        resolveDependencies: () => [],
      },
      rollupOptions: {
        input: {
          index: path.resolve(__dirname, 'index.html'),
          auth: path.resolve(__dirname, 'auth.html'),
          'index-init': path.resolve(__dirname, 'src/gateway/index-init.ts'),
          main: path.resolve(__dirname, 'src/main.tsx'),
        },
        // Suppress warnings for Node.js built-ins that are shimmed/externalized
        onwarn(warning, warn) {
          // Ignore "Module externalized for browser compatibility" warnings
          if (
            warning.code === 'MODULE_LEVEL_DIRECTIVE' ||
            (warning.message &&
              warning.message.includes(
                'externalized for browser compatibility'
              ))
          ) {
            return;
          }
          warn(warning);
        },
        output: {
          // Optimized chunking for parallel downloads with consistent naming
          // Pattern: chunk-<category>-<name> for better organization
          manualChunks: {
            // React Core - loaded by main app (~200KB)
            'chunk-react-core': [
              'react',
              'react-dom',
              'react/jsx-runtime',
              'react-router',
              'react-router-dom',
            ],
            // MUI + Emotion ecosystem (~700KB)
            'chunk-react-mui': [
              '@mui/material',
              '@mui/icons-material',
              '@mui/system',
              '@mui/x-date-pickers',
              '@mui/x-date-pickers-pro',
              '@mui/x-license',
              '@emotion/react',
              '@emotion/styled',
              '@emotion/cache',
            ],
            // Small React UI components (~100KB)
            'chunk-react-ui': [
              'react-helmet',
              'react-image-crop',
              'react-to-print',
              'mui-tel-input',
              'qrcode.react',
            ],
            // React Admin (~700KB)
            'chunk-react-admin': [
              'react-admin',
              'ra-core',
              'ra-ui-materialui',
              'ra-data-fakerest',
              '@react-admin/ra-editable-datagrid',
              '@react-admin/ra-form-layout',
              '@react-admin/ra-navigation',
              '@react-admin/ra-realtime',
              '@react-admin/ra-relationships',
              '@react-admin/ra-search',
            ],
            // Firebase CORE - shared by gateway and main app (~120KB)
            'chunk-firebase-core': [
              'firebase/app',
              'firebase/auth',
              'firebase/app-check',
            ],
            // Firebase SERVICES - only main app (~500KB)
            'chunk-firebase-services': [
              'firebase/firestore',
              'firebase/storage',
              'firebase/database',
              'firebase/functions',
            ],
            // Charts & Visualizations (~550KB)
            'chunk-viz-charts': [
              'chart.js',
              'react-chartjs-2',
              'recharts',
              '@fnando/sparkline',
            ],
            // Lodash utility (~80KB)
            'chunk-util-lodash': ['lodash'],
            // i18n (~50KB)
            'chunk-util-i18n': ['i18next', 'react-i18next'],
            // Date libraries (~30KB)
            'chunk-util-date': ['date-fns-v4', 'dayjs'],
            // DnD libraries (~160KB)
            'chunk-util-dnd': [
              '@dnd-kit/core',
              '@dnd-kit/modifiers',
              '@dnd-kit/sortable',
              '@hello-pangea/dnd',
            ],
            // Pure utilities (~300KB)
            'chunk-util-misc': [
              'localforage',
              'crypto-js',
              'fast-deep-equal',
              'fast-xml-parser',
              'jsonexport',
              'jszip',
              'qrcode',
              'remove-accents',
              'fakerest',
              'vanilla-cookieconsent',
              '@googlemaps/js-api-loader',
              '@tanstack/react-query-devtools',
            ],
          },
        },
      },
    },
    resolve: {
      preserveSymlinks: true,
      alias: {
        '~': path.resolve(__dirname, 'src'),
      },
    },
    appType: 'mpa', // Multi-page app - allows serving both index.html and auth.html
    //envPrefix: "SELIO_",
    worker: {
      format: 'es',
    },
  };
});
