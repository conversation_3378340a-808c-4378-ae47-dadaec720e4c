import { ReactNode } from 'react';
import { Box, Typography } from '@mui/material';
import { BooleanInputProps } from 'react-admin';

import { SwitchInput } from './SwitchInput';

export interface ModernBooleanInputProps extends Omit<
  BooleanInputProps,
  'label'
> {
  /** Main label displayed prominently */
  label?: string;
  /** Description text displayed below the label */
  description?: string;
  /** Optional badge/tag component to display next to the label */
  badge?: ReactNode;
}

/**
 * ModernBooleanInput - A styled boolean/switch input with label, description, and optional badge
 *
 * Features:
 * - Label displayed prominently with medium font weight
 * - Optional badge/tag next to label (e.g., "New Feature" tag)
 * - Description text below label with lighter weight
 * - Switch aligned to the right
 * - Responsive layout - stacks on mobile
 *
 * @example
 * <CustomInput
 *   type="modernBoolean"
 *   source="excludedFromDiscount"
 *   label="Excluded from Discount"
 *   description="Automatically protects key menu items from any discount"
 *   badge={<NewFeatureTag />}
 * />
 */
export const ModernBooleanInput = ({
  source,
  label,
  description,
  badge,
  ...props
}: ModernBooleanInputProps) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        gap: 1,
        overflow: 'visible',
      }}
    >
      {/* First row: label, badge, and switch - all centered */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'space-between',
          gap: 2,
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 1, md: 2 },
            flexWrap: 'wrap',
          }}
        >
          {label && <Typography fontWeight={500}>{label}</Typography>}
          {badge}
        </Box>
        <Box
          sx={{
            flexShrink: 0,
            display: 'flex',
            alignItems: 'flex-start',
            marginTop: { xs: '-10px', sm: '-8px' },
          }}
        >
          <SwitchInput source={source!} label="" {...props} />
        </Box>
      </Box>

      {/* Second row: description */}
      {description && (
        <Typography
          variant="body2"
          fontWeight={100}
          color="text.secondary"
          sx={{ width: '100%' }}
        >
          {description}
        </Typography>
      )}
    </Box>
  );
};

export default ModernBooleanInput;
