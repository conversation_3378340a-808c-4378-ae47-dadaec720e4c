import { useEffect, useMemo, useRef, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { FilterProvider } from '~/contexts/FilterContext';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import capitalize from '~/utils/capitalize';
import { OptionType } from '../reports/components/FilterItem';
import ReportDateTitle from '../reports/components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../reports/components/ReportFilters';
import { SourceTypes } from '../reports/report-sales-revenue/SalesRevenue';
import TransactionsCards from '../reports/report-transactions/components/TransactionsCards';
import { useGetListLive } from '@react-admin/ra-realtime';
import { useGetListLocationsLive } from '~/providers/resources';
import { downloadCSV } from 'react-admin';
import InvoicesTable from './InvoicesTable';
import InvoicesCards from './InvoicesCards';

const REPORT_TYPE = 'invoices';

const fieldsConstant = ['id'];

export default function InvoicesOverviewPage() {
  const { details: fbDetails } = useFirebase();
  const { t } = useTranslation('');
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [rawData, setRawData] = useState<any>();
  const [currency, setCurrency] = useState<any>();
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = (key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  const contentRef = useRef<HTMLDivElement>(null);


  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });

        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });

        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el, 
          });
        });

        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });

        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const { tableData } = useMemo(() => {
    if (!rawData || !filters)
      return { tableData: [], graphData: {}, composedFilters: [] };

    if (rawData.length) {
      setCurrency(rawData[0].currency);
    }

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      fieldsConstant
    );

    const sorted = groupedTableData[0]?.report?.sort((a, b) => {
      const aTime = a.closedAt || 0;
      const bTime = b.closedAt || 0;
      return bTime - aTime;
    });

    const tableData = sorted || [];

    return { tableData };
  }, [rawData, filters]);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    console.log(tableData);
    const title = 'Report invoices';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Bill Date',
        'Series and Number',
        'Emitted by',
        'Bill Name',
        'Fiscal Receipt',
        'Bill Payments Value',
      ].join(','),
      ...tableData.map(el =>
        [
          el.billDate,
          el.series + '-' + el.number,
          el.createdBy,
          el.billName,
          el.billFiscalNumber,
          el.billPaymentsValue / 10000 || 0,
        ].join(',')
      ),
    ].join('\n');
    downloadCSV(csvContent, 'invoices');
  };


  return (
    <Box sx={{ p: 2 }}>
      <FilterProvider>
      <Box
        sx={{
          width: '100%',
          px: { xs: 2, sm: 12 },
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
            px: 0,
          },
        }}
        p={2}
        ref={contentRef}
      >
        <ChangeViewportBtn />
        <PageTitle
          sx={{
            maxWidth: '700px !important',
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
          title={t('menu.invoices')}
          description={
            <>
              {t('menu.invoicesDescription')}
              <a href="https://selio.io/support-center" target="_blank">
                {t('support.support-link')}
              </a>
            </>
          }
          hideBorder
          doNotPrint
        />
        <ReportFilters
          defaultValues={defaultValues}
          onFiltersChange={setFilters}
          commonFields={commonFields}
          contentRef={contentRef}
          handleExport={handleExport}
        />
        <ReportDateTitle />
        <InvoicesCards tableData={tableData} />
        <InvoicesTable currency={currency} tableData={tableData || []} />
      </Box>
    </FilterProvider>
    </Box>
  );
}
