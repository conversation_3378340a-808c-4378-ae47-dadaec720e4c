// src/components/organisms/FileUpload/core/FileUploadManager.ts
import {
  deleteObject,
  getBlob,
  getDownloadURL,
  getMetadata,
  listAll,
  ref,
  uploadBytes,
} from 'firebase/storage';
import { cloneDeep, get, set } from 'lodash';

import {
  clearFilePreviewCacheForFile,
  clearFileUrlCache,
} from '~/components/organisms/FileUpload';
import { StandardImageSize } from '~/configs/imageSize';
import { generateUniqueId } from '~/utils/generateUniqueId';
import {
  IN_MEMORY_FILE_CONFIG,
  InMemoryFileData,
  InMemoryFileMetadata,
  PrivateFileContext,
  shouldCheckForVariants,
  UploadedFile,
} from '../types/fileUpload';
import {
  constructFullFilename,
  getStorageInstanceForFile,
} from '../utils/bucketManager';
import {
  ProcessedImageResult,
  processImageWithVariants,
} from '../utils/clientImageProcessor';
import {
  extractFileExtension,
  removeFileExtension,
  // bytesToKB, // removed - no longer needed as size field eliminated
} from '../utils/fileUtils';
import { generateFileUrl, getDestinationPath } from '../utils/urlGeneration';
import { inMemoryFileManager } from './InMemoryFileManager';

export interface FileLifecycleCallbacks {
  onFileUploaded?: (file: UploadedFile) => void;
  onFileMoved?: (file: UploadedFile) => void;
  onFileDeleted?: (file: UploadedFile) => void;
}

export const fileUploadManager = {
  // Private state
  callbacks: {} as FileLifecycleCallbacks,
  accountId: '',
  uploadedBy: '',
  isInitialized: false,

  // ===== SINGLETON SETUP =====

  /**
   * Initialize the singleton with context from FirebaseContext
   */
  initialize(
    accountId: string,
    uploadedBy: string,
    callbacks?: FileLifecycleCallbacks
  ): void {
    // Skip if already initialized with the same values
    if (
      this.isInitialized &&
      this.accountId === accountId &&
      this.uploadedBy === uploadedBy
    ) {
      return;
    }

    this.accountId = accountId;
    this.uploadedBy = uploadedBy;
    this.callbacks = callbacks || {};
    this.isInitialized = true;

    // Clear cache when reinitializing (account change)
    this.clearUrlCache();
  },

  /**
   * Reset the singleton (on logout or account change)
   */
  reset(): void {
    this.accountId = '';
    this.uploadedBy = '';
    this.callbacks = {};
    this.isInitialized = false;
    this.clearUrlCache();
  },

  /**
   * Update context without full reinitialization
   */
  updateContext(accountId?: string, uploadedBy?: string): void {
    if (accountId && accountId !== this.accountId) {
      this.accountId = accountId;
      this.clearUrlCache(); // Clear cache on account change
    }
    if (uploadedBy && uploadedBy !== this.uploadedBy) {
      this.uploadedBy = uploadedBy;
    }
  },

  /**
   * Get the current initialization state (for debugging)
   */
  getState(): {
    isInitialized: boolean;
    accountId: string;
    uploadedBy: string;
  } {
    return {
      isInitialized: this.isInitialized,
      accountId: this.accountId,
      uploadedBy: this.uploadedBy,
    };
  },

  /**
   * Check if manager is properly initialized
   */
  ensureInitialized(): void {
    if (!this.isInitialized || !this.accountId || !this.uploadedBy) {
      throw new Error(
        'FileUploadManager not properly initialized. ' +
          'Make sure FirebaseContext has initialized it with valid accountId and uploadedBy.'
      );
    }
  },

  // ===== CORE FILE OPERATIONS =====

  // ===== IN-MEMORY FILE OPERATIONS =====

  /**
   * Create an in-memory file (replaces uploadToTemp)
   * Keeps file in memory as blob instead of uploading to temp bucket
   * Automatically creates thumbnails for image files
   */
  async createInMemoryFile(
    file: File,
    fileType: 'i' | 'v' | 's' | 'p' = 'i'
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    // Check memory limits before proceeding
    const stats = inMemoryFileManager.getMemoryUsage();
    if (stats.totalSize + file.size > IN_MEMORY_FILE_CONFIG.maxMemoryUsage) {
      throw new Error(
        `File too large for in-memory storage. Current usage: ${(stats.totalSize / 1024 / 1024).toFixed(1)}MB, File size: ${(file.size / 1024 / 1024).toFixed(1)}MB, Limit: ${(IN_MEMORY_FILE_CONFIG.maxMemoryUsage / 1024 / 1024).toFixed(1)}MB`
      );
    }

    if (file.size > IN_MEMORY_FILE_CONFIG.maxFileSize) {
      throw new Error(
        `File size exceeds maximum allowed: ${(file.size / 1024 / 1024).toFixed(1)}MB > ${(IN_MEMORY_FILE_CONFIG.maxFileSize / 1024 / 1024).toFixed(1)}MB`
      );
    }

    if (stats.fileCount >= IN_MEMORY_FILE_CONFIG.maxConcurrentFiles) {
      throw new Error(
        `Too many files in memory: ${stats.fileCount} >= ${IN_MEMORY_FILE_CONFIG.maxConcurrentFiles}`
      );
    }

    const extension = extractFileExtension(file.name);
    const realNameWithoutExt = removeFileExtension(file.name);
    const uniqueFileName = generateUniqueId();

    // Convert file to blob (File extends Blob, so this is essentially a copy)
    const blob = new Blob([file], { type: file.type });

    // Create metadata
    const metadata: InMemoryFileMetadata = {
      originalFileName: file.name,
      uploadedBy: this.uploadedBy,
      accountId: this.accountId,
      uploadTimestamp: Date.now(),
      fileSize: file.size,
      mimeType: file.type,
    };

    // For image files, automatically create a thumbnail
    let variants: Map<string, Blob> | undefined;
    if (file.type.startsWith('image/')) {
      try {
        const processed = await processImageWithVariants(file, [], 0.8);
        variants = new Map();
        variants.set(
          'thumbnail',
          new Blob([processed.thumbnail], { type: processed.thumbnail.type })
        );

        // Update metadata with image info
        metadata.imageMetadata = {
          variants: [
            {
              key: 'thumbnail',
              width: 50, // Standard thumbnail size
              height: 50,
              size: processed.thumbnail.size,
            },
          ],
        };
      } catch (error) {
        console.warn('Failed to generate thumbnail for image file:', error);
        // Continue without thumbnail if generation fails
      }
    }

    // Create in-memory data
    const inMemoryData: InMemoryFileData = {
      original: blob,
      variants,
      metadata,
    };

    const uploadedFile: UploadedFile = {
      rn: realNameWithoutExt,
      f: uniqueFileName,
      e: extension,
      t: fileType,
      x: true, // Still temporary, but now in-memory
      inMemoryData,
    };

    // Track memory usage
    inMemoryFileManager.trackFile(uploadedFile);

    // Call callback
    this.callbacks.onFileUploaded?.(uploadedFile);

    return uploadedFile;
  },

  /**
   * Create an in-memory image with variants (replaces uploadPublicImageWithVariants)
   * Processes image client-side and stores all variants in memory
   */
  async createInMemoryImageWithVariants(
    file: File,
    targetSizes: StandardImageSize[] = [],
    quality: number = 0.9,
    onProgress?: (progress: number) => void
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image for variant processing');
    }

    onProgress?.(10);

    // Check memory limits before processing
    const stats = inMemoryFileManager.getMemoryUsage();
    const estimatedSize = file.size * (targetSizes.length + 2); // Original + variants + thumbnail

    if (
      stats.totalSize + estimatedSize >
      IN_MEMORY_FILE_CONFIG.maxMemoryUsage
    ) {
      throw new Error(
        `Estimated memory usage would exceed limit: ${(estimatedSize / 1024 / 1024).toFixed(1)}MB`
      );
    }

    if (stats.fileCount >= IN_MEMORY_FILE_CONFIG.maxConcurrentFiles) {
      throw new Error(
        `Too many files in memory: ${stats.fileCount} >= ${IN_MEMORY_FILE_CONFIG.maxConcurrentFiles}`
      );
    }

    const extension = extractFileExtension(file.name);
    const realNameWithoutExt = removeFileExtension(file.name);
    const uniqueFileName = generateUniqueId();

    onProgress?.(30);

    // Process image and generate variants client-side
    const processed = await processImageWithVariants(
      file,
      targetSizes,
      quality
    );

    onProgress?.(70);

    // Convert all files to blobs and store in memory
    const variants = new Map<string, Blob>();

    // Add thumbnail
    variants.set(
      'thumbnail',
      new Blob([processed.thumbnail], { type: processed.thumbnail.type })
    );

    // Add all variants
    const imageVariants: Array<{
      key: string;
      width: number;
      height: number;
      size: number;
    }> = [];
    for (const variant of processed.variants) {
      const variantBlob = new Blob([variant.file], { type: variant.file.type });
      variants.set(variant.size.key, variantBlob);

      imageVariants.push({
        key: variant.size.key,
        width: variant.size.width,
        height: variant.size.height,
        size: variantBlob.size,
      });
    }

    onProgress?.(90);

    // Create metadata with image information
    const metadata: InMemoryFileMetadata = {
      originalFileName: file.name,
      uploadedBy: this.uploadedBy,
      accountId: this.accountId,
      variantCount: variants.size + 1, // +1 for original
      uploadTimestamp: Date.now(),
      fileSize: file.size,
      mimeType: file.type,
      imageMetadata: {
        variants: imageVariants,
      },
    };

    // Create in-memory data
    const inMemoryData: InMemoryFileData = {
      original: new Blob([file], { type: file.type }),
      variants,
      metadata,
    };

    const uploadedFile: UploadedFile = {
      rn: realNameWithoutExt,
      f: uniqueFileName,
      e: extension,
      t: 'i',
      x: true,
      inMemoryData,
    };

    // Track memory usage
    inMemoryFileManager.trackFile(uploadedFile);

    onProgress?.(100);

    // Call callback
    this.callbacks.onFileUploaded?.(uploadedFile);

    return uploadedFile;
  },

  /**
   * Create an in-memory image with pre-cropped variants (replaces uploadImageWithPreCroppedVariants)
   * Stores original file and all cropped variants in memory
   */
  async createInMemoryImageWithCroppedVariants(
    originalFile: File,
    croppedFiles: File[] = [],
    quality: number = 0.9,
    onProgress?: (progress: number) => void
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    if (!originalFile.type.startsWith('image/')) {
      throw new Error('File must be an image for variant processing');
    }

    onProgress?.(10);

    // Check memory limits
    const stats = inMemoryFileManager.getMemoryUsage();
    const estimatedSize =
      originalFile.size + croppedFiles.reduce((sum, f) => sum + f.size, 0);

    if (
      stats.totalSize + estimatedSize >
      IN_MEMORY_FILE_CONFIG.maxMemoryUsage
    ) {
      throw new Error(
        `Estimated memory usage would exceed limit: ${(estimatedSize / 1024 / 1024).toFixed(1)}MB`
      );
    }

    if (stats.fileCount >= IN_MEMORY_FILE_CONFIG.maxConcurrentFiles) {
      throw new Error(
        `Too many files in memory: ${stats.fileCount} >= ${IN_MEMORY_FILE_CONFIG.maxConcurrentFiles}`
      );
    }

    const extension = extractFileExtension(originalFile.name);
    const realNameWithoutExt = removeFileExtension(originalFile.name);
    const uniqueFileName = generateUniqueId();

    onProgress?.(30);

    // Convert all files to blobs and store in memory
    const variants = new Map<string, Blob>();

    // Find thumbnail file (look for file with "thumbnail" in name)
    const thumbnailFile = croppedFiles.find(f =>
      removeFileExtension(f.name).toLowerCase().includes('thumbnail')
    );

    let thumbnail: File;
    if (thumbnailFile) {
      // Use the user-cropped file as thumbnail
      thumbnail = thumbnailFile;
    } else {
      // Generate thumbnail from original if no suitable cropped version
      const processed = await processImageWithVariants(
        originalFile,
        [],
        quality
      );
      thumbnail = processed.thumbnail;
    }

    // Add thumbnail
    variants.set('thumbnail', new Blob([thumbnail], { type: thumbnail.type }));

    onProgress?.(60);

    // Add all pre-cropped variants from the image editor
    const imageVariants: Array<{
      key: string;
      width: number;
      height: number;
      size: number;
    }> = [];

    for (const croppedFile of croppedFiles) {
      // Use the cropped file name to determine the variant name
      // Remove extension and use as variant name (e.g., "card", "detail", "banner_small")
      const variantName = removeFileExtension(croppedFile.name);
      const variantBlob = new Blob([croppedFile], { type: croppedFile.type });
      variants.set(variantName, variantBlob);

      // Try to get dimensions from the file (this is approximate)
      imageVariants.push({
        key: variantName,
        width: 0, // Would need to read image to get actual dimensions
        height: 0, // Would need to read image to get actual dimensions
        size: variantBlob.size,
      });
    }

    onProgress?.(90);

    // Create metadata
    const metadata: InMemoryFileMetadata = {
      originalFileName: originalFile.name,
      uploadedBy: this.uploadedBy,
      accountId: this.accountId,
      variantCount: variants.size + 1, // +1 for original
      uploadTimestamp: Date.now(),
      fileSize: originalFile.size,
      mimeType: originalFile.type,
      imageMetadata: {
        variants: imageVariants,
      },
    };

    // Create in-memory data
    const inMemoryData: InMemoryFileData = {
      original: new Blob([originalFile], { type: originalFile.type }),
      variants,
      metadata,
    };

    const uploadedFile: UploadedFile = {
      rn: realNameWithoutExt,
      f: uniqueFileName,
      e: extension,
      t: 'i',
      x: true,
      inMemoryData,
    };

    // Track memory usage
    inMemoryFileManager.trackFile(uploadedFile);

    onProgress?.(100);

    // Call callback
    this.callbacks.onFileUploaded?.(uploadedFile);

    return uploadedFile;
  },

  async moveToPermLocation(
    tempFile: UploadedFile,
    context?: PrivateFileContext
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    if (!tempFile.x) return tempFile;

    // Clean up any existing blob URL on the file object before moving
    if (tempFile.url && tempFile.url.startsWith('blob:')) {
      URL.revokeObjectURL(tempFile.url);
    }

    // Handle in-memory files
    if (tempFile.inMemoryData) {
      return await this.moveInMemoryFileToPermLocation(tempFile, context);
    }

    try {
      const tempStorage = getStorageInstanceForFile(tempFile.t, true);
      const permStorage = getStorageInstanceForFile(tempFile.t, false);

      // Check if this is an image with variants (folder structure)
      if (tempFile.t === 'i') {
        try {
          // Try to find folder structure for images with variants
          const folderPath = `i/${tempFile.f}/thumbnail.webp`;
          const tempFolderRef = ref(tempStorage, folderPath);
          await getBlob(tempFolderRef); // Test if folder structure exists

          // If we reach here, folder structure exists - move entire folder
          return await this.moveFolderToPermLocation(tempFile, context);
        } catch (folderError) {
          // Check if folder was already moved by verifying permanent storage
          try {
            const permFolderPath = `i/${tempFile.f}/thumbnail.webp`;
            const permFolderRef = ref(permStorage, permFolderPath);
            await getBlob(permFolderRef); // Test if folder exists in permanent storage

            // Clear URL cache for this file since it's now permanent
            clearFileUrlCache(tempFile);
            clearFilePreviewCacheForFile(tempFile);

            // Return permanent file reference since folder was already moved
            const permanentFile: UploadedFile = {
              rn: tempFile.rn,
              f: tempFile.f,
              e: tempFile.e,
              t: tempFile.t,
              url: undefined, // Explicitly set to undefined to clear any blob URLs
              // x: removed (undefined = permanent)
              // sizes: removed - size info now retrieved from Storage metadata
            };

            this.callbacks.onFileMoved?.(permanentFile);
            return permanentFile;
          } catch (permError) {
            // Neither temp nor permanent folder exists, proceed with single file move
          }
        }
      }

      // Handle single file movement (original logic)
      const tempFilename = constructFullFilename(tempFile);
      const tempRef = ref(tempStorage, tempFilename);

      // Check if single file exists in temp storage
      try {
        // Use organized path for permanent storage
        const destinationPath = getDestinationPath(tempFile, context);
        const permRef = ref(permStorage, destinationPath);

        // Download from temp location
        const blob = await getBlob(tempRef);

        let existingCustomMetadata = {};
        try {
          const tempMetadata = await getMetadata(tempRef);
          existingCustomMetadata = tempMetadata.customMetadata || {};
        } catch (metadataError) {
          // Ignore metadata retrieval errors
        }

        const metadata = {
          customMetadata: {
            ...existingCustomMetadata,
            accountId: this.accountId,
            realName: `${tempFile.rn}.${tempFile.e}`,
            movedFromTemp: 'true',
            // Store size information in metadata
            originalSize: blob.size.toString(),
            // For non-image files, there are no variants
            ...(tempFile.t !== 'i' && { variantsSize: '0' }),
            // Add context for private files
            ...(context &&
              tempFile.t === 'p' && {
                sellpointId: context.sellpointId || '',
                customPath: context.customPath || '',
              }),
          },
        };

        // Upload to permanent location
        await uploadBytes(permRef, blob, metadata);

        // Delete temp file
        await deleteObject(tempRef);

        // Clean up temp file blob URL if it exists
        if (tempFile.url) {
          const { revokeTempFileUrl } = await import('../utils/bucketManager');
          revokeTempFileUrl(tempFile.url);
        }

        // Clear URL cache for this file to prevent stale URLs
        clearFileUrlCache(tempFile);
        clearFilePreviewCacheForFile(tempFile);

        // Return permanent file WITHOUT url, s, and rn fields to optimize Firestore space
        // Real filename and size info now stored in Storage metadata and retrieved when needed
        const permanentFile: UploadedFile = {
          f: tempFile.f,
          e: tempFile.e,
          // s: removed - size now calculated at runtime to save Firestore space
          // rn: removed - real filename now retrieved from Storage metadata
          // sizes: removed - size info now retrieved from Storage metadata
          t: tempFile.t,
          url: undefined, // Explicitly set to undefined to clear any blob URLs
          // x: removed (undefined = permanent)
        };

        this.callbacks.onFileMoved?.(permanentFile);
        return permanentFile;
      } catch (singleFileError) {
        // Check if file was already moved to permanent storage
        const destinationPath = getDestinationPath(tempFile, context);
        const permRef = ref(permStorage, destinationPath);

        try {
          await getBlob(permRef); // Test if file exists in permanent storage

          // Clear URL cache for this file since it's now permanent
          clearFileUrlCache(tempFile);
          clearFilePreviewCacheForFile(tempFile);

          // Return permanent file reference since file was already moved
          // Real filename is now stored in Storage metadata and retrieved when needed
          const permanentFile: UploadedFile = {
            f: tempFile.f,
            e: tempFile.e,
            t: tempFile.t,
            url: undefined, // Explicitly set to undefined to clear any blob URLs
            // x: removed (undefined = permanent)
            // rn: removed - real filename now retrieved from Storage metadata
            // sizes: removed - size info now retrieved from Storage metadata
          };

          this.callbacks.onFileMoved?.(permanentFile);
          return permanentFile;
        } catch (permSingleError) {
          // File doesn't exist in either location - this indicates an error
          throw new Error(
            `File ${constructFullFilename(tempFile)} not found in temporary or permanent storage. This may indicate the file was already processed or there was an upload error.`
          );
        }
      }
    } catch (error) {
      throw error;
    }
  },

  /**
   * Move an in-memory file to permanent storage
   * Uploads directly from memory blobs to permanent storage
   */
  async moveInMemoryFileToPermLocation(
    inMemoryFile: UploadedFile,
    context?: PrivateFileContext
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    if (!inMemoryFile.inMemoryData) {
      throw new Error('File has no in-memory data to move');
    }

    const permStorage = getStorageInstanceForFile(inMemoryFile.t, false);

    // Handle images with variants (folder structure)
    if (
      inMemoryFile.t === 'i' &&
      inMemoryFile.inMemoryData.variants &&
      inMemoryFile.inMemoryData.variants.size > 0
    ) {
      return await this.moveInMemoryImageFolderToPerm(inMemoryFile, context);
    }

    // Handle single file
    const destPath = getDestinationPath(inMemoryFile, context);
    const fileRef = ref(permStorage, destPath);

    // Calculate size for metadata storage
    const originalSize = inMemoryFile.inMemoryData.metadata.fileSize;

    const metadata = {
      customMetadata: {
        accountId: this.accountId,
        uploadedBy: this.uploadedBy,
        realName: `${inMemoryFile.rn}.${inMemoryFile.e}`,
        movedFromMemory: 'true',
        originalFileName: inMemoryFile.inMemoryData.metadata.originalFileName,
        uploadTimestamp:
          inMemoryFile.inMemoryData.metadata.uploadTimestamp.toString(),
        // Store size information in metadata
        originalSize: originalSize.toString(),
        variantsSize: '0', // Single files don't have variants
        // Add context for private files
        ...(context &&
          inMemoryFile.t === 'p' && {
            sellpointId: context.sellpointId || '',
            customPath: context.customPath || '',
          }),
      },
    };

    try {
      // Upload original file from memory
      await uploadBytes(fileRef, inMemoryFile.inMemoryData.original, metadata);

      // Clean up in-memory data
      inMemoryFileManager.cleanupFile(inMemoryFile);

      // Clear URL cache for this file to prevent stale blob URLs
      clearFileUrlCache(inMemoryFile);
      clearFilePreviewCacheForFile(inMemoryFile);

      // Revoke any blob URL that might exist on the original file object
      if (inMemoryFile.url && inMemoryFile.url.startsWith('blob:')) {
        URL.revokeObjectURL(inMemoryFile.url);
      }

      // Return permanent file reference (explicitly remove url property)
      // Real filename is now stored in Storage metadata and retrieved when needed
      const permanentFile: UploadedFile = {
        f: inMemoryFile.f,
        e: inMemoryFile.e,
        t: inMemoryFile.t,
        url: undefined, // Explicitly set to undefined to clear any blob URLs
        // x: undefined (permanent file)
        // inMemoryData: explicitly omitted as it was cleaned up
        // rn: removed - real filename now retrieved from Storage metadata
        // sizes: removed - size info now retrieved from Storage metadata
      };

      this.callbacks.onFileMoved?.(permanentFile);
      return permanentFile;
    } catch (error) {
      throw new Error(
        `Failed to move in-memory file to permanent storage: ${error}`
      );
    }
  },

  /**
   * Move an in-memory image folder (with variants) to permanent storage
   */
  async moveInMemoryImageFolderToPerm(
    inMemoryFile: UploadedFile,
    context?: PrivateFileContext
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    if (!inMemoryFile.inMemoryData) {
      throw new Error('File has no in-memory data to move');
    }

    const permStorage = getStorageInstanceForFile(inMemoryFile.t, false);
    const folderPath = `i/${inMemoryFile.f}`;

    let moveCount = 0;

    try {
      // Calculate sizes from temporary file for metadata storage
      const originalSize = inMemoryFile.inMemoryData.metadata.fileSize;
      let totalVariantsSize = 0;
      if (inMemoryFile.inMemoryData.metadata.imageMetadata?.variants) {
        totalVariantsSize =
          inMemoryFile.inMemoryData.metadata.imageMetadata.variants.reduce(
            (total, variant) => total + variant.size,
            0
          );
      }

      const baseMetadata = {
        customMetadata: {
          accountId: this.accountId,
          uploadedBy: this.uploadedBy,
          realName: `${inMemoryFile.rn}.${inMemoryFile.e}`,
          movedFromMemory: 'true',
          originalFileName: inMemoryFile.inMemoryData.metadata.originalFileName,
          uploadTimestamp:
            inMemoryFile.inMemoryData.metadata.uploadTimestamp.toString(),
          // Store size information in metadata
          originalSize: originalSize.toString(),
          variantsSize: totalVariantsSize.toString(),
        },
      };

      // Upload original file
      const originalPath = `${folderPath}/original.${inMemoryFile.e}`;
      const originalRef = ref(permStorage, originalPath);
      await uploadBytes(originalRef, inMemoryFile.inMemoryData.original, {
        ...baseMetadata,
        customMetadata: {
          ...baseMetadata.customMetadata,
          variant: 'original',
        },
      });
      moveCount++;

      // Upload all variants
      if (inMemoryFile.inMemoryData.variants) {
        for (const [
          variantName,
          variantBlob,
        ] of inMemoryFile.inMemoryData.variants.entries()) {
          try {
            const variantPath = `${folderPath}/${variantName}.webp`;
            const variantRef = ref(permStorage, variantPath);

            await uploadBytes(variantRef, variantBlob, {
              ...baseMetadata,
              customMetadata: {
                ...baseMetadata.customMetadata,
                variant: variantName,
              },
            });
            moveCount++;
          } catch (variantError) {
            console.warn(
              `Failed to move variant ${variantName}:`,
              variantError
            );
            // Continue with other variants even if one fails
          }
        }
      }

      // Clean up in-memory data
      inMemoryFileManager.cleanupFile(inMemoryFile);

      // Clear URL cache for this file to prevent stale blob URLs
      clearFileUrlCache(inMemoryFile);
      clearFilePreviewCacheForFile(inMemoryFile);

      // Revoke any blob URL that might exist on the original file object
      if (inMemoryFile.url && inMemoryFile.url.startsWith('blob:')) {
        URL.revokeObjectURL(inMemoryFile.url);
      }

      // Return permanent file reference
      // Real filename and size info now stored in Storage metadata and retrieved when needed
      const permanentFile: UploadedFile = {
        f: inMemoryFile.f,
        e: inMemoryFile.e,
        t: inMemoryFile.t,
        url: undefined, // Explicitly set to undefined to clear any blob URLs
        // x: undefined (permanent file)
        // rn: removed - real filename now retrieved from Storage metadata
        // sizes: removed - size info now retrieved from Storage metadata
      };

      this.callbacks.onFileMoved?.(permanentFile);
      return permanentFile;
    } catch (error) {
      throw new Error(
        `Failed to move in-memory image folder to permanent storage. Moved ${moveCount} files. Error: ${error}`
      );
    }
  },

  /**
   * Move an entire folder (for images with variants) from temp to permanent storage
   */
  async moveFolderToPermLocation(
    tempFile: UploadedFile,
    context?: PrivateFileContext
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    const tempStorage = getStorageInstanceForFile(tempFile.t, true);
    const permStorage = getStorageInstanceForFile(tempFile.t, false);

    const folderPath = `i/${tempFile.f}`;
    const tempFolderRef = ref(tempStorage, folderPath);

    let moveCount = 0;

    // Calculate sizes from temporary file for metadata storage
    const originalSize = tempFile.sizes?.original || 0;
    const totalVariantsSize = tempFile.sizes?.variants || 0;

    try {
      // List all actual files in the temp folder
      const listResult = await listAll(tempFolderRef);

      // Move each variant that actually exists
      for (const itemRef of listResult.items) {
        try {
          const variantName = itemRef.name; // e.g., "thumbnail.webp"

          // Get the file blob
          const blob = await getBlob(itemRef);

          // Define permanent path - keep folder structure in permanent storage
          const permVariantPath = `i/${tempFile.f}/${variantName}`;
          const permVariantRef = ref(permStorage, permVariantPath);

          // Get metadata
          let existingCustomMetadata = {};
          try {
            const tempMetadata = await getMetadata(itemRef);
            existingCustomMetadata = tempMetadata.customMetadata || {};
          } catch (metadataError) {
            // Ignore metadata retrieval errors
          }

          const metadata = {
            customMetadata: {
              ...existingCustomMetadata,
              accountId: this.accountId,
              realName: `${tempFile.rn}.${tempFile.e}`,
              movedFromTemp: 'true',
              variant: variantName.replace(/\.[^/.]+$/, ''), // Remove extension
              // Store size information in metadata (all variants get the same total info)
              originalSize: originalSize.toString(),
              variantsSize: totalVariantsSize.toString(),
            },
          };

          // Upload to permanent location
          await uploadBytes(permVariantRef, blob, metadata);

          // Delete temp variant
          await deleteObject(itemRef);

          moveCount++;
        } catch (variantError) {
          // Continue with other variants even if one fails
        }
      }
    } catch (listError) {
      throw new Error(`Failed to list variants in folder: ${listError}`);
    }

    // Clean up temp file blob URL if it exists (after successful folder move)
    if (tempFile.url) {
      const { revokeTempFileUrl } = await import('../utils/bucketManager');
      revokeTempFileUrl(tempFile.url);
    }

    // Clear URL cache for this file to prevent stale blob URLs
    clearFileUrlCache(tempFile);
    clearFilePreviewCacheForFile(tempFile);

    // Return permanent file reference
    // Real filename is now stored in Storage metadata and retrieved when needed
    const permanentFile: UploadedFile = {
      f: tempFile.f,
      e: tempFile.e,
      t: tempFile.t,
      url: undefined, // Explicitly set to undefined to clear any blob URLs
      // x: removed (undefined = permanent)
      // rn: removed - real filename now retrieved from Storage metadata
      // sizes: removed - size info now retrieved from Storage metadata
    };

    this.callbacks.onFileMoved?.(permanentFile);
    return permanentFile;
  },

  async deleteFile(
    file: UploadedFile,
    context?: PrivateFileContext
  ): Promise<void> {
    this.ensureInitialized();

    try {
      const storage = getStorageInstanceForFile(file.t, file.x || false);

      // For image files, we need to handle the new folder structure
      if (file.t === 'i') {
        // Images use folder structure: i/{uniqueId}/*.{ext}
        // We need to delete all files in the folder
        await this.deleteImageFolder(file, storage);
      } else {
        // For other file types, use the standard path
        let filePath: string;
        if (file.x) {
          // Temporary files are stored at root with filename
          filePath = constructFullFilename(file);
        } else {
          // Permanent files use organized paths
          filePath = getDestinationPath(file, context);
        }

        const fileRef = ref(storage, filePath);
        await deleteObject(fileRef);
      }

      // Clear from shared URL cache
      const cacheKey = `${file.f}.${file.e}`;
      // URL caching is now handled by SecureFileManager

      this.callbacks.onFileDeleted?.(file);
    } catch (error: any) {
      // For authorization errors on permanent files, log a warning but don't throw
      // This prevents the entire form submission from failing due to file cleanup issues
      if (error?.code === 'storage/unauthorized' && !file.x) {
        return; // Don't throw for permission errors on permanent files
      }

      throw error;
    }
  },

  async deleteImageFolder(file: UploadedFile, storage: any): Promise<void> {
    try {
      const folderPath = `i/${file.f}/`;

      // List all files in the folder
      const folderRef = ref(storage, folderPath);
      const listResult = await listAll(folderRef);

      // Delete all files in the folder
      const deletePromises = listResult.items.map(itemRef => {
        return deleteObject(itemRef);
      });

      await Promise.all(deletePromises);
    } catch (error) {
      throw error;
    }
  }, // ===== SHARED URL CACHE MANAGEMENT =====

  async getFileUrl(
    file: UploadedFile,
    forceRefresh: boolean = false
  ): Promise<string> {
    // Delegate to secure file manager for all files
    // This ensures private files use secure caching and temp files show placeholders
    const { getSecureFileUrl } = await import('../utils/bucketManager');
    return getSecureFileUrl(file, { forceRefresh });
  },

  clearUrlCache(): void {
    // URL caching is now handled by SecureFileManager
    // Clear the private file cache instead
    const { clearPrivateFileCache } = require('./bucketManager');
    clearPrivateFileCache();
  },

  cleanExpiredUrls(): void {
    // No longer needed - SecureFileManager handles expiration
  },

  // ===== PATH RESOLUTION USING LODASH =====

  resolveFieldPaths(data: any, pathPattern: string): string[] {
    if (pathPattern.startsWith('*.')) {
      const fieldName = pathPattern.substring(2);
      return this.findAllPathsWithField(data, fieldName);
    }

    if (pathPattern.includes('*')) {
      return this.resolveWildcardPaths(data, pathPattern);
    }

    return [pathPattern];
  },

  findAllPathsWithField(
    data: any,
    fieldName: string,
    currentPath: string = ''
  ): string[] {
    const paths: string[] = [];

    if (!data || typeof data !== 'object') {
      return paths;
    }

    if (get(data, fieldName) !== undefined) {
      const fullPath = currentPath ? `${currentPath}.${fieldName}` : fieldName;
      paths.push(fullPath);
    }

    Object.keys(data).forEach(key => {
      const value = data[key];
      const newPath = currentPath ? `${currentPath}.${key}` : key;

      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (item && typeof item === 'object') {
            const arrayPath = `${newPath}.${index}`;
            paths.push(
              ...this.findAllPathsWithField(item, fieldName, arrayPath)
            );
          }
        });
      } else if (value && typeof value === 'object') {
        paths.push(...this.findAllPathsWithField(value, fieldName, newPath));
      }
    });

    return paths;
  },

  resolveWildcardPaths(data: any, pathPattern: string): string[] {
    const paths: string[] = [];
    const parts = pathPattern.split('.');

    const traverse = (
      obj: any,
      currentPath: string[],
      patternIndex: number
    ) => {
      if (patternIndex >= parts.length) {
        paths.push(currentPath.join('.'));
        return;
      }

      const part = parts[patternIndex];

      if (part === '*') {
        if (obj && typeof obj === 'object') {
          Object.keys(obj).forEach(key => {
            traverse(obj[key], [...currentPath, key], patternIndex + 1);
          });
        }
      } else {
        const value = get(obj, part);
        if (value !== undefined) {
          traverse(value, [...currentPath, part], patternIndex + 1);
        }
      }
    };

    traverse(data, [], 0);
    return paths;
  },

  // ===== BATCH OPERATIONS =====

  getFilesAtPath(data: any, path: string): UploadedFile[] {
    const files = get(data, path, []);
    console.log('🔍 [FileUploadManager] getFilesAtPath:', {
      path,
      dataKeys: Object.keys(data || {}),
      rawFiles: files,
      filesType: typeof files,
      isArray: Array.isArray(files),
      filesLength: Array.isArray(files) ? files.length : 'N/A',
    });
    return Array.isArray(files) ? files.filter(this.isValidUploadedFile) : [];
  },

  setFilesAtPath(data: any, path: string, files: UploadedFile[]): void {
    set(data, path, files);
  },

  async moveTemporaryFiles(
    data: any,
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<any> {
    this.ensureInitialized();

    console.log('📁 [FileUploadManager] moveTemporaryFiles called:', {
      uploadedFileFields,
      dataFields: uploadedFileFields.map(field => ({
        field,
        files: this.getFilesAtPath(data, field).map(
          f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
        ),
      })),
    });

    const processedData = cloneDeep(data);
    const movedFilesCache = new Map<string, UploadedFile>(); // Track files already moved in this operation

    for (const pathPattern of uploadedFileFields) {
      const resolvedPaths = this.resolveFieldPaths(processedData, pathPattern);

      for (const path of resolvedPaths) {
        const files = this.getFilesAtPath(processedData, path);
        const processedFiles: UploadedFile[] = [];

        console.log('🔄 [FileUploadManager] Processing files at path:', path, {
          files: files.map(f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`),
        });

        for (const file of files) {
          if (file.x) {
            const fileKey = `${file.f}.${file.e}`;

            // Check if this file was already moved in this operation
            if (movedFilesCache.has(fileKey)) {
              processedFiles.push(movedFilesCache.get(fileKey)!);
            } else {
              try {
                const movedFile = await this.moveToPermLocation(file, context);
                movedFilesCache.set(fileKey, movedFile); // Cache the moved file
                processedFiles.push(movedFile);
              } catch (error) {
                processedFiles.push(file);
              }
            }
          } else {
            // Permanent file - keep as-is
            processedFiles.push(file);
          }
        }

        console.log('✅ [FileUploadManager] Processed files for path:', path, {
          processedFiles: processedFiles.map(
            f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
          ),
        });

        this.setFilesAtPath(processedData, path, processedFiles);
      }
    }

    return processedData;
  },

  async deleteRemovedFiles(
    previousData: any,
    currentData: any,
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<void> {
    this.ensureInitialized();

    console.log('🗑️ [FileUploadManager] deleteRemovedFiles called:', {
      uploadedFileFields,
      previousDataFields: uploadedFileFields.map(field => ({
        field,
        files: this.getFilesAtPath(previousData, field).map(
          f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
        ),
      })),
      currentDataFields: uploadedFileFields.map(field => ({
        field,
        files: this.getFilesAtPath(currentData, field).map(
          f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
        ),
      })),
    });

    for (const pathPattern of uploadedFileFields) {
      const resolvedPaths = this.resolveFieldPaths(previousData, pathPattern);

      for (const path of resolvedPaths) {
        const previousFiles = this.getFilesAtPath(previousData, path);
        const currentFiles = this.getFilesAtPath(currentData, path);

        console.log('🔍 [FileUploadManager] Comparing files at path:', path, {
          previousFiles: previousFiles.map(
            f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
          ),
          currentFiles: currentFiles.map(
            f => `${f.f}.${f.e} (${f.x ? 'temp' : 'perm'})`
          ),
        });

        const currentFileIds = new Set(currentFiles.map(f => f.f));
        const filesToDelete = previousFiles.filter(
          f => !f.x && !currentFileIds.has(f.f)
        );

        console.log('🗑️ [FileUploadManager] Files to delete:', {
          currentFileIds: Array.from(currentFileIds),
          filesToDelete: filesToDelete.map(f => `${f.f}.${f.e}`),
        });

        for (const file of filesToDelete) {
          try {
            console.log(
              '🗑️ [FileUploadManager] Deleting file:',
              `${file.f}.${file.e}`
            );
            await this.deleteFile(file, context);
          } catch (error) {
            console.error(
              '❌ [FileUploadManager] Failed to delete file:',
              `${file.f}.${file.e}`,
              error
            );
            // Don't throw - continue with other operations even if file deletion fails
          }
        }
      }
    }
  },

  async deleteAllEntityFiles(
    entityData: any,
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<void> {
    this.ensureInitialized();

    for (const pathPattern of uploadedFileFields) {
      const resolvedPaths = this.resolveFieldPaths(entityData, pathPattern);

      for (const path of resolvedPaths) {
        const files = this.getFilesAtPath(entityData, path);

        for (const file of files) {
          if (!file.x) {
            try {
              await this.deleteFile(file, context);
            } catch (error) {}
          }
        }
      }
    }
  },

  // ===== LIFECYCLE METHODS =====

  async processBeforeCreate(
    data: any,
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<any> {
    return await this.moveTemporaryFiles(data, uploadedFileFields, context);
  },

  async processBeforeUpdate(
    data: any,
    previousData: any,
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<any> {
    console.log('🔄 [FileUploadManager] processBeforeUpdate called:', {
      dataType: typeof data,
      dataKeys: Object.keys(data || {}),
      hasImages: 'images' in (data || {}),
      imagesValue: data?.images,
      imagesType: typeof data?.images,
      imagesLength: Array.isArray(data?.images) ? data.images.length : 'N/A',
      previousDataKeys: Object.keys(previousData || {}),
      uploadedFileFields,
    });

    const processedData = await this.moveTemporaryFiles(
      data,
      uploadedFileFields,
      context
    );
    await this.deleteRemovedFiles(
      previousData,
      processedData,
      uploadedFileFields,
      context
    );
    return processedData;
  },

  async processBeforeDelete(
    entityData: any,
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<void> {
    await this.deleteAllEntityFiles(entityData, uploadedFileFields, context);
  },

  async processBeforeDeleteMany(
    entitiesData: any[],
    uploadedFileFields: string[],
    context?: PrivateFileContext
  ): Promise<void> {
    for (const entityData of entitiesData) {
      await this.deleteAllEntityFiles(entityData, uploadedFileFields, context);
    }
  },

  // ===== VALIDATION =====

  isValidUploadedFile(file: any): file is UploadedFile {
    return (
      file &&
      typeof file === 'object' &&
      typeof file.f === 'string' &&
      typeof file.e === 'string' &&
      typeof file.t === 'string' &&
      ['i', 'v', 's', 'p'].includes(file.t) &&
      (file.rn === undefined || typeof file.rn === 'string') &&
      (file.x === undefined || typeof file.x === 'boolean') &&
      (file.url === undefined || typeof file.url === 'string')
    );
  },

  // ===== UTILITY METHODS =====

  getStats() {
    return {
      accountId: this.accountId,
      uploadedBy: this.uploadedBy,
      isInitialized: this.isInitialized,
    };
  },

  /**
   * Move public image folder from temp to permanent storage
   * This moves all variants in the folder /i/filename/ from temp to permanent bucket
   */
  async movePublicImageToPermanent(
    uploadedFile: UploadedFile
  ): Promise<UploadedFile> {
    this.ensureInitialized();

    if (uploadedFile.t !== 'i' || !uploadedFile.x) {
      throw new Error('File must be a temporary public image to move');
    }

    const tempStorage = getStorageInstanceForFile('i', true);
    const permanentStorage = getStorageInstanceForFile('i', false);

    // List all files in the temp folder
    const folderPath = `i/${uploadedFile.f}/`;

    // Calculate sizes from temporary file for metadata storage
    const originalSize = uploadedFile.sizes?.original || 0;
    const totalVariantsSize = uploadedFile.sizes?.variants || 0;

    try {
      // List all actual files in the folder instead of assuming variants
      const tempFolderRef = ref(tempStorage, folderPath);
      const listResult = await listAll(tempFolderRef);
      const fileCount = listResult.items.length;

      for (const tempFileRef of listResult.items) {
        try {
          // Extract just the filename from the full path
          const fileName = tempFileRef.name;
          const permanentPath = `${folderPath}${fileName}`;
          const permanentRef = ref(permanentStorage, permanentPath);

          // Get the file blob and metadata
          const [blob, metadata] = await Promise.all([
            getBlob(tempFileRef),
            getMetadata(tempFileRef),
          ]);

          // Upload to permanent storage with enhanced metadata
          await uploadBytes(permanentRef, blob, {
            customMetadata: {
              ...(metadata.customMetadata || {}),
              // Add size information to all variants
              originalSize: originalSize.toString(),
              variantsSize: totalVariantsSize.toString(),
            },
            // Preserve other metadata fields that might be important
            contentType: metadata.contentType,
            contentLanguage: metadata.contentLanguage,
            contentEncoding: metadata.contentEncoding,
            contentDisposition: metadata.contentDisposition,
            cacheControl: metadata.cacheControl,
          });

          // Delete from temp storage
          await deleteObject(tempFileRef);
        } catch (error) {
          // Continue with other files even if one fails
        }
      }

      // Update the uploaded file to point to permanent storage
      // Real filename is now stored in Storage metadata and retrieved when needed
      const permanentFile: UploadedFile = {
        f: uploadedFile.f,
        e: uploadedFile.e,
        t: uploadedFile.t,
        // x: undefined (permanent file)
        // rn: removed - real filename now retrieved from Storage metadata
        // sizes: removed - size info now retrieved from Storage metadata
      };

      // Generate new URL for permanent storage (use imageVariant option instead of path)
      permanentFile.url = await generateFileUrl(
        {
          f: uploadedFile.f,
          e: uploadedFile.e,
          t: 'i',
          // x: undefined (permanent file)
        },
        { imageVariant: 'thumbnail' }
      );

      return permanentFile;
    } catch (error) {
      throw new Error(
        `Failed to move public image folder: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  },

  /**
   * Move multiple public images from temp to permanent storage
   * Helper for form submission handling
   */
  async movePublicImagesToPermanent(
    uploadedFiles: UploadedFile[]
  ): Promise<UploadedFile[]> {
    const publicImageFiles = uploadedFiles.filter(
      file => shouldCheckForVariants(file) && file.x === true
    );

    if (publicImageFiles.length === 0) {
      return uploadedFiles; // No public images to move
    }

    const movePromises = publicImageFiles.map(file =>
      this.movePublicImageToPermanent(file)
    );

    const movedFiles = await Promise.all(movePromises);

    // Replace the moved files in the original array
    const result = uploadedFiles.map(file => {
      const movedFile = movedFiles.find(moved => moved.f === file.f);
      return movedFile || file;
    });

    return result;
  },
};
