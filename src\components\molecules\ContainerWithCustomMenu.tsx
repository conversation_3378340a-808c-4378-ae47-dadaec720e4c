import { PropsWithChildren, useEffect, useState } from 'react';
import { Box, Theme, useMediaQuery } from '@mui/material';
import { useSidebarState } from 'react-admin';
import { useLocation } from 'react-router-dom';

import menuConfig from '../../data/menu-items';
import Footer from '../organisms/layout/Footer';
import { MenuItemI } from './DropdownMenuItem';
import SecondMenu, { DROPDOWN_MENU_WIDTH } from './SecondMenu';

const SIDEBAR_WIDTH = 300;

export default function ContainerWithCustomMenu({
  children,
}: PropsWithChildren) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { pathname } = useLocation();
  const [secondMenuItems, setSecondMenuItems] = useState<MenuItemI[]>([]);
  const [sidebarOpen] = useSidebarState();

  useEffect(() => {
    let changed = false;

    // Here we set items for the second menu by iterating
    // through menu config to find the current pathname
    menuConfig.forEach(menuGroup => {
      menuGroup.items?.forEach(subgroup => {
        if (subgroup.href === pathname && subgroup.items) {
          setSecondMenuItems(subgroup.items ?? []);
          changed = true;
        } else if (subgroup.items?.length) {
          subgroup.items.forEach(el => {
            if (el.href && pathname.includes(el.href)) {
              setSecondMenuItems(subgroup.items ?? []);
              changed = true;
            } else if (el.items?.length) {
              el.items.forEach(lastEl => {
                if (lastEl.href && pathname.includes(lastEl.href)) {
                  setSecondMenuItems(subgroup.items ?? []);
                  changed = true;
                }
              });
            }
          });
        }
      });

      if (!changed) {
        setSecondMenuItems([]);
      }
    });
  }, [pathname]);

  return (
    <Box sx={{ display: 'flex' }}>
      {!isXSmall && !!secondMenuItems.length && (
        <Box
          sx={{
            '@media print': { display: 'none' },
            left: 0,
            top: 60,
            position: 'fixed',
            width: `${DROPDOWN_MENU_WIDTH}px`,
            height: 'calc(100vh - 60px)',
            minHeight: 'auto',
            overflowY: 'auto',
          }}
        >
          <SecondMenu items={secondMenuItems} />
        </Box>
      )}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          position: 'fixed',
          left: isXSmall
            ? 0
            : secondMenuItems.length
              ? `${DROPDOWN_MENU_WIDTH}px`
              : sidebarOpen
                ? `${SIDEBAR_WIDTH}px`
                : 0,
          top: 60,
          width: isXSmall
            ? '100%'
            : secondMenuItems.length
              ? `calc(100% - ${DROPDOWN_MENU_WIDTH}px)`
              : sidebarOpen
                ? `calc(100% - ${SIDEBAR_WIDTH}px)`
                : '100%',
          height: 'calc(100vh - 60px)',
          minHeight: 'auto',
          overflowY: 'auto',
          transition: theme =>
            theme.transitions.create(['left', 'width'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          '@media print': {
            ml: 0,
            minHeight: 'auto',
          },
        }}
      >
        <Box sx={{ flex: 1 }}>{children}</Box>
        <Box sx={{ '@media print': { display: 'none' } }}>
          <Footer />
        </Box>
      </Box>
    </Box>
  );
}
