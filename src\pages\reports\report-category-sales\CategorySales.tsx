import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box, capitalize, LinearProgress, useMediaQuery } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV, useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { FieldOption } from '~/components/organisms/CustomTable/types/globals';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useReportRawData } from '~/hooks/useReportRawData';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import {
  useGetListHospitalityCategoriesLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import cleanStringArond from '~/utils/cleanStringArond';
import { CurrencyType } from '~/utils/formatNumber';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import CategorySalesGraph from './components/CategorySalesGraph';
import CategorySalesTable from './components/CategorySalesTable';

const REPORT_TYPE = 'groups';
const DEFAULT_FIELDS = [
  { isChecked: true, value: 'id' },
  { isChecked: true, value: 'netValue' },
  { isChecked: false, value: 'vat' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: true, value: 'couponsValue' },
  { isChecked: true, value: 'discountsValue' },
  { isChecked: true, value: 'itemsQty' },
  { isChecked: true, value: 'itemsValue' },
  { isChecked: true, value: 'modifiersQty' },
  { isChecked: true, value: 'value' },
  { isChecked: true, value: 'modifiersValue' },
];

export default function CategorySales() {
  const { t } = useTranslation();
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  // Replace manual data fetching with the hook
  const { data, dataTimestamp, isLoading, error, isInitialStorageFetchDone } =
    useReportRawData(REPORT_TYPE);

  // Use the data from the hook
  const rawData = useMemo(() => data, [data]);

  // Add isProcessingData state
  const [isProcessingData, setIsProcessingData] = useState(false);

  const [filters, setFilters] = useState<ReportFiltersState>();
  const [currency, setCurrency] = useState<CurrencyType | undefined>();
  const [tableFields, setTableFields] = useState<FieldOption[]>(DEFAULT_FIELDS);
  const [groupingItems, setGroupingItems] = useState<('vat' | 'id')[]>([]);
  const [enrichedCategorySalesData, setEnrichedCategorySalesData] = useState<
    any[]
  >([]);
  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const { data: categories } = useGetListHospitalityCategoriesLive();

  const updateCommonField = useCallback((key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  }, []);

  const contentRef = useRef<HTMLDivElement>(null);

  const defaultValues = useMemo(
    () => ({
      dateRange,
      sellpointId: sellPointId,
      timeRange: {
        allDay: !timeRange,
        start: timeRange?.[0],
        end: timeRange?.[1],
      },
      diningOption: DiningOption.ALL,
      source: SourceOption.ALL,
    }),
    [dateRange, sellPointId, timeRange]
  );

  // Set filters with default values on mount
  useEffect(() => {
    setFilters(defaultValues);
  }, [defaultValues]);

  // Extract processing common fields into its own effect
  useEffect(() => {
    if (!rawData || !Array.isArray(rawData) || rawData.length === 0) return;

    try {
      // Set currency from data
      if (rawData.length && rawData[0].currency) {
        setCurrency(rawData[0].currency as CurrencyType);
      }

      const commonFieldsValues = getReportCommonFieldsValues(
        REPORT_TYPE,
        rawData
      );

      // Process members
      const tmpMembers: OptionType[] = [];
      commonFieldsValues.member?.forEach((el, index) => {
        if (el) {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId?.[index] || el,
          });
        }
      });
      updateCommonField('member', tmpMembers);

      // Process floors
      const tmpFloors: OptionType[] = [];
      commonFieldsValues.section?.forEach(el => {
        if (el) {
          tmpFloors.push({
            label: el,
            value: el,
          });
        }
      });
      updateCommonField('floor', tmpFloors);

      // Process service types
      const tmpServiceType: OptionType[] = [];
      commonFieldsValues.serviceType?.forEach(el => {
        if (el) {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        }
      });
      updateCommonField('serviceType', tmpServiceType);

      // Process sources
      const tmpSources: OptionType[] = [];
      commonFieldsValues.source?.forEach(el => {
        if (el) {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        }
      });
      updateCommonField('sources', tmpSources);
    } catch (e) {
      console.error('Error processing common fields:', e);
    }
  }, [rawData, updateCommonField]);

  // Update data processing logic with isProcessingData
  const { graphData, groupedByItemsData } = useMemo(() => {
    if (!rawData || !filters || !Array.isArray(rawData)) {
      return {
        graphData: { datasets: [], labels: [] },
        groupedByItemsData: [],
      };
    }

    setIsProcessingData(true);
    try {
      const appliedFilters = composeFilters(filters, REPORT_TYPE);
      const filteredData = filterReport(
        REPORT_TYPE,
        rawData,
        appliedFilters,
        []
      );

      const topProductIds = (
        groupReport(REPORT_TYPE, filteredData, [], ['id'])[0]?.report ?? []
      )
        .sort((a, b) => b.itemsValue - a.itemsValue)
        ?.slice(0, 5)
        ?.map(el => el.id);

      const filteredByTopIds = filterReport(
        REPORT_TYPE,
        rawData,
        appliedFilters,
        [{ field: 'id', operator: 'in', value: topProductIds }]
      );

      const groupedByHour = groupReport(
        REPORT_TYPE,
        filteredByTopIds,
        ['hourOfDay'],
        ['id']
      );

      const labels = groupedByHour?.map(group => group.hourOfDay.toString());

      const datasets: { label: string; data: number[] }[] = topProductIds?.map(
        id => ({
          label: id,
          data: [],
        })
      );

      groupedByHour.forEach(({ report: items }) => {
        datasets.forEach(ds => {
          const item = items.find(i => i.id === ds.label);
          const value = item?.value || 0;
          const formattedValue = value / 10000;
          ds.data.push(formattedValue);
        });
      });

      const computedGraphData = {
        datasets: datasets?.map(ds => ({ ...ds, label: ds.label })),
        labels,
      };

      const availableFields = tableFields.filter((field: any) => {
        return (
          field.isChecked &&
          reportSpecificFields.groups.some(
            discountField =>
              cleanStringArond(field.value) === cleanStringArond(discountField)
          )
        );
      });

      const rawGroupedData = groupReport(
        REPORT_TYPE,
        filteredData,
        [],
        availableFields.map(item => item.value)
      );

      const hierarchicalGroupedData =
        groupGroupedReportBySpecificFieldsHierarchical(
          REPORT_TYPE,
          rawGroupedData,
          groupingItems
        )[0]?.report || [];

      setIsProcessingData(false);
      return {
        graphData: computedGraphData,
        composedFilters: appliedFilters,
        groupedByItemsData: hierarchicalGroupedData,
      };
    } catch (e) {
      console.error('Error processing report data:', e);
      setIsProcessingData(false);
      return {
        graphData: { datasets: [], labels: [] },
        groupedByItemsData: [],
      };
    }
  }, [filters, rawData, groupingItems, tableFields]);

  const fetchEnrichedCategorySalesData = useCallback(() => {
    if (!groupedByItemsData) return;

    try {
      const collectLeafAndGroupIds = (
        items: any[]
      ): { leafIds: string[]; groupIds: Set<string> } => {
        let leafIds: string[] = [];
        let groupIds: Set<string> = new Set();

        for (const item of items) {
          if (!item.subReport || item.subReport.length === 0) {
            leafIds.push(item.id);
            groupIds.add(item.id);
          } else {
            groupIds.add(item.id);
            const { leafIds: childLeafIds, groupIds: childGroupIds } =
              collectLeafAndGroupIds(item.subReport);

            leafIds = [...leafIds, ...childLeafIds];
            childGroupIds.forEach(id => groupIds.add(id));
          }
        }
        return { leafIds, groupIds };
      };

      const { leafIds } = collectLeafAndGroupIds(groupedByItemsData);
      const categoryNameMap: Record<string, string> = {};

      leafIds.map(id => {
        try {
          const categoryFound = categories?.find(
            category => category.id === id
          );
          categoryNameMap[id] = categoryFound?.name;
        } catch {
          categoryNameMap[id] = id;
        }
      });

      const enrichItems = (items: any[]): any[] => {
        return items.map(item => {
          if (item.groupedBy) {
            const groupField = item.groupedBy.field;
            const groupValue = item.groupedBy.value;

            return {
              ...item,
              name:
                groupField === 'id'
                  ? categoryNameMap[groupValue] || groupValue
                  : `${capitalize(groupField)}: ${groupValue}`,
              subReport: item.subReport
                ? enrichItems(item.subReport)
                : undefined,
            };
          } else {
            const enrichedItem = {
              ...item,
              name: categoryNameMap[item.id] || item.id,
            };
            if (item.subReport && item.subReport.length > 0) {
              enrichedItem.subReport = enrichItems(item.subReport);
            }
            return enrichedItem;
          }
        });
      };

      const enrichedData = enrichItems(groupedByItemsData);
      setEnrichedCategorySalesData(enrichedData);
    } catch (error: any) {
      console.error('Error fetching enriched category sales data:', error);
    }
  }, [groupedByItemsData, categories]);

  useEffect(() => {
    fetchEnrichedCategorySalesData();
  }, [fetchEnrichedCategorySalesData]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const finalTableData = useMemo(() => {
    const sortedData = sortData(enrichedCategorySalesData);
    return remapReports(sortedData, 'id');
  }, [enrichedCategorySalesData]);

  const onChangeGrouping = useCallback((items: ('vat' | 'id')[]) => {
    setGroupingItems(items);
  }, []);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  // Update the rendering conditions to always show existing data while loading
  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    console.log(finalTableData);
    const title = 'Report category sales';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Category',
        'VAT',
        'Items Sold',
        'Items Sales',
        'Modifiers Sold',
        'Modifiers Sales',
        'Gross Sales',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Sales',
      ].join(','),
      ...finalTableData.map(el =>
        [
          categories?.find(cat => cat.id === el.id)?.name || el.name,
          el.vat,
          el.itemsQty / 1000 || 0,
          el.itemsValue / 10000 || 0,
          el.modifiersQty / 1000 || 0,
          el.modifiersValue / 10000 || 0,
          el.value / 10000 || 0,
          -el.discountsValue / 10000 || 0,
          -el.couponsValue / 10000 || 0,
          el.promotionsValue / 10000 || 0,
          el.netValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        '',
        finalTableData.reduce((acc, el) => acc + (el.itemsQty || 0) / 1000, 0),
        finalTableData.reduce(
          (acc, el) => acc + (el.itemsValue || 0) / 10000,
          0
        ),
        finalTableData.reduce(
          (acc, el) => acc + (el.modifiersQty || 0) / 1000,
          0
        ),
        finalTableData.reduce(
          (acc, el) => acc + (el.modifiersValue || 0) / 10000,
          0
        ),
        finalTableData.reduce((acc, el) => acc + (el.value || 0) / 10000, 0),
        -finalTableData.reduce(
          (acc, el) => acc + (el.discountsValue || 0) / 10000,
          0
        ),
        -finalTableData.reduce(
          (acc, el) => acc + (el.couponsValue || 0) / 10000,
          0
        ),
        finalTableData.reduce(
          (acc, el) => acc + (el.promotionsValue || 0) / 10000,
          0
        ),
        finalTableData.reduce((acc, el) => acc + (el.netValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'categorySales');
  };

  const isSmall = useMediaQuery(theme => theme.breakpoints.down('md'));

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('categorySales.title')}
        description={
          <>
            {t('categorySales.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
        action={
          <ExportMenuButton
            contentRef={contentRef}
            handleExport={handleExport}
          />
        }
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
      />
      {/* Show linear progress at the top when loading */}
      <Box
        sx={{
          width: isSmall ? 'calc(100% + 50px)' : '100%',
          ml: isSmall ? '-25px' : 0,
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          height: 4,
        }}
      >
        {(isLoading || isProcessingData) && <LinearProgress />}
      </Box>

      <ReportDateTitle />

      {/* Show error when applicable */}
      {error && !isLoading && !isProcessingData ? (
        <Box sx={{ color: 'error.main', textAlign: 'center', my: 2 }}>
          {typeof error === 'object' && error && 'message' in error
            ? (error as any).message
            : String(error)}
        </Box>
      ) : null}

      {/* Always render content when we have data, regardless of loading state */}
      {isInitialStorageFetchDone && (
        <>
          {isSmall ? null : (
            <CategorySalesGraph data={graphData} currency={currency} />
          )}
          <Box sx={{ pb: 3 }}>
            <CategorySalesTable
              fields={tableFields}
              setFields={setTableFields}
              groupingItems={groupingItems}
              onChangeGrouping={onChangeGrouping}
              tableData={finalTableData || []}
            />
          </Box>
        </>
      )}
    </Box>
  );
}
