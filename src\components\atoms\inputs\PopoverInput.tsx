import { ReactNode, useRef, useState } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Box, InputAdornment, Popover, Typography } from '@mui/material';
import { useInput, useTranslate } from 'react-admin';

export interface PopoverInputProps {
  source: string;
  label?: string;
  helperText?: string;
  disabled?: boolean;
  readOnly?: boolean;
  /**
   * Render the content inside the popover dropdown
   * Receives current value, onChange handler, and onClose to close the popover
   */
  renderContent: (
    value: any,
    onChange: (value: any) => void,
    onClose: () => void
  ) => ReactNode;
  /**
   * Render the summary text shown in the input field when closed
   * Receives current value, returns string or ReactNode
   */
  renderSummary?: (value: any) => ReactNode;
  /**
   * Placeholder text when no value is set
   */
  placeholder?: string;
  /**
   * Width of the popover (default: 'auto', matches input width)
   */
  popoverWidth?: number | string;
  /**
   * Custom validation
   */
  validate?: any;
  /**
   * Default value
   */
  defaultValue?: any;
  /**
   * Custom sx for the input container
   */
  sx?: any;
  /**
   * Parse function (for compatibility with CustomInput)
   */
  parse?: (value: any) => any;
  /**
   * Format function (for compatibility with CustomInput)
   */
  format?: (value: any) => any;
}

/**
 * PopoverInput - A React-Admin compatible input that opens a popover with custom content
 * This is a standard react-admin input component. Use via CustomInput for custom UI styling.
 *
 * @example
 * // Direct usage (original UI)
 * <PopoverInput
 *   source="nutritionalValues"
 *   label="Nutritional Values"
 *   renderContent={(value, onChange, onClose) => (
 *     <NutritionalValuesTable value={value} onChange={onChange} onClose={onClose} />
 *   )}
 *   renderSummary={(value) => value ? "Values set" : undefined}
 *   placeholder="Click to set values"
 * />
 *
 * @example
 * // Via CustomInput with custom UI
 * <CustomInput
 *   type="popover"
 *   ui="custom"
 *   source="nutritionalValues"
 *   label="Nutritional Values"
 *   renderContent={(value, onChange, onClose) => (...)}
 * />
 */
export const PopoverInput = ({
  source,
  label,
  helperText,
  disabled = false,
  readOnly = false,
  renderContent,
  renderSummary,
  placeholder,
  popoverWidth,
  validate,
  defaultValue,
  sx,
}: PopoverInputProps) => {
  const translate = useTranslate();
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);

  const {
    field: { value, onChange },
    fieldState: { error, isTouched },
    isRequired,
  } = useInput({
    source,
    validate,
    defaultValue,
  });

  const handleOpen = () => {
    if (!disabled && !readOnly) {
      setOpen(true);
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (newValue: any) => {
    onChange(newValue);
  };

  // Determine what to show in the input
  const summary = renderSummary ? renderSummary(value) : undefined;
  const hasValue = value !== undefined && value !== null && value !== '';
  const displayPlaceholder =
    placeholder || translate('ra.action.select', { _: 'Click to set' });

  const isDisabled = disabled || readOnly;

  return (
    <Box sx={{ width: '100%', flex: 1, ...sx }} data-popover-input>
      <Box
        ref={anchorRef}
        onClick={handleOpen}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: isDisabled ? 'default' : 'pointer',
          minHeight: '40px',
          height: '100%',
          px: 1,
        }}
      >
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          {hasValue && summary ? (
            <Typography
              variant="body1"
              sx={{
                color: 'text.primary',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {summary}
            </Typography>
          ) : null}
        </Box>

        <InputAdornment position="end">
          <KeyboardArrowDownIcon
            sx={{
              transform: open ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s',
              color: 'action.active',
            }}
          />
        </InputAdornment>
      </Box>

      <Popover
        open={open}
        anchorEl={anchorRef.current}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              width: popoverWidth || anchorRef.current?.offsetWidth || 'auto',
              minWidth: 300,
              maxHeight: '80vh',
              overflow: 'auto',
              mt: 0.5,
            },
          },
        }}
      >
        {renderContent(value, handleChange, handleClose)}
      </Popover>
    </Box>
  );
};

export default PopoverInput;
