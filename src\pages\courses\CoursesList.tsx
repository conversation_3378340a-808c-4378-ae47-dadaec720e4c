import { useEffect, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { Box, Typography } from '@mui/material';
import { useUpdate } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';
import { RESOURCES } from '~/providers/resources';
import CategoriesEditModal, {
  Category,
} from './components/CategoriesEditModal';
import CoursesFormModal from './components/CoursesFormModal';

interface CoursesListProps {
  sellpointId: string;
  courses: string[];
  categories: Category[];
  openCategoriesModal: boolean;
  setOpenCategoriesModal: (open: boolean) => void;
}

// a little function to help us with reordering the result
const reorder = (list: string[], startIndex: number, endIndex: number) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

const rowStyle = {
  borderBottom: 'solid 1px',
  borderColor: 'custom.gray200',
  bgcolor: 'background.default',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  pl: 1,
  cursor: 'pointer',
};

export default function CoursesList({
  sellpointId,
  courses: initialCourses,
  categories: initialCategories,
  openCategoriesModal,
  setOpenCategoriesModal,
}: CoursesListProps) {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [update] = useUpdate();
  const [courses, setCourses] = useState(initialCourses);
  const [initialCoursesJSON, setInitalCoursesJSON] = useState(
    JSON.stringify(initialCourses)
  );
  const [openFormModal, setFormOpenModal] = useState(false);

  const [editingCourseIdx, setEditingCourseIdx] = useState<number | null>(null);
  const [categoriesNumber, setCategoriesNumber] = useState<number>();

  // const { data: categoriesData, isLoading } = useGetList('categories');

  useEffect(() => {
    setCourses(initialCourses);
    setInitalCoursesJSON(JSON.stringify(initialCourses));
    setCategoriesNumber(initialCategories.filter(el => el.straightFire).length);
  }, [sellpointId, initialCourses, initialCategories]);

  useEffect(() => {
    if (initialCoursesJSON !== JSON.stringify(courses)) {
      update(RESOURCES.LOCATIONS, {
        id: sellpointId,
        data: {
          courses,
        },
      });
      setInitalCoursesJSON(JSON.stringify(courses));
    }
  }, [courses]);

  const onDragEnd = (result: any) => {
    // dropped outside the list
    if (!result.destination) {
      return;
    }

    const items = reorder(
      courses,
      result.source.index,
      result.destination.index
    );

    setCourses(items);
  };

  const onClose = () => {
    setFormOpenModal(false);
    setEditingCourseIdx(null);
  };

  const handleDelete = () => {
    if (editingCourseIdx !== null) {
      const updatedCourses = courses.filter(
        (_, idx) => idx !== editingCourseIdx
      );

      setCourses(updatedCourses);
      setEditingCourseIdx(null);
      setFormOpenModal(false);
    }
  };

  const handleSaveCourses = (newName: string) => {
    let updatedCourses;
    if (editingCourseIdx !== null) {
      updatedCourses = courses.map((course, idx) =>
        idx === editingCourseIdx ? newName : course
      );
    } else {
      updatedCourses = [...courses, newName];
    }

    setCourses(updatedCourses);
    setEditingCourseIdx(null);
    setFormOpenModal(false);
  };

  const handleSaveCategories = (data: Category[]) => {
    setCategoriesNumber(data.filter(el => el.straightFire).length);
    update(RESOURCES.LOCATIONS, {
      id: sellpointId,
      data: {
        categories: data,
      },
    });
  };

  return (
    <Box>
      <Box
        sx={{
          py: 2,
          px: 1,
          mt: '12px',
          borderBottom: 'solid 1px',
          borderColor: 'custom.gray600',
          bgcolor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
        }}
      >
        <Typography variant="body2" fontWeight={500}>
          {t('shared.name')}
        </Typography>
      </Box>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="droppable">
          {(provided, _) => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {courses.map((item, index) => (
                <Draggable key={item} draggableId={item} index={index}>
                  {(provided, _) => (
                    <Box
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      sx={rowStyle}
                      onClick={() => {
                        setEditingCourseIdx(index);
                        setFormOpenModal(true);
                      }}
                    >
                      <Typography variant="body2" fontWeight={300}>
                        {item}
                      </Typography>
                      <Box
                        {...provided.dragHandleProps}
                        sx={{
                          py: 2,
                          px: 1,
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <DragIndicatorIcon sx={{ mt: 0 }} />
                      </Box>
                    </Box>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
      <Box
        sx={rowStyle}
        px={1}
        py={2}
        mb={2}
        onClick={() => {
          setFormOpenModal(true);
        }}
      >
        <Typography variant="body2" fontWeight={300} color="primary.main">
          {t('devices.courses.addCourse')}
        </Typography>
      </Box>

      <Typography variant="caption" color="custom.gray600">
        {t('devices.courses.tableCaption')}
      </Typography>

      <CoursesFormModal
        open={openFormModal}
        onClose={onClose}
        initialName={
          editingCourseIdx !== null ? courses[editingCourseIdx] : null
        }
        onDelete={handleDelete}
        onSave={handleSaveCourses}
      />

      <CategoriesEditModal
        open={openCategoriesModal}
        onClose={() => setOpenCategoriesModal(false)}
        onSave={handleSaveCategories}
        categoriesData={initialCategories ?? []}
      />
    </Box>
  );
}
