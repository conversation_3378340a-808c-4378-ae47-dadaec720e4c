/**
 * Hook to resolve a user ID to a display name
 *
 * Handles special cases:
 * - @system → "System"
 * - @na → "N/A"
 * - null/undefined → null
 * - Other IDs → Fetches displayName from team members, shows "Unknown User" if not found
 */

import { useMemo } from 'react';
import { useGetOne } from 'react-admin';

import { RESOURCES } from '../providers/resources';

/**
 * Resolves a user ID to a human-readable display name.
 *
 * @param userId - The user ID to resolve (can be a Firebase ID, @system, @na, or null/undefined)
 * @returns The resolved display name, or null if no userId provided
 *
 * @example
 * ```tsx
 * function MyComponent({ userId }: { userId: string }) {
 *   const displayName = useResolveUserName(userId);
 *   return <span>Modified by: {displayName}</span>;
 * }
 * ```
 */
export function useResolveUserName(
  userId: string | undefined | null
): string | null {
  // Determine if we need to fetch from the database
  const shouldFetch = useMemo(() => {
    if (!userId) return false;
    if (userId === '@na' || userId === '@system') return false;
    return true;
  }, [userId]);

  // Fetch team member data if needed
  const { data: teamMember } = useGetOne(
    RESOURCES.TEAM_MEMBERS,
    { id: userId || '' },
    { enabled: shouldFetch }
  );

  // Resolve the display name
  return useMemo(() => {
    if (!userId) return null;
    if (userId === '@system') return 'System';
    if (userId === '@na') return 'N/A';
    if (teamMember?.displayName) return teamMember.displayName;
    if (shouldFetch) return 'Unknown User';
    return null;
  }, [userId, teamMember, shouldFetch]);
}

export default useResolveUserName;
