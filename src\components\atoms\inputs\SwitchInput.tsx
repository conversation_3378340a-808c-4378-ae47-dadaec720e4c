import styled from '@emotion/styled';
import { Switch, SwitchProps } from '@mui/material';
import { BooleanInput, BooleanInputProps } from 'react-admin';

export const SwitchInput = styled(({ source, ...props }: BooleanInputProps) => (
  <BooleanInput
    {...props}
    source={source}
    focusVisibleClassName=".Mui-focusVisible"
    disableRipple
    label=""
  />
))(({ theme }) => ({
  width: 40,
  height: 24,
  padding: 0,
  marginRight: 0,
  marginLeft: 0,
  '& .MuiSwitch-switchBase': {
    width: 40,
    padding: 0,
    margin: 6,
    transitionDuration: '150ms',
    '&.Mui-checked': {
      transform: 'translateX(16px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color:
        (theme as any).palette.mode === 'light'
          ? (theme as any).palette.grey[100]
          : (theme as any).palette.grey[600],
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: (theme as any).palette.mode === 'light' ? 0.7 : 0.3,
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 12,
    height: 12,
    marginTop: 10,
    marginLeft: 0,
    marginRight: 10,
  },
  '& .MuiSwitch-track': {
    borderRadius: 26 / 2,
    height: 21,
    scale: 1.15,
    backgroundColor:
      (theme as any).palette.mode === 'light' ? '#E9E9EA' : '#39393D',
    opacity: 1,
    transition: (theme as any).transitions.create(['background-color'], {
      duration: 300,
    }),
  },
}));

export const MuiSwitchInput = styled((props: SwitchProps) => (
  <Switch {...props} focusVisibleClassName=".Mui-focusVisible" disableRipple />
))(({ theme }) => ({
  width: 40,
  height: 24,
  padding: 0,
  marginRight: 0,
  marginLeft: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    transitionDuration: '150ms',
    '&.Mui-checked': {
      transform: 'translateX(16px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color:
        (theme as any).palette.mode === 'light'
          ? (theme as any).palette.grey[100]
          : (theme as any).palette.grey[600],
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: (theme as any).palette.mode === 'light' ? 0.7 : 0.3,
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 12,
    height: 12,
    marginTop: 6,
    marginLeft: 8,
  },
  '& .MuiSwitch-track': {
    borderRadius: 26 / 2,
    backgroundColor:
      (theme as any).palette.mode === 'light' ? '#E9E9EA' : '#39393D',
    opacity: 1,
    transition: (theme as any).transitions.create(['background-color'], {
      duration: 300,
    }),
  },
}));
