/**
 * Consumer Hooks for Conflict Detection
 *
 * Hooks that components can use to interact with the conflict detection system.
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import {
  useConflictDetection,
  useConflictDetectionOptional,
} from './ConflictDetectionContext';
import { deepEqual } from './entityRegistry';
import { parsePathToEntity } from './pathUtils';
import { Conflict } from './types';

/**
 * Hook for tracking a specific field with conflict awareness.
 * Works with ANY resource - just pass the react-admin/form path.
 *
 * @example
 * ```tsx
 * function MyField() {
 *   const { value, setValue, hasConflict, wasUpdatedRemotely } = useTrackedField('pages[0].items[2].price');
 *   return (
 *     <TextField
 *       value={value}
 *       onChange={e => setValue(e.target.value)}
 *       error={hasConflict}
 *     />
 *   );
 * }
 * ```
 */
export function useTrackedField(path: string) {
  const ctx = useConflictDetection();
  const formContext = useFormContext();
  const value = useWatch({ name: path });

  // Parse path to find entity
  const { entityId, fieldPath } = useMemo(
    () =>
      parsePathToEntity(
        path,
        ctx.state.latestServerSnapshot,
        ctx.state.idField,
        ctx.state.arrayIdFields,
        ctx.state.referenceArrayPaths
      ),
    [
      path,
      ctx.state.latestServerSnapshot,
      ctx.state.idField,
      ctx.state.arrayIdFields,
      ctx.state.referenceArrayPaths,
    ]
  );

  // Track changes when value changes
  useEffect(() => {
    if (entityId && value !== undefined) {
      ctx.trackChange(path, value);
    }
  }, [value, path, entityId, ctx]);

  const setValue = useCallback(
    (newValue: any) => {
      if (entityId && entityId !== 'ROOT') {
        // Sync across all occurrences for multi-occurrence entities
        ctx.syncEntityField(entityId, fieldPath, newValue);
      } else {
        formContext.setValue(path, newValue, { shouldDirty: true });
      }
    },
    [entityId, fieldPath, path, ctx, formContext]
  );

  return {
    /** Current field value */
    value,
    /** Whether this field has a conflict */
    hasConflict: entityId ? ctx.hasFieldConflict(entityId, fieldPath) : false,
    /** Whether this field was updated remotely */
    wasUpdatedRemotely: entityId
      ? ctx.wasFieldUpdatedRemotely(entityId, fieldPath)
      : false,
    /** Remote value for this field (if updated remotely) */
    remoteValue: entityId
      ? ctx.getFieldRemoteValue(entityId, fieldPath)
      : undefined,
    /** Whether this field has local changes */
    isDirty: entityId ? ctx.isFieldDirty(entityId, fieldPath) : false,
    /** Entity ID this field belongs to */
    entityId,
    /** Field path within the entity */
    fieldPath,
    /** Set the field value (syncs across occurrences if multi-occurrence entity) */
    setValue,
  };
}

/**
 * Hook for modals/sub-forms editing a specific entity.
 * Handles path changes when entity moves in the tree.
 *
 * @example
 * ```tsx
 * function ItemModal({ entityId }: { entityId: string }) {
 *   const { path, isDeleted, hasConflicts } = useEntityViewport(entityId);
 *
 *   if (isDeleted) return <div>Item was deleted</div>;
 *
 *   return (
 *     <Modal>
 *       <TextField source={`${path}.name`} />
 *       {hasConflicts && <ConflictWarning />}
 *     </Modal>
 *   );
 * }
 * ```
 */
export function useEntityViewport(entityId: string, parentEntityId?: string) {
  const ctx = useConflictDetection();

  const [currentPath, setCurrentPath] = useState<string | null>(() =>
    ctx.getEntityPath(entityId, parentEntityId)
  );
  const [isDeleted, setIsDeleted] = useState(false);
  const [wasRemoved, setWasRemoved] = useState(false);

  useEffect(() => {
    // Get initial path
    const path = ctx.getEntityPath(entityId, parentEntityId);
    setCurrentPath(path);
    setIsDeleted(false);
    setWasRemoved(false);

    // Register for updates
    const unregister = ctx.registerViewport(entityId, {
      onPathChange: newPath => {
        setCurrentPath(newPath);
      },
      onEntityDeleted: () => {
        setIsDeleted(true);
      },
      onOccurrenceRemoved: () => {
        setWasRemoved(true);
      },
      parentEntityId,
    });

    return unregister;
  }, [entityId, parentEntityId, ctx]);

  // Check if entity has any conflicts
  const hasConflicts = useMemo(
    () =>
      ctx.conflicts.some(
        c =>
          (c.type === 'field_conflict' || c.type === 'entity_deleted') &&
          c.entityId === entityId
      ),
    [ctx.conflicts, entityId]
  );

  return {
    /** Current path to this entity (updates if entity moves) */
    path: currentPath,
    /** Entity ID */
    entityId,
    /** Whether the entity was deleted by another user */
    isDeleted,
    /** Whether this specific occurrence was removed (entity may exist elsewhere) */
    wasRemoved,
    /** Whether there are conflicts for this entity */
    hasConflicts,
    /** Get entity info */
    entity: ctx.getEntity(entityId),
  };
}

/**
 * Hook to get all conflicts for a specific entity.
 */
export function useEntityConflicts(entityId: string): Conflict[] {
  const ctx = useConflictDetection();

  return useMemo(
    () =>
      ctx.conflicts.filter(
        c =>
          (c.type === 'field_conflict' || c.type === 'entity_deleted') &&
          c.entityId === entityId
      ),
    [ctx.conflicts, entityId]
  );
}

/**
 * Hook to check if any field in an entity was updated remotely.
 */
export function useEntityWasUpdated(entityId: string): boolean {
  const ctx = useConflictDetection();

  return useMemo(() => {
    for (const [key] of ctx.state.remoteChanges) {
      if (key.startsWith(`${entityId}:`)) {
        return true;
      }
    }
    return false;
  }, [ctx.state.remoteChanges, entityId]);
}

/**
 * Hook to get the path for a field, relative to the form root.
 * Useful when you have an entity ID and field name but need the full path.
 */
export function useFieldPath(
  entityId: string,
  fieldPath: string,
  parentEntityId?: string
): string | null {
  const ctx = useConflictDetection();

  return useMemo(() => {
    if (entityId === 'ROOT') {
      return fieldPath;
    }

    const entityPath = ctx.getEntityPath(entityId, parentEntityId);
    if (!entityPath) return null;

    return fieldPath ? `${entityPath}.${fieldPath}` : entityPath;
  }, [entityId, fieldPath, parentEntityId, ctx]);
}

/**
 * Hook that provides conflict detection only if available.
 * Useful for components that may or may not be in a conflict detection context.
 */
export function useOptionalTrackedField(path: string) {
  const ctx = useConflictDetectionOptional();
  const formContext = useFormContext();
  const value = useWatch({ name: path });

  // Extract state values we need to watch for changes
  // These will trigger re-renders when remote changes arrive
  const latestServerSnapshot = ctx?.state.latestServerSnapshot;
  const idField = ctx?.state.idField ?? 'id';
  const arrayIdFields = ctx?.state.arrayIdFields ?? {};
  const remoteChanges = ctx?.state.remoteChanges;
  const conflicts = ctx?.state.conflicts;
  const localChanges = ctx?.state.localChanges;

  // Parse path to find entity - must be computed regardless of ctx to maintain hook order
  const parsedPath = useMemo(() => {
    if (!latestServerSnapshot) {
      return { entityId: null, entityPath: null, fieldPath: path };
    }
    return parsePathToEntity(
      path,
      latestServerSnapshot,
      idField,
      arrayIdFields,
      ctx?.state.referenceArrayPaths ?? []
    );
  }, [
    path,
    latestServerSnapshot,
    idField,
    arrayIdFields,
    ctx?.state.referenceArrayPaths,
  ]);

  // Track previous value to avoid infinite loops
  const prevValueRef = useRef<any>(undefined);
  const hasInitializedRef = useRef(false);

  // CRITICAL: Track local changes when value changes
  // This is what enables conflict detection to know the user has modified a field
  // We use a ref to compare against previous value to avoid infinite loops
  useEffect(() => {
    if (!ctx || !parsedPath.entityId || value === undefined) {
      return;
    }

    // Skip the initial render - we don't want to track the initial value as a "change"
    if (!hasInitializedRef.current) {
      hasInitializedRef.current = true;
      prevValueRef.current = value;
      return;
    }

    // Only track if value actually changed from the previous tracked value
    // Use deepEqual for proper array/object comparison
    if (!deepEqual(prevValueRef.current, value)) {
      prevValueRef.current = value;
      ctx.trackChange(path, value);
    }
  }, [ctx, path, value, parsedPath.entityId, parsedPath.fieldPath]);

  // Compute conflict/update status - depends on remoteChanges Map which should trigger re-render
  const conflictStatus = useMemo(() => {
    if (!ctx || !latestServerSnapshot) {
      return {
        hasConflict: false,
        wasUpdatedRemotely: false,
        remoteValue: undefined,
        remoteUserId: undefined,
        remoteTimestamp: undefined,
        isDirty: false,
      };
    }

    const { entityId, fieldPath } = parsedPath;

    if (!entityId) {
      return {
        hasConflict: false,
        wasUpdatedRemotely: false,
        remoteValue: undefined,
        remoteUserId: undefined,
        remoteTimestamp: undefined,
        isDirty: false,
      };
    }

    // Check directly against the Maps for proper reactivity
    const changeKey = `${entityId}:${fieldPath}`;

    // Helper to check if a path or any of its parent paths match
    // e.g., for "charge.fixed.value", check "charge.fixed.value", "charge.fixed", "charge"
    const findMatchingChange = (
      changesMap: Map<string, any> | undefined,
      entId: string,
      fPath: string
    ): { key: string; change: any } | null => {
      if (!changesMap) return null;

      // First check exact path
      const exactKey = `${entId}:${fPath}`;
      if (changesMap.has(exactKey)) {
        return { key: exactKey, change: changesMap.get(exactKey) };
      }

      // Check parent paths (for nested object updates)
      // e.g., "charge.fixed.value" -> check "charge.fixed", then "charge"
      const pathParts = fPath.split('.');
      for (let i = pathParts.length - 1; i > 0; i--) {
        const parentPath = pathParts.slice(0, i).join('.');
        const parentKey = `${entId}:${parentPath}`;
        if (changesMap.has(parentKey)) {
          return { key: parentKey, change: changesMap.get(parentKey) };
        }
      }

      return null;
    };

    // Check for conflicts - exact match or parent path where the specific nested value conflicts
    const hasConflict =
      conflicts?.some(c => {
        if (c.type !== 'field_conflict' || c.entityId !== entityId)
          return false;
        // Exact match
        if (c.fieldPath === fieldPath) return true;
        // Check if conflict is on a parent path (nested object was changed)
        if (fieldPath.startsWith(c.fieldPath + '.')) {
          // Extract the specific nested values to check if THIS field conflicts
          const remainingPath = fieldPath.slice(c.fieldPath.length + 1);
          let localNestedValue = c.localValue;
          let remoteNestedValue = c.remoteValue;
          let originalNestedValue = c.originalValue;
          for (const part of remainingPath.split('.')) {
            localNestedValue = localNestedValue?.[part];
            remoteNestedValue = remoteNestedValue?.[part];
            originalNestedValue = originalNestedValue?.[part];
          }
          // Only conflict if this specific nested value differs between local and remote
          // AND at least one of them changed from original
          const localChanged = !deepEqual(
            localNestedValue,
            originalNestedValue
          );
          const remoteChanged = !deepEqual(
            remoteNestedValue,
            originalNestedValue
          );
          const valuesDiffer = !deepEqual(localNestedValue, remoteNestedValue);
          return localChanged && remoteChanged && valuesDiffer;
        }
        return false;
      }) ?? false;

    // Check for remote changes - exact match or parent path
    const matchingRemoteChange = findMatchingChange(
      remoteChanges,
      entityId,
      fieldPath
    );

    // Get the remote value - for nested paths, extract the specific value
    let remoteValue: any;
    let remoteUserId: string | undefined;
    let remoteTimestamp: number | undefined;
    let wasUpdatedRemotely = false;

    if (matchingRemoteChange) {
      const change = matchingRemoteChange.change;
      remoteUserId = change?.userId;
      remoteTimestamp = change?.timestamp;

      // If the change is on a parent path, extract the nested value
      const changePath = matchingRemoteChange.key.split(':')[1];
      if (changePath !== fieldPath && fieldPath.startsWith(changePath + '.')) {
        // Extract nested value from the changed object (both old and new)
        const remainingPath = fieldPath.slice(changePath.length + 1);
        let newNestedValue = change?.newValue;
        let oldNestedValue = change?.oldValue;
        for (const part of remainingPath.split('.')) {
          newNestedValue = newNestedValue?.[part];
          oldNestedValue = oldNestedValue?.[part];
        }
        remoteValue = newNestedValue;
        // Only mark as updated if the specific nested value actually changed
        wasUpdatedRemotely = !deepEqual(oldNestedValue, newNestedValue);
      } else {
        // Exact match - the field itself was updated
        remoteValue = change?.newValue;
        wasUpdatedRemotely = true;
      }
    }

    // Check if field is dirty (local changes)
    const matchingLocalChange = findMatchingChange(
      localChanges,
      entityId,
      fieldPath
    );
    const isDirty = matchingLocalChange !== null;

    return {
      hasConflict,
      wasUpdatedRemotely,
      remoteValue,
      remoteUserId,
      remoteTimestamp,
      isDirty,
    };
  }, [
    ctx,
    latestServerSnapshot,
    parsedPath,
    remoteChanges,
    conflicts,
    localChanges,
    path,
  ]);

  // Create setValue callback
  const setValue = useCallback(
    (newValue: any) => {
      const { entityId, fieldPath } = parsedPath;
      if (ctx && entityId && entityId !== 'ROOT') {
        ctx.syncEntityField(entityId, fieldPath, newValue);
      } else {
        formContext.setValue(path, newValue, { shouldDirty: true });
      }
    },
    [ctx, parsedPath, formContext, path]
  );

  return {
    value,
    ...conflictStatus,
    entityId: parsedPath.entityId,
    fieldPath: parsedPath.fieldPath,
    setValue,
  };
}
