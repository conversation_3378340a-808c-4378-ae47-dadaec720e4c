import { useState } from 'react';
import { Button, Theme, useMediaQuery } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  FormDataConsumer,
  SaveButton,
  SimpleForm,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import { PermissionsParts } from './components';

export default function PermissionCreate() {
  const { t } = useTranslation();
  const redirect = useRedirect();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const [step, setStep] = useState<number>(0);

  const handleClose = () => {
    setStep(0);
    redirect('list', RESOURCES.PERMISSIONS);
  };

  return (
    <CreateDialog {...getFullscreenModalProps()}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={handleClose}
          title={t('createPermissions.title')}
          alignCenter={!isXSmall}
        >
          <FormDataConsumer>
            {({ formData }) => (
              <>
                {step === 0 ? (
                  <Button
                    variant="contained"
                    onClick={() => setStep(1)}
                    disabled={!formData.name || !formData.permissions?.length}
                  >
                    {t('shared.continue')}
                  </Button>
                ) : (
                  <SaveButton
                    type="submit"
                    label={t('shared.save')}
                    icon={<></>}
                    alwaysEnable
                    mutationOptions={{ onSuccess: handleClose }}
                  />
                )}
              </>
            )}
          </FormDataConsumer>
        </ModalHeader>

        <PermissionsParts.Form step={step} setStep={setStep} />
      </SimpleForm>
    </CreateDialog>
  );
}
