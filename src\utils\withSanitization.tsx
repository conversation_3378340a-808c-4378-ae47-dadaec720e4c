/**
 * Higher-Order Component for Input Sanitization
 *
 * Wraps any input component and automatically sanitizes text input
 * by intercepting onChange/onInputChange event handlers.
 *
 * @module withSanitization
 */

import React, { useCallback } from 'react';

import {
  SanitizationOptions,
  sanitizeTextInput,
  SINGLE_LINE_PRESET,
} from './sanitizeTextInput';

/**
 * Configuration options for the withSanitization HOC
 */
export interface WithSanitizationConfig extends SanitizationOptions {
  /**
   * Event handler names to intercept and sanitize
   * @default ['onChange', 'onInputChange']
   */
  handlers?: ('onChange' | 'onInputChange' | 'onBlur')[];

  /**
   * Enable debug logging for sanitization
   * @default false
   */
  debug?: boolean;

  /**
   * Custom sanitization function (overrides default)
   */
  customSanitizer?: (value: any) => any;
}

/**
 * Default configuration for withSanitization HOC
 */
const DEFAULT_CONFIG: Required<
  Omit<WithSanitizationConfig, keyof SanitizationOptions | 'customSanitizer'>
> &
  SanitizationOptions = {
  ...SINGLE_LINE_PRESET,
  handlers: ['onChange', 'onInputChange'],
  debug: false,
};

/**
 * Higher-Order Component that adds text sanitization to any input component
 *
 * @param Component - The component to wrap
 * @param config - Sanitization configuration
 * @returns Wrapped component with sanitization
 *
 * @example
 * ```typescript
 * import { TextField } from '@mui/material';
 * import { withSanitization } from '~/utils/withSanitization';
 *
 * const SanitizedTextField = withSanitization(TextField);
 *
 * // Use it like the original component
 * <SanitizedTextField
 *   label="Name"
 *   value={name}
 *   onChange={(e) => setName(e.target.value)}
 * />
 * ```
 *
 * @example
 * ```typescript
 * // With custom options
 * const UppercaseTextField = withSanitization(TextField, {
 *   uppercase: true,
 *   handlers: ['onChange', 'onBlur'],
 * });
 * ```
 */
export function withSanitization<P extends Record<string, any>>(
  Component: React.ComponentType<P>,
  userConfig: WithSanitizationConfig = {}
): React.ComponentType<P> {
  const config = { ...DEFAULT_CONFIG, ...userConfig };
  const { handlers, debug, customSanitizer, ...sanitizationOptions } = config;

  const sanitize = customSanitizer
    ? customSanitizer
    : (value: any) => sanitizeTextInput(value, sanitizationOptions);

  const SanitizedComponent = React.forwardRef<any, P>((props, ref) => {
    /**
     * Creates a sanitized version of an event handler
     */
    const createSanitizedHandler = useCallback(
      (
        handlerName: string,
        originalHandler: ((...args: any[]) => void) | undefined
      ) => {
        if (!originalHandler) return undefined;

        return (...args: any[]) => {
          try {
            let sanitizedArgs = [...args];

            // Handle different component patterns
            switch (handlerName) {
              case 'onChange': {
                // MUI TextField pattern: onChange(event)
                if (args[0]?.target !== undefined) {
                  const event = args[0];
                  const originalValue = event.target.value;
                  const sanitizedValue = sanitize(originalValue);

                  if (debug && originalValue !== sanitizedValue) {
                    console.log('[withSanitization] Sanitized onChange:', {
                      original: originalValue,
                      sanitized: sanitizedValue,
                      component: Component.displayName || Component.name,
                    });
                  }

                  // Create a new event object with sanitized value
                  const sanitizedEvent = {
                    ...event,
                    target: {
                      ...event.target,
                      value: sanitizedValue,
                    },
                  };

                  sanitizedArgs = [sanitizedEvent, ...args.slice(1)];
                }
                // React-Admin pattern: onChange(value)
                else {
                  const originalValue = args[0];
                  const sanitizedValue = sanitize(originalValue);

                  if (debug && originalValue !== sanitizedValue) {
                    console.log('[withSanitization] Sanitized onChange:', {
                      original: originalValue,
                      sanitized: sanitizedValue,
                      component: Component.displayName || Component.name,
                    });
                  }

                  sanitizedArgs = [sanitizedValue, ...args.slice(1)];
                }
                break;
              }

              case 'onInputChange': {
                // MUI Autocomplete pattern: onInputChange(event, value, reason)
                const [event, value, reason] = args;
                const sanitizedValue = sanitize(value);

                if (debug && value !== sanitizedValue) {
                  console.log('[withSanitization] Sanitized onInputChange:', {
                    original: value,
                    sanitized: sanitizedValue,
                    reason,
                    component: Component.displayName || Component.name,
                  });
                }

                sanitizedArgs = [event, sanitizedValue, reason];
                break;
              }

              case 'onBlur': {
                // MUI TextField pattern: onBlur(event)
                if (args[0]?.target !== undefined) {
                  const event = args[0];
                  const originalValue = event.target.value;
                  const sanitizedValue = sanitize(originalValue);

                  if (debug && originalValue !== sanitizedValue) {
                    console.log('[withSanitization] Sanitized onBlur:', {
                      original: originalValue,
                      sanitized: sanitizedValue,
                      component: Component.displayName || Component.name,
                    });
                  }

                  const sanitizedEvent = {
                    ...event,
                    target: {
                      ...event.target,
                      value: sanitizedValue,
                    },
                  };

                  sanitizedArgs = [sanitizedEvent, ...args.slice(1)];
                } else {
                  // Direct value handler
                  const originalValue = args[0];
                  const sanitizedValue = sanitize(originalValue);

                  if (debug && originalValue !== sanitizedValue) {
                    console.log('[withSanitization] Sanitized onBlur:', {
                      original: originalValue,
                      sanitized: sanitizedValue,
                      component: Component.displayName || Component.name,
                    });
                  }

                  sanitizedArgs = [sanitizedValue, ...args.slice(1)];
                }
                break;
              }

              default:
                // Unknown handler - pass through
                break;
            }

            return originalHandler(...sanitizedArgs);
          } catch (error) {
            console.error(
              `[withSanitization] Error in ${handlerName} handler:`,
              error
            );
            // Fallback to original handler with original args
            return originalHandler(...args);
          }
        };
      },
      [sanitize, debug]
    );

    // Create sanitized props by intercepting specified handlers
    const sanitizedProps = { ...props };

    handlers.forEach(handlerName => {
      const originalHandler = props[handlerName];
      const sanitizedHandler = createSanitizedHandler(
        handlerName,
        originalHandler
      );

      if (sanitizedHandler) {
        // @ts-ignore - We're dynamically assigning handlers
        sanitizedProps[handlerName] = sanitizedHandler;
      }
    });

    return <Component ref={ref} {...(sanitizedProps as P)} />;
  });

  // Set display name for better debugging
  const componentName = Component.displayName || Component.name || 'Component';
  SanitizedComponent.displayName = `Sanitized(${componentName})`;

  return SanitizedComponent as unknown as React.ComponentType<P>;
}

/**
 * Utility function to create a sanitized component with specific presets
 */
export const createSanitizedComponent = {
  /**
   * Create a sanitized component for single-line text inputs
   */
  singleLine: <P extends Record<string, any>>(
    Component: React.ComponentType<P>
  ) =>
    withSanitization(Component, {
      ...SINGLE_LINE_PRESET,
      handlers: ['onChange', 'onBlur'],
    }),

  /**
   * Create a sanitized component for multi-line text inputs
   */
  multiLine: <P extends Record<string, any>>(
    Component: React.ComponentType<P>
  ) =>
    withSanitization(Component, {
      preserveLineBreaks: true,
      handlers: ['onChange', 'onBlur'],
    }),

  /**
   * Create a sanitized component for autocomplete inputs
   */
  autocomplete: <P extends Record<string, any>>(
    Component: React.ComponentType<P>
  ) =>
    withSanitization(Component, {
      handlers: ['onInputChange'],
    }),

  /**
   * Create a sanitized component for identifier inputs (SKU, codes)
   */
  identifier: <P extends Record<string, any>>(
    Component: React.ComponentType<P>
  ) =>
    withSanitization(Component, {
      uppercase: true,
      handlers: ['onChange', 'onBlur'],
    }),
};
