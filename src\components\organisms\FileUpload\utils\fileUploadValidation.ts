import { FileUploadConfig, ValidationError } from '../types';
import { formatFileSize } from './fileSizeUtils';

/**
 * Translation function type - compatible with react-i18next
 */
type TFunction = (key: string, defaultValue?: string, options?: any) => string;

/**
 * Validate a single file against the configuration
 */
export const validateFile = (
  file: File,
  config: FileUploadConfig,
  existingFiles: File[] = [],
  t?: TFunction
): ValidationError[] => {
  const errors: ValidationError[] = [];
  const fileId = `${file.name}-${file.size}-${file.lastModified}`;
  const fileName =
    file.name ||
    t?.('fileUpload.validation.unknownFile', 'Unknown file') ||
    'Unknown file';

  // File size validation
  if (file.size > config.maxSize) {
    errors.push({
      id: `${fileId}-size`,
      message: t
        ? t(
            'fileUpload.validation.fileTooLarge',
            'File "{{fileName}}" is too large. Maximum size is {{maxSize}}.',
            {
              fileName,
              maxSize: formatFileSize(config.maxSize),
            }
          )
        : `File "${fileName}" is too large. Maximum size is ${formatFileSize(config.maxSize)}.`,
      severity: 'error',
      fileName,
      file,
    });
  }

  // Minimum file size validation
  if (
    config.validation?.minFileSize &&
    file.size < config.validation.minFileSize
  ) {
    errors.push({
      id: `${fileId}-min-size`,
      message: t
        ? t(
            'fileUpload.validation.fileTooSmall',
            'File "{{fileName}}" is too small. Minimum size is {{minSize}}.',
            {
              fileName,
              minSize: formatFileSize(config.validation.minFileSize),
            }
          )
        : `File "${fileName}" is too small. Minimum size is ${formatFileSize(config.validation.minFileSize)}.`,
      severity: 'error',
      fileName,
      file,
    });
  }

  // File type validation
  if (!config.acceptedTypes.includes(file.type)) {
    errors.push({
      id: `${fileId}-type`,
      message: t
        ? t(
            'fileUpload.validation.unsupportedFileType',
            'File "{{fileName}}" has an unsupported type. Accepted types: {{acceptedTypes}}.',
            {
              fileName,
              acceptedTypes: config.acceptedTypes.join(', '),
            }
          )
        : `File "${fileName}" has an unsupported type. Accepted types: ${config.acceptedTypes.join(', ')}.`,
      severity: 'error',
      fileName,
      file,
    });
  }

  // Custom validation
  if (config.validation?.customValidation) {
    const customError = config.validation.customValidation(file);
    if (customError) {
      errors.push({
        id: `${fileId}-custom`,
        message: customError,
        severity: 'error',
        fileName,
        file,
      });
    }
  }

  return errors;
};

/**
 * Validate multiple files against the configuration
 */
export const validateFiles = (
  files: File[],
  config: FileUploadConfig,
  existingFiles: File[] = [],
  t?: TFunction
): ValidationError[] => {
  const errors: ValidationError[] = [];
  const allFiles = [...existingFiles, ...files];

  // File count validation
  if (!config.multiple && files.length > 1) {
    errors.push({
      id: 'multiple-not-allowed',
      message: t
        ? t(
            'fileUpload.validation.onlyOneFileAllowed',
            'Only one file is allowed.'
          )
        : 'Only one file is allowed.',
      severity: 'error',
    });
  }

  if (allFiles.length > config.maxFiles) {
    errors.push({
      id: 'max-files-exceeded',
      message: t
        ? t(
            'fileUpload.validation.tooManyFiles',
            'Too many files. Maximum allowed: {{maxFiles}}.',
            {
              maxFiles: config.maxFiles,
            }
          )
        : `Too many files. Maximum allowed: ${config.maxFiles}.`,
      severity: 'error',
    });
  }

  // Minimum file count validation
  if (
    config.validation?.minFileCount &&
    allFiles.length < config.validation.minFileCount
  ) {
    errors.push({
      id: 'min-files-not-met',
      message: t
        ? t(
            'fileUpload.validation.notEnoughFiles',
            'Not enough files. Minimum required: {{minFiles}}.',
            {
              minFiles: config.validation.minFileCount,
            }
          )
        : `Not enough files. Minimum required: ${config.validation.minFileCount}.`,
      severity: 'warning',
    });
  }

  // Validate each file individually
  files.forEach(file => {
    const fileErrors = validateFile(file, config, existingFiles, t);
    errors.push(...fileErrors);
  });

  // Check for duplicate files
  const fileMap = new Map<string, File[]>();
  allFiles.forEach((file, index) => {
    // Only process files that have valid size
    if (file && typeof file.size === 'number') {
      // Use file name if available, otherwise create a unique identifier for unnamed files
      const fileName = file.name || `unnamed-file-${index}`;
      const key = `${fileName}-${file.size}`;

      if (!fileMap.has(key)) {
        fileMap.set(key, []);
      }
      fileMap.get(key)!.push(file);
    }
  });

  fileMap.forEach((duplicateFiles, key) => {
    if (duplicateFiles.length > 1) {
      // Only show duplicate warning if files actually have names (not our generated unnamed identifiers)
      const firstFile = duplicateFiles[0];
      if (firstFile && firstFile.name && !key.startsWith('unnamed-file-')) {
        const fileName = firstFile.name;
        errors.push({
          id: `duplicate-${key}`,
          message: t
            ? t(
                'fileUpload.validation.duplicateFile',
                'Duplicate file detected: "{{fileName}}".',
                {
                  fileName,
                }
              )
            : `Duplicate file detected: "${fileName}".`,
          severity: 'warning',
          fileName,
        });
      }
    }
  });

  return errors;
};

/**
 * Filter files based on validation results
 */
export const filterValidFiles = (
  files: File[],
  validationErrors: ValidationError[],
  allowInvalidFiles: boolean = false
): File[] => {
  if (allowInvalidFiles) {
    return files;
  }

  const errorFiles = new Set(
    validationErrors
      .filter(error => error.severity === 'error' && error.file)
      .map(error => error.file!)
  );

  return files.filter(file => !errorFiles.has(file));
};

/**
 * Check if validation errors contain any errors (not just warnings)
 */
export const hasValidationErrors = (errors: ValidationError[]): boolean => {
  return errors.some(error => error.severity === 'error');
};

/**
 * Group validation errors by severity
 */
export const groupValidationErrors = (errors: ValidationError[]) => {
  const grouped = {
    errors: errors.filter(error => error.severity === 'error'),
    warnings: errors.filter(error => error.severity === 'warning'),
  };

  return {
    ...grouped,
    hasErrors: grouped.errors.length > 0,
    hasWarnings: grouped.warnings.length > 0,
    total: errors.length,
  };
};

/**
 * Get validation summary message
 */
export const getValidationSummary = (errors: ValidationError[]): string => {
  const grouped = groupValidationErrors(errors);

  if (!grouped.hasErrors && !grouped.hasWarnings) {
    return 'All files are valid.';
  }

  const parts: string[] = [];

  if (grouped.hasErrors) {
    parts.push(
      `${grouped.errors.length} error${grouped.errors.length > 1 ? 's' : ''}`
    );
  }

  if (grouped.hasWarnings) {
    parts.push(
      `${grouped.warnings.length} warning${grouped.warnings.length > 1 ? 's' : ''}`
    );
  }

  return `Found ${parts.join(' and ')}.`;
};

/**
 * Validate file type configuration
 */
export const validateFileTypeConfiguration = (
  config: FileUploadConfig
): string[] => {
  const errors: string[] = [];

  // Check if file type matches accepted types
  const fileTypeMap = {
    images: ['image/'],
    videos: ['video/'],
    public: ['application/', 'text/', 'image/'],
    private: ['application/', 'text/', 'image/'],
  };

  const expectedPrefixes = fileTypeMap[config.fileType] || [];
  const hasMatchingTypes = config.acceptedTypes.some(type =>
    expectedPrefixes.some(prefix => type.startsWith(prefix))
  );

  if (!hasMatchingTypes) {
    errors.push(`Accepted types don't match file type "${config.fileType}"`);
  }

  // Validate image-specific configuration
  if (config.fileType === 'images') {
    if (!config.acceptedTypes.some(type => type.startsWith('image/'))) {
      errors.push('Image file type must include image MIME types');
    }

    if (config.imageConfig?.editorConfig && !config.imageConfig.targetSizes) {
      // This is actually valid - editor can be enabled without target sizes for manual cropping
    }
  } else {
    // Non-image file types shouldn't have image config
    if (config.imageConfig?.editorConfig) {
      errors.push(
        `Image editor is not supported for file type "${config.fileType}"`
      );
    }
  }

  return errors;
};
