import { Box } from '@mui/material';
import { required, useInput } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomInput } from '~/components/atoms/inputs/CustomInput/CustomInput';
import ControlledRadioInputGroup from '~/components/molecules/controlled-input-groups/ControlledRadioInputGroup';
import { useGetListLocationsLive } from '~/providers/resources';

export default function MenuCreateStep2() {
  const { data: sellPoints } = useGetListLocationsLive();
  const { t } = useTranslation();
  const { field } = useInput({ source: 'type', defaultValue: 'pos' });

  return (
    <>
      <Box>
        <CustomInput
          type="text"
          ui="custom"
          sanitize="singleLine"
          source="name"
          label={t('shared.name')}
          validate={[required()]}
          roundedCorners="top"
        />
        <ControlledRadioInputGroup
          title={t('menu.menuType')}
          value={field.value}
          setValue={val => {
            field.onChange(val);
          }}
          choices={[
            {
              id: 'pos',
              name: t('menu.editPOSLayout'),
              description: t('menu.posLayoutDescription'),
            },
            {
              id: 'list',
              name: t('menu.menuList'),
              description: t('menu.listViewDescription'),
            },
          ]}
        />
        <CustomInput
          source="sellPointIds"
          choices={sellPoints}
          type="selectArray"
          ui="custom"
          label={t('shared.location')}
          placeholder={t('shared.none', { context: 'female' })}
          optionText="name"
          optionValue="id"
          validate={[required()]}
          roundedCorners="bottom"
        />
      </Box>
    </>
  );
}
